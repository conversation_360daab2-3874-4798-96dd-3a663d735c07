
//统一页面启动
$(document).ready(() => {
  let params = {}
  //获取关键字数据
  let url = window.location.href.split("?") || []
  let text = url[1].split("&")
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
  WRT_config.url = params
  app.init()
})

var app = {
  init: function () {
    WRT_e.api.BrMainLeft.getinit({
      params: {
        ll_blid: WRT_config.url.al_blid,
        ls_idb: WRT_config.url.ls_idb,
        al_zkid:WRT_config.url.ZKID,
        al_yhid:WRT_config.url.YSYHID
      },
      success(data) {
        let obj = JSON.parse(data.Result)
        console.log('图标数据',obj);

        if (data.Code == 1) {
          WRT_config.BrMainLeft = obj
          // WRT_config.BrMainLeft.rlDbz_signal = true
          // WRT_config.BrMainLeft.rlDbz_rl = "100"
          // WRT_config.BrMainLeft.rlDbz_dbz = "50"
          // WRT_config.BrMainLeft.rlDbz_PnEn = [
          //   {"pn":"0.03","en":"0.97","xgsj":"2023-09-21T16:22:51"},
          //   {"pn":"0.02","en":"0.98","xgsj":"2023-10-11T14:44:45"},
          //   {"pn":"0.05","en":"0.95","xgsj":"2023-10-11T14:59:41"},
          //   {"pn":"0.05","en":"0.95","xgsj":"2023-10-13T09:09:05"},
          //   {"pn":"0.06","en":"0.94","xgsj":"2023-10-13T10:11:55"},
          //   {"pn":"0.05","en":"0.95","xgsj":"2023-10-20T15:05:28"},
          //   {"pn":"0.02","en":"0.98","xgsj":"2023-10-20T15:58:54"},
          //   {"pn":"0.04","en":"0.96","xgsj":"2023-10-23T15:44:12"},
          //   {"pn":"0.02","en":"0.98","xgsj":"2023-10-24T11:19:32"},
          //   {"pn":"0.05","en":"0.95","xgsj":"2023-10-24T11:20:06"},
          //   {"pn":"0.02","en":"0.98","xgsj":"2023-11-15T11:10:17"}
          // ]
          nldbz_Chart()
        }
      }
    })
  }
}


/***********  视图  ***********/

// 摄入蛋白质和能力 医嘱页面
function nldbz_Chart(){
  var myChart = echarts.init(document.getElementById('NLCharts'));
  // let a = [...WRT_config.BrMainLeft.rlDbz_PnEn, ...WRT_config.BrMainLeft.rlDbz_PnEn]
  let xAxisData = [];
  let data1 = [];
  let data2 = [];
  (WRT_config.BrMainLeft.rlDbz_PnEn).forEach(item => {
    xAxisData.push(item.xgsj.replace('T', '\n'))
    data2.push(item.pn)
    data1.push(item.en)
  });
  // 获取数据，数据整理
  // console.log('hsuju ',WRT_config.BrMainLeft);
  // for (let i = 0; i < 10; i++) {
  //   xAxisData.push('Class' + i); // 换成时间
  //   data1.push(+(Math.random() * 2).toFixed(2)); // rlDbz_PN
  //   data2.push(+(Math.random() * 5).toFixed(2)); // rlDbz_EN
  // }
  var emphasisStyle = {
    itemStyle: {
      shadowBlur: 10,
      shadowColor: 'rgba(0,0,0,0.3)'
    }
  };
  var option = {
    legend: {
      data: ['PN %', '（EN + 经口） %'],
      right: 10,
      top:30
    },
    title: {
      left: 'center',
      text: '摄入热量和蛋白质柱状图',
      textStyle: {
        fontSize: 22
      }
    },
    tooltip: {},
    // // 日期区间
    // dataZoom: [
    //   {
    //     type: 'inside'
    //   },
    //   {
    //     type: 'slider'
    //   }
    // ],
    xAxis: {
      data: xAxisData,
      name: '日 期',
      axisLabel: {
        // interval: 0,
        rotate:-30,
        width:10,
        padding: [10, 0],
        margin: 18,
        textShadowOffsetX:'breakAll'
      },
    },
    yAxis: {
      axisLabel: {
        formatter: '{value} %'
      }
    },
    grid: {
      top: 60,
      bottom: 90
    },
    series: [
      {
        name: '（EN + 经口） %',
        type: 'bar',
        stack: 'one',
        emphasis: emphasisStyle,
        data: data1
      },
      {
        name: 'PN %',
        type: 'bar',
        stack: 'one',
        emphasis: emphasisStyle,
        data: data2
      }
    ]
  };

  option && myChart.setOption(option);
}