WRT_e.api = WRT_e.api || {}

WRT_e.api.blcxgj = {
  //初始化
  getinit: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":blcxgj
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/blcx/blcxgj.aspx/e_init',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if(o.success) o.success(msg.d)
        },
        error:function(errpr){
        }
      })
    }
  },
  getSearch: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":blnrcx
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/blcx/blcxgj.aspx/e_Search',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
        }
      })
    }
  },
}