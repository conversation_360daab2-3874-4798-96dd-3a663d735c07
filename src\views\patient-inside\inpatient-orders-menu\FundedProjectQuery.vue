<template>
  <div class="container">
    <div class="header">
      <div class="button">
        <el-button class="purple-button" type="primary" @click="getSelfPayItems(false)">
          查询
        </el-button>
        <el-button type="primary" @click="getSelfPayItems(true)">自费金额≥200</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">自费项目查询</div>
      </div>
      <div class="table">
        <el-table
          border
          :data="tableData"
          style="width: 1150px"
          height="100%"
          max-height="560px"
          :span-method="objectSpanMethod"
        >
          <el-table-column
            prop="fenLeiMaMC"
            width="100"
            label="收费类别"
            align="center"
          ></el-table-column>
          <el-table-column prop="shouFeiSJ" width="150" label="收费时间"></el-table-column>
          <el-table-column prop="mingCheng" label="收费名称"></el-table-column>
          <el-table-column prop="guiGe" width="200" label="用法用量"></el-table-column>
          <el-table-column prop="shuLiang" width="60" label="数量"></el-table-column>
          <el-table-column prop="shouFeiDJ" width="80" label="单价"></el-table-column>
          <el-table-column prop="zongJinE" width="80" label="金额"></el-table-column>
          <el-table-column prop="yiShengXM" width="90" label="开单医生"></el-table-column>
          <el-table-column prop="shouFeiRYXM" width="90" label="收费人员"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getSelfPayItemsByCondition } from '@/api/inpatient-order'

export default {
  name: 'FundedProjectQuery',
  data() {
    return {
      tableData: []
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.getSelfPayItems(false)
  },
  methods: {
    async getSelfPayItems(greaterThan200) {
      const res = await getSelfPayItemsByCondition({
        bingLiID: this.bingLiID,
        greaterThan200: greaterThan200
      })
      this.tbArrange(res, 'fenLeiMaMC')
    },
    tbArrange(res, zdName) {
      for (let i = 0; i < res.data.length; i++) {
        res.data[i]['rowspan'] = 1
        for (let j = i + 1; j < res.data.length; j++) {
          if (res.data[i][zdName] === res.data[j][zdName]) {
            res.data[i]['rowspan'] += 1
            res.data[j]['rowspan'] = 0
            if (j === res.data.length - 1) {
              i = j
            }
          } else {
            i = j - 1
            break
          }
        }
      }
      this.tableData = res.data
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (row.rowspan !== 0) {
          return {
            rowspan: row.rowspan,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  padding: 12px 14px;

  .button {
    white-space: nowrap;
    margin-left: 6px;

    .purple-button {
      background: var(--color-purple);
      border: 1px solid var(--color-purple);

      &:hover,
      &:focus {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  .table {
    flex: 1;
    overflow: auto;
  }
}

::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
</style>
