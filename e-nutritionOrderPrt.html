<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>营养医嘱</title>
  <meta name="description" content="营养医嘱(打印)">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
#nutritionOrder {
  width: 100%;
}
#nutritionOrder, #nutriOrderShowBaseInfo, #nutriOrderTableAll, .nutriOrderTitle, .nutriOrderList, .nutriOrderCountTable, .nutriOrderBtnAll, .nutriOrderShowTable {
  text-align: center;
  margin: auto;
}

 /* 基础信息 */
#nutriOrderShowBaseInfo {
  font-family: 'Microsoft YaHei';
  width: 96%;
  padding: 1px 3px;
  /* padding: 1%; */
  align-items: center;
  /* border-bottom: 1px solid #E6E6E6; */
}
.tBinfo {
  /* padding-right: 10px;     */
  /* width: 1000px; */
  text-align: center;
  margin: auto;
}


/* 第二部分 */
.line1 {
  padding-top: 2px;
  display: flex;
  justify-content: center;
}
#tb_Table {
  width: 500px;
  /* border: 1px solid #808080; */
}
#table_header {
  border: 1px solid #808080; 
}
.table_nrInput {
  width: 48px;
  color: red;
}
/* 最终数据展示部分 */
.line2 {
  padding-top: 15px;
}
.show_Table {
  width: 100%;
  /* width: 96%; */
  margin: auto;
  /* border: 1px solid #EBEBEB; */
  border: 1px solid #808080;
  
}
.show_Table .table-nr{
  padding: 2px 5px;
}
.table-nr > span {
  display: flex;
  justify-content: space-between;
}
.table-nr1 >span {
  display: flex;
  justify-content: flex-start;
}
/* 第三部分 */
.line3 {
  text-align: start;
  /* width: 50%; */
  padding: 30px 5px 5px;
  display: flex;
  justify-content: flex-start;
  border-bottom: 1px solid #808080;
  border-right: 1px solid #808080;

}
</style>
<body id="nutritionOrder">
  <div class="nutriOrderMain">
    <!-- 基础 -->
    <div id="nutriOrderShowBaseInfo"></div>
    <!-- 表格汇总 -->
    <div id="nutriOrderTableAll">
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面JS -->
  <!-- <script src='js/e-EhrSz.js'></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-nutritionOrderPrt.js'><\/script>");
  </script>
</body>
</html>