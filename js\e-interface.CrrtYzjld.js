WRT_e.api = WRT_e.api || {}

WRT_e.api.CrrtYzjld = {
  // 外部 患者基本信息 + 获取处方单表格数据 + 详情 + 结束（判断用户身份 + 获取用户信息） + 停止（判断用户身份 + 获取用户信息
  //  患者基本信息  【生命体征】按病历ID获取生命体征列表  10.104.141.230:38081/patient/v1/in-patient/getInPatientByZyID?zhuYuanID=
  getbrSMTZ: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/patient/v1/in-patient/getInPatientByZyID?zhuYuanID=${o.params.zhuYuanID}`,
        type: 'post',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 生命体征  10.104.141.230:38081/nursingservice/v1/ShengMingTZ/getVitalSignList?bingLiID=451078
  getSMTZ: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/nursingservice/v1/ShengMingTZ/getVitalSignList?bingLiID=${o.params.bingLiID}`,
        type: 'post',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 判断用户身份  http://10.104.141.230:38081/staff/v1/doctor/getMedicalStaffType?yongHulD=519
  getMedicalStaffType: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/staff/v1/doctor/getMedicalStaffType?yongHuID=${o.params.yongHuID}`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 获取用户信息 10.104.141.230:38081/staff/v1/doctor/getDoctorSimpleByYongHuID?yongHuID=
  getDoctorSimpleByYongHuID: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/staff/v1/doctor/getDoctorSimpleByYongHuID?yongHuID=${o.params.yongHuID}`,
        type: 'post',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 结束停止按钮触发   更新  10.104.141.230:38081/medicalrecord/v1/Crrt/update
  updateCrrt: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicalrecord/v1/Crrt/update`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 获取处方单表格数据    根据病例ID查询   10.104.141.230:38081/medicalrecord/v1/Crrt/getListByBingLiID?bingLiID=
  getinit: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicalrecord/v1/Crrt/getListByBingLiID?bingLiID=${o.params.bingLiID}`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },



  
  // 内部 
  // 新增按钮（处方单数据展示<保存处方单 + 保存相关检验项目数据> + 处方医嘱<获取处方医嘱表数据 + 保存处方医嘱 + 删除> + 获取相关检验项目数据）;
  // 详情按钮（处方单数据展示<不调用接口> + 处方医嘱<获取处方医嘱表数据 + 保存 + 删除> + 获取相关检验项目数据)
  
  // 保存处方单    新增   10.104.141.230:38081/medicalrecord/v1/Crrt/addList
  addListCrrt: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicalrecord/v1/Crrt/addList`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: JSON.parse(error.responseText).errorMessage
            })
          }
        }
      })
    }
  },
  // 保存相关检验项目数据    新增化验结果   10.104.141.230:38081/medicalrecord/v1/Crrt/insertHuayanjg
  insertHuayanjg: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicalrecord/v1/Crrt/insertHuayanjg`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 获取处方医嘱表数据    表格展示数据  根据ID查询医嘱 10.104.141.230:38081/medicalrecord/v1/Crrt/getMedAdviceListByID?ID=
  getMedAdviceListByID: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicalrecord/v1/Crrt/getMedAdviceListByID?ID=${o.params.ID}`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 删除处方医嘱 根据ID序号删除医嘱 10.104.141.230:38081/medicalrecord/v1/Crrt/deleteMedAdviceByIDAndXuHao?ID=&xuHao=
  delYZByIDAndXuHao: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicalrecord/v1/Crrt/deleteMedAdviceByIDAndXuHao?ID=${o.params.ID}&xuHao=${o.params.xuHao}`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 保存处方医嘱  新增医嘱   10.104.141.230:38081/medicalrecord/v1/Crrt/insertMedAdvice
  saveYZTB: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicalrecord/v1/Crrt/insertMedAdvice`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 获取相关检验项目数据     根据病历ID查询化验结果_初始化调用   10.104.141.230:38081/medicalrecord/v1/Crrt/getTestResultsForCrrt?bingLiID=
  getXGJYXMByID: function (o = {}) { 
    o.params = o.params || {}
    // console.log(o.params,o.params.bingLiID);
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicalrecord/v1/Crrt/getTestResultsForCrrt?bingLiID=${o.params.bingLiID}`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 二级签名   http://10.104.141.230:38081/sysmanage/v1/user/checkSecendPassword?param=${o.params.param}&ms=${o.params.ms}
  checkPassword: function (o = {}) {
    o.params = o.params || {}
    // console.log(o.params, o);
    // console.log('ms签名：', o.params.ms, '参数：', JSON.stringify(o.params.data));
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/sysmanage/v1/user/checkSecendPassword`,
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params.data),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          XMLHttpRequest.setRequestHeader("ms-sign", o.params.ms);
        },
        // headers: {
        //   "ms-sign": o.params.ms
        // },
        contentType: "application/json",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },


// 不知用处
  // 根据处方单ID查询(处方单ID获取方式 ———— 病例id查询返回结果的id)
  getCFDID: function (o = {}) { 
    o.params = o.params || {}
    // console.log(o.params,o.params.bingLiID);
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicalrecord/v1/Crrt/getByChuFangDanID?yiZhuDanID=${o.params.yiZhuDanID}`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 根据处方单ID和序号查询   10.104.141.230:38081/medicalrecord/v1/Crrt/getByIdXuHao?chuFangDanID=&xuHao=
  getByIdXuHao: function (o = {}) { 
    o.params = o.params || {}
    console.log(o.params,o.params.bingLiID);
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicalrecord/v1/Crrt/getByIdXuHao?chuFangDanID=${o.params.chuFangDanID}&xuHao=${o.params.xuHao}`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },

  // 根据处方单ID删除     10.104.141.230:38081/medicalrecord/v1/Crrt/deleteByChuFangDanID?chuFangDanID=
  deleteByChuFangDanID: function (o = {}) { 
    o.params = o.params || {}
    console.log(o.params,o.params.bingLiID);
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicalrecord/v1/Crrt/deleteByChuFangDanID?chuFangDanID=${o.params.chuFangDanID}`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },

  // 根据处方单ID和序号删除   10.104.141.230:38081/medicalrecord/v1/Crrt/deleteByIdXuHao?chuFangDanID=&xuHao=
  deleteByIdXuHao: function (o = {}) { 
    o.params = o.params || {}
    console.log(o.params,o.params.bingLiID);
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicalrecord/v1/Crrt/deleteByIdXuHao?chuFangDanID=${o.params.chuFangDanID}&xuHao=${o.params.xuHao}`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },

  // 护士签名   10.104.141.230:38081/medicalrecord/v1/Crrt/nurseSign?ID=
  nurseSign: function (o = {}) { 
    o.params = o.params || {}
    console.log(o.params,o.params.ID);
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicalrecord/v1/Crrt/nurseSign?ID=${o.params,o.params.ID}`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 护士签名更新   10.104.141.231:38081/medicalrecord/v1/Crrt/updateMedAdvice
  updateMedAdvice: function (o = {}) { 
    o.params = o.params || {}
    console.log(o.params,o.params.ID);
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicalrecord/v1/Crrt/updateMedAdvice`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },

  

}