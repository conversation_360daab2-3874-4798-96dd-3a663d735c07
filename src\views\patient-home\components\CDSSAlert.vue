<template>
  <el-table :data="tableData" border stripe size="mini">
    <el-table-column prop="bingRenXM" label="病人名称"></el-table-column>
    <el-table-column prop="bingQuMC" label="病区"></el-table-column>
    <el-table-column prop="chuang<PERSON>eiHao" label="床位号"></el-table-column>
  </el-table>
</template>

<script>
import { getCDSSForEHR } from '@/api/patient'
import fetchData from '@/views/patient-home/components/mixin/fetchData'

export default {
  name: 'CDSSAlert',
  mixins: [fetchData],
  data() {
    return {
      tableData: []
    }
  },
  methods: {
    fetchData() {
      getCDSSForEHR({
        zhuanKeID: this.zhuanKeID
        // zhiLiaoZuID: this.zhiLiaoZuID,
        // bingQuID: this.bingQuID
      }).then((res) => {
        if (res.hasError === 0) {
          console.log('cdss', res.data)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
