// 病人详情动态模块
const patientDetailModule = {
  namespaced: true,
  state: () => ({
    patientInit: {}, // 病人初始化数据
    activeMenuItem: null, // 当前激活的菜单项
    visitedViews: [], // 添加visitedViews数组
    activeRoute: '', // 当前激活的路由
    isLoading: false, // 添加加载状态标志
    isLoaded: false // 添加已加载标志
  }),
  mutations: {
    SET_PATIENT_INIT(state, data) {
      state.patientInit = data
    },
    SET_ACTIVE_MENU_ITEM(state, item) {
      state.activeMenuItem = item
    },
    // 添加管理visitedViews的mutations
    ADD_VISITED_VIEW(state, view) {
      if (state.visitedViews.some((v) => v.name === view.name)) return
      state.visitedViews.push(
        Object.assign({}, view, {
          title: view.meta.title || 'no-name'
        })
      )
    },
    DEL_VISITED_VIEW(state, name) {
      const index = state.visitedViews.findIndex((v) => v.name === name)
      if (index > -1) {
        state.visitedViews.splice(index, 1)
      }
    },
    SET_ACTIVE_ROUTE(state, route) {
      state.activeRoute = route
    },
    FILTER_VISITED_VIEWS(state, name) {
      state.visitedViews = state.visitedViews.filter((v) => v.meta.affix || v.name === name)
    },
    INIT_TAGS(state) {
      // 初始化标签，只保留固定标签
      state.visitedViews = state.visitedViews.filter((v) => v.meta && v.meta.affix)
      // 如果没有标签，添加默认标签
      if (state.visitedViews.length === 0) {
        const tag = {
          name: 'InPatientOrders',
          component: null, // 组件会在TagsView中动态加载
          meta: {
            title: '住院医嘱',
            affix: true
          },
          caiDanLX: 'C'
        }
        state.visitedViews.push(
          Object.assign({}, tag, {
            title: tag.meta.title || 'no-name'
          })
        )
      }
      // 设置默认激活路由
      if (state.visitedViews.length > 0) {
        state.activeRoute = state.visitedViews[0].name
      }
    },
    SET_LOADING(state, status) {
      state.isLoading = status
    },
    SET_LOADED(state, status) {
      state.isLoaded = status
    }
  },
  actions: {
    async getPatientInit({ commit, state }, bingLiID) {
      // 如果已经在加载，直接返回当前状态
      if (state.isLoading) {
        console.log('已经在加载中，不重复请求')
        return state.patientInit
      }

      // 如果已经加载完成且有数据，直接返回
      if (state.isLoaded && state.patientInit.bingRenBH) {
        console.log('已经加载完成，使用缓存数据')
        return state.patientInit
      }

      // 设置加载状态
      commit('SET_LOADING', true)
      commit('SET_LOADED', false)

      try {
        // 分离动态导入和API调用，确保错误处理更清晰
        let PatientInitApi
        try {
          // 动态导入API模块
          const module = await import('@/api/patient-info')
          PatientInitApi = module.PatientInit
        } catch (importError) {
          console.error('导入API模块失败:', importError)
          return { hasError: 1, error: importError, message: '导入API模块失败' }
        }

        // 调用API
        try {
          console.log('开始请求病人数据, bingLiID:', bingLiID)
          const res = await PatientInitApi(bingLiID)
          console.log('请求病人数据完成:', res)

          // 无论API返回成功还是失败，都标记为已加载
          commit('SET_LOADED', true)

          if (res.hasError === 0 && res.data) {
            // 只在成功且有数据时更新patientInit
            commit('SET_PATIENT_INIT', res.data)
            return res
          } else {
            console.warn('API返回错误:', res.errorMsg || '未知错误')
            return res
          }
        } catch (apiError) {
          console.error('调用API失败:', apiError)
          commit('SET_LOADED', true)
          return { hasError: 1, error: apiError, message: '调用API失败' }
        }
      } catch (error) {
        // 捕获所有其他可能的错误
        console.error('加载病人数据过程中发生未知错误:', error)
        commit('SET_LOADED', true)
        return { hasError: 1, error, message: '未知错误' }
      } finally {
        // 确保在所有情况下都重置loading状态
        console.log('重置loading状态')
        commit('SET_LOADING', false)
      }
    },
    setActiveMenuItem({ commit }, item) {
      commit('SET_ACTIVE_MENU_ITEM', item)
      // 同时添加到visitedViews
      commit('ADD_VISITED_VIEW', item)
      commit('SET_ACTIVE_ROUTE', item.name)
    },
    addVisitedView({ commit }, view) {
      commit('ADD_VISITED_VIEW', view)
    },
    delVisitedView({ commit, state }, name) {
      commit('DEL_VISITED_VIEW', name)
      // 如果删除的是当前激活的路由，需要切换到最后一个标签
      if (state.activeRoute === name) {
        const latestView = state.visitedViews.slice(-1)[0]
        if (latestView) {
          commit('SET_ACTIVE_ROUTE', latestView.name)
        } else {
          commit('SET_ACTIVE_ROUTE', 'InPatientOrders')
        }
      }
      return state.visitedViews
    },
    setActiveRoute({ commit }, route) {
      commit('SET_ACTIVE_ROUTE', route)
    },
    closeOthersTags({ commit }, name) {
      commit('FILTER_VISITED_VIEWS', name)
    },
    initTags({ commit }) {
      commit('INIT_TAGS')
    }
  }
}

export default patientDetailModule
