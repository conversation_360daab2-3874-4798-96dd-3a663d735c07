/*
 * @Description: 打码及相关处理
 * @Author: Fran
 * @Date: 2023-11-23 15:26:51
 * @LastEditors: Fran
 * @LastEditTime: 2024-06-18 09:27:41
 */

import { isEmpty } from 'lodash'

/**
 * 卡号打码(去除所有的空格）
 * @param {*} cardNum 未打码卡号
 * @param {*} emptyReplacer 卡号为空时的返回值
 * @returns {string} 打码之后的卡号
 */
export function cardNumMask(cardNum, emptyReplacer = '卡号未知') {
  if (isEmpty(cardNum)) {
    return emptyReplacer
  }
  if (cardNum.length <= 4) {
    return cardNum
  }

  return '****' + cardNum.substring(cardNum.length - 4)
}

/**
 * 姓名打码(去除所有的空格）
 * Tower 任务: 自查&整改患者隐私屏蔽规范 ( https://tower.im/teams/660087/todos/105722 )
 * @param {*} name 未打码的名字
 * @param {*} emptyReplacer 名字为空的时候的返回值
 * @returns {string} 打码之后的名字
 */
export function nameMask(name, emptyReplacer = '') {
  if (isEmpty(name)) {
    return emptyReplacer
  }
  const trimedName = name.trim()
  const nameLength = name.replace(/\s+/g, '').length

  if (nameLength <= 1) {
    return trimedName
  }

  if (nameLength <= 3) {
    return '*' + trimedName.substring(1)
  }

  // 为了特殊匹配 "文 文" "文  文" "文   文"等中间多空格的情况
  const secondChar = trimedName.substring(1).trim().substring(0, 1)
  const secondCharIndex = trimedName.indexOf(secondChar)
  const charList = trimedName.split('')

  charList[0] = '*' // 将第一个文字替换成星号
  charList[secondCharIndex] = '*' // 将第二个非空格的文字替换成星号
  return charList.join('')
}

/**
 * 手机号码打码(隐藏中间四位）
 * @param {*} phoneNumber 未打码的手机号
 * @returns {string} 打码之后的手机号
 */
export const phoneNumberMask = (phoneNumber) => {
  if (isEmpty(phoneNumber)) {
    return ''
  }
  return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 身份证打码(隐藏中间6位）
 * @param {*} idCard 未打码的身份证
 * @returns {string} 打码之后的身份证
 */
export const idCardMask = (idCard) => {
  if (isEmpty(idCard)) {
    return ''
  }
  return idCard.replace(/^(\d{4})\d{10}(\d{4})$/, '$1**********$2')
}
