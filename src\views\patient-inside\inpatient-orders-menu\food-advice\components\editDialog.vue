<template>
  <el-dialog :visible="visible" width="420px" @open="initFormData()" @close="updateVisible(false)">
    <span slot="title">
      <span class="drawer-title">
        <i class="el-icon-menu"></i>
        医院用食品医嘱记录单-{{ dialogType ? '编辑' : '新增' }}
      </span>
    </span>
    <span>
      <div class="drawer-component">
        <el-form ref="ruleForm" :model="formData" :rules="rules">
          <table v-if="formData">
            <tbody>
              <tr>
                <td class="info-label">药品名称:</td>
                <td class="info-value">
                  <el-form-item prop="yaoPinID">
                    <el-select
                      v-model="formData.yaoPinID"
                      filterable
                      remote
                      :remote-method="remoteMethod"
                      placeholder=""
                    >
                      <el-option
                        v-for="item in yaoPinOption"
                        :key="item.yaoPinID"
                        :label="item.yaoPinMC"
                        :value="item.yaoPinID"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">规格:</td>
                <td class="info-value">
                  <el-input v-model="formData.guiGe" />
                </td>
              </tr>
              <tr>
                <td class="info-label">包装量:</td>
                <td class="info-value">
                  <el-input-number v-model="formData.baoZhuangLiang" :min="1" />
                </td>
              </tr>
              <tr>
                <td class="info-label">一次用量:</td>
                <td class="info-value">
                  <el-input-number v-model="formData.yiCiYL" :min="1" />
                </td>
              </tr>
              <tr>
                <td class="info-label">单位:</td>
                <td class="info-value">
                  <el-select v-model="formData.danWei" placeholder="">
                    <el-option
                      v-for="item in [{ value: 'g', label: 'g' }]"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </td>
              </tr>
              <tr>
                <td class="info-label">用药方式:</td>
                <td class="info-value">
                  <el-select v-model="formData.yongYaoFF" placeholder="">
                    <el-option
                      v-for="item in foodInfo.drugMethodVos"
                      :key="item.fangFaDM"
                      :label="item.fangFaMC"
                      :value="item.fangFaDM"
                    ></el-option>
                  </el-select>
                </td>
              </tr>
              <tr>
                <td class="info-label">频率:</td>
                <td class="info-value">
                  <el-form-item prop="yongYaoPL">
                    <el-select v-model="formData.yongYaoPL" placeholder="">
                      <el-option
                        v-for="item in foodInfo.drugFrequencyVos"
                        :key="item.pinLuDM"
                        :label="item.pinLuMC"
                        :value="item.pinLuDM"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">给药时间:</td>
                <td class="info-value">
                  <el-select v-model="formData.geiYaoSJ" placeholder="">
                    <el-option
                      v-for="item in foodInfo.drugUseTimeVos"
                      :key="item.geiYaoSJDM"
                      :label="item.geiYaoSJMC"
                      :value="item.geiYaoSJDM"
                    ></el-option>
                  </el-select>
                </td>
              </tr>
              <tr>
                <td class="info-label">用法补充说明:</td>
                <td class="info-value">
                  <el-input v-model="formData.yongFaBCSM" />
                </td>
              </tr>
              <tr v-if="yiZhuLB !== '1'">
                <td class="info-label">数量:</td>
                <td class="info-value">
                  <el-input-number v-model="formData.shuLiang" :min="1" />
                </td>
              </tr>
              <tr>
                <td class="info-label">单价:</td>
                <td class="info-value">
                  <el-input-number v-model="formData.danJia" :precision="2" :min="0" />
                </td>
              </tr>
              <tr>
                <td class="info-label">计划开始时间:</td>
                <td class="info-value">
                  <el-date-picker
                    v-model="formData.kaiShiSJ"
                    type="datetime"
                    placeholder="选择日期"
                    value-format="yyyy-MM-dd hh:mm:ss"
                  />
                </td>
              </tr>
              <tr v-if="yiZhuLB === '1'">
                <td class="info-label">计划结束时间:</td>
                <td class="info-value">
                  <el-date-picker
                    v-model="formData.jieShuSJ"
                    type="datetime"
                    placeholder="选择日期"
                    value-format="yyyy-MM-dd hh:mm:ss"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </el-form>
      </div>
    </span>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="uploadForm()">保 存</el-button>
      <el-button @click="updateVisible(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { deepClone } from '@/utils'
import { getStoreDrugsInUsingByCodeAndName } from '@/api/doctors-advice'

export default {
  name: 'EditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dialogType: {
      type: Number,
      default: 0
    },
    yiZhuLB: {
      type: String,
      default: '1'
    },
    foodData: {
      type: Object,
      default: null
    },
    foodInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      notes: '',
      formData: null,
      yaoPinOption: [],

      rules: {
        yaoPinID: [{ required: true, message: ' ' }],
        yongYaoPL: [{ required: true, message: ' ' }]
      }
    }
  },
  methods: {
    uploadForm() {
      this.$refs.ruleForm.validate((valid, object) => {
        console.log(valid, this.formData, object)
        // return
        if (valid) {
          this.formData.yaoPinMC = this.yaoPinOption.find((item) => {
            return item.yaoPinID === this.formData.yaoPinID
          })?.yaoPinMC
          this.$emit('upload-success', this.formData)
          this.updateVisible(false)
        } else {
          return false
        }
      })
    },
    async remoteMethod(query) {
      const res = await getStoreDrugsInUsingByCodeAndName({
        isPage: false,
        pageNum: 1,
        fenLeiMa: '44',
        shuRu: 'PY',
        pageSize: 999,
        shuRuMa: query
      })

      console.log('特医食品医嘱列表', res)
      if (res.hasError === 0) {
        this.yaoPinOption = res.data ? res.data.YjkYkypVoList : []
      }
    },
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    initFormData() {
      this.$refs.ruleForm?.resetFields()
      this.formData = deepClone(this.foodData)
      this.yaoPinOption = [{ yaoPinID: this.formData.yaoPinID, yaoPinMC: this.formData.yaoPinMC }]
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-title {
  font-size: 16px;
}

.drawer-component {
  padding: 0px 16px;
  table {
    width: 100%;
  }
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 35px;
    padding: 5px;
  }
  .info-label {
    text-align: right;
    width: 110px;
    background-color: #eaf0f9;
    span {
      color: #f35656;
      position: relative;
      top: 3px;
      right: 3px;
    }
  }
  .info-value {
    width: 230px;
    background-color: #ffffff;
    .el-select {
      width: 100%;
    }
    .el-date-editor {
      width: 100%;
    }
    .el-input-number {
      width: 100%;
    }
    .el-form-item {
      margin-bottom: auto;
    }
    a {
      text-decoration: underline;
      color: #356ac5;
    }
  }
}
:deep(.el-dialog__footer) {
  border-top: none;
}

:deep(.el-radio) input[aria-hidden='true'] {
  display: none !important;
}
</style>
