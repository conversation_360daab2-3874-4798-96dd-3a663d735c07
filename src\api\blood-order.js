import request from '@/utils/request'

// 获取输血申请单（住院）列表
export function listBloodTransSqd(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/listBloodTransSqd',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取血型和RH的化验结果
export function getAssayBloodTypeAndRH(data) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqd/getAssayBloodTypeAndRH',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 获取输血理由列表
export function getBloodTransReasonList(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqd/getBloodTransReasonList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取输血类型列表（紧急输血、平诊输血等等）
export function getBloodTransType(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqd/getBloodTransType',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 输血医嘱基础数据
export function getBaseInfo(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getBaseInfo',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取输血申请单详情
export function getSqdAndAdviceListByShenQingDanID(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqd/getSqdAndAdviceListByShenQingDanID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 输血可开附加化验医嘱列表
export function getEnableAddAssayListByLeiXing(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqd/getEnableAddAssayListByLeiXing',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 新增输血申请单
export function addBloodTransSqd(data) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/addBloodTransSqd',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 查询血库血型
export function getBloodBankInventory(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getBloodBankInventory',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据申请单ID获取血袋数量
export function getBloodQuantityBySQDID(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getBloodQuantityBySQDID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取右侧化验指标
export function getInPatientBloodTypeByLB(data) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getInPatientBloodTypeByLB',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 删除-输血申请单（住院）
export function delBloodTransSqd(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/delBloodTransSqd',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据病区ID获取院区和血库编号
export function getXueKuBH(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getXueKuBH',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 修改-输血申请单（住院）
export function updateBloodTransSqd(data) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/updateBloodTransSqd',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据成分代码获取需要导入的化验指标
export function getLaboratoryIndicatorsByCFDM(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getLaboratoryIndicatorsByCFDM',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据化验项目获取对应化验指标
export function getLaboratoryIndicatorsByHYXM(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getLaboratoryIndicatorsByHYXM',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
