<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <div class="header-item-title">审批状态:</div>
        <div class="header-item-radio">
          <el-radio-group v-model="radio1">
            <el-radio label="1" size="large">
              <span class="search-label">待审批</span>
            </el-radio>
            <el-radio label="2" size="large">
              <span class="search-label">审批通过</span>
            </el-radio>
            <el-radio label="3" size="large">
              <span class="search-label">审批未通过</span>
            </el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">审批日期:</div>
        <div class="header-item-date">
          <el-date-picker
            v-model="approvalDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            size="default"
          />
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">住院号:</div>
        <div class="header-item-input">
          <el-input v-model="hospitalizationNumber" style="width: 170px" />
        </div>
        <div class="header-item-checkbox">
          <el-checkbox v-model="checked1" label="与本人相关" size="large" />
        </div>
        <div class="header-item-button"><el-button>查询</el-button></div>
        <div class="header-item-button">
          <el-button style="background-color: #356ac5">导出</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">【医务处】多学科会诊审批</div>
      </div>
      <div class="table">
        <el-table
          max-height="648"
          stripe
          border
          :data="processedData"
          :span-method="objectSpanMethod"
        >
          <el-table-column prop="name" width="90" label="姓名"></el-table-column>
          <el-table-column prop="sex" width="90" label="性别"></el-table-column>
          <el-table-column prop="wardBedNumber" width="100" label="病区-床位号"></el-table-column>
          <el-table-column
            prop="hospitalizationNumber"
            width="100"
            label="住院号"
          ></el-table-column>
          <el-table-column prop="outpatientNumber" width="220" label="门诊号"></el-table-column>
          <el-table-column prop="applicationTime" width="130" label="申请时间"></el-table-column>
          <el-table-column
            prop="specialistApplication"
            width="130"
            label="申请专科"
          ></el-table-column>
          <el-table-column
            prop="physicianApplication"
            width="90"
            label="申请医师"
          ></el-table-column>
          <el-table-column
            prop="physicianApplicationPhone"
            width="130"
            label="申请医师联系电话"
          ></el-table-column>
          <el-table-column
            prop="consultationSpecialist"
            width="100"
            label="会诊专科"
          ></el-table-column>
          <el-table-column
            prop="invitedConsultationPhysician"
            width="100"
            label="邀请会诊医师"
          ></el-table-column>
          <el-table-column
            prop="inviteDoctorPatientOffice"
            width="130"
            label="邀请医患办"
          ></el-table-column>
          <el-table-column
            prop="appointmentConsultationTime"
            width="150"
            label="预约会诊时间"
          ></el-table-column>
          <el-table-column
            prop="appointmentConsultationLocation"
            width="130"
            label="预约会诊地点"
          ></el-table-column>
          <el-table-column
            prop="departmentApprover"
            width="90"
            label="科室审批人"
          ></el-table-column>
          <el-table-column prop="departmentApproveResults" width="100" label="科室审批结果">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.departmentApproveResults === 0">同意</el-tag>
              <el-tag v-else-if="scope.row.departmentApproveResults === 1" type="danger">
                拒绝
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="departmentApproveTime"
            width="150"
            label="科室审批时间"
          ></el-table-column>
          <el-table-column
            prop="DoctorPatientDepartmentApproval"
            width="150"
            label="医务处/医患处审批"
          >
            <template slot-scope="scope">
              <el-radio-group v-model="scope.row.DoctorPatientDepartmentApproval">
                <el-radio label="0" size="large">
                  <span class="search-label">同意</span>
                </el-radio>
                <el-radio label="1" size="large">
                  <span class="search-label">拒绝</span>
                </el-radio>
              </el-radio-group>
            </template>
          </el-table-column>
          <el-table-column
            prop="DoctorPatientDepartmentApprovalRemarks"
            width="300"
            label="医务处/医患处审批备注"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.DoctorPatientDepartmentApprovalRemarks"
                :rows="7"
                type="textarea"
                placeholder="备注"
              />
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="200" label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">
                查看病人
              </el-button>
              <el-button type="text" size="small" @click="handleClick(scope.row)">查看</el-button>
              <el-button type="text" size="small" @click="handleClick(scope.row)">审批</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      radio1: '1',
      approvalDate: '',
      hospitalizationNumber: '',
      checked1: '',

      processedData: [],
      tableData: [
        {
          name: '吴昌汉',
          sex: '男',
          wardBedNumber: '002-007',
          hospitalizationNumber: '2048540',
          outpatientNumber: '**********',
          applicationTime: '07-05 09:29',
          specialistApplication: 'ICU',
          physicianApplication: '陈浩1',
          physicianApplicationPhone: '663003',
          consultationSpecialist: [
            '心血管内科',
            '呼吸内科',
            '肾内科',
            '营养科',
            '感染科',
            '内分泌科',
            'A-临床药学实验室'
          ],
          invitedConsultationPhysician: [
            '瞿翔',
            '董大鹏',
            '章建闭',
            '谷斌斌',
            '金晓艺',
            '朱奇涵',
            '履映'
          ],
          inviteDoctorPatientOffice: '否',
          appointmentConsultationTime: '2023-07-05 14:00',
          appointmentConsultationLocation: 'ICU2病区',
          departmentApprover: '卢颖如',
          departmentApproveResults: 0,
          departmentApproveTime: '2023-07-05 09:32',
          DoctorPatientDepartmentApproval: '0',
          DoctorPatientDepartmentApprovalRemarks: '123'
        },
        {
          name: '吴昌汉',
          sex: '男',
          wardBedNumber: '002-007',
          hospitalizationNumber: '2048540',
          outpatientNumber: '33030099000000003280091617',
          applicationTime: '07-05 09:29',
          specialistApplication: 'ICU',
          physicianApplication: '陈浩1',
          physicianApplicationPhone: '663003',
          consultationSpecialist: [
            '肾内科',
            '泌尿外科',
            '放射科',
            '内分泌科',
            '血管外科',
            '脊柱外科',
            '针推理疗科',
            '神经内科'
          ],
          invitedConsultationPhysician: [
            '邱文仙',
            '李叶平',
            '陈聪',
            '朱虹',
            '潘乐门',
            '聪红林',
            '叶天申',
            '朱蓓蕾'
          ],
          inviteDoctorPatientOffice: '否',
          appointmentConsultationTime: '2023-07-05 14:00',
          appointmentConsultationLocation: 'ICU2病区',
          departmentApprover: '卢颖如',
          departmentApproveResults: 1,
          departmentApproveTime: '2023-07-05 09:32',
          DoctorPatientDepartmentApproval: '1',
          DoctorPatientDepartmentApprovalRemarks: '123'
        }
      ]
    }
  },
  async mounted() {
    let processedData = this.processedData
    for (let i = 0; i < this.tableData.length; i++) {
      for (let j = 0; j < this.tableData[i].consultationSpecialist.length; j++) {
        let d = { ...this.tableData[i] }
        d.consultationSpecialist = this.tableData[i].consultationSpecialist[j]
        d.invitedConsultationPhysician = this.tableData[i].invitedConsultationPhysician[j]
        if (j === 0) {
          d.size = this.tableData[i].consultationSpecialist.length
        }
        processedData.push(d)
      }
    }
  },
  methods: {
    handleClick(row) {
      console.log(row)
      console.log(this.approvalDate)
    },
    objectSpanMethod(row, column, rowIndex, columnIndex) {
      let size = row.row.size
      if (
        !(
          row.column.property === 'consultationSpecialist' ||
          row.column.property === 'invitedConsultationPhysician'
        )
      ) {
        if (size !== undefined) {
          return {
            rowspan: size,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
    font-weight: bold;
  }
  .header-item-input {
    margin-right: 8px;
  }
  .header-item-date {
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
