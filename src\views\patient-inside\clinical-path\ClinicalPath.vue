<template>
  <div class="clinical-path-container">
    <!-- 上下布局-->
    <div class="clinical-path-header">
      <el-row class="clinical-path-btn">
        <el-button type="primary" @click="onSaveLinChuangLuJing()">保存</el-button>
        <el-button type="primary" @click="addZhiLuJing()">加入支路径</el-button>
        <el-button type="primary" @click="getinitChuJingData()">强制出径</el-button>
        <el-button type="primary" @click="onWanChengLuJing">完成路径</el-button>
        <el-button class="clinical-path-btn-reevaluate" @click="onChongXinPingGu()">重新评估</el-button>
        <el-button v-if="tianXieBYLY === 1" type="primary" @click="onOpenLiYou">填写理由</el-button>
      </el-row>
    </div>
    <!-- 左右布局 -->
    <div class="clinical-path-layout">
      <!-- 左侧列表 -->
      <div class="left-panel">
        <div class="left-panel-header">
          <div class="title-log">路径选择</div>
          <div class="left-panel-container">
            <div class="lujing-lists">
              <div v-for="(item, key) in zhuLuJingBrrjjlVo" :key="key">
                <div v-if="key === 0" class="lujing-list-top">
                  主：{{ item.chuJingSJ }} {{ item.luJingMC }}
                </div>
                <div
                  v-else
                  :class="['lujing-list', key === LuJingIndex ? 'active' : '']"
                  @click="dbSelectLuJing(key)"
                >
                  {{ item.chuJingSJ_TXT }} {{ item.zhenLiaoJDMC }}
                </div>
              </div>
            </div>
            <div class="lujing-lists">
              <div v-for="(item, key) in zhiLuJingBrrjjlVoList" :key="key">
                <div v-if="key === 0" class="lujing-list-top">
                  次：{{ item.chuJingSJ }} {{ item.luJingMC }}
                </div>
                <div
                  v-else
                  :class="['lujing-list', key === LuJingIndex ? 'active' : '']"
                  @click="dbSelectLuJing(key)"
                >
                  {{ item.chuJingSJ_TXT }} {{ item.zhenLiaoJDMC }}{{ key - LuJingIndex }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧详情 -->
      <div class="right-panel">
        <div class="right-panel-header">
          <el-row :gutter="10">
            <el-col :span="12">
              <div class="title-log">
                待办事项列表
                <span>{{ bianYiYY }}</span>
              </div>
              <el-table
                :data="daiBanShiXiangList"
                height="150px"
                highlight-current-row
                border
                stripe
                size="mini"
              >
                <el-table-column width="33">
                  <template slot-scope="scope">
                    <span v-if="scope.row.shiJiWCSJ">√</span>
                    <span v-else-if="false">!!</span>
                    <el-checkbox v-else @change="handleSelectionChange(scope.row)"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column prop="daiBanSXMC" label="事项名称"></el-table-column>
                <el-table-column prop="shiJiWCSJ" label="完成时间"></el-table-column>
              </el-table>
            </el-col>
            <el-col :span="12">
              <div class="title-log">已开医嘱列表</div>
              <el-table
                :data="yiKaiYiZhuList"
                height="150px"
                highlight-current-row
                border
                stripe
                size="mini"
              >
                <el-table-column prop="daiBanSXMC" label="事项名称"></el-table-column>
                <el-table-column prop="zhiXingRYXM" label="操作人"></el-table-column>
                <el-table-column prop="zuiChiWCSJ" label="修改时间"></el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </div>
        <div class="right-panel-container">
          <el-row>
            <el-col :span="16" class="right-panel-left-moBan">
              <div class="title-log">待办事项详细内容</div>
              <el-table
                class="right-panel-DaiBanXiangQing"
                :data="daiBanShiXiangXXList"
                highlight-current-row
                border
                @current-change="handleCurrentChange"
                @row-click="handleRowChange"
              >
                <!-- <el-table-column type="selection" width="33"></el-table-column> -->
                <el-table-column prop="daiBanSXMC" label="模板名称">
                  <template slot-scope="scope">
                    <span
                      class="middle-crile grey"
                      :class="
                        scope.row.selRowType === 1
                          ? 'crile_color'
                          : scope.row.selRowType === 2
                          ? 'crile_blue'
                          : ''
                      "
                    ></span>
                    <span>{{ scope.row.daiBanSXMC }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="daiBanSXLX" label="类型" width="80">
                  <template slot-scope="scope">
                    {{
                      scope.row.daiBanSXLX === 'yp'
                        ? '药品'
                        : scope.row.daiBanSXLX === 'zl'
                        ? '治疗'
                        : scope.row.daiBanSXLX === 'tj'
                        ? '特检'
                        : '化验'
                    }}
                  </template>
                </el-table-column>
                <el-table-column label="医嘱内容" width="350">
                  <template slot-scope="scope">
                    <!-- {{ JSON.stringify(scope.row) }} -->
                    <div v-for="item in scope.row.yiZhuXiangXiNR" :key="item.guanLianLXID">
                      <div>
                        <h3>{{ scope.row.moBanMC }}</h3>
                      </div>
                      <div>
                        <span>{{ item.yaoPinMC }}</span>
                        <span>{{ item.jiLiang }}</span>
                      </div>
                    </div>
                    <div v-for="item in scope.row.hanYangXiangXiNR" :key="item.guanLianLXID">
                      <div>
                        <h3>{{ item }}</h3>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="xiuGaiSJ" label="最迟完成时间"></el-table-column>
              </el-table>
            </el-col>
            <el-col :span="8">
              <div class="right-panel-right-yiZhuNR">
                <div class="title-log">医嘱详细内容</div>
                <el-table
                  v-if="daiBanSXLX === 'yp'"
                  class="right-panel-DaiBanXiangQing"
                  :data="yiZhuXiangXXList"
                  border
                >
                  <!-- <el-table-column type="selection" width="33"></el-table-column> -->
                  <!-- <el-table-column prop="moBanMC" label="模板名称" width="100"></el-table-column> -->
                  <el-table-column label="医嘱内容">
                    <template slot-scope="scope">
                      <div>
                        <div class="right-YiZhuNeiRong-title">
                          <h3>
                            {{ scope.row.moBanMC }}（{{
                              scope.row.shiFouBX === 2 ? '必选' : '可选'
                            }}
                            {{ scope.row.moBanLB === 2 ? '单选' : '多选' }}）
                          </h3>
                        </div>
                        <div
                          v-for="(item, index) in scope.row.ylLjMbxxsVoList"
                          :key="index"
                          class="right-YiZhuNeiRongList-box"
                        >
                          <!-- <div><el-checkbox v-model="item.checked" @change="selectYiZhuNR(scope.row, index, 'yp')" ></el-checkbox></div> -->
                          <span class="right-YiZhuNeiRongList-YaoPinMC">{{ item.yaoPinMC }}</span>
                          <span>{{ item.jiLiang }}</span>
                          <div>
                            <el-checkbox
                              v-model="item.checked"
                              @change="selectYiZhuNR(scope.row, index, 'yp')"
                            ></el-checkbox>
                            <el-input
                              v-model="item.yiCiYL"
                              class="right-YiZhuNeiRongList-input"
                              type="text"
                              size="mini"
                            ></el-input>
                            {{ item.jiLiangDW }}
                            <el-select
                              v-model="item.zhiXingFF"
                              class="right-YiZhuNeiRongList-select"
                              size="mini"
                              filterable
                              placeholder="请选择"
                            >
                              <el-option
                                v-for="ev in feiCaoYaoYYFFList"
                                :key="ev.fangFaDM"
                                :label="ev.fangFaMC"
                                :value="ev.fangFaDM"
                              ></el-option>
                            </el-select>
                            <el-select
                              v-model="item.zhiXingPL"
                              class="right-YiZhuNeiRongList-select"
                              size="mini"
                              filterable
                              placeholder="请选择"
                            >
                              <el-option
                                v-for="ev in yongYaoPLList"
                                :key="ev.pinLuDM"
                                :label="ev.pinLuMC"
                                :value="ev.pinLuDM"
                              ></el-option>
                            </el-select>
                          </div>
                          <el-date-picker
                            v-model="item.kaiShiSJ"
                            class="right-YiZhuNeiRongList-datetime"
                            size="mini"
                            type="datetime"
                            placeholder="选择日期时间"
                            default-time="12:00:00"
                          ></el-date-picker>
                          <span class="right-YiZhuNeiRongList-sapn">{{ item.geiYaoSJ || '' }}</span>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <!-- 变异理由 -->
    <default-dialog
      class="history-drug-dialog"
      title="填写变异理由"
      width="350px"
      :visible.sync="dialogBianYiVisible"
      @confirm="onSaveQiangZhiCJ"
      @close="dialogBianYiVisible = false"
    >
      <el-form
        ref="bianYiForm"
        :model="bianYiForm"
        :rules="bianYiRules"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="变异原因" prop="chuJingLYDM">
          <el-select v-model="bianYiForm.bianYiLYDM" placeholder="请选择变异原因">
            <el-option
              v-for="item in bianYiLYDMList"
              :key="item.daiMa"
              :label="item.mingCheng"
              :value="item.daiMa"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="变异理由" prop="bianYiJTLY">
          <el-input
            v-model="bianYiForm.bianYiJTLY"
            type="textarea"
            :rows="2"
            placeholder="出径理由"
          ></el-input>
        </el-form-item>
      </el-form>
    </default-dialog>
    <!-- 强制出径 -->
    <default-dialog
      class="history-drug-dialog"
      title="强制出径"
      width="350px"
      :visible.sync="dialogQZCJVisible"
      @confirm="onQiangZhiCJ"
      @close="dialogQZCJVisible = false"
    >
      <el-form
        ref="chuJingForm"
        :model="chuJingForm"
        :rules="chuJingRules"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="出径原因" prop="chuJingLYDM">
          <el-select v-model="chuJingForm.chuJingLYDM" placeholder="请选择出径原因">
            <el-option
              v-for="item in chuJingLYDMList"
              :key="item.daiMa"
              :label="item.mingCheng"
              :value="item.daiMa"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="出径理由" prop="chuJingJTLY">
          <el-input
            v-model="chuJingForm.chuJingJTLY"
            type="textarea"
            :rows="2"
            placeholder="出径理由"
          ></el-input>
        </el-form-item>
      </el-form>
    </default-dialog>
    <!-- 麻醉药品用途 -->
    <default-dialog
      :visible.sync="mzytDialog"
      title="麻醉药品用途"
      width="350px"
      confirm-button-text="保存"
      cancel-button-text="关闭"
      @confirm="handleMzytSave"
      @cancel="handleMzytClose"
    >
      <div class="mzyt-dialog">
        <el-radio-group v-model="mzytValue">
          <el-radio v-for="item in mzytList" :key="item.daiMa" :label="item.daiMa">
            {{ item.mingCheng }}
          </el-radio>
        </el-radio-group>
      </div>
    </default-dialog>
    <!--社保审批弹框-->
    <she-bao-dialog
      :visible.sync="sheBaoDialog"
      :display-list="displayList"
      :dialog-type="dialogType"
      :she-bao-yao-pin-data="sheBaoYaoPinData"
      :inpatient-init="inpatientInit"
      @confirm="handleSheBaoConfirm"
      @close="handleSheBaoClose"
    ></she-bao-dialog>
    <!--处方药诊断弹窗-->
    <default-dialog
      :visible.sync="chuFangZDDialog"
      title="处方诊断"
      width="500px"
      @confirm="handleChuFangZDDialogConfirm"
      @cancel="handleChuFangZDDialogCancel"
    >
      <div class="chu-fang-zd-dialog">
        <div class="chu-fang-zd-header">
          <div class="header-title">处方诊断</div>
          <el-button type="primary" size="small" @click="handleAddChuFangZD">新增</el-button>
        </div>
        <div class="chu-fang-zd-content">
          <el-table :data="chuFangZDList" border size="mini" style="width: 100%">
            <el-table-column prop="mingChen" label="名称" min-width="200">
              <template #default="{ row, $index }">
                <span class="zd-name-link" @click="handleSelectChuFangZD($index)">
                  {{ row.mingChen }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="{ $index }">
                <el-button type="text" @click="handleDeleteChuFangZD($index)">删除</el-button>
              </template>
            </el-table-column>
            <el-table-column label="排序" align="center">
              <template #default="{ $index }">
                <div class="sort-buttons">
                  <el-button :disabled="$index === 0" @click="handleSortUp($index)">
                    <i class="el-icon-arrow-up"></i>
                  </el-button>
                  <el-button
                    :disabled="$index === chuFangZDList.length - 1"
                    @click="handleSortDown($index)"
                  >
                    <i class="el-icon-arrow-down"></i>
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </default-dialog>
    <!--诊断列表弹窗-->
    <default-dialog
      :visible.sync="zhenDuanListDialog"
      title="选择处方诊断"
      width="700px"
      :close-on-click-modal="false"
      :append-to-body="true"
      pop-type="tip"
    >
      <div class="zhen-duan-list-dialog">
        <div class="search-bar">
          <el-input
            v-model="zhenDuanFilter"
            placeholder="请输入诊断名称"
            style="width: 400px; margin: 0 10px"
            @input="searchZhenDuan"
          ></el-input>
          <el-button
            type="primary"
            :disabled="!isChuFangSelected"
            @click="handleSelectZhenDuanConfirm"
          >
            确定
          </el-button>
        </div>
        <div class="zhen-duan-table">
          <el-table
            :data="zhenDuanListData"
            border
            style="width: 100%"
            height="600px"
            @row-click="handleSelectZhenDuan"
          >
            <el-table-column prop="zhenDuanMC" label="诊断名称" min-width="400">
              <template #default="{ row }">
                <span class="zd-name-link">{{ row.zhenDuanMC }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="icd" label="诊断ICD" width="200"></el-table-column>
          </el-table>
        </div>
        <div class="pagination-container">
          <el-pagination
            :current-page="pageIndex"
            :page-sizes="[20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleZhenDuanSizeChange"
            @current-change="handleZhenDuanCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </default-dialog>
    <!-- 加入支路径 重新评估-->
    <default-dialog
      :visible.sync="xuanZeLuJingDialog"
      title="选择路径"
      width="700px"
      :close-on-click-modal="false"
      :append-to-body="true"
      :destroy-on-close="true"
      pop-type="tip"
    >
    <Join-Clinical-Path 
    :isLuJingDialog="isLuJingDialog" 
    :fenZhiLJ="fenZhiLJ" 
    :chongXinPG="chongXinPG" 
    @confirm="closeLuJing"
    ></Join-Clinical-Path>
  </default-dialog>
  </div>
</template>

<script>
import {
  getClinicalPathInit,
  getBingRenDbsxDetail,
  getBrDbsxYiZhu,
  saveWanChengLJ,
  saveQiangZhiCJ,
  getErJiMbAndMbXxsByZhid,
  getHuaYanMbDetailByMbId,
  zhiXingDbsx,
  getinitChuJing,
  getJianYanSfXmid
} from '@/api/clinical-path'
import { inpatientInit, getChuFangZD, getMaZuiYT, getZhenDuanList } from '@/api/inpatient-order'
import { isArray, isPlainObject } from 'lodash'
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import SheBaoDialog from '@/components/Dialog/sheBaoDialog.vue'
import JoinClinicalPath from './JoinClinicalPath.vue'
import { mapState } from 'vuex'
import { format } from 'date-fns'
import { getYaoPinFJXX } from '@/api/inpatient-order'

export default {
  name: 'ClinicalPathway',
  components: {
    DefaultDialog,
    SheBaoDialog,
    JoinClinicalPath
  },
  data() {
    return {
      //医嘱初始化
      inpatientInit: {},
      //临床路径初始化
      initData: {},
      // 加载状态
      loading: false,
      //强制出径
      dialogQZCJVisible: false,
      chuJingForm: {
        //出径理由
        chuJingJTLY: '',
        //出径理由代码
        chuJingLYDM: ''
      },
      //出径规则
      chuJingRules: {
        chuJingLYDM: [{ required: true, message: '请选择出径原因', trigger: 'change' }]
      },
      chuJingLYDMList: [],
      //变异弹框
      dialogBianYiVisible: false,
      //变异理由
      bianYiForm: {
        //变异理由
        bianYiJTLY: '',
        //变异理由代码
        bianYiLYDM: ''
      },
      //变异规则
      bianYiRules: {
        bianYiLYDM: [{ required: true, message: '请选择变异原因', trigger: 'change' }]
      },
      bianYiLYDMList: [],
      bianYiYY: '',
      tianXieBYLY: '',
      //路径序号
      LuJingIndex: null,
      //路径选择数据
      zhuLuJingBrrjjlVo: [],
      //路径ID
      luJingID: '',
      zhenLiaoJDID: '',
      ////支路径入径记录
      zhiLuJingBrrjjlVoList: [],
      //待办事项（选中）
      XuanZhongDaiBanList: [],
      //待办事项
      daiBanShiXiangList: [],
      //选中待办内容
      guanLianLXID: '',
      daiBanSXLX: '',
      //当前高亮行
      currentRow: '',
      //已开医嘱
      yiKaiYiZhuList: [],
      //待办事项详细内容
      daiBanShiXiangXXList: [],
      //医嘱详细内容
      yiZhuXiangXXList: [],
      //用药频率
      yongYaoPLList: [],
      //非草药用药方法
      feiCaoYaoYYFFList: [],
      // 麻醉用途弹窗
      mzytDialog: false,
      mzytList: [],
      mzytValue: '',
      // 社保审批弹窗
      sheBaoDialog: false,
      displayList: [],
      sheBaoYaoPinData: null,
      dialogType: '',
      // 处方药诊断弹窗
      chuFangZDDialog: false,
      chuFangZDList: [], // 处方诊断列表
      // 诊断列表弹窗
      zhenDuanListDialog: false,
      currentZhenDuanIndex: -1, // 当前选中的处方诊断索引
      zhenDuanListData: [], // 诊断列表数据
      zhenDuanFilter: '', // 诊断名称搜索
      icd: '', // 诊断ICD
      pageIndex: 1, // 当前页码
      pageSize: 20, // 每页条数
      total: 0, // 总条数
      isChuFangSelected: false, // 是否选择了处方诊断
      //加入支路径 重新评估
      xuanZeLuJingDialog: false,
      fenZhiLJ: "",
      chongXinPG: "",
      isLuJingDialog:false
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    zhuYuanID() {
      return this.patientInfo.zhuYuanID
    },
    yongHuID() {
      return getYongHuID()?.toString()
    },
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientInfo: ({ patient }) => patient.patientInit
      // zhuanKeID: () => '42'
    })
  },
  created() {
    // 初始化数据
    this.fetchInitData()
  },
  methods: {
    // 获取初始化数据
    async fetchInitData() {
      this.XuanZhongDaiBanList = []
      this.loading = true
      try {
        // 获取初始化数据
        const initRes = await getClinicalPathInit({
          bingLiID: this.bingLiID
        })

        if (initRes.hasError === 0) {
          this.initData = initRes.data
          const data = initRes.data
          //处理数据（时间）
          if (data.zhuLuJingBrrjjlVo) {
            data.zhuLuJingBrrjjlVo.bingRenZlJdVoList.map((key) => {
              key.chuJingSJ_TXT = format(new Date(data.zhuLuJingBrrjjlVo.chuJingSJ), 'yyyy-MM-dd')
            })
            this.zhuLuJingBrrjjlVo = [
              {
                luJingMC: data.zhuLuJingBrrjjlVo.luJingMC,
                chuJingSJ: format(new Date(data.zhuLuJingBrrjjlVo.chuJingSJ), 'yyyy-MM-dd')
              },
              ...data.zhuLuJingBrrjjlVo.bingRenZlJdVoList
            ]
            this.zhiLuJingBrrjjlVoList = data.zhiLuJingBrrjjlVoList

            this.LuJingIndex = 1
            //查询_病人待办事项详情(日志)
            const sel = data.zhuLuJingBrrjjlVo.bingRenZlJdVoList[0] || {}
            this.selZhuLuJingBrrjjVo = 0
            this.loadInitData(sel)
          }
        }
        const res = await inpatientInit({ bingLiID: this.bingLiID })
        if (res.hasError === 0) {
          this.inpatientInit = res.data
          this.yongYaoPLList = res.data.yongYaoPL
          this.feiCaoYaoYYFFList = res.data.feiCaoYaoYYFF
        }
      } catch (error) {
        console.error('初始化失败', error)
        this.$message.error('初始化失败')
      } finally {
        this.loading = false
      }
    },
    //查询_病人待办事项详情
    async loadInitData(sel) {
      //查询_病人待办事项详情(日志)
      this.XuanZhongDaiBanList = []
      this.luJingID = sel.luJingID
      this.zhenLiaoJDID = sel.zhenLiaoJDID
      this.bianYiYY = sel.bianYiYY
      this.tianXieBYLY = sel.tianXieBYLY
      const danBanRes = await getBingRenDbsxDetail({
        bingLiID: this.bingLiID,
        luJingID: sel.luJingID,
        zhenLiaoJDID: sel.zhenLiaoJDID
      })
      if (danBanRes.hasError === 0) {
        const data = danBanRes.data
        if (data) {
          this.daiBanShiXiangList = [...data.huLiGZ, ...data.zhenLiaoGZ]
          this.yiKaiYiZhuList = data.yiWanChengYZ
        }
      }
      //待办事项详细内容
      const yiKaiYiZhuRes = await getBrDbsxYiZhu({
        bingLiID: this.bingLiID,
        luJingID: sel.luJingID,
        zhenLiaoJDID: sel.zhenLiaoJDID
      })
      if (yiKaiYiZhuRes.hasError === 0) {
        this.daiBanShiXiangXXList = yiKaiYiZhuRes.data.map(function (item) {
          item.selRowType = 0
          item.yiZhuXiangXiNR = []
          item.hanYangXiangXiNR = []
          return item
        })
      }
      this.yiZhuXiangXXList = []
    },
    //变异理由初始化
    async onOpenLiYou() {
      const res = await getinitChuJing()
      if (res.hasError === 0) {
        this.bianYiLYDMListLYDMList = res.data.bianYiLYList || []
        this.dialogBianYiVisible = true
        this.bianYiForm.bianYiJTLY = ''
      }
    },
    //保存变异理由
    async onSaveQiangZhiCJ() {
      const res = await saveQiangZhiCJ({
        bingLiID: this.bingLiID,
        luJingID: this.luJingID,
        bianYiLYDM: this.bianYiForm.bianYiLYDM,
        bianYiJTLY: this.bianYiForm.bianYiJTLY
      })
      if (res.hasError === 0) {
        const data = res.data
        if (data) {
          this.$message.success('保存成功！')
        }
      }
    },
    //强制出径初始化
    async getinitChuJingData() {
      const res = await getinitChuJing()
      if (res.hasError === 0) {
        this.chuJingLYDMList = res.data.chuJingLYList || []
        this.dialogQZCJVisible = true
        this.chuJingForm.chuJingJTLY = ''
      }
    },
    //强制出径
    async onQiangZhiCJ() {
      const res = await saveQiangZhiCJ({
        bingLiID: this.bingLiID,
        luJingID: this.luJingID,
        chuJingLYDM: this.chuJingForm.chuJingLYDM,
        chuJingJTLY: this.chuJingForm.chuJingJTLY
      })
      if (res.hasError === 0) {
        const data = res.data
        if (data) {
          this.$message.success('出径成功！')
        }
      }
    },
    //完成路径
    async onWanChengLuJing() {
      const res = await saveWanChengLJ({
        bingLiID: this.bingLiID,
        luJingID: this.luJingID
      })
      if (res.hasError === 0) {
        const data = res.data
        if (data) {
          this.$message.success('保存成功！')
        }
      }
    },
    //选择路径
    dbSelectLuJing(key) {
      const list = this.zhuLuJingBrrjjlVo[key]
      this.LuJingIndex = key
      this.loadInitData(list)
    },
    //待办事项行点击事件
    handleCurrentChange(val) {
      this.currentRow = val
      this.daiBanShiXiangXXList.forEach((item) => {
        if (
          (item.yiZhuXiangXiNR && item.yiZhuXiangXiNR.length > 0) ||
          (item.hanYangXiangXiNR && item.hanYangXiangXiNR.length > 0) ||
          item.zhiLiaoXiangXiNR
        ) {
          item.selRowType = 2
        } else {
          item.selRowType = 0
        }
      })
      if (val.selRowType === 1) {
        val.selRowType = 0
      } else if (val.selRowType === 2) {
        if (val.daiBanSXLX === 'zl') {
          val.selRowType = 0
          val.zhiLiaoXiangXiNR = false
        } else if(val.daiBanSXLX === 'hy'){
          val.selRowType = 0
          val.hanYangXiangXiNR = []
        } else {
          val.selRowType = 1
        }
      } else {
        val.selRowType = 1
      }
    },
    //获取待办事项（选中）
    async handleSelectionChange(row) {
      if (row) {
        row.checked = !(row.checked || false)
        if (row.checked) {
          this.XuanZhongDaiBanList.push(row)
        } else {
          this.XuanZhongDaiBanList.forEach((item, index) => {
            if (item.daiBanSXID === row.daiBanSXID) {
              this.XuanZhongDaiBanList.splice(index, 1)
            }
          })
        }
      }
    },
    //获取医嘱模板
    async handleRowChange(row, column, event) {
      this.daiBanSXLX = row.daiBanSXLX
      this.guanLianLXID = row.guanLianLXID
      this.yiZhuXiangXXList = []
      if (row.daiBanSXLX === 'yp') {
        if (row && row.guanLianLXID) {
          const res = await getErJiMbAndMbXxsByZhid({
            zuHeID: row.guanLianLXID
          })
          if (res.hasError === 0) {
            this.yiZhuXiangXXList = res.data.map((ev) => {
              ev.ylLjMbxxsVoList.map((key) => {
                let checked = row.yiZhuXiangXiNR.filter((e) => e.yaoPinID === key.yaoPinID)
                if (checked && checked.length > 0) {
                  key.checked = true
                }
                key.kaiShiSJ = format(new Date(), 'yyyy-MM-dd HH:mm:ss')
                return key
              })
              return ev
            })
          }
        }
      } else if (row.daiBanSXLX === 'hy') {
        if (row.selRowType >= 1) {
          const msg = await getHuaYanMbDetailByMbId({
            huaYanMbId: row.guanLianLXID
          })
          if (msg.hasError === 0) {
            row.hanYangXiangXiNR = msg.data
          }
        } else {
          row.hanYangXiangXiNR = []
          row.selRowType = 0
        }
      } else if (row.daiBanSXLX === 'zl') {
        if (row.selRowType >= 1) {
          row.zhiLiaoXiangXiNR = true
        } else {
          row.zhiLiaoXiangXiNR = false
          row.selRowType = 0
        }
      }
    },
    //选中医嘱
    async selectYiZhuNR(row, index, daiBanSXLXType) {
      if (row) {
        let arr = this.daiBanShiXiangXXList || []
        switch (daiBanSXLXType) {
          case 'yp':
            let list = row.ylLjMbxxsVoList[index]
            if (list.checked) {
              list.moBanMC = row.moBanMC
              list.moBanList = row
              const FJXX = await this.onYaoPinFJXXApi(list)
              // 药品附加信息判断弹窗队列
              let type = false
              const continueProcess = await this.handleAlertQueue(row, FJXX, list)
              if (continueProcess.length == 0) {
                type = true
              }
              continueProcess.forEach((ev) => {
                switch (ev.type) {
                  case 'mzyt': // 麻醉用途
                    list.maZuiYT = ev.maZuiYT
                    type = true
                    break
                  case 'sheBaoSP': // 社保审批
                    list.ziFei = ev.ziFei
                    type = true
                    break
                  case 'chuFangZD': // 处方药诊断
                    list.chuFangZDList = ev.chuFangZDList
                    type = true
                    break
                }
              })
              if (type) {
                arr.forEach((e) => {
                  if (e.guanLianLXID === this.guanLianLXID) {
                    if (e.yiZhuXiangXiNR) {
                      e.yiZhuXiangXiNR.push(list)
                    } else {
                      e.yiZhuXiangXiNR = [list]
                    }
                  }
                })
              }
              //已被药房停止使用
              //根据医院部方点评工作组反馈和医院规定,您被限制开具药品
              //高警示药品
              //国家采集提示
              //抗肿瘤药物的处方权限
              //毒性药品控制
              //麻醉药品控制
              //处方药判断
              //麻醉药选择用途
              //药品审批
              //抗菌药品的判断
              //抗菌会诊单
            } else {
              arr.forEach((e) => {
                if (e.guanLianLXID === this.guanLianLXID) {
                  let list = row.ylLjMbxxsVoList[index]
                  e.yiZhuXiangXiNR.forEach((obj, key) => {
                    if (obj.moBanMXID === list.moBanMXID) {
                      e.yiZhuXiangXiNR.splice(key, 1)
                      return
                    }
                  })
                }
              })
            }
            break
        }
        this.daiBanShiXiangXXList = arr
      }
    },
    //药品附加信息
    async onYaoPinFJXXApi(updateData) {
      const res = await getYaoPinFJXX({
        bingLiID: this.bingLiID, //病历ID
        jiLiangDW: updateData.jiLiangDW || '', //计量单位
        yaoFangDM: this.initData.yaoFangDM, //药房代码
        yaoPinID: updateData.yaoPinID, //药品ID
        yaoPinLB: updateData.yaoPinLB, //药品类别（0=本院药品，1=外购药品，2=赠送药品）
        yaoPinMC: updateData.yaoPinMC, //药品名称
        yiCiYL: updateData.yiCiYL || '', //一次用量
        yiZhuLB: 'cq', //医嘱类别（大类，cq长期,ls临时）
        yiZhuLX: 'cq', //医嘱类型（cq=长期,ls=临时,cy=草药,zcydy=草药带药,cydy=出院带药）
        zhiXingFF: updateData.zhiXingFF || '', //执行方法
        zhiXingPL: updateData.zhiXingPL || '', //执行频率
        zhuYuanID: this.zhuYuanID //住院ID
      })
      if (res.hasError === 0) {
        console.log(res)
        return res.data
      }
    },
    //药品附加信息判断
    async handleAlertQueue(row, FJXX, drugRow) {
      const alertQueue = []
      const { drugAttributesVos } = FJXX

      // 收集所有需要提示的消息
      if (FJXX) {
        // 抗菌药物耐药率提示
        if (FJXX.naiYaoLvTS) {
          alertQueue.push({
            message: FJXX.naiYaoLvTS,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 血制品提醒
        if (FJXX.xueZhiPinTX) {
          alertQueue.push({
            message: FJXX.xueZhiPinTX,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 二甲双胍(guā)提醒
        if (FJXX.erJiaShuangGuaTX) {
          alertQueue.push({
            message: FJXX.erJiaShuangGuaTX,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 麻醉精神药品提醒
        const mzypMessage = this.isMZJSYP(drugAttributesVos)
        if (mzypMessage) {
          alertQueue.push({
            message: mzypMessage,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 药物过敏提示
        if (FJXX.guoMinTX) {
          alertQueue.push({
            message: FJXX.guoMinTX,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 高警示药品提醒
        const gwypItem = drugAttributesVos.find(
          (item) => item.shuXingDM === 'gwyp' && item.shuXingZhi === '1'
        )
        if (gwypItem) {
          alertQueue.push({
            message: `${drugRow.yaoPinMC}是高警示药品`,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 静脉营养药品
        const jpypItem = drugAttributesVos.find(
          (item) =>
            item.shuXingDM === 'jpyp' && (item.shuXingZhi === '1' || item.shuXingZhi === '2')
        )
        if (jpypItem) {
          alertQueue.push({
            message: `须完成"营养风险筛查"（本次住院期间完成即可）`,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 国家采集提示(剩余使用量提示)
        if (FJXX.shengYuSYLTS) {
          alertQueue.push({
            message: FJXX.shengYuSYLTS,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 非中选药品弹出中选药品列表
        // if (FJXX.guoJiaCG === '12' || FJXX.guoJiaCG === '13' || FJXX.guoJiaCG === '14') {
        //   alertQueue.push({
        //     type: 'zhongXuan', // 中选药品弹框
        //     title: '中选药品列表',
        //     drugRow, // 传递当前药品参数
        //     row // 行数据
        //   })
        // }

        // NIHSS评分表提示 TODO: 需要打开链接
        if (FJXX.needNIHSS === '1') {
          alertQueue.push({
            message: `必须完成美国国立卫生院神经功能缺损评分表(NIHSS)评分后才可开溶栓药`,
            title: '信息窗口',
            type: 'delete' // 提示后删除
          })
        }

        // 呕吐分级提醒
        if (FJXX.zhiOuYPTX) {
          alertQueue.push({
            message: FJXX.zhiOuYPTX,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 儿童用药提示
        if (FJXX.erTongYYTS) {
          alertQueue.push({
            message: FJXX.erTongYYTS,
            title: '信息窗口',
            type: 'notify' // 仅提示类型
          })
        }

        // 抗肿瘤药物的处方权限
        const kzpjItem = drugAttributesVos.find(
          (item) => item.shuXingDM === 'kzpj' && item.shuXingZhi === '1'
        )
        const kzxjItem = drugAttributesVos.find(
          (item) => item.shuXingDM === 'kzxj' && item.shuXingZhi === '1'
        )
        if (kzpjItem && !this.inpatientInit.puTongJiZLYWQX) {
          alertQueue.push({
            message: `${drugRow.yaoPinMC}是普通使用级抗肿瘤药物，你没有开具普通级肿瘤药物的权限！药品ID=${drugRow.daiMa}`,
            title: '信息窗口',
            type: 'delete' // 提示后删除
          })
        } else if (kzxjItem && !this.inpatientInit.xianZhiJiZLYWQX) {
          alertQueue.push({
            message: `${drugRow.yaoPinMC}是限制使用级抗肿瘤药物，你没有开具限制级肿瘤药物的权限！药品ID=${drugRow.daiMa}`,
            title: '信息窗口',
            type: 'delete' // 提示后删除
          })
        }
        // 毒性药品控制
        if (drugRow?.guanLiLX === 'D' && !this.inpatientInit.duXingYPCFQX) {
          alertQueue.push({
            message: `对不起，你没有毒性药品处方权限，无法开具药品【${drugRow.yaoPinMC}】！药品ID=${drugRow.yaoPinID} 管理类型=${drugRow?.guanLiLX}`,
            title: '信息窗口',
            type: 'delete' // 提示后删除
          })
        }
        // 麻醉药品控制
        if (
          (drugRow?.guanLiLX === 'M' || drugRow?.guanLiLX === '1') &&
          !this.inpatientInit.maZuiCFQX
        ) {
          alertQueue.push({
            message: `对不起，你没有麻醉处方权限，无法开具药品【${drugRow.yaoPinMC}】！药品ID=${drugRow.yaoPinID} 管理类型=${drugRow?.guanLiLX}`,
            title: '信息窗口',
            type: 'delete' // 提示后删除
          })
        }
        // 处方药判断
        const isMaZuiYaoPin = drugRow?.guanLiLX === '1' || drugRow?.guanLiLX === 'M'

        // 检查是否需要处方药诊断
        if (isMaZuiYaoPin) {
          try {
            // 获取处方药诊断信息
            const cfzdRes = await getChuFangZD({
              bingLiID: this.bingLiID
            })

            if (cfzdRes.hasError === 0) {
              if (cfzdRes.data.length > 0) {
                alertQueue.push({
                  type: 'chuFangZD', // 处方药诊断弹窗
                  title: '处方诊断',
                  data: cfzdRes.data,
                  drugRow
                })
              } else {
                alertQueue.push({
                  message: `未获取到大病历四或五的临床诊断！`,
                  title: '信息窗口',
                  type: 'delete' // 提示后删除
                })
              }
            }
          } catch (error) {
            console.error('获取处方药诊断失败:', error)
          }
        }

        // 麻醉药选择用途
        if (drugRow?.guanLiLX === 'M') {
          const mzytRes = await getMaZuiYT()
          if (mzytRes.hasError === 0 && res.data?.length) {
            alertQueue.push({
              type: 'mzyt', // 麻醉药品用途弹框
              title: '麻醉药品用途',
              data: res.data
            })
          } else {
            alertQueue.push({
              message: `未获取到麻醉药品用途！`,
              title: '信息窗口',
              type: 'delete' // 提示后删除
            })
          }
        }
        // 药品审批
        if (FJXX.sheBaoSP) {
          if (FJXX.sheBaoSP.xiangDingFW?.length) {
            // 新的审批弹窗
            alertQueue.push({
              type: 'sheBaoSP',
              dialogType: 'new',
              title: '药品审批',
              data: FJXX.sheBaoSP,
              drugRow
            })
          } else if (FJXX.sheBaoSP.type !== '0') {
            //  老的审批弹窗
            alertQueue.push({
              type: 'sheBaoSP',
              dialogType: 'old',
              title: '药品审批',
              data: FJXX.sheBaoSP,
              drugRow
            })
          }
        }
        // 抗菌药物限制
        const kjypItem1 = drugAttributesVos.find(
          (item) => row.yiZhuLX === 'kjyp' && item.shuXingZhi === '1'
        )
        const kjypItem2 = drugAttributesVos.find(
          (item) => row.yiZhuLX === 'kjyp' && item.shuXingZhi === '2'
        )
        const kjypItem3 = drugAttributesVos.find(
          (item) => row.yiZhuLX === 'kjyp' && item.shuXingZhi === '3'
        )
        if (kjypItem1 || kjypItem2 || kjypItem3) {
          // 判断工种代码
          let tygzdm = '' // 通用工种代码
          let tygzmc = '' // 职位名称

          // 获取医生工种（职称）
          const gzdm = this.inpatientInit.gongZhongDM

          // 判断工种代码对应的级别
          switch (gzdm) {
            case '0010': // 市级名中医参照主任医师的抗生素级别
            case '0011': // 主任医师
            case '0381': // 主任中医师
              tygzdm = '0011' // 主任医师
              tygzmc = '主任医师'
              break
            case '0382': // 副主任中医师
            case '0012': // 副主任医师
              tygzdm = '0012' // 副主任医师
              tygzmc = '副主任医师'
              break
            case '0383': // 主治中医师
            case '0013': // 主治医师
              tygzdm = '0013' // 主治医师
              tygzmc = '主治医师'
              break
            case '0384': // 中医师
            case '0014': // 住院医师
              tygzdm = '0014' // 住院医师
              tygzmc = '住院医师'
              break
            case '0385': // 中医士
            case '0015': // 医士
              tygzdm = '0015' // 医士 级别5
              tygzmc = '医士'
              break
            default:
              tygzdm = gzdm
              break
          }

          // 工种代码未设定
          if (tygzdm === '' || tygzdm === '0000') {
            await this.$confirm('工种代码未设定，无法开具抗生素类药品！', '信息窗口', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            })
            // this.handleDeleteDrawerRow(row)
            return false
          }

          // 非限制使用级抗菌药物，各级医师都可以开
          if (kjypItem1) {
            // TODO: 选择抗菌药物使用方法
          } else if (kjypItem2) {
            // 限制使用级抗菌药物
            // 判断是否有权限开具限制使用级抗菌药物
            // 副高及以上职称
            // 无副高及以上职称的科室，科室主任职务
            // 特殊科室（感染科、血液内科、急诊科、呼吸内科、ICU和CCU）的医师，中级及以上职称
            const isFuGao = tygzdm === '0012' || tygzdm === '0011'
            const isZhuRen =
              this.inpatientInit.existFuGaoZC === '0' && this.inpatientInit.iszhiXingZR === '1'
            const isTeShuKeShi = this.inpatientInit.ganRanZKBZ === '1' && tygzdm === '0013'

            if (isFuGao || isZhuRen || isTeShuKeShi) {
              // TODO: 选择抗菌药物使用方法
            } else {
              // 无权限
              await this.$confirm(
                `您的职称（${tygzmc}）无法开具限制使用级抗菌药物。根据规定，副高及以上职称才能开具限制使用级抗菌药物！`,
                '信息窗口',
                {
                  confirmButtonText: '确定',
                  showCancelButton: false,
                  type: 'warning'
                }
              )
              // this.handleDeleteDrawerRow(row)
              return false
            }
          } else if (kjypItem3) {
            // 特殊使用级抗菌药物
            if (row.yiZhuLX === 'cq') {
              // 长期医嘱
              // 正高级职称
              // 无正高职称医师的科室，科室主任职务
              // 特殊科室（感染科、血液内科、急诊科、呼吸内科、ICU和CCU）的医师，副高及以上职称
              const isZhengGao = tygzdm === '0011'
              const isZhuRen =
                this.inpatientInit.existZhengGaoZC === '0' && this.inpatientInit.iszhiXingZR === '1'
              const isTeShuKeShi = this.inpatientInit.ganRanZKBZ === '1' && tygzdm === '0012'

              if (isZhengGao || isZhuRen || isTeShuKeShi) {
                // 检查特殊抗菌药物会诊单ID
                if (FJXX.teShuKjywHzdID === 0) {
                  await this.$confirm(
                    '请先进行特殊类抗生素药物会诊单会诊通过后再进行医嘱的下达。',
                    '信息窗口',
                    {
                      confirmButtonText: '确定',
                      showCancelButton: false,
                      type: 'warning'
                    }
                  )
                  // this.handleDeleteDrawerRow(row)
                  return false
                } else {
                  // TODO: 选择抗菌药物使用方法
                }
              } else {
                // 无权限
                await this.$confirm(
                  `您的职称（${tygzmc}）无法开具特殊使用级抗菌药物。长期医嘱中，正高级职称才能开具特殊使用级抗菌药物！`,
                  '信息窗口',
                  {
                    confirmButtonText: '确定',
                    showCancelButton: false,
                    type: 'warning'
                  }
                )
                // this.handleDeleteDrawerRow(row)
                return false
              }
            } else {
              // 临时医嘱
              // 副高及以上职称
              // 无副高及以上职称的科室，科室主任职务
              // 特殊科室（感染科、血液内科、急诊科、呼吸内科、ICU和CCU）的医师，中级及以上职称
              const isFuGao = tygzdm === '0012' || tygzdm === '0011'
              const isZhuRen =
                this.inpatientInit.existFuGaoZC === '0' && this.inpatientInit.iszhiXingZR === '1'
              const isTeShuKeShi = this.inpatientInit.ganRanZKBZ === '1' && tygzdm === '0013'
              const isJiZhen = String(this.curBrZhuanKeID) === '49' // 急诊抢救科室ID

              if (isFuGao || isZhuRen || isTeShuKeShi || isJiZhen) {
                // 检查是否已有会诊单
                if (FJXX.teShuKjywHzdID === 0) {
                  // TODO:打开会诊单弹窗
                } else {
                  // TODO: 选择抗菌药物使用方法
                }
              } else {
                // 无权限
                await this.$confirm(
                  `您的职称（${tygzmc}）无法开具特殊使用级抗菌药物。临时医嘱中，副高及以上职称才能开具特殊使用级抗菌药物！`,
                  '信息窗口',
                  {
                    confirmButtonText: '确定',
                    showCancelButton: false,
                    type: 'warning'
                  }
                )
                // this.handleDeleteDrawerRow(row)
                return false
              }
            }
          }
        }
        // TODO：特治特药备案
        // TODO：特殊病备案
      }

      // 递归显示消息队列
      const showNextAlert = async (index = 0) => {
        if (index >= alertQueue.length) return alertQueue

        const alertItem = alertQueue[index]

        try {
          switch (alertItem.type) {
            case 'notify': // 仅提示
              await this.$confirm(alertItem.message, alertItem.title, {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'info'
              })
              return await showNextAlert(index + 1)

            case 'delete': // 提示后删除跳出循环
              await this.$confirm(alertItem.message, alertItem.title, {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'info'
              })
              // this.handleDeleteDrawerRow(row)
              return false

            case 'mzyt': // 麻醉用途
              this.mzytList = alertItem.data
              this.mzytDialog = true
              // 等待用户操作
              return new Promise((resolve) => {
                const handler = (result) => {
                  if (result) {
                    // 用户保存麻醉用途
                    alertItem.maZuiYT = result
                    // this.$refs.drawerTable.handleUpdateRow(row, 'maZuiYT', result)
                    resolve(showNextAlert(index + 1))
                  } else {
                    // 用户关闭弹窗
                    // this.handleDeleteDrawerRow(row)
                    resolve(false)
                  }
                }
                this.$once('mzytSelect', handler)
              })
            case 'sheBaoSP': // 社保审批
              if (isPlainObject(alertItem.data)) {
                this.displayList = [alertItem.data]
              } else if (isArray(alertItem.data)) {
                this.displayList = alertItem.data
              }
              this.sheBaoYaoPinData = alertItem.drugRow
              this.dialogType = alertItem.dialogType
              this.sheBaoDialog = true
              // 等待用户操作
              return new Promise((resolve) => {
                const handler = (result) => {
                  if (result) {
                    // 用户保存
                    alertItem.ziFei = result
                    // this.$refs.drawerTable.handleUpdateRow(row, 'ziFei', result)
                    resolve(showNextAlert(index + 1))
                  } else {
                    // 用户关闭弹窗
                    // this.handleDeleteDrawerRow(row)
                    resolve(false)
                  }
                }
                this.$once('sheBaoSelect', handler)
              })
            case 'chuFangZD': // 处方药诊断
              // 直接使用接口返回的数据
              this.chuFangZDList = alertItem.data || [] // 设置处方诊断列表
              this.chuFangZDDialog = true

              // 等待用户操作
              return new Promise((resolve) => {
                const handler = (result) => {
                  if (result && Array.isArray(result)) {
                    // 用户选择了确定
                    alertItem.chuFangZDList = result
                    // this.$refs.drawerTable.handleUpdateRow(row, 'chuFangZDList', result)
                    resolve(showNextAlert(index + 1))
                  } else {
                    // 用户关闭弹窗
                    // this.handleDeleteDrawerRow(row)
                    resolve(false)
                  }
                }
                this.$once('chuFangZDSelect', handler)
              })
            default:
              return await showNextAlert(index + 1)
          }
        } catch (error) {
          // 用户关闭弹窗，继续显示下一个
          return await showNextAlert(index + 1)
        }
      }

      // 开始显示消息队列
      return await showNextAlert()
    },
    // 是否麻醉精神药品
    isMZJSYP(drugAttributesVos) {
      let message = ''
      if (!drugAttributesVos || drugAttributesVos.length === 0) {
        return ''
      }
      // 查找麻醉药品或精神药品标记
      const mzypItem = drugAttributesVos.find(
        (item) => item.shuXingDM === 'maZuiYP' && item.shuXingZhi === '1'
      )
      // 查找控制使用药品标记
      const kzypItem = drugAttributesVos.find(
        (item) => item.shuXingDM === 'kzsy' && item.shuXingZhi === '1'
      )
      if (mzypItem && kzypItem) {
        message =
          '此药为麻醉药品或一类精神药品！进口药、贵重药、用量较大的药品，控制使用。建议选择同类低价药品!'
        return message
      }
      if (mzypItem) {
        message = '此药为麻醉药品或一类精神药品！'
      }

      if (kzypItem) {
        message = '此药为进口药、贵重药、用量较大的药品，控制使用。建议选择同类低价药品!'
      }

      return message
    },
    // 麻醉用途保存
    handleMzytSave() {
      if (this.mzytValue) {
        this.$emit('mzytSelect', this.mzytValue)
        this.mzytDialog = false
      } else {
        this.$alert('麻醉药品用途不能为空！', '信息窗口')
      }
    },
    // 麻醉用途关闭
    handleMzytClose() {
      this.$emit('mzytSelect', false)
      this.mzytDialog = false
    },
    // 社保审批弹窗确定
    handleSheBaoConfirm(value) {
      this.$emit('sheBaoSelect', value)
      this.sheBaoDialog = false
    },
    // 社保审批弹窗关闭
    handleSheBaoClose() {
      this.$emit('sheBaoSelect', false)
      this.sheBaoDialog = false
    },
    // 添加处方诊断
    handleAddChuFangZD() {
      const xuHao = this.chuFangZDList[this.chuFangZDList.length - 1].xuHao + 1
      this.chuFangZDList.push({
        mingChen: '新增',
        icd: '',
        xuHao: xuHao
      })
    },
    // 删除处方诊断
    handleDeleteChuFangZD(index) {
      this.chuFangZDList.splice(index, 1)
    },
    // 上移处方诊断
    handleSortUp(index) {
      if (index > 0) {
        // 交换xuHao字段的值
        const currentXuHao = this.chuFangZDList[index].xuHao
        this.chuFangZDList[index].xuHao = this.chuFangZDList[index - 1].xuHao
        this.chuFangZDList[index - 1].xuHao = currentXuHao

        // 按xuHao排序
        this.chuFangZDList.sort((a, b) => a.xuHao - b.xuHao)
      }
    },

    // 下移处方诊断
    handleSortDown(index) {
      if (index < this.chuFangZDList.length - 1) {
        // 交换xuHao字段的值
        const currentXuHao = this.chuFangZDList[index].xuHao
        this.chuFangZDList[index].xuHao = this.chuFangZDList[index + 1].xuHao
        this.chuFangZDList[index + 1].xuHao = currentXuHao

        // 按xuHao排序
        this.chuFangZDList.sort((a, b) => a.xuHao - b.xuHao)
      }
    },
    // 处方药诊断弹窗确认
    handleChuFangZDDialogConfirm() {
      if (this.chuFangZDList.length === 0) {
        this.$alert('请至少添加一个处方诊断', '信息窗口')
        return
      }

      // 验证处方诊断名称
      for (const item of this.chuFangZDList) {
        if (!item.mingChen || item.mingChen === '新增') {
          this.$alert('处方诊断名称不能为空', '信息窗口')
          return
        }
      }

      this.$emit('chuFangZDSelect', this.chuFangZDList)
      this.chuFangZDDialog = false
    },

    // 处方药诊断弹窗取消
    handleChuFangZDDialogCancel() {
      this.$emit('chuFangZDSelect', false)
      this.chuFangZDDialog = false
    },
    // 打开处方诊断选择弹窗
    async handleSelectChuFangZD(index) {
      this.currentZhenDuanIndex = index
      this.icd = ''
      this.zhenDuanFilter = ''
      this.pageIndex = 1
      this.pageSize = 20
      this.zhenDuanListDialog = true
      this.isChuFangSelected = false
      // 加载诊断列表
      await this.loadZhenDuanList()
    },

    // 加载诊断列表
    async loadZhenDuanList() {
      try {
        const res = await getZhenDuanList({
          wenBen: this.zhenDuanFilter,
          pageIndex: this.pageIndex,
          pageSize: this.pageSize
        })

        if (res.hasError === 0) {
          this.zhenDuanListData = res.data || []
          if (res.extendData.total) {
            this.total = res.extendData.total
          }
        }
      } catch (error) {
        console.error('获取诊断列表失败', error)
        this.$message.error('获取诊断列表失败:' + error.errorMessage)
      }
    },

    // 搜索诊断
    async searchZhenDuan() {
      this.pageIndex = 1
      await this.loadZhenDuanList()
    },

    // 选择诊断
    handleSelectZhenDuan(row) {
      this.icd = row.icd
      this.zhenDuanFilter = row.zhenDuanMC
      this.isChuFangSelected = true
    },
    // 确认选择诊断
    handleSelectZhenDuanConfirm() {
      if (this.currentZhenDuanIndex >= 0) {
        this.$set(this.chuFangZDList, this.currentZhenDuanIndex, {
          icd: this.icd,
          mingChen: this.zhenDuanFilter
        })
      }
      this.zhenDuanListDialog = false
    },

    // 分页大小变化
    handleZhenDuanSizeChange(val) {
      this.pageSize = val
      this.loadZhenDuanList()
    },

    // 页码变化
    handleZhenDuanCurrentChange(val) {
      this.pageIndex = val
      this.loadZhenDuanList()
    },

    //加入支路径 重新评估
    addZhiLuJing() {
      this.fenZhiLJ = "1"
      this.chongXinPG = ""
      this.isLuJingDialog = true
      this.xuanZeLuJingDialog = true
    },
    //加入支路径 重新评估
    closeLuJing(){
      this.fenZhiLJ = ""
      this.chongXinPG = ""
      this.isLuJingDialog = false
      this.xuanZeLuJingDialog = false
      this.fetchInitData()
    },
    //加入支路径 重新评估
    onChongXinPingGu(){
      this.fenZhiLJ = ""
      this.chongXinPG = "1"
      this.isLuJingDialog = true
      this.xuanZeLuJingDialog = true
    },
    //处理保存是医嘱数据
    async dealBaiBanXiangXiData() {
      let dataList = []
      this.daiBanShiXiangXXList.forEach((item) => {
        if (item.selRowType >= 1) {
          let params = {
            bingLiID: this.bingLiID, //病历ID
            luJingID: item.luJingID, //路径ID
            zhenLiaoJDID: item.zhenLiaoJDID, //诊疗阶段ID
            daiBanSXID: item.daiBanSXID, //待办事项id
            daiBanSXMC: item.daiBanSXMC, //待办事项名称
            biXuanXiang: '', //必须选
            riJianBZ: item.riJianBZ, //日间标志
            zuiChiWCSJ: '', //最迟完成时间
            shiJiWCSJ: format(new Date(), 'yyyy-MM-dd HH:mm:ss'), //实际完成时间
            guanLianLX: '', //关联类型
            guanLianLXID: item.guanLianLXID, //关联类型id
            zhiXingJG: '', //执行结果
            zhiXingRYID: this.yongHuID, //执行人员id
            zhiXingRYXM: '', //执行人员姓名
            leiBie: item.leiBie //类别
          }
          switch (item.daiBanSXLX) {
            case 'yp':
              const lists = {}
              if (item.yiZhuXiangXiNR.length > 0) {
                this.XuanZhongDaiBanList.push(params)
                item.yiZhuXiangXiNR.forEach((ev) => {
                  if (lists[ev.moBanID]) {
                    lists[ev.moBanID].push(ev)
                  } else {
                    lists[ev.moBanID] = [ev]
                  }
                })
                for (let i in lists) {
                  let index = 0
                  lists[i].map((ev, key) => {
                    index++
                    ev.zuHao = index
                    if (key === 0) {
                      //┐|┘
                      ev.zuHaoTXT = '┐'
                    } else if (key === lists[i].length - 1) {
                      ev.zuHaoTXT = '┘'
                    } else {
                      ev.zuHaoTXT = '|'
                    }
                    const yaoPaoList = {
                      muBanMC: ev.muBanMC, //模板名称
                      ganRanZD: ev.ganRanZD, //感染诊断
                      kangJunYaoYfVo: ev.kangJunYaoYfVo,
                      zuHao: ev.zuHao, //组号
                      zuHaoTXT: ev.zuHaoTXT, //组号TXT
                      yaoPinID: ev.yaoPinID, //药品ID
                      yaoFangDM: ev.yaoFangDM, //药房代码
                      yaoPinMC: ev.yaoPinMC, //药品名称
                      danJia: ev.danJia, //单价
                      guiGe: ev.guiGe, //规格
                      jiXing: ev.jiXing, //剂型
                      yiCiYL: ev.yiCiYL, //一次用量
                      jiLiangDW: ev.jiLiangDW, //剂量单位
                      zhiXingPL: ev.zhiXingPL, //执行频率
                      zhiXingFF: ev.zhiXingFF, //执行方法
                      geiYaoSJ: ev.geiYaoSJ, //给药时间
                      teShuYF: ev.teShuYF, //特殊用法
                      kaiShiSJ: ev.kaiShiSJ, //开始时间
                      jieShuSJ: ev.jieShuSJ, //结束时间
                      chiXuTS: ev.chiXuTS, //持续天数
                      ziFei: ev.ziFei, //自费
                      kangJunYP: ev.kangJunYP, //抗菌药品
                      yongYaoTS: ev.yongYaoTS, //用药天数
                      danWei: ev.danWei, //单位
                      teShuKJYWHZDID: ev.teShuKJYWHZDID, //特殊抗菌药物会诊单ID
                      xianDingFW: ev.xianDingFW, //限定范围
                      fenLeiMa: ev.fenLeiMa, //分类码
                      sheBaoDM: ev.sheBaoDM //社保代码
                    }
                    dataList.push(yaoPaoList)
                  })
                }
              }
              break
            case 'hy':
              if (item.hanYangXiangXiNR.length > 0) {
                this.XuanZhongDaiBanList.push(params)
              }
              break
            case 'zl':
              if (item.zhiLiaoXiangXiNR) {
                this.XuanZhongDaiBanList.push(params)
              }
              break
          }
        }
      })
      return dataList
    },
    //保存
    async onSaveLinChuangLuJing() {
      let daiBanSXList = await dealBaiBanXiangXiData()
      this.XuanZhongDaiBanList.forEach((item) => {
        item.shiJiWCSJ = format(new Date(), 'yyyy-MM-dd HH:mm:ss')
      })
      const params = {
        bingLiID: this.bingLiID,
        luJingID: this.luJingID,
        zhenLiaoJDID: this.zhenLiaoJDID,
        daiBanSXVos: this.XuanZhongDaiBanList,
        yaoPinYzVos: daiBanSXList
        // [{ "muBanMC":"", "zuHao":"", "yaoPinID":"", "yaoPinMC":"", "yiCiYL":"","jiLiangDW":"", "zhiXingPL":"", "zhiXingFF":"", "geiYaoSJ":"", "teShuYF":"", "kaiShiSJ":"", "jieShuSJ":"", "chiXuTS":"", "shanChuBZ":""}]
      }
      let res = await zhiXingDbsx(params)
      if (res.hasError === 0) {
        this.$message.success('保存成功！')
        this.loadInitData({
          luJingID: this.luJingID,
          zhenLiaoJDID: this.zhenLiaoJDID
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.clinical-path-container {
  height: 100%;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  background-color: #fff;
  position: relative;
}

.clinical-path-header {
  height: 50px;
  display: flex;
  background-color: #eff3fb;
  border-radius: 4px;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
}

.clinical-path-btn {
  padding: 10px;
}

.clinical-path-btn-reevaluate {
  background-color: #a66dd4;
  color: #fff;
}

.clinical-path-layout {
  padding: 10px;
  display: flex;
  height: 100%;
  background-color: #eff3fb;
  margin-top: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.left-panel {
  width: 300px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #eff3fb;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.left-panel-header {
  padding: 10px;
}

.title-log {
  font-weight: 600;
  border-left: 4px solid #356ac5;
  padding-left: 8px;
  margin-bottom: 8px;
}

.lujing-list-top {
  border: 1px solid #dcdfe6;
  padding: 6px;
}

.lujing-list {
  border-left: 1px solid #dcdfe6;
  border-right: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  padding: 6px;
  cursor: pointer;
}

.lujing-lists .active {
  background-color: #356ac5;
  color: #fff;
}

.lujing-list:nth-child(even) {
  background-color: #fff;
}

.right-panel {
  flex: 1;
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  background-color: #eff3fb;
  // padding: 10px;
  border-radius: 4px;
}

.right-panel-header {
  height: 210px;
  background-color: #eff3fb;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  padding: 10px;
  // box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
}

.right-panel-container {
  height: 100%;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  // background-color: #fff;
  position: relative;
  border: 1px solid #dcdfe6;
  margin-top: 10px;
}
.right-panel-DaiBanXiangQing {
  width: 100%;
  // margin-top: 20px
}
.right-panel-left-moBan {
  padding-right: 10px;
}
.right-panel-right-yiZhuNR {
  padding: 10px;
  border: 1px solid #dcdfe6;
  height: 400px;
  overflow-y: auto;
}
.history-drug-dialog {
  ::v-deep .el-dialog__body {
    .drug-filter {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .el-radio-group {
        margin-right: 10px;
      }
      .el-date-picker {
        width: 260px;
      }
    }
  }
}
.right-YiZhuNeiRong-title {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 6px;
}
.right-YiZhuNeiRongList-box {
  padding: 7px 0;
  border-bottom: 1px solid #dcdfe6;
}
.right-YiZhuNeiRongList-YaoPinMC {
  padding-left: 24px;
}
.right-YiZhuNeiRongList-input {
  max-width: 60px;
  margin: 5px 10px;
}
.right-YiZhuNeiRongList-select {
  max-width: 100px;
  margin: 5px 10px;
}
.right-YiZhuNeiRongList-datetime {
  margin-left: 24px;
}
.right-YiZhuNeiRongList-input {
  margin: 5px 10px;
}
.middle-crile {
  display: inline-block;
  width: 14px;
  height: 14px;
  border-radius: 16px;
  margin-right: 10px;
  vertical-align: middle;
}
.grey {
  /* background: rgba(128, 128, 128, 0.5); */
  background: #fff;
}
.crile_blue {
  background: #3d6cc8;
}
.crile_color {
  background: #5cb85c;
}
</style>
