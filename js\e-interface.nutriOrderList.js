WRT_e.api = WRT_e.api || {}

WRT_e.api.nutriOrderList = {
	// 营养医嘱单列表界面
	// 获取单条医嘱单的记录（al_blid） 
	getNurtionOrderList: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_GetNurtionOrderList',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
					withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
					if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
	
	// 停止营养医嘱单
	stopNurtionOrder: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_StopNurtionOrder',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
					withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
					if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},

	// 营养医嘱单
	//初始化
	getInit: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				// url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_Init',
				url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_Init',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
					withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
					if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},

	// BMI计算
	getBMI: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_GetBMI',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
					withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
					if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
	
	// 获取bee
	getBee: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_GetBee',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
					withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
					if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},

	// 获取身高或者体重
	getSGTZ: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_GetSGTZ',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
					withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
					if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
	
	//  获取患者营养医嘱单的记录（al_yzdid）
	getNurtionOrder: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_GetNurtionOrder',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
					withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
					if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},

	// 获取营养医嘱药品列表
	getNurtionDrugList: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_GetNurtionDrugList',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
					withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
					if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},

	// 获取营养医嘱药品列表（标记列表初始参数？？）
	getNurtionOrderDrugs: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_GetnurtionOrderDrugs',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
					withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
					if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
	

	//获取所选药品的营养元素
	getDrugsNurtion: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_GetDrugsNurtion',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
					withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
					if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},

	//获取病人相关检验结果
	getHyjg: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_GetHyjg',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
					withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
					if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
	
	//保存
	saveNurtionOrder: function(o = {}) {
		o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_Save',
        type: 'Post',
        dataType: "json",
        data:JSON.stringify(o.params),
				async: false,
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
		// o.params = o.params || {}
		// if (WRT_config.mockType == '1') {
		// 	if (o.success) o.success({
		// 		Code: "1",
		// 		CodeMsg: "获取数据成功！",
		// 		Result: []
		// 	})
		// } else {
		// 	$.ajax({
		// 		url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_Save',
		// 		type: 'POST',
		// 		data: JSON.stringify(o.params),
		// 		dataType: "json",
		// 		async: false,
		// 		cache:false,
		// 		crossDomain:true, //设置跨域为true
		// 		xhrFields: {
		// 			withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
		// 		},
		// 		contentType: "application/json; charset=utf-8",
		// 		success: function (msg) {
		// 			if (o.success) o.success(msg)
		// 		},
		// 		error: function (error) {
		// 			if (error.statusText == 'error') {
		// 				WRT_e.ui.hint({
		// 					type: 'error',
		// 					msg: "服务器连接失败"
		// 				})
		// 			}
		// 		}
		// 	})
		// }
	},

	// 提交
	submitNurtionOrder: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				url: WRT_config.server + '/yyyzd/yyyzd_new.aspx/e_SubmitYz',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
					withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
					if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
	
	// 获取频率、用法、给药方法(和住院药品医嘱一样获取的)
	getYpyfpl: function (o = {}) { 
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if(o.success) o.success({
			Code: "1",
			CodeMsg: "获取数据成功！",
			Result:"[]"
			})
		}else{
			$.ajax({
			url: WRT_config.server+'/yz_sz/ypfjxx.aspx/e_GetYpyfpl',
			type: 'POST',
			data:JSON.stringify(o.params),
			dataType: "json",
			cache:false,
			crossDomain:true, //设置跨域为true
			async:false,
			xhrFields: {
				withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
			},
			contentType: "application/json; charset=utf-8",
			success: function (msg) {
				if (o.success) o.success(msg.d)
			},
			error:function(error){
				WRT_e.ui.hint({
				type: 'error',
				msg: JSON.parse(error.responseText).Message
				})
			}
			})
		}
	},
	
  //药品附加信息
  getYpFjxx: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:yz_sz
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/ypfjxx.aspx/e_GetYpFjxx',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
				async:false,
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },

  //获取未提交医嘱
  getWtjYz: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:yz_sz
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_GetWtjYz',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
}