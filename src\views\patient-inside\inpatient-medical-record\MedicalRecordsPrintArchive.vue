<template>
  <div class="container">
    <div class="content-top">
      <el-button type="primary" @click="selAll(true)">全选</el-button>
      <el-button type="primary" @click="selAll(false)">全不选</el-button>
      <el-button type="primary" @click="print">打印</el-button>
      <el-button type="primary" @click="getSelfPayItems(true)">生成完整病历</el-button>
      <el-button type="primary" @click="getSelfPayItems(true)">病历自检</el-button>
      <el-button type="primary" @click="getSelfPayItems(true)">查看闭环</el-button>
      <el-button v-if="gdState === 1" type="primary" @click="medicalRecordArchiving">
        病历归档
      </el-button>
      <el-button v-else-if="gdState === -1" type="primary" disabled>归档完成</el-button>
      <el-button v-else-if="gdState === 2" type="primary" @click="revokeArchive">
        撤销病历归档(医生)
      </el-button>
    </div>
    <div class="content-body">
      <template v-for="(bodyList, index) in modelData">
        <div :key="index" class="body-list" :style="index === 0 ? { 'margin-left': '0' } : {}">
          <div class="list-top" @click="expand(index)">
            <el-button v-if="columnList[index].selected === false" @click="selected(true, index)">
              全选该列
            </el-button>
            <el-button v-else type="primary" @click="selected(false, index)">全选该列</el-button>
            <i
              v-if="columnList[index].expand === false"
              class="el-collapse-item__arrow el-icon-arrow-right"
            ></i>
            <i v-else class="el-collapse-item__arrow el-icon-arrow-right is-active"></i>
          </div>
          <template v-for="(item, index2) in bodyList">
            <el-collapse :key="index2" v-model="expandList" style="margin-top: 10px">
              <div class="model-item">
                <el-collapse-item slot="title" :name="index + '_' + index2">
                  <template slot="title">
                    <el-checkbox
                      v-model="item.checkAll"
                      :indeterminate="item.isIndeterminate"
                      @change="handleCheckAllChange($event, index, index2)"
                    >
                      {{ item.name }}
                    </el-checkbox>
                  </template>
                  <el-checkbox-group
                    v-model="item.checkedCities"
                    @change="handleCheckedCitiesChange($event, index, index2)"
                  >
                    <template v-for="(children, index3) in item.data">
                      <div :key="index3" class="model-item-child">
                        <el-checkbox
                          v-if="children.jilluSJ"
                          :key="children.id"
                          :label="children.id"
                        >
                          {{ children.jilluSJ.slice(0, -3) + ' ' + children.wenShuMC }}
                        </el-checkbox>
                        <el-checkbox v-else :key="children.id" :label="children.id">
                          {{ children.wenShuMC }}
                        </el-checkbox>
                      </div>
                    </template>
                  </el-checkbox-group>
                </el-collapse-item>
              </div>
            </el-collapse>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import {
  canGuiDang,
  getDoctorInfoEhr,
  getJiZhongDYInit,
  GetPrintContent,
  huShiGD,
  yiShiCH,
  yiShiGD
} from '@/api/progress-note'
import { getYongHuID } from '@/utils/auth'
import { mapState } from 'vuex'
import axios from 'axios'

export default {
  name: 'MedicalRecordsPrintArchive',
  data() {
    return {
      modelData: [],
      columnList: [],
      expandList: [],

      gdState: '',

      yongHuID: '',
      renYuanLB: '',
      renYuanType: ''
      // checkAll: false,
      // checkedCities: ['上海', '北京'],
      // cities: ['上海', '北京'],
      // isIndeterminate: true,
    }
  },
  computed: {
    ...mapState({
      patientInit: ({ patient }) => patient.patientInit
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      //获取用户ID
      this.yongHuID = getYongHuID()
      //获取人员类别
      await this.getRenYuanLB()
      //获取归档状态
      await this.getGdState()
      //获取页面初始信息，整理数据
      await this.getModelData()
    },
    message(data) {
      if (data.hasError === -1) {
        this.$message.error(data.errorMessage)
      } else {
        this.$message({
          message: data.errorMessage,
          type: 'success'
        })
      }
    },
    async getGdState() {
      const res = await canGuiDang({
        bingLiID: this.bingLiID,
        renYuanLB: this.renYuanLB
      })
      if (res.data === 1) {
        this.gdState = 1
      } else if (res.data === 2) {
        this.gdState = 2
      } else if (res.data === 7 && this.renYuanType === 0) {
        this.gdState = 2
      } else {
        this.gdState = -1
      }
    },
    async medicalRecordArchiving() {
      let res = ''
      const data = {
        bingLiID: this.bingLiID,
        yongHuID: this.yongHuID
      }
      if (this.renYuanType === 0) {
        res = await yiShiGD(data)
        console.log('医生归档')
      } else if (this.renYuanType === 1) {
        res = await huShiGD(data)
        console.log('护士归档')
      }
      console.log('res:', res)
      this.message(res)
      await this.getGdState()
    },
    async revokeArchive() {
      console.log('撤销归档')
      const res = await yiShiCH({
        bingLiID: this.bingLiID,
        yongHuID: this.yongHuID
      })
      console.log('res:', res)
    },
    async getRenYuanLB() {
      const YongHuData = await getDoctorInfoEhr({
        yongHuID: this.yongHuID
      })
      this.renYuanLB = YongHuData.data.renYuanLB
      const ysList = ['01', '02', '03', '05', '06']
      const hsList = ['04', '07', '09', '99']
      if (ysList.includes(this.renYuanLB)) {
        this.renYuanType = 0
      } else {
        if (hsList.includes(this.renYuanLB)) {
          this.renYuanType = 1
        }
      }
    },
    async getModelData() {
      const res = await getJiZhongDYInit({
        bingLiID: this.bingLiID
      })
      const dvJyjc = [
        { wenShuMC: '心电图', id: 'TJBG-XDT' },
        { wenShuMC: '动态心电图/血压', id: 'TJBG-DTXDT' },
        { wenShuMC: '脑电图', id: 'TJBG-NDT' },
        { wenShuMC: '普放', id: 'TJBG-PF' },
        { wenShuMC: 'CT', id: 'TJBG-CT' },
        { wenShuMC: 'PET', id: 'TJBG-PET' },
        { wenShuMC: 'MR', id: 'TJBG-MR' },
        { wenShuMC: 'B超', id: 'TJBG-BC' },
        { wenShuMC: '心超', id: 'TJBG-XC' },
        { wenShuMC: '超声介入', id: 'TJBG-JRBG' },
        { wenShuMC: 'TCD', id: 'TJBG-TCD' },
        { wenShuMC: '肺功能', id: 'TJBG-FGN' },
        { wenShuMC: '骨密度', id: 'TJBG-GMD' },
        { wenShuMC: '眼科报告', id: 'TJBG-YKYD' },
        { wenShuMC: '内镜报告', id: 'TJBG-NJBG' },
        { wenShuMC: '病理报告', id: 'TJBG-BLBG' },
        { wenShuMC: '检验图文报告', id: 'TJBG-JYTW' },
        { wenShuMC: '检验', id: 'TJBG-JY' },
        { wenShuMC: '神经免疫', id: 'TJBG-SJMY' },
        { wenShuMC: '肌电图', id: 'TJBG-JDYF' }
      ]
      const dvQt = [
        { wenShuMC: '其他拍摄报告', id: 'TJBG-QTPSJL' },
        { wenShuMC: '会诊单', id: 'HZD' },
        { wenShuMC: '体温单', id: 'TWD' },
        { wenShuMC: '医嘱单', id: 'YZD' },
        { wenShuMC: '护理记录', id: 'HLJL' },
        { wenShuMC: '护理会诊', id: 'HLHZ' },
        { wenShuMC: '输血单', id: 'SXD' },
        { wenShuMC: '住院费用汇总清单', id: 'ZYFY' }
      ]
      let modelData = [
        [
          { key: 'ruYuanJL', name: '入院记录列表' },
          { key: 'bingChengJL', name: '病程记录列表' }
        ],
        [
          { key: 'huanZheSQS', name: '患者授权列表' },
          { key: 'shuXueZQTYS', name: '输血知情同意列表' },
          { key: 'teShuBingQZLJL', name: '特殊病情和治疗记录列表' },
          { key: 'teShuJCJL', name: '特殊检查记录列表' },
          { key: 'teShuZLJL', name: '特殊治疗记录列表' },
          { key: 'bingQingTHJL', name: '病情谈话列表' },
          { key: 'bingQingGZS', name: '病情告知列表' },
          { key: 'qiTaGZS', name: '其他告知列表' },
          { key: 'qiTaYiShengQS', name: '其他医师签署列表' },
          { key: 'chaoShengWS', name: '超声介入' },
          { key: 'jiZhenQJWS', name: '急诊抢救列表' }
        ],
        [
          { key: 'shouSuJL', name: '手术记录列表' },
          { key: 'qiTaJL', name: '其他记录列表' },
          { key: 'qiTaZZWS', name: '本院其他纸质文书列表' },
          { key: 'lbpfb', name: '量表/评分表' },
          { key: 'xiangGuanZKPGJLD', name: '相关专科记录评估单列表' },
          { key: 'bingLiWZXZCB', name: '病历完整性自查列表' },
          { key: 'fangLiaoJL', name: '放疗记录列表' }
        ],
        [
          { key: 'dvJyjc', name: '检查检验', data: dvJyjc },
          { key: 'dvQt', name: '其他', data: dvQt }
        ],
        [
          { key: 'huLingRuYuanPGD', name: '护理入院评估单' },
          { key: 'huLingJKJYPGD', name: '护理健康教育评价单' },
          { key: 'zhuYuanHZJKWTD', name: '住院患者健康问题表' },
          { key: 'qiTaPGJLD', name: '其他评估、记录单及交接' },
          { key: 'huShiDangShengZhiNengShiYong', name: '由护士签署的知情同意书' },
          { key: 'zhuanYunJiaoJD', name: '转运交接单' }
        ]
      ]
      console.log('modelData:', modelData)
      for (let i = 0; i < modelData.length; i++) {
        this.columnList[i] = {
          expand: false,
          selected: false
        }
        for (let j = 0; j < modelData[i].length; j++) {
          if (!modelData[i][j].data) {
            if (Array.isArray(res.data[modelData[i][j]['key']])) {
              modelData[i][j]['data'] = res.data[modelData[i][j]['key']]
            } else if (!res.data[modelData[i][j]['key']]) {
              modelData[i][j]['data'] = []
            } else {
              modelData[i][j]['data'] = res.data[modelData[i][j]['key']].wenShuLieBiao
            }
          }
          let cities = []
          if (modelData[i][j].data) {
            for (const d of modelData[i][j].data) {
              cities.push(d.id)
            }
          }
          modelData[i][j]['checkAll'] = false
          modelData[i][j]['checkedCities'] = []
          modelData[i][j]['cities'] = cities
          modelData[i][j]['isIndeterminate'] = false
        }
      }
      this.modelData = modelData
    },
    handleCheckAllChange(val, index, index2) {
      this.modelData[index][index2].checkAll = val
      this.modelData[index][index2].checkedCities = val ? this.modelData[index][index2].cities : []
      this.modelData[index][index2].isIndeterminate = false
    },
    handleCheckedCitiesChange(value, index, index2) {
      let checkedCount = value.length
      this.modelData[index][index2].checkAll =
        checkedCount === this.modelData[index][index2].cities.length
      this.modelData[index][index2].isIndeterminate =
        checkedCount > 0 && checkedCount < this.modelData[index][index2].cities.length
    },
    expand(index) {
      this.columnList[index].expand = !this.columnList[index].expand
      this.expandList = []
      for (let i = 0; i < this.columnList.length; i++) {
        if (this.columnList[i].expand) {
          for (let j = 0; j < this.modelData[i].length; j++) {
            this.expandList.push(i + '_' + j)
          }
        }
      }
    },
    selected(val, index) {
      this.columnList[index].expand = !val
      this.columnList[index].selected = val
      for (let i = 0; i < this.modelData[index].length; i++) {
        this.handleCheckAllChange(val, index, i)
      }
    },
    async print() {
      let lieBiao = ''
      for (const data of this.modelData) {
        for (const d of data) {
          for (const id of d.checkedCities) {
            lieBiao += id + ','
          }
        }
      }
      lieBiao = lieBiao.slice(0, -1)
      const res = await GetPrintContent({
        bingLiID: this.bingLiID,
        yongHuID: this.yongHuID,
        lieBiao: lieBiao
      })
      this.openUrl(res.data)
    },
    selAll(val) {
      for (let i = 0; i < this.modelData.length; i++) {
        // this.selected(val,i)
        this.columnList[i].selected = val
        for (let j = 0; j < this.modelData[i].length; j++) {
          this.handleCheckAllChange(val, i, j)
        }
      }
    },
    // Base64转Blob（适用于PDF/图片）
    dataURLtoBlob(base64String) {
      const base64Data = base64String.split(',')[1] || base64String
      const byteCharacters = atob(base64Data)
      const byteNumbers = new Array(byteCharacters.length)
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i)
      }
      return new Blob([new Uint8Array(byteNumbers)], { type: 'application/pdf' })
    },
    openUrl(base64String) {
      const blob = this.dataURLtoBlob(base64String)
      // const urlTwo=URL.createObjectURL(blob);
      // window.open(urlTwo);

      const url = URL.createObjectURL(blob)

      const iframe = document.createElement('iframe')
      iframe.style.display = 'none'
      document.body.appendChild(iframe)

      iframe.onload = () => {
        iframe.contentWindow.print() // 触发打印
        // URL.revokeObjectURL(url);      // 释放内存
        // document.body.removeChild(iframe);
      }
      iframe.src = url
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}
.content-top {
  display: flex;
  background-color: #eff3fb;
  padding: 14px;
}
.content-body {
  margin-top: 10px;
  flex: 1;
  display: flex;
  background-color: #eff3fb;
  padding: 14px;

  .body-list {
    margin-left: 10px;
    flex: 1;
    border: #dadee5 1px solid;
    padding: 10px;
    display: flex;
    flex-direction: column;

    max-height: 588px;
    overflow: auto;

    .list-top {
      cursor: pointer;
      display: flex;
      .el-icon-arrow-right {
        font-size: 16px;
        display: flex;
        align-items: center; /* 垂直居中 */
        justify-content: flex-end; /* 右侧对齐 */
      }
    }
    .model-item {
      border: 1px solid #dcdfe6;
      background-color: #eaf0f9;
      .model-item-child {
        font-size: 14px;
        padding: 7px;
        user-select: none;
        border-top: 1px solid #dcdfe6;
        border-bottom: 1px solid #dcdfe6;
        background-color: #f6f6f6;
      }
      ::v-deep .el-collapse-item__header::before {
        display: none;
      }
      ::v-deep .el-collapse-item__header {
        padding-left: 7px;
      }
      ::v-deep .el-collapse-item__content {
        padding: 0;
      }
    }
  }
}

.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 14px;
  .title {
    position: relative;
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 9px;
  }
  .title::before {
    position: absolute;
    left: -9px;
    width: 3px;
    height: 14px;
    content: '';
    background-color: #356ac5;
  }
}
.table {
  max-width: 1400px;
  flex: 1;
  overflow: auto;
  //width: 702px;
  //max-width: 800px;
  ::v-deep .el-tag {
    border-color: #356ac5;
  }
}
::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
</style>
