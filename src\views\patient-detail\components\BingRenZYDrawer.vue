<template>
  <el-dialog :visible="visible" width="450px" @close="updateVisible(false)">
    <span slot="title">
      <span class="drawer-title">
        <i class="el-icon-menu"></i>
        修改病人住院状态
      </span>
    </span>
    <span>
      <div class="drawer-component">
        <table>
          <tbody>
            <tr>
              <td class="info-label">病人当前状态:</td>
              <td class="info-value">在院</td>
            </tr>
            <tr>
              <td class="info-label">修改病人状态为:</td>
              <td class="info-value">
                <el-select style="width: 100%" :value="0" placeholder="请选择病人状态">
                  <el-option
                    v-for="item in [
                      {
                        value: 0,
                        label: '医嘱离院'
                      }
                    ]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </td>
            </tr>
            <tr>
              <td class="info-label">确认病人主管医师:</td>
              <td class="info-value">
                <el-select style="width: 100%" :value="0" placeholder="请选择主管医师">
                  <el-option
                    v-for="item in [
                      {
                        value: 0,
                        label: '黄德幸'
                      }
                    ]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </td>
            </tr>
            <tr>
              <td class="info-label">医嘱选择:</td>
              <td class="info-value">
                <span>
                  <el-checkbox v-model="checkbox1" />
                  停止长期医嘱
                </span>
                <span>
                  <el-checkbox v-model="checkbox2" />
                  下达出院医嘱
                </span>
              </td>
            </tr>
          </tbody>
        </table>
        <span class="hint-info">
          <i class="el-icon-warning" />
          <span>
            温馨提示：
            <br />
            1.【停止长期医嘱】不选择，则不停止长期医嘱;
            <br />
            2.【下达出院医嘱】勾选，则根据出院情况下达【今日出统】或者【宣布死亡】，不勾选，则不下达任何医
            。若患者以日间手术收治，但住晚时长超过24小时或整个住统时间中，未进行任何手术或操作（首页），下达医寝出院时，强制退出日间手术流程;
            <br />
            3.选择【转院】则必须填写转院单位，及转院理由;
            <br />
            4.【死亡病人】请准确的填写死亡事件，该时间作为【宣布死亡】医嘱的时间。
          </span>
        </span>
      </div>
    </span>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="updateVisible(false)">保 存</el-button>
      <el-button @click="updateVisible(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'BingRenZYDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      checkbox1: true,
      checkbox2: true
    }
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-title {
  font-size: 16px;
}

.drawer-component {
  padding: 0px 16px;
  table {
    width: 100%;
  }
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 35px;
    padding: 6px;
  }
  .info-label {
    text-align: right;
    width: 120px;
    background-color: #eaf0f9;
  }
  .info-value {
    width: 230px;
    background-color: #ffffff;
  }
  .el-checkbox {
    margin: 0 7px;
  }
  .hint-info {
    margin-top: 10px;
    display: flex;
    .el-icon-warning {
      margin: 3px 3px 0 0;
      color: #356ac5;
    }
  }
}
:deep(.el-dialog__footer) {
  border-top: none;
}
</style>
