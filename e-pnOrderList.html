<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>儿科营养医嘱</title>
  <meta name="description" content="新营养医嘱">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
  #pnOrderList {
    text-align: center;
    margin: auto;
  }
  .pnOrderListMain {
    width: 95%;
    padding: 18px 22px;  
    margin-top: 10px;
  }
  /*  标题+按钮 */
  .pnOLTitleABtn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0;
  }
  .pnOLTitleABtn>span {
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
  }
  .btn_save {
    height: 32px;
    line-height: 32px;
    padding: 0 25px;
    border: 1px solid  #489CFF;
    box-sizing: border-box;
    border-radius: 4px;
    background:  #489CFF;
    color: #fff;
    /* color:  #489CFF;
    background: transparent; */
    cursor: pointer;
    transition: 0.2s;
  }
  .btn_save:hover {
    color:  #489CFF;
    background: transparent;
    /* background:  #489CFF;
    color: #fff; */
  }
  /* 表格 */
  .pnOLTable {
    background: #F5F8FD;
    border-radius: 8px;
    padding: 35px 15px;
  }
  /* 新增按钮打开————弹窗页面 */
  #tableFr_List {
    width: 100%;
    height: 600px;
  }
</style>
<body id="pnOrderList">
  <div class="pnOrderListMain">
    <!-- 标题+按钮 -->
    <div class="pnOLTitleABtn">
      <span>记录列表</span>
      <span>
        <button class="btn_save" onclick="reloadData()">刷新</button>
        <button class="btn_save" onclick="addNewNutritionOrder()">新增</button>
      </span>
    </div>
    <!-- 展示表格 -->
    <div class="pnOLTable"></div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面JS -->
  <!-- <script src='js/e-EhrSz.js'></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-pnOrderList.js'><\/script>");
  </script>
</body>
</html>