<template>
  <el-date-picker
    v-model="dateTime"
    class="custom-date-picker"
    value-format="yyyy-MM-dd HH:mm:ss"
    format="yyyy-MM-dd HH:mm"
    type="datetime"
    @change="handleChange"
  ></el-date-picker>
</template>

<script>
export default {
  name: 'DrawerTableDatePicker',
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    column: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    dateTime: {
      get() {
        return this.row[this.column.value]
      },
      set(val) {
        this.$emit('updateRow', { prop: this.column.value, updateValue: val })
      }
    }
  },
  methods: {
    handleChange(value) {
      this.$emit('updateRow', { prop: this.column.value, updateValue: value })
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-date-picker {
  width: 165px;
  ::v-deep .el-input__inner {
    padding: 0 25px;
  }
}
</style>
