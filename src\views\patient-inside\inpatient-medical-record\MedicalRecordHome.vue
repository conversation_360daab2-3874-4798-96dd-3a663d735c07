<template>
  <div class="surgical-notice-view">
    <el-container>
      <el-main>
        <div class="right">
          <!-- 头部 -->
          <div class="right-header">
            <div class="title">手术通知单</div>
            <div class="button-group">
              <el-button type="primary" @click="handleSave">保存</el-button>
              <el-button type="primary">打印</el-button>
              <el-button type="primary">提交</el-button>
              <el-button type="primary" @click="surgeriesVisible = true">常用手术</el-button>
              <el-button type="primary" @click="diagnosesVisible = true">常用诊断</el-button>
            </div>
          </div>
          <!-- 标题 -->
          <div class="title-group">
            <h2 class="title-describe">温州医科大学附属第一医院</h2>
            <div>
              <span>医疗付费情况：</span>
              <span style="display: inline-block; width: 120px; margin: 0 5px">
                <el-select v-model="patientRecord.bingRenJBXX.yiLiaoFFFS" placeholder="请选择">
                  <el-option
                    v-for="item in medicalRecord.yiLiaoZFFSDM"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </span>
              <span>病案号：</span>
              <span class="" style="display: inline-block; width: 120px; margin: 0 5px">
                <el-input
                  disabled
                  v-model="patientRecord.bingRenJBXX.empi"
                  class="select-input"
                ></el-input>
              </span>
              <span>第</span>
              <span class="" style="display: inline-block; width: 40px; margin: 0 5px">
                <el-input
                  disabled
                  v-model="patientRecord.bingRenJBXX.zhuYuanCS"
                  class="select-input"
                ></el-input>
              </span>
              <span>次</span>
            </div>
          </div>
          <!-- 主体内容 -->
          <div class="right-content">
            <el-container>
              <el-main>
                <!-- 主体内容头部 -->
                <div class="right-header" style="margin: 5px 0 16px">
                  <div class="title">病人基本信息</div>
                  <div class="button-group">
                    <el-button type="primary">同步获取</el-button>
                  </div>
                </div>
                <table class="right-table">
                  <tr>
                    <td class="right-table-title">姓名:</td>
                    <td class="right-table-content">{{ patientRecord.bingRenJBXX.bingRenXM }}</td>
                    <td class="right-table-title">性别:</td>
                    <td class="right-table-content">
                      {{ patientRecord.bingRenJBXX.bingRenXB == 1 ? '男' : '女' }}
                    </td>
                    <td class="right-table-title">出生日期:</td>
                    <td class="right-table-content">
                      <el-date-picker
                        v-model="patientRecord.bingRenJBXX.chuShengRQ"
                        placeholder="选择日期时间"
                      ></el-date-picker>
                    </td>
                    <td class="right-table-title">年龄：</td>
                    <td class="right-table-content">
                      {{ calculateAge(patientRecord.bingRenJBXX.chuShengRQ) }}岁
                    </td>
                    <td class="right-table-title">国籍:</td>
                    <td class="right-table-content">
                      <el-select v-model="patientRecord.bingRenJBXX.guoJi" placeholder="请选择">
                        <el-option
                          v-for="item in medicalRecord.guoJiDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr v-if="keshi1 == '儿科' || keshi2 == '妇科'">
                    <!-- <td class="right-table-title" colspan="2">年龄不足一周岁</td> -->
                    <td class="right-table-title">年龄:</td>
                    <td class="right-table-content">
                      {{ patientRecord.bingRenJBXX.xinShengErYL }}-{{
                        patientRecord.bingRenJBXX.xinShengErTS
                      }}/30
                    </td>
                    <td class="right-table-title">新生儿出生体重:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.xinShengErCSTZ"
                        class="select-input"
                      >
                        克
                      </el-input>
                    </td>
                    <td class="right-table-title">新生儿入院体重:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.xinShengErRYTZ"
                        class="select-input"
                      >
                        克
                      </el-input>
                    </td>
                    <td class="right-table-title">父母姓名:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.fuMuXM"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">长期居住地：</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.zhangQiJZD"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">出生地:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.chuShengDe"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">籍贯:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.chuShengDe"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">民族:</td>
                    <td class="right-table-content">
                      <el-select v-model="patientRecord.bingRenJBXX.minZuDM" placeholder="请选择">
                        <el-option
                          v-for="item in medicalRecord.mingZuDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="right-table-title">职业:</td>
                    <td class="right-table-content">
                      <el-select v-model="patientRecord.bingRenJBXX.zhiYeDM" placeholder="请选择">
                        <el-option
                          v-for="item in medicalRecord.zhiYeDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="right-table-title">婚姻:</td>
                    <td class="right-table-content">
                      <el-select v-model="patientRecord.bingRenJBXX.hunYinQK" placeholder="请选择">
                        <el-option
                          v-for="item in medicalRecord.hunYinDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title zjlx" colspan="2">
                      <el-select
                        v-model="patientRecord.bingRenJBXX.zhengJianLX"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in zhengJianLXDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="right-table-content" colspan="2">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.shenFenZH"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">电话:</td>
                    <td class="right-table-content" colspan="5">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.xianZhuZhiDH"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">现住地:</td>
                    <td class="right-table-content" colspan="7">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.xianJuZhuDZ"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">邮编:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.xianZhuZhiYB"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">户口地址:</td>
                    <td class="right-table-content" colspan="7">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.huKouDZ"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">邮编:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.huKouYB"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">工作单位及地址:</td>
                    <td class="right-table-content" colspan="5">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.gongZuoDWJDZ"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">单位电话:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.danWeiDH"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">邮编:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.danWeiYB"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">联系人姓名:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.lianXiRenXM"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">关系:</td>
                    <td class="right-table-content">
                      <el-select
                        v-model="patientRecord.bingRenJBXX.lianXiRenGX"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in medicalRecord.lianXiRenGXDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="right-table-title">地址:</td>
                    <td class="right-table-content" colspan="3">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.lianXiRenDZ"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">电话:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.lianXiRenDH"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr v-if="keshi2 == '妇科'">
                    <td class="right-table-title">丈夫姓名:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.zhangFuXM"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">丈夫身份证:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.zhangFuSFZH"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr v-if="keshi2 == '妇科'">
                    <td class="right-table-title">产前检查:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenJBXX.chanQianJC" label="1">
                        无
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenJBXX.chanQianJC" label="2">
                        有
                      </el-radio>
                    </td>
                    <td class="right-table-title">末次月经:</td>
                    <td class="right-table-content">
                      <el-date-picker
                        v-model="patientRecord.bingRenJBXX.moCiYJ"
                        placeholder="选择日期时间"
                      ></el-date-picker>
                    </td>
                    <td class="right-table-title">预产期:</td>
                    <td class="right-table-content">
                      <el-date-picker
                        v-model="patientRecord.bingRenJBXX.yuChanQiSJ"
                        placeholder="选择日期时间"
                      ></el-date-picker>
                    </td>
                    <td class="right-table-title">孕次:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.huaiYunCS"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">产次:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenJBXX.chanCi"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">病历陈述者:</td>
                    <td class="right-table-content">
                      <el-select v-model="patientRecord.bingRenJBXX.bingLiCSZ" placeholder="请选择">
                        <el-option
                          v-for="item in bingLiCSZDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="right-table-title">入院途径:</td>
                    <td class="right-table-content" colspan="7">
                      <el-radio
                        v-model="patientRecord.bingRenJBXX.ruYuanTJ"
                        v-for="(item, index) in ruYuanTJDM"
                        :key="index"
                        :label="item.daiMa"
                      >
                        {{ item.mingCheng }}
                      </el-radio>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">入院时间:</td>
                    <td class="right-table-content">{{ patientRecord.bingRenJBXX.ruYuanSJ }}</td>
                    <td class="right-table-title">入院科别:</td>
                    <td class="right-table-content">{{ patientRecord.bingRenJBXX.ruYuanZKMC }}</td>
                    <td class="right-table-title">病房:</td>
                    <td class="right-table-content">{{ patientRecord.bingRenJBXX.ruYuanBQMC }}</td>
                    <td class="right-table-title">转科级别</td>
                    <td class="right-table-content">{{ patientRecord.bingRenJBXX.zhuanKeKB }}</td>
                  </tr>
                  <tr>
                    <td class="right-table-title">出院时间:</td>
                    <td class="right-table-content">{{ patientRecord.bingRenJBXX.chuYuanSJ }}</td>
                    <td class="right-table-title">出院科别:</td>
                    <td class="right-table-content">{{ patientRecord.bingRenJBXX.chuYuanZKMC }}</td>
                    <td class="right-table-title">病房:</td>
                    <td class="right-table-content">{{ patientRecord.bingRenJBXX.chuYuanBQMC }}</td>
                    <td class="right-table-title">实际住院：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenJBXX.zhuYuanTS }}天</td>
                  </tr>
                </table>
                <div class="right-header" style="margin: 5px 0 16px">
                  <div class="title">病人医疗情况</div>
                </div>
                <table class="right-table">
                  <tr>
                    <td class="right-table-title">门(急)诊诊断:</td>
                    <td class="right-table-content" colspan="2">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.menZhenZD"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">疾病编码:</td>
                    <td class="right-table-content">
                      <el-select
                        v-model="patientRecord.bingRenYLQK.menZhenZDDM"
                        filterable
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in jbbmDM"
                          :key="item.daiMa"
                          :label="item.mingChen"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="right-table-title">确诊日期:</td>
                    <td class="right-table-content">
                      <el-date-picker
                        v-model="patientRecord.bingRenYLQK.queZhenRQ"
                        placeholder="选择日期时间"
                      ></el-date-picker>
                    </td>
                  </tr>
                  <tr>
                    <td colspan="10" style="position: relative; padding: 13px 6px">
                      <span style="padding: 12px">出入院诊断详情？</span>
                      <span style="position: absolute; right: 38px; margin-top: -7px">
                        <el-button @click="handleMoveUp(row)">添加</el-button>
                        <el-button @click="handleMoveDown(row)">删除</el-button>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td colspan="10">
                      <el-table :data="tableData" border style="width: 100%">
                        <el-table-column
                          type="selection"
                          header-align="center"
                          align="center"
                          class="no-header"
                        ></el-table-column>
                        <el-table-column label="诊断类别" width="100">
                          <template slot-scope="scope">
                            <span style="margin-left: 10px">{{ scope.row.date }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="诊断名称" width="100">
                          <template slot-scope="scope">
                            <span style="margin-left: 10px">{{ scope.row.name }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="疾病编码" width="100">
                          <template slot-scope="scope">
                            <span style="margin-left: 10px">{{ scope.row.name }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="入院病情" width="400">
                          <template #default="{ row }">
                            <el-radio v-model="row.yuYueBDQP" label="0">有</el-radio>
                            <el-radio v-model="row.yuYueBDQP" label="1">临床未确认</el-radio>
                            <el-radio v-model="row.yuYueBDQP" label="2">情况不明</el-radio>
                            <el-radio v-model="row.yuYueBDQP" label="3">无</el-radio>
                          </template>
                        </el-table-column>
                        <el-table-column label="" width="200">
                          <template #default="{ row }">
                            <div class="move-buttons">
                              <el-button @click="handleMoveUp(row)">
                                <i class="el-icon-top"></i>
                              </el-button>
                              <el-button @click="handleMoveDown(row)">
                                <i class="el-icon-bottom"></i>
                              </el-button>
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                    </td>
                  </tr>
                  <tr v-if="keshi3 == '中医'">
                    <td colspan="10" style="position: relative; padding: 13px 6px">
                      <span style="padding: 12px">中医诊断详情？</span>
                      <span style="position: absolute; right: 38px; margin-top: -7px">
                        <el-button @click="handleMoveUp(row)">添加</el-button>
                        <el-button @click="handleMoveDown(row)">删除</el-button>
                      </span>
                    </td>
                  </tr>
                  <tr v-if="keshi3 == '中医'">
                    <td colspan="10">
                      <el-table :data="tableData" border style="width: 100%">
                        <el-table-column
                          type="selection"
                          header-align="center"
                          align="center"
                          class="no-header"
                        ></el-table-column>
                        <el-table-column label="诊断类别" width="100">
                          <template slot-scope="scope">
                            <span style="margin-left: 10px">{{ scope.row.date }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="诊断名称" width="100">
                          <template slot-scope="scope">
                            <span style="margin-left: 10px">{{ scope.row.name }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="疾病编码" width="100">
                          <template slot-scope="scope">
                            <span style="margin-left: 10px">{{ scope.row.name }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="入院病情" width="100">
                          <template #default="{ row }">
                            <el-radio v-model="row.yuYueBDQP" label="0">有</el-radio>
                            <el-radio v-model="row.yuYueBDQP" label="1">临床未确认</el-radio>
                            <el-radio v-model="row.yuYueBDQP" label="2">情况不明</el-radio>
                            <el-radio v-model="row.yuYueBDQP" label="3">无</el-radio>
                          </template>
                        </el-table-column>
                        <el-table-column label="" width="100">
                          <template #default="{ row }">
                            <div class="move-buttons">
                              <el-button @click="handleMoveUp(row)">
                                <i class="el-icon-top"></i>
                              </el-button>
                              <el-button @click="handleMoveDown(row)">
                                <i class="el-icon-bottom"></i>
                              </el-button>
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">损伤、中毒的外部原因:</td>
                    <td class="right-table-content" colspan="3">
                      <el-select
                        v-model="patientRecord.bingRenYLQK.sunShangZDWBYY"
                        filterable
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in sszdyyDM"
                          :key="item.daiMa"
                          :label="item.mingChen"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="right-table-title">疾病代码:</td>
                    <td class="right-table-content">
                      <el-input
                        disabled
                        v-model="patientRecord.bingRenYLQK.sunShangZDJBBM"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">病理诊断:</td>
                    <td class="right-table-content" colspan="3">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.bingLiZD"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">疾病编码:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.bingLiZDJBBM"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">病理号:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.bingLiHao"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">药物过敏:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenYLQK.yaoWuGM" label="1">无</el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQK.yaoWuGM" label="2">有</el-radio>
                    </td>
                    <td class="right-table-title">过敏药物:</td>
                    <td class="right-table-content" colspan="3">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.guoMinYW"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">死亡患者尸检:</td>
                    <td class="right-table-content" colspan="3">
                      <el-radio
                        v-model="patientRecord.bingRenYLQK.siWangHZSJ"
                        v-for="(item, index) in siWangHZSJDM"
                        :key="index"
                        :label="item.daiMa"
                      >
                        {{ item.mingCheng }}
                      </el-radio>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">血型:</td>
                    <td class="right-table-content" colspan="3">
                      <el-radio
                        v-model="patientRecord.bingRenYLQK.xieXing"
                        v-for="(item, index) in xieXingDM"
                        :key="index"
                        :label="item.daiMa"
                      >
                        {{ item.mingCheng }}
                      </el-radio>
                    </td>
                    <td class="right-table-title">RH:</td>
                    <td class="right-table-content" colspan="5">
                      <el-radio
                        v-model="patientRecord.bingRenYLQK.rh"
                        v-for="(item, index) in rhDM"
                        :key="index"
                        :label="item.daiMa"
                      >
                        {{ item.mingCheng }}
                      </el-radio>
                    </td>
                  </tr>
                  <tr>
                    <td rowspan="2" colspan="2" style="text-align: center">诊断符合情况</td>
                    <td>门诊与出院</td>
                    <td class="right-table-content">
                      <el-select
                        v-model="patientRecord.bingRenYLQK.menChuFHBZ"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in zdfhqk"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td>入院与出院</td>
                    <td class="right-table-content">
                      <el-select
                        v-model="patientRecord.bingRenYLQK.ruYuanCYFHBZ"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in zdfhqk"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td>术前与术后</td>
                    <td class="right-table-content">
                      <el-select
                        v-model="patientRecord.bingRenYLQK.shuQianSHFHBZ"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in zdfhqk"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td>临床与病理</td>
                    <td class="right-table-content">
                      <el-select
                        v-model="patientRecord.bingRenYLQK.linBingFHBZ"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in zdfhqk"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td>放射与病理</td>
                    <td class="right-table-content">
                      <el-select
                        v-model="patientRecord.bingRenYLQK.fangBingFHBZ"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in zdfhqk"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">主诊组长:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.keZhuRenMC"
                        class="select-input"
                        disabled
                      ></el-input>
                    </td>
                    <td class="right-table-title">(副)主任医师:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.zhuRenYSMC"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">主治医师:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.zhuZhiYSMC"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">住院医师：</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.zhuYuanYSMC"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">病历监管医师:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.bingLiJGYSMC"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">责任护士:</td>
                    <td class="right-table-content">
                      <el-input
                        disabled
                        v-model="patientRecord.bingRenYLQK.zeRenHSMC"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">进修医师:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.jinXiuYSMC"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">实习医师:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.shiXiYSMC"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">编码人员</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.bianMaYuanMC"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">病案质量:</td>
                    <td class="right-table-content">
                      <el-radio
                        v-model="patientRecord.bingRenYLQK.bingAnZL"
                        v-for="(item, index) in bingAnZLDM"
                        :key="index"
                        :label="item.daiMa"
                      >
                        {{ item.mingCheng }}
                      </el-radio>
                    </td>
                    <td class="right-table-title">质控医师:</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.zhiKongYSMC"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">质控护士:</td>
                    <td class="right-table-content">
                      <el-input
                        disabled
                        v-model="patientRecord.bingRenYLQK.zhiKongHSMC"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">质控日期</td>
                    <td class="right-table-content">
                      <el-date-picker
                        v-model="patientRecord.bingRenYLQK.zhiKongSJ"
                        placeholder="选择日期时间"
                      ></el-date-picker>
                    </td>
                  </tr>
                  <tr>
                    <td colspan="10" style="position: relative; padding: 13px 6px">
                      <span style="padding: 12px">
                        手术情况(手术数据由手术记录数据自动关联产生)
                      </span>
                      <span style="position: absolute; right: 38px; margin-top: -7px">
                        <el-button @click="handleMoveUp(row)">添加</el-button>
                        <el-button @click="handleMoveDown(row)">删除</el-button>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td colspan="10">
                      <el-table :data="tableData" border style="width: 100%">
                        <el-table-column
                          type="selection"
                          header-align="center"
                          align="center"
                        ></el-table-column>
                        <el-table-column label="手术及操作编码" width="130">
                          <template #default="{ row }">
                            <el-input
                              v-model="patientRecord.bingRenJBXX.chuShengDe"
                              class="select-input"
                            ></el-input>
                          </template>
                        </el-table-column>
                        <el-table-column label="手术及操作日期" width="170">
                          <template #default="{ row }">
                            <el-input
                              v-model="patientRecord.bingRenJBXX.chuShengDe"
                              class="select-input"
                            ></el-input>
                          </template>
                        </el-table-column>
                        <el-table-column label="手术级别" width="120">
                          <template #default="{ row }">
                            <el-select
                              v-model="patientRecord.bingRenJBXX.chuShengDe"
                              placeholder="请选择"
                            >
                              <el-option
                                v-for="item in row.geLiZL"
                                :key="item.daiMa"
                                :label="item.mingCheng"
                                :value="item.daiMa"
                              ></el-option>
                            </el-select>
                          </template>
                        </el-table-column>
                        <el-table-column label="手术及操作名称" width="190">
                          <template #default="{ row }">
                            <el-input
                              v-model="patientRecord.bingRenJBXX.chuShengDe"
                              class="select-input"
                            ></el-input>
                          </template>
                        </el-table-column>
                        <el-table-column label="主术者" width="120">
                          <template #default="{ row }">
                            <el-input
                              v-model="patientRecord.bingRenJBXX.chuShengDe"
                              class="select-input"
                            ></el-input>
                          </template>
                        </el-table-column>
                        <el-table-column label="I助" width="120">
                          <template #default="{ row }">
                            <el-input
                              v-model="patientRecord.bingRenJBXX.chuShengDe"
                              class="select-input"
                            ></el-input>
                          </template>
                        </el-table-column>
                        <el-table-column label="II助" width="120">
                          <template #default="{ row }">
                            <el-input
                              v-model="patientRecord.bingRenJBXX.chuShengDe"
                              class="select-input"
                            ></el-input>
                          </template>
                        </el-table-column>
                        <el-table-column label="切口" width="120">
                          <template #default="{ row }">
                            <el-select
                              v-model="patientRecord.bingRenJBXX.chuShengDe"
                              placeholder="请选择"
                            >
                              <el-option
                                v-for="item in row.geLiZL"
                                :key="item.daiMa"
                                :label="item.mingCheng"
                                :value="item.daiMa"
                              ></el-option>
                            </el-select>
                          </template>
                        </el-table-column>
                        <el-table-column label="愈合" width="120">
                          <template #default="{ row }">
                            <el-select
                              v-model="patientRecord.bingRenJBXX.chuShengDe"
                              placeholder="请选择"
                            >
                              <el-option
                                v-for="item in row.geLiZL"
                                :key="item.daiMa"
                                :label="item.mingCheng"
                                :value="item.daiMa"
                              ></el-option>
                            </el-select>
                          </template>
                        </el-table-column>
                        <el-table-column label="麻醉方式" width="120">
                          <template #default="{ row }">
                            <el-input v-model="initTZD.zhuDaoYSXM" class="select-input"></el-input>
                          </template>
                        </el-table-column>
                        <el-table-column label="麻醉医师" width="120">
                          <template #default="{ row }">
                            <el-input v-model="initTZD.zhuDaoYSXM" class="select-input"></el-input>
                          </template>
                        </el-table-column>
                        <el-table-column label="">
                          <template #default="{ row }">
                            <div class="move-buttons">
                              <el-button @click="handleMoveUp(row)">
                                <i class="el-icon-top"></i>
                              </el-button>
                              <el-button @click="handleMoveDown(row)">
                                <i class="el-icon-bottom"></i>
                              </el-button>
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">主诊断转归情况:</td>
                    <td class="right-table-content">
                      <el-select
                        v-model="patientRecord.bingRenYLQKZJ.zhuZhenDuanZGQK"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in zzdzgqk"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="right-table-title">并发症情况:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.bingFaZhengQK" label="1">
                        无
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.bingFaZhengQK" label="2">
                        有
                      </el-radio>
                    </td>
                    <td class="right-table-title">手术患者类型:</td>
                    <td class="right-table-content" colspan="3">
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.shouShuHZLX" label="0">
                        非手术患者
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.shouShuHZLX" label="1">
                        急诊患者
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.shouShuHZLX" label="2">
                        择期手术
                      </el-radio>
                    </td>
                    <td class="right-table-title">非计划再次手术:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.feiJiHuaZCSS" label="1">
                        无
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.feiJiHuaZCSS" label="2">
                        有
                      </el-radio>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">医院感染:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.yiYuanGR" label="1">
                        无
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.yiYuanGR" label="2">
                        有
                      </el-radio>
                    </td>
                    <td class="right-table-title">单病种管理:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.danBingZhongGL" label="1">
                        否
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.danBingZhongGL" label="2">
                        是
                      </el-radio>
                    </td>
                    <td class="right-table-title">临床路径:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.shiFouJRLCLJ" label="1">
                        否
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.shiFouJRLCLJ" label="2">
                        是
                      </el-radio>
                    </td>
                    <td class="right-table-title">是否完成临床路径:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.shiFouWCLCLJ" label="1">
                        否
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.shiFouWCLCLJ" label="2">
                        是
                      </el-radio>
                    </td>
                    <td class="right-table-title">住院期间有无告病危:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.zhuYuanQJYWGBW" label="1">
                        无
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.zhuYuanQJYWGBW" label="2">
                        有
                      </el-radio>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">入住ICU情况:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.ruZhuICUQK" label="1">
                        无
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.ruZhuICUQK" label="2">
                        有
                      </el-radio>
                    </td>
                    <td class="right-table-title">抢救次数(次):</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQKZJ.qiangJiuCS"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">成功(次):</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQKZJ.chengGongCS"
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">呼吸机使用时间(小时):</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQKZJ.huXiJiSYSJ"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">离院方式:</td>
                    <td class="right-table-content">
                      <el-select v-model="patientRecord.bingRenYLQK.liYuanFS" placeholder="请选择">
                        <el-option
                          v-for="item in liYuanFSDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="right-table-title">拟接收医疗机构名称:</td>
                    <td class="right-table-content" colspan="7">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.zhuanYuanJSJGMC"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">是否有出院31天再住院计划:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenYLQK.shiFouYZYJH" label="1">
                        无
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQK.shiFouYZYJH" label="2">
                        有
                      </el-radio>
                    </td>
                    <td class="right-table-title">目的:</td>
                    <td class="right-table-content" colspan="7">
                      <el-input
                        v-model="patientRecord.bingRenYLQK.zaiZhuYuanMD"
                        class="select-input"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">颅脑损伤患者昏迷时间:</td>
                    <td class="right-table-content" colspan="9">
                      <span>入院前</span>
                      <span class="" style="display: inline-block; width: 50px; margin: 0 5px">
                        <el-input
                          v-model="patientRecord.bingRenYLQK.ruYuanQianHMSJ1"
                          class="select-input"
                        ></el-input>
                      </span>
                      <span>天</span>
                      <span class="" style="display: inline-block; width: 50px; margin: 0 5px">
                        <el-input
                          v-model="patientRecord.bingRenYLQK.ruYuanQianHMSJ2"
                          class="select-input"
                        ></el-input>
                      </span>
                      <span>小时</span>
                      <span class="" style="display: inline-block; width: 50px; margin: 0 5px">
                        <el-input
                          v-model="patientRecord.bingRenYLQK.ruYuanQianHMSJ3"
                          class="select-input"
                        ></el-input>
                      </span>
                      <span style="margin-right: 100px">分钟</span>
                      <span>入院后</span>
                      <span class="" style="display: inline-block; width: 50px; margin: 0 5px">
                        <el-input
                          v-model="patientRecord.bingRenYLQK.ruYuanHouHMSJ1"
                          class="select-input"
                        ></el-input>
                      </span>
                      <span>天</span>
                      <span class="" style="display: inline-block; width: 50px; margin: 0 5px">
                        <el-input
                          v-model="patientRecord.bingRenYLQK.ruYuanHouHMSJ2"
                          class="select-input"
                        ></el-input>
                      </span>
                      <span>小时</span>
                      <span class="" style="display: inline-block; width: 50px; margin: 0 5px">
                        <el-input
                          v-model="patientRecord.bingRenYLQK.ruYuanHouHMSJ3"
                          class="select-input"
                        ></el-input>
                      </span>
                      <span>分钟</span>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">发生压疮:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.shiFouFSYC" label="1">
                        否
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.shiFouFSYC" label="2">
                        是
                      </el-radio>
                    </td>
                    <td class="right-table-title">是否住院期间发生:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.shiFouZYQJFS" label="1">
                        否
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.shiFouZYQJFS" label="2">
                        是
                      </el-radio>
                    </td>
                    <td class="right-table-title">住院期间跌倒或坠床:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.zhuYuanQJFSDDHZC" label="1">
                        否
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.zhuYuanQJFSDDHZC" label="2">
                        是
                      </el-radio>
                    </td>
                    <td class="right-table-title">住院期间身体约束:</td>
                    <td class="right-table-content">
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.zhuYuanQJSTYS" label="1">
                        否
                      </el-radio>
                      <el-radio v-model="patientRecord.bingRenYLQKZJ.zhuYuanQJSTYS" label="2">
                        是
                      </el-radio>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">特级护理天数(天):</td>
                    <td class="right-table-content">
                      <el-input v-model="patientRecord.bingRenYLQKZJ.teJiHLTS" class="select-input">
                        -
                      </el-input>
                    </td>
                    <td class="right-table-title">一级护理天数(天):</td>
                    <td class="right-table-content">
                      <el-input v-model="patientRecord.bingRenYLQKZJ.yiJiHLTS" class="select-input">
                        -
                      </el-input>
                    </td>
                    <td class="right-table-title">二级护理天数(天):</td>
                    <td class="right-table-content">
                      <el-input v-model="patientRecord.bingRenYLQKZJ.erJiHLTS" class="select-input">
                        -
                      </el-input>
                    </td>
                    <td class="right-table-title">三级护理天数(天):</td>
                    <td class="right-table-content">
                      <el-input
                        v-model="patientRecord.bingRenYLQKZJ.sanJiHLTS"
                        class="select-input"
                      >
                        -
                      </el-input>
                    </td>
                  </tr>
                </table>
                <div class="right-header" style="margin: 5px 0 16px">
                  <div class="title">
                    病人费用情况(具体费用清单请见医院信息系统提供的住院费用清单。)
                  </div>
                </div>
                <table class="right-table">
                  <tr>
                    <td class="right-table-title" colspan="2" style="text-align: center">
                      住院费用:
                    </td>
                    <td class="right-table-title">总费用：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.zongFeiYong }}</td>
                    <td class="right-table-title">(其中自付金额)：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.ziFeiJE }}</td>
                  </tr>
                  <tr v-if="costShow">
                    <td class="right-table-title" colspan="2" style="text-align: center">
                      1.综合医疗服务类
                    </td>
                    <td class="right-table-title">(1)一般医疗服务费：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.yiBanYLFWF }}</td>
                    <td class="right-table-title">(2)一般治疗操作费：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.yiBanZLCZF }}</td>
                    <td class="right-table-title">(3)护理费：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.huLiFei }}</td>
                    <td class="right-table-title">(4)其他费用：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.zongGeQTFY }}</td>
                  </tr>
                  <tr v-if="costShow">
                    <td class="right-table-title" colspan="2" style="text-align: center">
                      2.诊断类
                    </td>
                    <td class="right-table-title">(5)病理诊断费：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.bingLiZDF }}</td>
                    <td class="right-table-title">(6)实验室诊断费：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.shiYanSZDF }}</td>
                    <td class="right-table-title">(7)影像学诊断费：</td>
                    <td class="right-table-content">
                      {{ patientRecord.bingRenSFMX.yingXiangXZDF }}
                    </td>
                    <td class="right-table-title">(8)临床诊断项目费：</td>
                    <td class="right-table-content">
                      {{ patientRecord.bingRenSFMX.linChuangZDXMF }}
                    </td>
                  </tr>
                  <tr v-if="costShow">
                    <td
                      class="right-table-title"
                      colspan="2"
                      style="text-align: center"
                      rowspan="2"
                    >
                      3.治疗类
                    </td>
                    <td class="right-table-title">(9)非手术治疗项目费：</td>
                    <td class="right-table-content">
                      {{ patientRecord.bingRenSFMX.feiShouSZLXMF }}
                    </td>
                    <td class="right-table-title">(临床物理治疗费)：</td>
                    <td class="right-table-content">
                      {{ patientRecord.bingRenSFMX.linChuangWLZLF }}
                    </td>
                  </tr>
                  <tr v-if="costShow">
                    <td class="right-table-title">(10)手术治疗费：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.shouShuZLF }}</td>
                    <td class="right-table-title">(麻醉费)：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.maZuiFei }}</td>
                    <td class="right-table-title">(手术费)：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.shouShuFei }}</td>
                  </tr>
                  <tr v-if="costShow">
                    <td class="right-table-title" colspan="2" style="text-align: center">
                      4.康复类
                    </td>
                    <td class="right-table-title">(11)康复费：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.kangFuFei }}</td>
                  </tr>
                  <tr v-if="costShow">
                    <td class="right-table-title" colspan="2" style="text-align: center">
                      5.中医类
                    </td>
                    <td class="right-table-title">(12)中医治疗费：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.zhongYiZLF }}</td>
                  </tr>
                  <tr v-if="costShow">
                    <td class="right-table-title" colspan="2" style="text-align: center">
                      6.西药类
                    </td>
                    <td class="right-table-title">(13)西医费用：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.xiYaoFei }}</td>
                    <td class="right-table-title">(抗菌药物费用)</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.kangJunYWFY }}</td>
                  </tr>
                  <tr v-if="costShow">
                    <td class="right-table-title" colspan="2" style="text-align: center">
                      7.中药类
                    </td>
                    <td class="right-table-title">(14)中成药费：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.zhongYaoFei }}</td>
                    <td class="right-table-title">(15)中草药费：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.caoYaoFei }}</td>
                  </tr>

                  <tr v-if="costShow">
                    <td
                      class="right-table-title"
                      colspan="2"
                      style="text-align: center"
                      rowspan="2"
                    >
                      8.血液和血液制品类
                    </td>
                    <td class="right-table-title">(16)血费：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.shuXieFei }}</td>
                    <td class="right-table-title">(17)蛋白质类制品费：</td>
                    <td class="right-table-content">
                      {{ patientRecord.bingRenSFMX.baiDanBaiLZPF }}
                    </td>
                    <td class="right-table-title">(18)球蛋白类制品费：</td>
                    <td class="right-table-content">
                      {{ patientRecord.bingRenSFMX.qiuDanBaiLZPF }}
                    </td>
                    <td class="right-table-title">(19)凝血因子类制品费：</td>
                    <td class="right-table-content">
                      {{ patientRecord.bingRenSFMX.ningXieYZLZPF }}
                    </td>
                  </tr>
                  <tr v-if="costShow">
                    <td class="right-table-title">(20)细胞因子类制品费：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.xiBaoYZLZPF }}</td>
                  </tr>
                  <tr v-if="costShow">
                    <td class="right-table-title" colspan="2" style="text-align: center">
                      9.耗材类
                    </td>
                    <td class="right-table-title">(21)检查用一次性医用材料费：</td>
                    <td class="right-table-content">
                      {{ patientRecord.bingRenSFMX.jianChaYongYCXYYCLF }}
                    </td>
                    <td class="right-table-title">(22)治疗用一次性医用材料费：</td>
                    <td class="right-table-content">
                      {{ patientRecord.bingRenSFMX.zhiLiaoYongYCXYYCLF }}
                    </td>
                    <td class="right-table-title">(23)手术用一次性医用材料费：</td>
                    <td class="right-table-content">
                      {{ patientRecord.bingRenSFMX.shouShuYongYCXYYCLF }}
                    </td>
                  </tr>
                  <tr v-if="costShow">
                    <td class="right-table-title" colspan="2" style="text-align: center">
                      10.其他类
                    </td>
                    <td class="right-table-title">(24)其他费：</td>
                    <td class="right-table-content">{{ patientRecord.bingRenSFMX.qiTaFei }}</td>
                  </tr>
                </table>
              </el-main>
            </el-container>
          </div>
        </div>
      </el-main>
    </el-container>

    <!-- 常见手术列表弹框 -->
    <div class="surgeries-class">
      <el-dialog
        :visible.sync="surgeriesVisible"
        title="常见手术列表"
        width="620px"
        pop-type="tip"
        :close-on-click-modal="false"
      >
        <el-table :data="currentPageSurgeries" border stripe size="mini" max-height="480px">
          <el-table-column prop="mingCheng" label="手术名称" width="400"></el-table-column>
          <el-table-column prop="shuLiang" label="数量"></el-table-column>
        </el-table>
        <el-pagination
          @current-change="handleSurgeriesPageChange"
          :current-page.sync="surgeriesPagination.currentPage"
          :page-size.sync="surgeriesPagination.pageSize"
          layout="total, prev, pager, next"
          :total="SurgeriesData.length"
        ></el-pagination>
        <span slot="footer" class="dialog-footer">
          <el-button @click="surgeriesVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>

    <!-- 常用诊断列表弹框 -->
    <div class="surgeries-class">
      <el-dialog
        :visible.sync="diagnosesVisible"
        title="常用诊断列表"
        width="620px"
        pop-type="tip"
        :close-on-click-modal="false"
      >
        <el-table :data="currentPageDiagnoses" border stripe size="mini" max-height="480px">
          <el-table-column prop="mingCheng" label="诊断名称" width="400"></el-table-column>
          <el-table-column prop="shuLiang" label="数量"></el-table-column>
        </el-table>
        <el-pagination
          @current-change="handleDiagnosesPageChange"
          :current-page.sync="diagnosesPagination.currentPage"
          :page-size.sync="diagnosesPagination.pageSize"
          layout="total, prev, pager, next"
          :total="DiagnosesData.length"
        ></el-pagination>
        <span slot="footer" class="dialog-footer">
          <el-button @click="diagnosesVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import {
  InitTheFirstPageOfMedicalRecord,
  saveTheFirstPageOfMedicalRecord,
  getBingAnSYByBingLiID,
  getPatientBasic,
  getCommonSurgeriesByZhuanKe,
  getCommonDiagnosesByZhuanKe,
  getYlBasySswyByPageAndZhuangTai,
  getzhenduanbypageandzhuantai,
  getYlSsicdByPageAndZhuanTai
} from '@/api/medical-record-home'
import { format, getYear, parseISO } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      surgeriesVisible: false,
      diagnosesVisible: false,
      SurgeriesData: [],
      DiagnosesData: [],
      surgeriesPagination: {
        currentPage: 1,
        pageSize: 10
      },
      diagnosesPagination: {
        currentPage: 1,
        pageSize: 10
      },

      zhengJianLXDM: [
        { mingCheng: '身份证', daiMa: '1' },
        { mingCheng: '护照', daiMa: '2' },
        { mingCheng: '中国人民解放军军人身份证件', daiMa: '3' },
        { mingCheng: '中国人民武装警察身份证件', daiMa: '4' },
        { mingCheng: '港澳居民来往内地通行证', daiMa: '5' },
        { mingCheng: '台湾居民来往大陆通行证', daiMa: '6' },
        { mingCheng: '其他', daiMa: '99' }
      ],
      bingLiCSZDM: [
        { mingCheng: '患者', daiMa: '患者' },
        { mingCheng: '患者家属', daiMa: '患者家属' },
        { mingCheng: '患者及其家属', daiMa: '患者及其家属' },
        { mingCheng: '孕妇本人', daiMa: '孕妇本人' },
        { mingCheng: '产科医师', daiMa: '产科医师' },
        { mingCheng: '助产士', daiMa: '助产士' },
        { mingCheng: '儿科医师', daiMa: '儿科医师' },
        { mingCheng: '者家属及产科医师', daiMa: '者家属及产科医师' },
        { mingCheng: '患者家属及助产士', daiMa: '患者家属及助产士' },
        { mingCheng: '患者家属及儿科医师', daiMa: '患者家属及儿科医师' },
        { mingCheng: '其他', daiMa: '其他' }
      ],
      ruYuanTJDM: [
        { mingCheng: '急诊', daiMa: '1' },
        { mingCheng: '门诊', daiMa: '2' },
        { mingCheng: '其他医疗机构转入', daiMa: '3' },
        { mingCheng: '其他', daiMa: '4' }
      ],
      siWangHZSJDM: [
        { mingCheng: '是', daiMa: '1' },
        { mingCheng: '否', daiMa: '2' },
        { mingCheng: '其他', daiMa: null }
      ],
      jbbmDM: [],
      sszdyyDM: [],
      xieXingDM: [
        { mingCheng: 'A', daiMa: '1' },
        { mingCheng: 'B', daiMa: '2' },
        { mingCheng: 'O', daiMa: '3' },
        { mingCheng: 'AB', daiMa: '4' },
        { mingCheng: '不详', daiMa: '5' },
        { mingCheng: '未查', daiMa: '6' }
      ],
      rhDM: [
        { mingCheng: '阴', daiMa: '1' },
        { mingCheng: '阳', daiMa: '2' },
        { mingCheng: '不详', daiMa: '3' },
        { mingCheng: '未查', daiMa: '4' }
      ],
      zdfhqk: [
        { mingCheng: '未做', daiMa: '0' },
        { mingCheng: '符合', daiMa: '1' },
        { mingCheng: '不符合', daiMa: '2' },
        { mingCheng: '不肯定', daiMa: '3' }
      ],
      bingAnZLDM: [
        { mingCheng: '甲', daiMa: '1' },
        { mingCheng: '乙', daiMa: '2' },
        { mingCheng: '丙', daiMa: '3' }
      ],
      zzdzgqk: [
        { mingCheng: '治愈', daiMa: '1' },
        { mingCheng: '好转', daiMa: '2' },
        { mingCheng: '未愈', daiMa: '3' },
        { mingCheng: '死亡', daiMa: '4' },
        { mingCheng: '其他', daiMa: '5' }
      ],
      liYuanFSDM: [
        { mingCheng: '医嘱离院', daiMa: '1' },
        { mingCheng: '医嘱转院', daiMa: '2' },
        { mingCheng: '医嘱转社区卫生服务机构/乡镇卫生院', daiMa: '3' },
        { mingCheng: '非医嘱离院', daiMa: '4' },
        { mingCheng: '死亡', daiMa: '5' },
        { mingCheng: '其他', daiMa: '9' }
      ],
      keshi1: '',
      keshi2: '',
      keshi3: '',
      costShow: false,
      tableData: [
        {
          date: '过敏史',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1518 弄'
        },
        {
          date: '过敏史',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1517 弄'
        },
        {
          date: '过敏史',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1519 弄'
        }
      ],
      options1: [
        {
          value: '选项1',
          label: '黄金糕'
        },
        {
          value: '选项2',
          label: '双皮奶'
        },
        {
          value: '选项3',
          label: '蚵仔煎'
        },
        {
          value: '选项4',
          label: '龙须面'
        },
        {
          value: '选项5',
          label: '北京烤鸭'
        }
      ],
      value1: '',
      shuZhongHuiZhenList: [],
      saveDisabled: false,
      xinJiShuXXMList: [],
      shouShuTZD: {},
      inpatientInit: {},
      initTZD: {},
      maZuiHZ: false,
      canGuanZhe: '',
      riJianSS: false,
      xuYaoJS: false,
      teShuYQ: '',
      zhuYuanID: null, //住院ID
      visible: false, //头部右侧三个点按钮是否显示
      shouShuTZDList: [], //通知单列表
      zhuDaoYSDialog: false, //主刀医生选择窗口
      niShiShouShuDialog: false, //拟施手术窗口
      shouShuBWDialog: false, //手术部位窗口
      shouShuBWSelection: {}, //手术部位选择
      shouShuBW: '左侧',
      zhuDaoYSList: [], //主刀医生列表
      // currentPage: 1,
      // pageSize: 13, //每页条数
      shouShuXZSelectionList: [],
      niShiShouShuList: [],
      shouShuXZCurrentPage: 1,
      shouShuXZPageSize: 7,
      shouShuQueryMC: '', //手术选择窗口查询手术名称
      benZhuanKe: true, //手术选择窗口本专科
      renYuanXM: '',
      qiYaZL: false,
      zhongDaSS: false,
      patientRecord: {},
      medicalRecord: {}
    }
  },
  computed: {
    // 手术列表当前页数据
    currentPageSurgeries() {
      const start = (this.surgeriesPagination.currentPage - 1) * this.surgeriesPagination.pageSize
      const end = start + this.surgeriesPagination.pageSize
      return this.SurgeriesData.slice(start, end)
    },

    // 诊断列表当前页数据
    currentPageDiagnoses() {
      const start = (this.diagnosesPagination.currentPage - 1) * this.diagnosesPagination.pageSize
      const end = start + this.diagnosesPagination.pageSize
      return this.DiagnosesData.slice(start, end)
    },
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit
    }),
    zhuDaoYSListFilter() {
      if (this.benZhuanKe) {
        return this.zhuDaoYSList.filter((item) => item.xianZhuanKeID == this.zhuanKeID)
      }
      return this.zhuDaoYSList
    },
    currentPageZhuDaoYSList() {
      return this.zhuDaoYSListFilter.slice(
        (this.currentPage - 1) * this.pageSize,
        (this.currentPage - 1) * this.pageSize + this.pageSize
      )
    },
    shuZhongHUiZhenRowNum() {
      return function (length) {
        if (length % 3 == 0) {
          return length / 3
        } else {
          return Math.floor(length / 3) + 1
        }
      }
    },
    shuZhongHUiZhenColNum() {
      return function (rowNum, length) {
        if (length % 3 == 0) {
          return 3
        } else {
          if (rowNum < Math.floor(length / 3) + 1) {
            return 3
          } else {
            return length % 3
          }
        }
      }
    },
    shiShiShouShuMapper() {
      return function (shouShuList) {
        let shiShiShouShu = ''
        if (shouShuList != null) {
          shouShuList.map((item) => {
            shiShiShouShu += item.mingCheng
            if (item.shouShuBW != null) {
              shiShiShouShu += '(' + item.shouShuBW + ')'
            }
            shiShiShouShu += ' '
          })
        }
        return shiShiShouShu
      }
    },
    maZuiHZMapper: {
      get() {
        return this.shouShuTZD.maZuiHZ == '1' ? true : false
      },
      set(val) {
        this.shouShuTZD.maZuiHZ = val ? '1' : '0'
      }
    },
    // xiaoZuTaiXuList() {
    //   return xiaoZuTaiXuList
    // },
    bingQu() {
      return function (daiMa) {
        return this.inpatientInit.zhiXingBQs.find((item) => item.daiMa == daiMa).mingCheng
      }
    },
    formatDate() {
      return function (date, formatType) {
        if (!date) {
          return ''
        }
        return format(Date.parse(date), formatType)
      }
    },
    bingLiID() {
      return this.$route.params.id
    },
    zhuangTaiMC() {
      return function (zhuangTaiBZ) {
        switch (zhuangTaiBZ) {
          case '0':
            return '暂停'
          case '1':
            return '普通'
          case '2':
            return '锁定'
          case '3':
            return '医生已排班'
          case '4':
            return '护士已排班'
          case '5':
            return '已审核'
          case '6':
            return '删除'
          default:
            return '无'
        }
      }
    }
  },
  async mounted() {
    await this.init()
    console.log(this.patientDetail)
  },
  methods: {
    // 手术列表分页处理
    handleSurgeriesPageChange(page) {
      this.surgeriesPagination.currentPage = page
    },

    // 诊断列表分页处理
    handleDiagnosesPageChange(page) {
      this.diagnosesPagination.currentPage = page
    },
    calculateAge(birthYear) {
      const currentYear = getYear(new Date())
      return currentYear - format(new Date(birthYear), 'yyyy')
    },
    // 向上移动
    handleMoveUp(index) {
      if (index > 0) {
        const temp = this.tableConfigList[index]
        this.$set(this.tableConfigList, index, this.tableConfigList[index - 1])
        this.$set(this.tableConfigList, index - 1, temp)
      }
    },

    // 向下移动
    handleMoveDown(index) {
      if (index < this.tableConfigList.length - 1) {
        const temp = this.tableConfigList[index]
        this.$set(this.tableConfigList, index, this.tableConfigList[index + 1])
        this.$set(this.tableConfigList, index + 1, temp)
      }
    },
    // 初始化
    async init() {
      this.InitTheFirstPageOfMedicalRecord()
      this.getCommonSurgeriesByZhuanKe()
      this.getCommonDiagnosesByZhuanKe()
    },
    // 住院医生站_查询_病案首页初始化
    async InitTheFirstPageOfMedicalRecord(data) {
      try {
        const res = await InitTheFirstPageOfMedicalRecord({
          bingLiID: this.patientDetail.bingLiID,
          zhuYuanID: this.patientDetail.zhuYuanID
        })
        if (res.hasError === 0) {
          if (res.data.jianDanZD.财务明细展示 == 1) {
            this.costShow = true
          }
          if (res.data.jianDanZD.特殊专科 == 1) {
            this.keshi1 = '儿科'
            this.keshi2 = '妇科'
          } else if (res.data.jianDanZD.特殊专科 == 2) {
            this.keshi2 = '妇科'
          } else if (res.data.jianDanZD.特殊专科 == 3) {
            this.keshi3 = '中医'
          }
          // this.keshi1 = '儿科'
          // this.keshi2 = '妇科'
          // this.keshi3 = '中医'

          if (res.data.jianDanZD.提交状态 == 1) {
            this.getBingAnSYByBingLiID()
          } else {
            this.patientRecord = res.data.theFirstPageOfMedicalRecordVo
            const [qian1, qian2, qian3] = this.patientRecord.bingRenYLQK.ruYuanQianHMSJ.split('^')
            const [hou1, hou2, hou3] = this.patientRecord.bingRenYLQK.ruYuanHouHMSJ.split('^')

            this.patientRecord.bingRenYLQK = {
              ...this.patientRecord.bingRenYLQK,
              ruYuanQianHMSJ1: qian1,
              ruYuanQianHMSJ2: qian2,
              ruYuanQianHMSJ3: qian3,
              ruYuanHouHMSJ1: hou1,
              ruYuanHouHMSJ2: hou2,
              ruYuanHouHMSJ3: hou3
            }
            this.medicalRecord = res.data
            this.getYlBasySswyByPageAndZhuangTai()
            this.getzhenduanbypageandzhuantai()
            this.getYlSsicdByPageAndZhuanTai()
          }
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 住院医生站_查询_查询一个BingLiID的病案首页
    async getBingAnSYByBingLiID(data) {
      try {
        const res = await getBingAnSYByBingLiID({
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 电子病历_查询_查询一个专科id的常用手术
    async getCommonSurgeriesByZhuanKe(data) {
      try {
        const res = await getCommonSurgeriesByZhuanKe({
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.SurgeriesData = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 电子病历_查询_查询一个科室id的常用诊断
    async getCommonDiagnosesByZhuanKe(data) {
      try {
        const res = await getCommonDiagnosesByZhuanKe({
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.DiagnosesData = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 住院医生站_查询_分页查询YlBasySswy取状态
    async getYlBasySswyByPageAndZhuangTai(data) {
      try {
        const res = await getYlBasySswyByPageAndZhuangTai({
          pageIndex: 1,
          pageSize: 100000000,
          zhuangTaiBZ: 1
        })
        if (res.hasError === 0) {
          this.sszdyyDM = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 住院医生站_查询_分页查询YlBasySswy取状态
    async getzhenduanbypageandzhuantai(data) {
      try {
        const res = await getzhenduanbypageandzhuantai({
          pageIndex: 1,
          pageSize: 100000000
        })
        if (res.hasError === 0) {
          this.jbbmDM = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 住院医生站_查询_分页查询手术icd识别状态
    async getYlSsicdByPageAndZhuanTai(data) {
      try {
        const res = await getYlSsicdByPageAndZhuanTai({
          pageIndex: 1,
          pageSize: 100000000
        })
        if (res.hasError === 0) {
          this.ssmcDM = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },

    async handleSave() {
      this.$message({
        message: '保存成功',
        type: 'success'
      })
    }
  }
}
</script>
<style>
.right-header-poperover {
  padding: 8px;
}
</style>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
}
.Dialog {
  ::v-deep .el-dialog__body {
    padding: 10px 20px;
    .renYuanXMInput {
      width: 43%;
      margin-right: 10px;
      .el-input__inner {
        padding: 0 6px;
        height: 24px;
        font-size: 13px;
      }
    }
    .bottom-btn {
      margin-top: 30px;
      flex-direction: row-reverse;
      .el-button {
        margin-left: 10px;
      }
    }
  }
  .shouShuHeader {
    padding: 6px 10px;
    border: 1px solid #dcdfe6;
  }
  .shouShuTable {
    padding: 12px;
    border: 1px solid #dcdfe6;
    border-top: none;
    position: relative;
    .pagination {
      position: absolute;
      bottom: 10px;
      right: 10px;
    }
  }
  .shouShuXZList {
    height: 400px;
  }
  .yiXuanZeShouShuList {
    height: 242px;
  }
  .shouShuXZHeader {
    justify-content: space-between;
  }
  .yiXuanZeSSHeader {
    margin-top: 10px;
  }
}
.shouShuBWDialog {
  ::v-deep th {
    padding: 6px;
  }
  ::v-deep td {
    padding: 6px;
  }
  ::v-deep tr:nth-child(even) {
    background-color: #eff3fb !important;
  }
  ::v-deep tr:nth-child(odd) {
    background-color: #f6f6f6 !important;
  }
}
.text-center {
  text-align: center;
}
.title {
  font-weight: 600;
  border-left: 4px solid #356ac5;
  padding-left: 8px;
  text-align: left;
}

.popover {
  .popover-item {
    padding: 3px 0;
    cursor: pointer;
  }
  .popover-item:hover {
    background-color: #dedede;
  }
}
.surgical-notice-view {
  background-color: #fff;
  padding: 8px;
  height: 100%;
  .left-aside {
    width: 300px !important;
  }
  .right-aside {
    width: 220px !important;
  }
  .el-main {
    padding: 0 0 0 10px !important;
  }
  .left {
    background-color: #eaf0f9;
    border-radius: 4px;
    padding: 8px;
    height: 100%;
    .left-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;

      ::v-deep .el-button {
        padding: 6px 10px;
        background-color: #3b76ef;
      }
    }
  }
  .right {
    background-color: #eaf0f9;
    border-radius: 4px;
    padding: 8px;
    // height: 100%;
    .right-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // height: 5%;
      .button-group {
        display: flex;
        align-items: center;
        ::v-deep .el-popover__reference {
          background-color: #eff3fb;
          border: none;
        }
        .el-icon-more {
          transform: rotateZ(90deg);
          color: #356ac5;
          font-size: 18px;
        }
      }
    }
    .title-group {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 16px 0;
      .title-describe {
        font-size: 22px;
        font-weight: bold;
        margin-bottom: 14px;
      }
    }
    .right-content {
      .niShiShouShuList {
        height: 100px;
        border: 1px solid #000;
        background-color: rgba(59, 118, 239, 0.1);
        border-radius: 4px;
        padding: 6px;
        .el-tag {
          border: 1px solid rgba($color: #155bd4, $alpha: 0.45) !important;
          margin: 2px;
        }
      }
      font-size: 12px;
      padding: 16px 8px;
      height: 95%;
      border: 1px solid #e0e0e0;
      .el-main {
        .right-table {
          text-align: left;
          // height: 100%;
          width: 100%;
          td {
            border-collapse: collapse;
            border: 1px solid #dcdfe6;
            padding: 6px;
          }
          .right-table-title {
            text-align: right;
            background-color: #eff3fb;
            width: 8%;
            height: 100%;
            line-height: 16px;
          }
          .right-table-content {
            background-color: #ffffff;
            width: 11%;
            height: 45px;
            .el-checkbox {
              margin-right: 8px;
            }
          }
          .right-table-footer-tip {
            padding: 0 0 10px 0;
            background-color: #eaf0f9;
          }
          .select-input {
            ::v-deep .el-input__inner {
              // background-color: #e4ecfb;
              // height: 24px;
            }
          }
        }
      }
      .el-aside {
        margin: 0 0 0 10px !important;
        padding: 10px !important;
        border: 1px solid #dcdfe6;
        .right-aside-header {
          text-align: right;
          ::v-deep .el-button {
            background-color: #a66dd4;
            color: #fff;
          }
        }
        .right-aside-table {
          width: 100%;
          margin-top: 20px;
          td {
            border-collapse: collapse;
            border: 1px solid #dcdfe6;
            padding: 2px 6px;
          }
          thead {
            background-color: #eaf0f9;
          }
          tbody {
            td {
              background-color: #f6f6f6;
            }
          }
        }
      }
    }
  }
}
.no-header .cell {
  display: none;
}
::v-deep .el-input .el-input__inner {
  width: 173px;
}
::v-deep .zjlx .el-input .el-input__inner {
  text-align: right;
}

// 新增输血库弹框
.surgeries-class {
  ::v-deep .el-dialog {
    padding: 6px 10px;
  }

  ::v-deep .el-table--mini .el-table__cell {
    padding: 12px 0;
  }

  .dialog-footer {
    padding-bottom: 15px;
  }

  ::v-deep .el-dialog__header {
    // border-bottom: 1px solid #dadee6;
    padding: 10px 14px;
    font: var(--font-medium);
    font-size: var(--font-size-medium);
    color: #171c28;
    display: flex;
    align-items: center;
  }

  ::v-deep .el-dialog__header::before {
    content: url('~@/assets/images/info.png');
    // width: 3px;
    // height: 16px;
    // background: #356ac5;
    // margin-right: 6px;
    position: absolute;
    top: 5px;
    left: 6px;
    transform: scale(0.55);
  }

  ::v-deep .el-dialog__title {
    font-weight: bold;
    margin-left: 18px;
    opacity: 0.8;
  }

  ::v-deep .el-pager li {
    margin: 0 4px;
  }
}
</style>
