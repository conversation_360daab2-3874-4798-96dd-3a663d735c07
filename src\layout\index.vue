<template>
  <layout-less v-if="_layoutless" />
  <el-container v-else :key="componentKey">
    <el-header height="52">
      <navbar />
    </el-header>
    <div style="height: calc(100% - 52px)">
      <tags-view />
      <app-main />
    </div>
  </el-container>
</template>

<script>
import { Navbar, AppMain, LayoutLess, TagsView } from './components'
import ResizeMixin from '@/layout/mixin/ResizeHandler'
import { mapGetters } from 'vuex'
import store from '@/store'
import { EventBus } from '@/utils/event-bus'

export default {
  name: 'Layout',
  components: {
    TagsView,
    LayoutLess,
    AppMain,
    Navbar
  },
  mixins: [ResizeMixin],
  data() {
    return {
      componentKey: 0
    }
  },
  computed: {
    ...mapGetters(['permission_routes', 'sidebar', 'sidebarRouters']),
    sidebar() {
      return store.state.app.sidebar
    },
    device() {
      return store.state.app.device
    },
    fixedHeader() {
      return store.state.settings.fixedHeader
    },
    classObj() {
      return {
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    },
    _layoutless() {
      return this.$route.query.layoutless
    },
    opened: {
      get() {
        return this.sidebar.opened
      },
      set(val) {
        if (!val) this.toggleSideBar()
      }
    }
  },
  async mounted() {
    await store.dispatch('footerInfo/getFooterInfo')
    this.checkDisableContextMenu()
    EventBus.$on('updateViews', () => this.componentKey++)
  },
  beforeDestroy() {
    EventBus.$off('updateViews')
  },
  methods: {
    handleClickOutside() {
      store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    toggleSideBar() {
      store.dispatch('app/toggleSideBar')
    },
    checkDisableContextMenu() {
      if (store.state.settings.disableContextMenu) {
        document.oncontextmenu = function () {
          return false
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin';
@import '@/styles/variables';

.el-container {
  height: 100%;
  background: #eff3fb;

  .el-header {
    height: 52px;
    padding: 0;
  }
}
</style>
