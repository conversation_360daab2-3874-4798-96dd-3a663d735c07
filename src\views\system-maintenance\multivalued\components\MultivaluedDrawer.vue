<template>
  <el-dialog :visible="visible" width="700px" @close="updateVisible(false)">
    <span slot="title">
      <span class="drawer-title">
        <i class="el-icon-menu"></i>
        病案首页多值表维护-编辑
      </span>
    </span>
    <span>
      <div class="drawer-component">
        <table v-if="formData">
          <tbody>
            <tr>
              <td class="info-label">
                <span>*</span>
                类别:
              </td>
              <td class="info-value">
                <el-select v-model="formData.field2" placeholder="请选择类别">
                  <el-option
                    v-for="item in tableOptions"
                    :key="item.label"
                    :label="item.label"
                    :value="item.label"
                  ></el-option>
                </el-select>
              </td>
              <td class="info-label">
                <span>*</span>
                代码:
              </td>
              <td class="info-value">
                <el-input v-model="formData.field1" placeholder="请输入代码" />
              </td>
            </tr>
            <tr>
              <td class="info-label">
                <span>*</span>
                名称:
              </td>
              <td class="info-value" colspan="3">
                <el-input v-model="formData.field2" placeholder="请输入名称" />
              </td>
            </tr>
            <tr>
              <td class="info-label">
                <span>*</span>
                排序:
              </td>
              <td class="info-value">
                <el-input v-model="formData.field3" placeholder="请输入排序" />
              </td>
              <td class="info-label">
                <span>*</span>
                状态:
              </td>
              <td class="info-value">
                <el-radio v-model="formData.field4" :label="true">启用</el-radio>
                <el-radio v-model="formData.field4" :label="false">停用</el-radio>
              </td>
            </tr>
            <tr>
              <td class="info-label" style="vertical-align: top; padding-top: 10px">备注:</td>
              <td class="info-value" colspan="3">
                <el-input
                  v-model="notes"
                  type="textarea"
                  maxlength="1000"
                  clearable
                  show-word-limit
                  :autosize="{ minRows: 6 }"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </span>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="updateVisible(false)">保 存</el-button>
      <el-button @click="updateVisible(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { deepClone } from '@/utils'

export default {
  name: 'MultivaluedDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    multivaluedData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      notes: '',
      formData: null,
      tableOptions: [
        {
          value: 0,
          label: '病案首页目录'
        },
        {
          value: 1,
          label: '测试选项'
        }
      ]
    }
  },
  watch: {
    multivaluedData(newValue) {
      if (newValue) {
        this.formData = deepClone(this.multivaluedData)
      }
    }
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-title {
  font-size: 16px;
}

.drawer-component {
  padding: 0px 16px;
  table {
    width: 100%;
  }
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 35px;
    padding: 5px;
  }
  .info-label {
    text-align: right;
    width: 110px;
    background-color: #eaf0f9;
    span {
      color: #f35656;
      position: relative;
      top: 3px;
      right: 3px;
    }
  }
  .info-value {
    width: 230px;
    background-color: #ffffff;
    a {
      text-decoration: underline;
      color: #356ac5;
    }
  }
}
:deep(.el-dialog__footer) {
  border-top: none;
}

:deep(.el-radio) input[aria-hidden='true'] {
  display: none !important;
}
</style>
