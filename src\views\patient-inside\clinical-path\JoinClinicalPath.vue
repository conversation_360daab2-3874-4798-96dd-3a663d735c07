<template>
  <div class="join-clinical-path">
    <div class="join-clinical-path-header">
      <el-steps :active="activeIndex" align-center>
        <el-step title="临床路径选择"></el-step>
        <el-step title="完成路径"></el-step>
      </el-steps>
    </div>
    <div class="join-clinical-path-container">
      <el-row class="join-clinical-path-list" :gutter="20">
        <el-col :span="12" :offset="6">
          <div class="grid-content bg-purple">
            <div class="title-log">{{ activeName }}</div>
            <el-table
              v-if="activeIndex == 0"
              :data="luJingXuanZeList"
              width="350px"
              highlight-current-row
              border
              stripe
              size="mini"
            >
              <el-table-column prop="luJingMC" label="名称">
                <template slot-scope="scope">
                  <el-radio v-model="fenZhiLJID" :label="scope.row.luJingID">
                    {{ scope.row.luJingMC }}
                  </el-radio>
                </template>
              </el-table-column>
            </el-table>
            <el-table
              v-else
              :data="luJingPingFengList"
              width="350px"
              highlight-current-row
              border
              stripe
              size="mini"
            >
              <el-table-column prop="panDingGLNR" label="名称"></el-table-column>
              <el-table-column label="是否符合">
                <template slot-scope="scope">
                  <el-radio v-model="scope.row.pingGuJG" label="1">是</el-radio>
                  <el-radio v-model="scope.row.pingGuJG" label="2">否</el-radio>
                </template>
              </el-table-column>
            </el-table>
            <el-button
              v-if="activeIndex == 0"
              type="primary"
              class="join-clinical-path-btn"
              @click="nextStep()"
            >
              下一步
            </el-button>
            <el-button v-else type="primary" class="join-clinical-path-btn" @click="saveLJStep()">
              确认入径
            </el-button>
            <el-button
              v-if="activeIndex != 0"
              type="primary"
              class="join-clinical-path-btn"
              @click="backStep()"
            >
              上一步
            </el-button>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import {
  getXuanZeLuJingDy,
  getValidRuJingPgWhList,
  saveBingRenRjPgAndRjJl
} from '@/api/clinical-path'
import { format } from 'date-fns'
import { getYongHuID } from '@/utils/auth'
export default {
  name: 'JoinClinicalPathway',
  props: {
    isLuJingDialog: {
      type: Boolean,
      default: false
    },
    fenZhiLJ: {
      default: false,
      type: String
    },
    chongXinPG: {
      default: false,
      type: String
    },
  },
  data() {
    return {
      //当前选择步骤
      activeIndex: 0,
      //当前标题名称
      activeName: '',
      //入径数据
      luJingXuanZeList: [],
      fenZhiLJID: '',
      //入径评分数据
      luJingPingFengList: []
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    yongHuID() {
      return getYongHuID()?.toString()
    }
  },
  created() {
    // 初始化数据
    this.loadInitData()
  },
  methods: {
    //获取路径信息
    async loadInitData() {
      try {
        if(!this.isLuJingDialog){
          this.fenZhiLJ = ""
          this.chongXinPG = ""
        }
        this.activeName = '临床路径选择'
        // 获取初始化数据
        const initRes = await getXuanZeLuJingDy({
          bingLiID: this.bingLiID,
          fenZhiLJ: this.fenZhiLJ||""
        })
        if (initRes.hasError === 0) {
          this.luJingXuanZeList = initRes.data
        }
      } catch (error) {
        console.error('初始化失败', error)
      } finally {
      }
    },
    //下一步
    async nextStep() {
      this.activeIndex = 1
      this.activeName = '入径评估'
      const res = await getValidRuJingPgWhList({
        luJingID: this.fenZhiLJID,
        bingLiID: this.bingLiID,
        chongXinPG:this.chongXinPG||""
      })
      if (res.hasError == 0) {
        res.data.map((item) => {
          item.pingGuJG = ''
        })
        this.luJingPingFengList = res.data
      }
    },
    //上一步
    backStep(obj) {
      this.activeIndex = 0
      this.activeName = '临床路径选择'
    },
    //保存
    async saveLJStep() {
      console.log(this.luJingPingFengList)
      let arr = []
      this.luJingPingFengList.forEach((item) => {
        arr.push({
          bingLiID: this.bingLiID,
          luJingID: this.luJingID,
          pingGuID: item.pingGuID,
          paiXu: item.paiXu,
          pingGuNR: item.panDingGLNR,
          panDingBZ: item.panDingBZ,
          guanLianBZ: '',
          guanLianZhi: item.paiXu,
          caoZuoZheID: this.yongHuID,
          caoZuoZheXM: '',
          xiuGaiSJ: format(new Date(), 'yyyy-MM-dd mm:hh:ss'),
          pingGuJG: item.pingGuJG
        })
      })
      const params = {
        shiFouZLJ: '',
        bingLiID: this.bingLiID,
        luJingID: this.fenZhiLJID,
        chongXinPG:this.chongXinPG,
        ruJIngPgSjVoList: arr
      }
      const res = await saveBingRenRjPgAndRjJl({ params })
      if(res.hasError === 0){
        this.$message.success('入径成功！')
        if(this.isLuJingDialog){
          this.$emit('confirm', "")
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.title-log {
  font-weight: 600;
  border-left: 4px solid #356ac5;
  padding-left: 8px;
  margin-bottom: 8px;
}

.join-clinical-path {
  height: 100%;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  background-color: #fff;
  position: relative;
}

.join-clinical-path-header {
  height: 80px;
  background-color: #eff3fb;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
}

.join-clinical-path-container {
  padding: 10px;
  display: flex;
  height: 100%;
  background-color: #eff3fb;
  margin-top: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.bg-purple {
  padding-top: 20px;
}

.join-clinical-path-list {
  width: 100%;
}

.join-clinical-path-btn {
  margin: 10px;
  float: right;
}
</style>
