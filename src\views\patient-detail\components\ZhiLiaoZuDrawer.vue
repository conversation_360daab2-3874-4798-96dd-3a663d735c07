<template>
  <el-dialog
    :visible="visible"
    width="350px"
    @open="selectItme = zhiLiaoZuID"
    @close="updateVisible(false)"
  >
    <span slot="title">
      <span class="drawer-title">
        <i class="el-icon-menu"></i>
        修改治疗组
      </span>
    </span>
    <span>
      <div class="drawer-component">
        <div class="drawer-table">
          <table>
            <thead>
              <tr style="text-align: left">
                <th>本专科治疗组列表</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in zhiLiaoZuList">
                <tr :key="index" :class="{ 'tr-two': index % 2 == 1 }">
                  <td>
                    <el-radio v-model="selectItme" :label="item.zhiLiaoZuID">
                      {{ item.zhiLiaoZuMC }}
                    </el-radio>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
        <span class="hint-info">
          <i class="el-icon-warning" />
          <span>
            温馨提示：
            <br />
            选择病人所在的治疗组后点击保存，转专科后强制选择。
          </span>
        </span>
      </div>
    </span>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="uploadForm()">保 存</el-button>
      <el-button @click="updateVisible(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
import { setZhiLiaoZuID } from '@/api/patient-info'

export default {
  name: 'ZhiLiaoZuDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    zhuYuanID: {
      type: Number,
      default: null
    },
    zhiLiaoZuID: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      selectItme: ''
    }
  },
  computed: {
    ...mapState({
      zhiLiaoZuList: ({ patient }) => patient.zhiLiaoZuList
    })
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    uploadForm() {
      setZhiLiaoZuID({
        zhiLiaoZuID: this.selectItme,
        zhuYuanID: this.zhuYuanID
      }).then((res) => {
        if (res.hasError === 0) {
          console.log('setZhiLiaoZuID res:', res)
          this.updateVisible(false)
          this.$emit('upload-success')
          this.$message({
            message: '保存成功',
            type: 'success',
            duration: 700
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-title {
  font-size: 16px;
}
.drawer-component {
  padding: 0px 16px;
  .drawer-table {
    max-height: 400px;
    overflow-y: auto;
    table {
      width: 100%;
      max-height: 500px;
      overflow: auto;
    }
    th,
    td {
      border: 1px solid #ddd;
      border-collapse: collapse; /* 移除表格内边框间的间隙 */
      height: 35px;
      padding: 10px;
    }
    th {
      background-color: #eaf0f9;
    }
    .tr-two {
      background-color: #eaf0f9;
    }
  }
  .hint-info {
    margin-top: 10px;
    display: flex;
    .el-icon-warning {
      margin: 3px 3px 0 0;
      color: #356ac5;
    }
  }
  :deep(.el-radio__label) {
    --color-primary: #171c28;
  }
}

:deep(.el-dialog__footer) {
  border-top: none;
}

:deep(.el-radio) input[aria-hidden='true'] {
  display: none !important;
}
</style>
