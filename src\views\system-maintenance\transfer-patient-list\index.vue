<template>
  <div class="container">
    <div class="header">
      <el-select v-model="query">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <div class="query-word">转科时间：</div>
      <el-date-picker
        v-model="zhuanKeSJ"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      ></el-date-picker>
      <div class="query-word">姓名:</div>
      <div>
        <el-input v-model="xingMing"></el-input>
      </div>
      <div class="query-word">病案号：</div>
      <div>
        <el-input v-model="bingAnHao"></el-input>
      </div>
      <div class="button">
        <el-button type="primary">查询</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">本专科转科病人列表维护</div>
        <!-- <div class="button"><el-button type="primary">新增</el-button></div> -->
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData" style="width: 1476px">
          <el-table-column prop="bingQu" width="110" label="病区"></el-table-column>
          <el-table-column prop="chuangHao" width="110" label="床号"></el-table-column>
          <el-table-column prop="xingMing" width="110" label="姓名"></el-table-column>
          <el-table-column prop="bingAnHao" width="110" label="病案号"></el-table-column>
          <el-table-column prop="xingBie" width="110" label="性别"></el-table-column>
          <el-table-column prop="chuShengSJ" width="110" label="出生日期"></el-table-column>
          <el-table-column prop="ruYuanPanDuan" width="240" label="入院诊断"></el-table-column>
          <el-table-column prop="ruYuanSJ" width="110" label="入院日期"></el-table-column>
          <el-table-column prop="zhuanKeSJ" width="160" label="转科时间"></el-table-column>
          <el-table-column prop="zhiLiaoZu" width="200" label="治疗组"></el-table-column>
          <el-table-column prop="caoZuo" width="104" label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: '选项1',
          label: '张三'
        },
        {
          value: '选项2',
          label: '李四'
        },
        {
          value: '选项3',
          label: '王五'
        },
        {
          value: '选项4',
          label: '赵六'
        }
      ],
      query: '',
      zhuanKeSJ: '',
      xingMing: '',
      bingAnHao: '',
      tableData: [
        {
          bingQu: '242',
          chuangHao: '303',
          xingMing: '叶莲华',
          bingAnHao: '0015314056',
          xingBie: '女',
          chuShengSJ: '1962-05-29',
          ruYuanPanDuan: '右肺占位性病变,带状疱疹',
          ruYuanSJ: '2022-03-11',
          zhuanKeSJ: '2022-03-15 08:14:57',
          zhiLiaoZu: ''
        },
        {
          bingQu: '271',
          chuangHao: '025',
          xingMing: '陈林娥',
          bingAnHao: '0014976948',
          xingBie: '女',
          chuShengSJ: '1948-03-20',
          ruYuanPanDuan: '右肺占位性病变',
          ruYuanSJ: '2022-03-10',
          zhuanKeSJ: '2022-03-15 20:53:09',
          zhiLiaoZu: ''
        }
      ]
    }
  },
  methods: {
    handleClick(row) {
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 700px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 1476px;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}
</style>
