<template>
  <transition mode="out-in" name="fade-transform">
    <keep-alive :include="cachedViews">
      <router-view :key="key" style="height: 100%; width: 100%" />
    </keep-alive>
  </transition>
</template>

<script>
export default {
  name: 'PatientDetailMain',
  computed: {
    // 需要缓存的页面
    cachedViews() {
      return this.$store.state.tagsView.patientCachedViews
    },
    key() {
      // 使用路由路径和病人ID组合作为key，确保不同病人的相同页面不会混淆
      return `${this.$route.path}-${this.$route.params.id}`
    }
  }
}
</script>

<style lang="scss" scoped></style>
