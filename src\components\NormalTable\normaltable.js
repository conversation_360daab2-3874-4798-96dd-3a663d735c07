// import type { ElTable } from 'element-ui/types/table'
// import type { ElTableColumn } from 'element-ui/types/table-column'
//
// interface TableConfig extends ElTable {
//   isSelection?: boolean
//   isSelectMore?: boolean
//   isRowClick?: boolean
//   isStripe?: boolean
//   isShowSummary?: boolean
//   isIndex?: boolean
//   tableHeight?: string
//   treeProps?: NonNullable<any>
// }
//
// type TableHeaderItem = ElTableColumn
//
// export interface NormalTableProps {
//   config: TableConfig
//   tableHeader: Array<TableHeaderItem>
//   data: Array<NonNullable<unknown>>
//   rowClassName?: () => object
//   summaryMethod?: () => object
//   spanMethod?: () => object
//   rowStyle?: () => object
//   cellClassName?: () => object
//   cellStyle?: () => object
//   loading?: boolean
//   showPage?: boolean
//   pageNum?: number
//   pageSize?: number
//   total?: number
//   pagerCount?: number
//   pageSizes?: Array<number>
//   paginationSmall?: boolean
//   paginationLayout?: string
//   disabledArr?: Array<string>
//   search?: () => void
// }
