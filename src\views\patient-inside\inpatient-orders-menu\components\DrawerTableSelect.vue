<template>
  <el-select v-model="select" :disabled="isDisabled" filterable style="width: 120px">
    <el-option
      v-for="item in columnOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    ></el-option>
  </el-select>
</template>

<script>
import { disable } from 'darkreader'

export default {
  name: 'DrawerTableSelect',
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    column: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      columnOptions: []
    }
  },
  computed: {
    select: {
      get() {
        return this.row[this.column.value]
      },
      set(value) {
        this.$emit('updateRow', { prop: this.column.value, updateValue: value })
      }
    },
    isDisabled() {
      return this.column.value === 'yiZhuLX' && !!this.row.mingCheng
    }
  },
  watch: {
    'column.options': {
      handler(newValue) {
        this.columnOptions = newValue
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped></style>
