!function($){var settings={},roots={},caches={},_consts={className:{BUTTON:"button",LEVEL:"level",ICO_LOADING:"ico_loading",SWITCH:"switch",NAME:"node_name"},event:{NODECREATED:"ztree_nodeCreated",CLICK:"ztree_click",EXPAND:"ztree_expand",COLLAPSE:"ztree_collapse",ASYNC_SUCCESS:"ztree_async_success",ASYNC_ERROR:"ztree_async_error",REMOVE:"ztree_remove",SELECTED:"ztree_selected",UNSELECTED:"ztree_unselected"},id:{A:"_a",ICON:"_ico",SPAN:"_span",SWITCH:"_switch",UL:"_ul"},line:{ROOT:"root",ROOTS:"roots",CENTER:"center",BOTTOM:"bottom",NOLINE:"noline",LINE:"line"},folder:{OPEN:"open",CLOSE:"close",DOCU:"docu"},node:{CURSELECTED:"curSelectedNode"}},_setting={treeId:"",treeObj:null,view:{addDiyDom:null,autoCancelSelected:!0,dblClickExpand:!0,expandSpeed:"fast",fontCss:{},nodeClasses:{},nameIsHTML:!1,selectedMulti:!0,showIcon:!0,showLine:!0,showTitle:!0,txtSelectedEnable:!1},data:{key:{isParent:"isParent",children:"children",name:"name",title:"",url:"url",icon:"icon"},render:{name:null,title:null},simpleData:{enable:!1,idKey:"id",pIdKey:"pId",rootPId:null},keep:{parent:!1,leaf:!1}},async:{enable:!1,contentType:"application/x-www-form-urlencoded",type:"post",dataType:"text",headers:{},xhrFields:{},url:"",autoParam:[],otherParam:[],dataFilter:null},callback:{beforeAsync:null,beforeClick:null,beforeDblClick:null,beforeRightClick:null,beforeMouseDown:null,beforeMouseUp:null,beforeExpand:null,beforeCollapse:null,beforeRemove:null,onAsyncError:null,onAsyncSuccess:null,onNodeCreated:null,onClick:null,onDblClick:null,onRightClick:null,onMouseDown:null,onMouseUp:null,onExpand:null,onCollapse:null,onRemove:null}},_initRoot=function(e){var t=data.getRoot(e);t||(t={},data.setRoot(e,t)),data.nodeChildren(e,t,[]),t.expandTriggerFlag=!1,t.curSelectedList=[],t.noSelection=!0,t.createdNodes=[],t.zId=0,t._ver=(new Date).getTime()},_initCache=function(e){var t=data.getCache(e);t||(t={},data.setCache(e,t)),t.nodes=[],t.doms=[]},_bindEvent=function(d){var e=d.treeObj,t=consts.event;e.bind(t.NODECREATED,function(e,t,n){tools.apply(d.callback.onNodeCreated,[e,t,n])}),e.bind(t.CLICK,function(e,t,n,o,a){tools.apply(d.callback.onClick,[t,n,o,a])}),e.bind(t.EXPAND,function(e,t,n){tools.apply(d.callback.onExpand,[e,t,n])}),e.bind(t.COLLAPSE,function(e,t,n){tools.apply(d.callback.onCollapse,[e,t,n])}),e.bind(t.ASYNC_SUCCESS,function(e,t,n,o){tools.apply(d.callback.onAsyncSuccess,[e,t,n,o])}),e.bind(t.ASYNC_ERROR,function(e,t,n,o,a,r){tools.apply(d.callback.onAsyncError,[e,t,n,o,a,r])}),e.bind(t.REMOVE,function(e,t,n){tools.apply(d.callback.onRemove,[e,t,n])}),e.bind(t.SELECTED,function(e,t,n){tools.apply(d.callback.onSelected,[t,n])}),e.bind(t.UNSELECTED,function(e,t,n){tools.apply(d.callback.onUnSelected,[t,n])})},_unbindEvent=function(e){var t=e.treeObj,n=consts.event;t.unbind(n.NODECREATED).unbind(n.CLICK).unbind(n.EXPAND).unbind(n.COLLAPSE).unbind(n.ASYNC_SUCCESS).unbind(n.ASYNC_ERROR).unbind(n.REMOVE).unbind(n.SELECTED).unbind(n.UNSELECTED)},_eventProxy=function(e){var t=e.target,n=data.getSetting(e.data.treeId),o="",a=null,r="",d="",i=null,s=null,l=null;if(tools.eqs(e.type,"mousedown")?d="mousedown":tools.eqs(e.type,"mouseup")?d="mouseup":tools.eqs(e.type,"contextmenu")?d="contextmenu":tools.eqs(e.type,"click")?tools.eqs(t.tagName,"span")&&null!==t.getAttribute("treeNode"+consts.id.SWITCH)?(o=tools.getNodeMainDom(t).id,r="switchNode"):(l=tools.getMDom(n,t,[{tagName:"a",attrName:"treeNode"+consts.id.A}]))&&(o=tools.getNodeMainDom(l).id,r="clickNode"):tools.eqs(e.type,"dblclick")&&(d="dblclick",(l=tools.getMDom(n,t,[{tagName:"a",attrName:"treeNode"+consts.id.A}]))&&(o=tools.getNodeMainDom(l).id,r="switchNode")),0<d.length&&0==o.length&&(l=tools.getMDom(n,t,[{tagName:"a",attrName:"treeNode"+consts.id.A}]))&&(o=tools.getNodeMainDom(l).id),0<o.length)switch(a=data.getNodeCache(n,o),r){case"switchNode":data.nodeIsParent(n,a)&&(tools.eqs(e.type,"click")||tools.eqs(e.type,"dblclick")&&tools.apply(n.view.dblClickExpand,[n.treeId,a],n.view.dblClickExpand))?i=handler.onSwitchNode:r="";break;case"clickNode":i=handler.onClickNode}switch(d){case"mousedown":s=handler.onZTreeMousedown;break;case"mouseup":s=handler.onZTreeMouseup;break;case"dblclick":s=handler.onZTreeDblclick;break;case"contextmenu":s=handler.onZTreeContextmenu}return{stop:!1,node:a,nodeEventType:r,nodeEventCallback:i,treeEventType:d,treeEventCallback:s}},_initNode=function(e,t,n,o,a,r,d){if(n){var i=data.getRoot(e),s=data.nodeChildren(e,n);n.level=t,n.tId=e.treeId+"_"+ ++i.zId,n.parentTId=o?o.tId:null,n.open="string"==typeof n.open?tools.eqs(n.open,"true"):!!n.open;var l=data.nodeIsParent(e,n);tools.isArray(s)?(data.nodeIsParent(e,n,!0),n.zAsync=!0):(l=data.nodeIsParent(e,n,l),n.open=!(!l||e.async.enable)&&n.open,n.zAsync=!l),n.isFirstNode=a,n.isLastNode=r,n.getParentNode=function(){return data.getNodeCache(e,n.parentTId)},n.getPreNode=function(){return data.getPreNode(e,n)},n.getNextNode=function(){return data.getNextNode(e,n)},n.getIndex=function(){return data.getNodeIndex(e,n)},n.getPath=function(){return data.getNodePath(e,n)},n.isAjaxing=!1,data.fixPIdKeyValue(e,n)}},_init={bind:[_bindEvent],unbind:[_unbindEvent],caches:[_initCache],nodes:[_initNode],proxys:[_eventProxy],roots:[_initRoot],beforeA:[],afterA:[],innerBeforeA:[],innerAfterA:[],zTreeTools:[]},data={addNodeCache:function(e,t){data.getCache(e).nodes[data.getNodeCacheId(t.tId)]=t},getNodeCacheId:function(e){return e.substring(e.lastIndexOf("_")+1)},addAfterA:function(e){_init.afterA.push(e)},addBeforeA:function(e){_init.beforeA.push(e)},addInnerAfterA:function(e){_init.innerAfterA.push(e)},addInnerBeforeA:function(e){_init.innerBeforeA.push(e)},addInitBind:function(e){_init.bind.push(e)},addInitUnBind:function(e){_init.unbind.push(e)},addInitCache:function(e){_init.caches.push(e)},addInitNode:function(e){_init.nodes.push(e)},addInitProxy:function(e,t){t?_init.proxys.splice(0,0,e):_init.proxys.push(e)},addInitRoot:function(e){_init.roots.push(e)},addNodesData:function(e,t,n,o){var a,r=data.nodeChildren(e,t);r?n>=r.length&&(n=-1):(r=data.nodeChildren(e,t,[]),n=-1),0<r.length&&0===n?(r[0].isFirstNode=!1,view.setNodeLineIcos(e,r[0])):0<r.length&&n<0&&(r[r.length-1].isLastNode=!1,view.setNodeLineIcos(e,r[r.length-1])),data.nodeIsParent(e,t,!0),n<0?data.nodeChildren(e,t,r.concat(o)):(a=[n,0].concat(o),r.splice.apply(r,a))},addSelectedNode:function(e,t){var n=data.getRoot(e);data.isSelectedNode(e,t)||n.curSelectedList.push(t)},addCreatedNode:function(e,t){(e.callback.onNodeCreated||e.view.addDiyDom)&&data.getRoot(e).createdNodes.push(t)},addZTreeTools:function(e){_init.zTreeTools.push(e)},exSetting:function(e){$.extend(!0,_setting,e)},fixPIdKeyValue:function(e,t){e.data.simpleData.enable&&(t[e.data.simpleData.pIdKey]=t.parentTId?t.getParentNode()[e.data.simpleData.idKey]:e.data.simpleData.rootPId)},getAfterA:function(e,t,n){for(var o=0,a=_init.afterA.length;o<a;o++)_init.afterA[o].apply(this,arguments)},getBeforeA:function(e,t,n){for(var o=0,a=_init.beforeA.length;o<a;o++)_init.beforeA[o].apply(this,arguments)},getInnerAfterA:function(e,t,n){for(var o=0,a=_init.innerAfterA.length;o<a;o++)_init.innerAfterA[o].apply(this,arguments)},getInnerBeforeA:function(e,t,n){for(var o=0,a=_init.innerBeforeA.length;o<a;o++)_init.innerBeforeA[o].apply(this,arguments)},getCache:function(e){return caches[e.treeId]},getNodeIndex:function(e,t){if(!t)return null;for(var n=t.parentTId?t.getParentNode():data.getRoot(e),o=data.nodeChildren(e,n),a=0,r=o.length-1;a<=r;a++)if(o[a]===t)return a;return-1},getNextNode:function(e,t){if(!t)return null;for(var n=t.parentTId?t.getParentNode():data.getRoot(e),o=data.nodeChildren(e,n),a=0,r=o.length-1;a<=r;a++)if(o[a]===t)return a==r?null:o[a+1];return null},getNodeByParam:function(e,t,n,o){if(!t||!n)return null;for(var a=0,r=t.length;a<r;a++){var d=t[a];if(d[n]==o)return t[a];var i=data.nodeChildren(e,d),s=data.getNodeByParam(e,i,n,o);if(s)return s}return null},getNodeCache:function(e,t){if(!t)return null;var n=caches[e.treeId].nodes[data.getNodeCacheId(t)];return n||null},getNodePath:function(e,t){return t?((n=t.parentTId?t.getParentNode().getPath():[])&&n.push(t),n):null;var n},getNodes:function(e){return data.nodeChildren(e,data.getRoot(e))},getNodesByParam:function(e,t,n,o){if(!t||!n)return[];for(var a=[],r=0,d=t.length;r<d;r++){var i=t[r];i[n]==o&&a.push(i);var s=data.nodeChildren(e,i);a=a.concat(data.getNodesByParam(e,s,n,o))}return a},getNodesByParamFuzzy:function(e,t,n,o){if(!t||!n)return[];var a=[];o=o.toLowerCase();for(var r=0,d=t.length;r<d;r++){var i=t[r];"string"==typeof i[n]&&-1<t[r][n].toLowerCase().indexOf(o)&&a.push(i);var s=data.nodeChildren(e,i);a=a.concat(data.getNodesByParamFuzzy(e,s,n,o))}return a},getNodesByFilter:function(e,t,n,o,a){if(!t)return o?null:[];for(var r=o?null:[],d=0,i=t.length;d<i;d++){var s=t[d];if(tools.apply(n,[s,a],!1)){if(o)return s;r.push(s)}var l=data.nodeChildren(e,s),c=data.getNodesByFilter(e,l,n,o,a);if(o&&c)return c;r=o?c:r.concat(c)}return r},getPreNode:function(e,t){if(!t)return null;for(var n=t.parentTId?t.getParentNode():data.getRoot(e),o=data.nodeChildren(e,n),a=0,r=o.length;a<r;a++)if(o[a]===t)return 0==a?null:o[a-1];return null},getRoot:function(e){return e?roots[e.treeId]:null},getRoots:function(){return roots},getSetting:function(e){return settings[e]},getSettings:function(){return settings},getZTreeTools:function(e){var t=this.getRoot(this.getSetting(e));return t?t.treeTools:null},initCache:function(e){for(var t=0,n=_init.caches.length;t<n;t++)_init.caches[t].apply(this,arguments)},initNode:function(e,t,n,o,a,r){for(var d=0,i=_init.nodes.length;d<i;d++)_init.nodes[d].apply(this,arguments)},initRoot:function(e){for(var t=0,n=_init.roots.length;t<n;t++)_init.roots[t].apply(this,arguments)},isSelectedNode:function(e,t){for(var n=data.getRoot(e),o=0,a=n.curSelectedList.length;o<a;o++)if(t===n.curSelectedList[o])return!0;return!1},nodeChildren:function(e,t,n){if(!t)return null;var o=e.data.key.children;return void 0!==n&&(t[o]=n),t[o]},nodeIsParent:function(e,t,n){if(!t)return!1;var o=e.data.key.isParent;return void 0!==n?("string"==typeof n&&(n=tools.eqs(n,"true")),n=!!n,t[o]=n):"string"==typeof t[o]?t[o]=tools.eqs(t[o],"true"):t[o]=!!t[o],t[o]},nodeName:function(e,t,n){var o=e.data.key.name;void 0!==n&&(t[o]=n);var a=""+t[o];return"function"==typeof e.data.render.name?e.data.render.name.call(this,a,t):a},nodeTitle:function(e,t){var n=""+t[""===e.data.key.title?e.data.key.name:e.data.key.title];return"function"==typeof e.data.render.title?e.data.render.title.call(this,n,t):n},removeNodeCache:function(e,t){var n=data.nodeChildren(e,t);if(n)for(var o=0,a=n.length;o<a;o++)data.removeNodeCache(e,n[o]);data.getCache(e).nodes[data.getNodeCacheId(t.tId)]=null},removeSelectedNode:function(e,t){for(var n=data.getRoot(e),o=0,a=n.curSelectedList.length;o<a;o++)t!==n.curSelectedList[o]&&data.getNodeCache(e,n.curSelectedList[o].tId)||(n.curSelectedList.splice(o,1),e.treeObj.trigger(consts.event.UNSELECTED,[e.treeId,t]),o--,a--)},setCache:function(e,t){caches[e.treeId]=t},setRoot:function(e,t){roots[e.treeId]=t},setZTreeTools:function(e,t){for(var n=0,o=_init.zTreeTools.length;n<o;n++)_init.zTreeTools[n].apply(this,arguments)},transformToArrayFormat:function(n,e){if(!e)return[];var o=[];if(tools.isArray(e))for(var t=0,a=e.length;t<a;t++){r(e[t])}else r(e);return o;function r(e){o.push(e);var t=data.nodeChildren(n,e);t&&(o=o.concat(data.transformToArrayFormat(n,t)))}},transformTozTreeFormat:function(e,t){var n,o,a=e.data.simpleData.idKey,r=e.data.simpleData.pIdKey;if(!a||""==a||!t)return[];if(tools.isArray(t)){var d=[],i={};for(n=0,o=t.length;n<o;n++)i[t[n][a]]=t[n];for(n=0,o=t.length;n<o;n++){var s=i[t[n][r]];if(s&&t[n][a]!=t[n][r]){var l=data.nodeChildren(e,s);(l=l||data.nodeChildren(e,s,[])).push(t[n])}else d.push(t[n])}return d}return[t]}},event={bindEvent:function(e){for(var t=0,n=_init.bind.length;t<n;t++)_init.bind[t].apply(this,arguments)},unbindEvent:function(e){for(var t=0,n=_init.unbind.length;t<n;t++)_init.unbind[t].apply(this,arguments)},bindTree:function(e){var t={treeId:e.treeId},n=e.treeObj;e.view.txtSelectedEnable||n.bind("selectstart",handler.onSelectStart).css({"-moz-user-select":"-moz-none"}),n.bind("click",t,event.proxy),n.bind("dblclick",t,event.proxy),n.bind("mouseover",t,event.proxy),n.bind("mouseout",t,event.proxy),n.bind("mousedown",t,event.proxy),n.bind("mouseup",t,event.proxy),n.bind("contextmenu",t,event.proxy)},unbindTree:function(e){e.treeObj.unbind("selectstart",handler.onSelectStart).unbind("click",event.proxy).unbind("dblclick",event.proxy).unbind("mouseover",event.proxy).unbind("mouseout",event.proxy).unbind("mousedown",event.proxy).unbind("mouseup",event.proxy).unbind("contextmenu",event.proxy)},doProxy:function(e){for(var t=[],n=0,o=_init.proxys.length;n<o;n++){var a=_init.proxys[n].apply(this,arguments);if(t.push(a),a.stop)break}return t},proxy:function(e){var t=data.getSetting(e.data.treeId);if(!tools.uCanDo(t,e))return!0;for(var n=event.doProxy(e),o=!0,a=0,r=n.length;a<r;a++){var d=n[a];d.nodeEventCallback&&(o=d.nodeEventCallback.apply(d,[e,d.node])&&o),d.treeEventCallback&&(o=d.treeEventCallback.apply(d,[e,d.node])&&o)}return o}},handler={onSwitchNode:function(e,t){var n=data.getSetting(e.data.treeId);if(t.open){if(0==tools.apply(n.callback.beforeCollapse,[n.treeId,t],!0))return!0;data.getRoot(n).expandTriggerFlag=!0,view.switchNode(n,t)}else{if(0==tools.apply(n.callback.beforeExpand,[n.treeId,t],!0))return!0;data.getRoot(n).expandTriggerFlag=!0,view.switchNode(n,t)}return!0},onClickNode:function(e,t){var n=data.getSetting(e.data.treeId),o=n.view.autoCancelSelected&&(e.ctrlKey||e.metaKey)&&data.isSelectedNode(n,t)?0:n.view.autoCancelSelected&&(e.ctrlKey||e.metaKey)&&n.view.selectedMulti?2:1;return 0==tools.apply(n.callback.beforeClick,[n.treeId,t,o],!0)||(0==o?view.cancelPreSelectedNode(n,t):view.selectNode(n,t,2==o),n.treeObj.trigger(consts.event.CLICK,[e,n.treeId,t,o])),!0},onZTreeMousedown:function(e,t){var n=data.getSetting(e.data.treeId);return tools.apply(n.callback.beforeMouseDown,[n.treeId,t],!0)&&tools.apply(n.callback.onMouseDown,[e,n.treeId,t]),!0},onZTreeMouseup:function(e,t){var n=data.getSetting(e.data.treeId);return tools.apply(n.callback.beforeMouseUp,[n.treeId,t],!0)&&tools.apply(n.callback.onMouseUp,[e,n.treeId,t]),!0},onZTreeDblclick:function(e,t){var n=data.getSetting(e.data.treeId);return tools.apply(n.callback.beforeDblClick,[n.treeId,t],!0)&&tools.apply(n.callback.onDblClick,[e,n.treeId,t]),!0},onZTreeContextmenu:function(e,t){var n=data.getSetting(e.data.treeId);return tools.apply(n.callback.beforeRightClick,[n.treeId,t],!0)&&tools.apply(n.callback.onRightClick,[e,n.treeId,t]),"function"!=typeof n.callback.onRightClick},onSelectStart:function(e){var t=e.originalEvent.srcElement.nodeName.toLowerCase();return"input"===t||"textarea"===t}},tools={apply:function(e,t,n){return"function"==typeof e?e.apply(zt,t||[]):n},canAsync:function(e,t){var n=data.nodeChildren(e,t),o=data.nodeIsParent(e,t);return e.async.enable&&t&&o&&!(t.zAsync||n&&0<n.length)},clone:function(e){if(null===e)return null;var t=tools.isArray(e)?[]:{};for(var n in e)t[n]=e[n]instanceof Date?new Date(e[n].getTime()):"object"==typeof e[n]?tools.clone(e[n]):e[n];return t},eqs:function(e,t){return e.toLowerCase()===t.toLowerCase()},isArray:function(e){return"[object Array]"===Object.prototype.toString.apply(e)},isElement:function(e){return"object"==typeof HTMLElement?e instanceof HTMLElement:e&&"object"==typeof e&&null!==e&&1===e.nodeType&&"string"==typeof e.nodeName},$:function(e,t,n){return t&&"string"!=typeof t&&(n=t,t=""),"string"==typeof e?$(e,n?n.treeObj.get(0).ownerDocument:null):$("#"+e.tId+t,n?n.treeObj:null)},getMDom:function(e,t,n){if(!t)return null;for(;t&&t.id!==e.treeId;){for(var o=0,a=n.length;t.tagName&&o<a;o++)if(tools.eqs(t.tagName,n[o].tagName)&&null!==t.getAttribute(n[o].attrName))return t;t=t.parentNode}return null},getNodeMainDom:function(e){return $(e).parent("li").get(0)||$(e).parentsUntil("li").parent().get(0)},isChildOrSelf:function(e,t){return 0<$(e).closest("#"+t).length},uCanDo:function(e,t){return!0}},view={addNodes:function(e,t,n,o,a){var r=data.nodeIsParent(e,t);if(!e.data.keep.leaf||!t||r)if(tools.isArray(o)||(o=[o]),e.data.simpleData.enable&&(o=data.transformTozTreeFormat(e,o)),t){var d=$$(t,consts.id.SWITCH,e),i=$$(t,consts.id.ICON,e),s=$$(t,consts.id.UL,e);t.open||(view.replaceSwitchClass(t,d,consts.folder.CLOSE),view.replaceIcoClass(t,i,consts.folder.CLOSE),t.open=!1,s.css({display:"none"})),data.addNodesData(e,t,n,o),view.createNodes(e,t.level+1,o,t,n),a||view.expandCollapseParentNode(e,t,!0)}else data.addNodesData(e,data.getRoot(e),n,o),view.createNodes(e,0,o,null,n)},appendNodes:function(e,t,n,o,a,r,d){if(!n)return[];var i,s,l=[],c=o||data.getRoot(e),u=data.nodeChildren(e,c);(!u||a>=u.length-n.length)&&(a=-1);for(var p=0,f=n.length;p<f;p++){var g=n[p];r&&(i=(0===a||u.length==n.length)&&0==p,s=a<0&&p==n.length-1,data.initNode(e,t,g,o,i,s,d),data.addNodeCache(e,g));var v=data.nodeIsParent(e,g),N=[],h=data.nodeChildren(e,g);h&&0<h.length&&(N=view.appendNodes(e,t+1,h,g,-1,r,d&&g.open)),d&&(view.makeDOMNodeMainBefore(l,e,g),view.makeDOMNodeLine(l,e,g),data.getBeforeA(e,g,l),view.makeDOMNodeNameBefore(l,e,g),data.getInnerBeforeA(e,g,l),view.makeDOMNodeIcon(l,e,g),data.getInnerAfterA(e,g,l),view.makeDOMNodeNameAfter(l,e,g),data.getAfterA(e,g,l),v&&g.open&&view.makeUlHtml(e,g,l,N.join("")),view.makeDOMNodeMainAfter(l,e,g),data.addCreatedNode(e,g))}return l},appendParentULDom:function(e,t){var n=[],o=$$(t,e);!o.get(0)&&t.parentTId&&(view.appendParentULDom(e,t.getParentNode()),o=$$(t,e));var a=$$(t,consts.id.UL,e);a.get(0)&&a.remove();var r=data.nodeChildren(e,t),d=view.appendNodes(e,t.level+1,r,t,-1,!1,!0);view.makeUlHtml(e,t,n,d.join("")),o.append(n.join(""))},asyncNode:function(setting,node,isSilent,callback){var i,l,isParent=data.nodeIsParent(setting,node);if(node&&!isParent)return tools.apply(callback),!1;if(node&&node.isAjaxing)return!1;if(0==tools.apply(setting.callback.beforeAsync,[setting.treeId,node],!0))return tools.apply(callback),!1;if(node){node.isAjaxing=!0;var icoObj=$$(node,consts.id.ICON,setting);icoObj.attr({style:"",class:consts.className.BUTTON+" "+consts.className.ICO_LOADING})}var tmpParam={},autoParam=tools.apply(setting.async.autoParam,[setting.treeId,node],setting.async.autoParam);for(i=0,l=autoParam.length;node&&i<l;i++){var pKey=autoParam[i].split("="),spKey=pKey;1<pKey.length&&(spKey=pKey[1],pKey=pKey[0]),tmpParam[spKey]=node[pKey]}var otherParam=tools.apply(setting.async.otherParam,[setting.treeId,node],setting.async.otherParam);if(tools.isArray(otherParam))for(i=0,l=otherParam.length;i<l;i+=2)tmpParam[otherParam[i]]=otherParam[i+1];else for(var p in otherParam)tmpParam[p]=otherParam[p];var _tmpV=data.getRoot(setting)._ver;return $.ajax({contentType:setting.async.contentType,cache:!1,type:setting.async.type,url:tools.apply(setting.async.url,[setting.treeId,node],setting.async.url),data:-1<setting.async.contentType.indexOf("application/json")?JSON.stringify(tmpParam):tmpParam,dataType:setting.async.dataType,headers:setting.async.headers,xhrFields:setting.async.xhrFields,success:function(msg){if(_tmpV==data.getRoot(setting)._ver){var newNodes=[];try{newNodes=msg&&0!=msg.length?"string"==typeof msg?eval("("+msg+")"):msg:[]}catch(e){newNodes=msg}node&&(node.isAjaxing=null,node.zAsync=!0),view.setNodeLineIcos(setting,node),newNodes&&""!==newNodes?(newNodes=tools.apply(setting.async.dataFilter,[setting.treeId,node,newNodes],newNodes),view.addNodes(setting,node,-1,newNodes?tools.clone(newNodes):[],!!isSilent)):view.addNodes(setting,node,-1,[],!!isSilent),setting.treeObj.trigger(consts.event.ASYNC_SUCCESS,[setting.treeId,node,msg]),tools.apply(callback)}},error:function(e,t,n){_tmpV==data.getRoot(setting)._ver&&(node&&(node.isAjaxing=null),view.setNodeLineIcos(setting,node),setting.treeObj.trigger(consts.event.ASYNC_ERROR,[setting.treeId,node,e,t,n]))}}),!0},cancelPreSelectedNode:function(e,t,n){var o,a,r=data.getRoot(e).curSelectedList;for(o=r.length-1;0<=o;o--)if(t===(a=r[o])||!t&&(!n||n!==a)){if($$(a,consts.id.A,e).removeClass(consts.node.CURSELECTED),t){data.removeSelectedNode(e,t);break}r.splice(o,1),e.treeObj.trigger(consts.event.UNSELECTED,[e.treeId,a])}},createNodeCallback:function(e){if(e.callback.onNodeCreated||e.view.addDiyDom)for(var t=data.getRoot(e);0<t.createdNodes.length;){var n=t.createdNodes.shift();tools.apply(e.view.addDiyDom,[e.treeId,n]),e.callback.onNodeCreated&&e.treeObj.trigger(consts.event.NODECREATED,[e.treeId,n])}},createNodes:function(e,t,n,o,a){if(n&&0!=n.length){var r=data.getRoot(e),d=!o||o.open||!!$$(data.nodeChildren(e,o)[0],e).get(0);r.createdNodes=[];var i,s,l=view.appendNodes(e,t,n,o,a,!0,d);if(o){var c=$$(o,consts.id.UL,e);c.get(0)&&(i=c)}else i=e.treeObj;i&&(0<=a&&(s=i.children()[a]),0<=a&&s?$(s).before(l.join("")):i.append(l.join(""))),view.createNodeCallback(e)}},destroy:function(e){e&&(data.initCache(e),data.initRoot(e),event.unbindTree(e),event.unbindEvent(e),e.treeObj.empty(),delete settings[e.treeId])},expandCollapseNode:function(e,t,n,o,a){var r,d=data.getRoot(e);if(t){var i=data.nodeChildren(e,t),s=data.nodeIsParent(e,t);if(d.expandTriggerFlag&&(r=a,a=function(){r&&r(),t.open?e.treeObj.trigger(consts.event.EXPAND,[e.treeId,t]):e.treeObj.trigger(consts.event.COLLAPSE,[e.treeId,t])},d.expandTriggerFlag=!1),!t.open&&s&&(!$$(t,consts.id.UL,e).get(0)||i&&0<i.length&&!$$(i[0],e).get(0))&&(view.appendParentULDom(e,t),view.createNodeCallback(e)),t.open!=n){var l=$$(t,consts.id.UL,e),c=$$(t,consts.id.SWITCH,e),u=$$(t,consts.id.ICON,e);s?(t.open=!t.open,t.iconOpen&&t.iconClose&&u.attr("style",view.makeNodeIcoStyle(e,t)),t.open?(view.replaceSwitchClass(t,c,consts.folder.OPEN),view.replaceIcoClass(t,u,consts.folder.OPEN),0==o||""==e.view.expandSpeed?(l.show(),tools.apply(a,[])):i&&0<i.length?l.slideDown(e.view.expandSpeed,a):(l.show(),tools.apply(a,[]))):(view.replaceSwitchClass(t,c,consts.folder.CLOSE),view.replaceIcoClass(t,u,consts.folder.CLOSE),0!=o&&""!=e.view.expandSpeed&&i&&0<i.length?l.slideUp(e.view.expandSpeed,a):(l.hide(),tools.apply(a,[])))):tools.apply(a,[])}else tools.apply(a,[])}else tools.apply(a,[])},expandCollapseParentNode:function(e,t,n,o,a){t&&(t.parentTId?(view.expandCollapseNode(e,t,n,o),t.parentTId&&view.expandCollapseParentNode(e,t.getParentNode(),n,o,a)):view.expandCollapseNode(e,t,n,o,a))},expandCollapseSonNode:function(e,t,n,o,a){var r=data.getRoot(e),d=t?data.nodeChildren(e,t):data.nodeChildren(e,r),i=!t&&o,s=data.getRoot(e).expandTriggerFlag;if(data.getRoot(e).expandTriggerFlag=!1,d)for(var l=0,c=d.length;l<c;l++)d[l]&&view.expandCollapseSonNode(e,d[l],n,i);data.getRoot(e).expandTriggerFlag=s,view.expandCollapseNode(e,t,n,o,a)},isSelectedNode:function(e,t){if(!t)return!1;var n,o=data.getRoot(e).curSelectedList;for(n=o.length-1;0<=n;n--)if(t===o[n])return!0;return!1},makeDOMNodeIcon:function(e,t,n){var o=data.nodeName(t,n),a=t.view.nameIsHTML?o:o.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;");e.push("<span id='",n.tId,consts.id.ICON,"' title='' treeNode",consts.id.ICON," class='",view.makeNodeIcoClass(t,n),"' style='",view.makeNodeIcoStyle(t,n),"'></span><span id='",n.tId,consts.id.SPAN,"' class='",consts.className.NAME,"'>",a,"</span>")},makeDOMNodeLine:function(e,t,n){e.push("<span id='",n.tId,consts.id.SWITCH,"' title='' class='",view.makeNodeLineClass(t,n),"' treeNode",consts.id.SWITCH,"></span>")},makeDOMNodeMainAfter:function(e,t,n){e.push("</li>")},makeDOMNodeMainBefore:function(e,t,n){e.push("<li id='",n.tId,"' class='",consts.className.LEVEL,n.level,"' tabindex='0' hidefocus='true' treenode>")},makeDOMNodeNameAfter:function(e,t,n){e.push("</a>")},makeDOMNodeNameBefore:function(e,t,n){var o=data.nodeTitle(t,n),a=view.makeNodeUrl(t,n),r=view.makeNodeFontCss(t,n),d=view.makeNodeClasses(t,n),i=[];for(var s in r)i.push(s,":",r[s],";");e.push("<a id='",n.tId,consts.id.A,"' class='",consts.className.LEVEL,n.level,d.add?" "+d.add.join(" "):"","' treeNode",consts.id.A,n.click?' onclick="'+n.click+'"':"",null!=a&&0<a.length?" href='"+a+"'":""," target='",view.makeNodeTarget(n),"' style='",i.join(""),"'"),tools.apply(t.view.showTitle,[t.treeId,n],t.view.showTitle)&&o&&e.push("title='",o.replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),"'"),e.push(">")},makeNodeFontCss:function(e,t){var n=tools.apply(e.view.fontCss,[e.treeId,t],e.view.fontCss);return n&&"function"!=typeof n?n:{}},makeNodeClasses:function(e,t){var n=tools.apply(e.view.nodeClasses,[e.treeId,t],e.view.nodeClasses);return n&&"function"!=typeof n?n:{add:[],remove:[]}},makeNodeIcoClass:function(e,t){var n=["ico"];if(!t.isAjaxing){var o=data.nodeIsParent(e,t);n[0]=(t.iconSkin?t.iconSkin+"_":"")+n[0],o?n.push(t.open?consts.folder.OPEN:consts.folder.CLOSE):n.push(consts.folder.DOCU)}return consts.className.BUTTON+" "+n.join("_")},makeNodeIcoStyle:function(e,t){var n=[];if(!t.isAjaxing){var o=data.nodeIsParent(e,t)&&t.iconOpen&&t.iconClose?t.open?t.iconOpen:t.iconClose:t[e.data.key.icon];o&&n.push("background:url(",o,") 0 0 no-repeat;"),0!=e.view.showIcon&&tools.apply(e.view.showIcon,[e.treeId,t],!0)||n.push("display:none;")}return n.join("")},makeNodeLineClass:function(e,t){var n=[];return e.view.showLine?0==t.level&&t.isFirstNode&&t.isLastNode?n.push(consts.line.ROOT):0==t.level&&t.isFirstNode?n.push(consts.line.ROOTS):t.isLastNode?n.push(consts.line.BOTTOM):n.push(consts.line.CENTER):n.push(consts.line.NOLINE),data.nodeIsParent(e,t)?n.push(t.open?consts.folder.OPEN:consts.folder.CLOSE):n.push(consts.folder.DOCU),view.makeNodeLineClassEx(t)+n.join("_")},makeNodeLineClassEx:function(e){return consts.className.BUTTON+" "+consts.className.LEVEL+e.level+" "+consts.className.SWITCH+" "},makeNodeTarget:function(e){return e.target||"_blank"},makeNodeUrl:function(e,t){var n=e.data.key.url;return t[n]?t[n]:null},makeUlHtml:function(e,t,n,o){n.push("<ul id='",t.tId,consts.id.UL,"' class='",consts.className.LEVEL,t.level," ",view.makeUlLineClass(e,t),"' style='display:",t.open?"block":"none","'>"),n.push(o),n.push("</ul>")},makeUlLineClass:function(e,t){return e.view.showLine&&!t.isLastNode?consts.line.LINE:""},removeChildNodes:function(e,t){if(t){var n=data.nodeChildren(e,t);if(n){for(var o=0,a=n.length;o<a;o++)data.removeNodeCache(e,n[o]);if(data.removeSelectedNode(e),delete t[e.data.key.children],e.data.keep.parent)$$(t,consts.id.UL,e).empty();else{data.nodeIsParent(e,t,!1),t.open=!1;var r=$$(t,consts.id.SWITCH,e),d=$$(t,consts.id.ICON,e);view.replaceSwitchClass(t,r,consts.folder.DOCU),view.replaceIcoClass(t,d,consts.folder.DOCU),$$(t,consts.id.UL,e).remove()}}}},scrollIntoView:function(e,t){if(t)if("undefined"!=typeof Element&&"undefined"!=typeof HTMLElement)Element.prototype.scrollIntoViewIfNeeded||(Element.prototype.scrollIntoViewIfNeeded=function(n){"use strict";function e(e,t){return{start:e,length:t,end:e+t}}function t(e,t){return!1===n||t.start<e.end&&e.start<t.end?Math.max(e.end-t.length,Math.min(t.start,e.start)):(e.start+e.end-t.length)/2}function a(n,o){return{x:n,y:o,translate:function(e,t){return a(n+e,o+t)}}}function o(e,t){for(;e;)t=t.translate(e.offsetLeft,e.offsetTop),e=e.offsetParent;return t}for(var r,d=o(this,a(0,0)),i=a(this.offsetWidth,this.offsetHeight),s=this.parentNode;s instanceof HTMLElement;)r=o(s,a(s.clientLeft,s.clientTop)),s.scrollLeft=t(e(d.x-r.x,i.x),e(s.scrollLeft,s.clientWidth)),s.scrollTop=t(e(d.y-r.y,i.y),e(s.scrollTop,s.clientHeight)),d=d.translate(-s.scrollLeft,-s.scrollTop),s=s.parentNode}),t.scrollIntoViewIfNeeded();else{var n=e.treeObj.get(0).getBoundingClientRect(),o=t.getBoundingClientRect();(o.top<n.top||o.bottom>n.bottom||o.right>n.right||o.left<n.left)&&t.scrollIntoView()}},setFirstNode:function(e,t){var n=data.nodeChildren(e,t);0<n.length&&(n[0].isFirstNode=!0)},setLastNode:function(e,t){var n=data.nodeChildren(e,t);0<n.length&&(n[n.length-1].isLastNode=!0)},removeNode:function(e,t){var n=data.getRoot(e),o=t.parentTId?t.getParentNode():n;if(t.isFirstNode=!1,t.isLastNode=!1,t.getPreNode=function(){return null},t.getNextNode=function(){return null},data.getNodeCache(e,t.tId)){$$(t,e).remove(),data.removeNodeCache(e,t),data.removeSelectedNode(e,t);for(var a=data.nodeChildren(e,o),r=0,d=a.length;r<d;r++)if(a[r].tId==t.tId){a.splice(r,1);break}view.setFirstNode(e,o),view.setLastNode(e,o);var i,s,l,c=a.length;if(e.data.keep.parent||0!=c){if(e.view.showLine&&0<c){var u=a[c-1];if(i=$$(u,consts.id.UL,e),s=$$(u,consts.id.SWITCH,e),l=$$(u,consts.id.ICON,e),o==n)if(1==a.length)view.replaceSwitchClass(u,s,consts.line.ROOT);else{var p=$$(a[0],consts.id.SWITCH,e);view.replaceSwitchClass(a[0],p,consts.line.ROOTS),view.replaceSwitchClass(u,s,consts.line.BOTTOM)}else view.replaceSwitchClass(u,s,consts.line.BOTTOM);i.removeClass(consts.line.LINE)}}else data.nodeIsParent(e,o,!1),o.open=!1,delete o[e.data.key.children],i=$$(o,consts.id.UL,e),s=$$(o,consts.id.SWITCH,e),l=$$(o,consts.id.ICON,e),view.replaceSwitchClass(o,s,consts.folder.DOCU),view.replaceIcoClass(o,l,consts.folder.DOCU),i.css("display","none")}},replaceIcoClass:function(e,t,n){if(t&&!e.isAjaxing){var o=t.attr("class");if(null!=o){var a=o.split("_");switch(n){case consts.folder.OPEN:case consts.folder.CLOSE:case consts.folder.DOCU:a[a.length-1]=n}t.attr("class",a.join("_"))}}},replaceSwitchClass:function(e,t,n){if(t){var o=t.attr("class");if(null!=o){var a=o.split("_");switch(n){case consts.line.ROOT:case consts.line.ROOTS:case consts.line.CENTER:case consts.line.BOTTOM:case consts.line.NOLINE:a[0]=view.makeNodeLineClassEx(e)+n;break;case consts.folder.OPEN:case consts.folder.CLOSE:case consts.folder.DOCU:a[1]=n}t.attr("class",a.join("_")),n!==consts.folder.DOCU?t.removeAttr("disabled"):t.attr("disabled","disabled")}}},selectNode:function(e,t,n){n||view.cancelPreSelectedNode(e,null,t),$$(t,consts.id.A,e).addClass(consts.node.CURSELECTED),data.addSelectedNode(e,t),e.treeObj.trigger(consts.event.SELECTED,[e.treeId,t])},setNodeFontCss:function(e,t){var n=$$(t,consts.id.A,e),o=view.makeNodeFontCss(e,t);o&&n.css(o)},setNodeClasses:function(e,t){var n=$$(t,consts.id.A,e),o=view.makeNodeClasses(e,t);"add"in o&&o.add.length&&n.addClass(o.add.join(" ")),"remove"in o&&o.remove.length&&n.removeClass(o.remove.join(" "))},setNodeLineIcos:function(e,t){if(t){var n=$$(t,consts.id.SWITCH,e),o=$$(t,consts.id.UL,e),a=$$(t,consts.id.ICON,e),r=view.makeUlLineClass(e,t);0==r.length?o.removeClass(consts.line.LINE):o.addClass(r),n.attr("class",view.makeNodeLineClass(e,t)),data.nodeIsParent(e,t)?n.removeAttr("disabled"):n.attr("disabled","disabled"),a.removeAttr("style"),a.attr("style",view.makeNodeIcoStyle(e,t)),a.attr("class",view.makeNodeIcoClass(e,t))}},setNodeName:function(e,t){var n=data.nodeTitle(e,t),o=$$(t,consts.id.SPAN,e);o.empty(),e.view.nameIsHTML?o.html(data.nodeName(e,t)):o.text(data.nodeName(e,t)),tools.apply(e.view.showTitle,[e.treeId,t],e.view.showTitle)&&$$(t,consts.id.A,e).attr("title",n||"")},setNodeTarget:function(e,t){$$(t,consts.id.A,e).attr("target",view.makeNodeTarget(t))},setNodeUrl:function(e,t){var n=$$(t,consts.id.A,e),o=view.makeNodeUrl(e,t);null==o||0==o.length?n.removeAttr("href"):n.attr("href",o)},switchNode:function(e,t){if(t.open||!tools.canAsync(e,t))view.expandCollapseNode(e,t,!t.open);else if(e.async.enable){if(!view.asyncNode(e,t))return void view.expandCollapseNode(e,t,!t.open)}else t&&view.expandCollapseNode(e,t,!t.open)}};$.fn.zTree={consts:_consts,_z:{tools:tools,view:view,event:event,data:data},getZTreeObj:function(e){var t=data.getZTreeTools(e);return t||null},destroy:function(e){if(e&&0<e.length)view.destroy(data.getSetting(e));else for(var t in settings)view.destroy(settings[t])},init:function(e,t,n){var s=tools.clone(_setting);$.extend(!0,s,t),s.treeId=e.attr("id"),s.treeObj=e,s.treeObj.empty(),settings[s.treeId]=s,void 0===document.body.style.maxHeight&&(s.view.expandSpeed=""),data.initRoot(s);var o=data.getRoot(s);n=n?tools.clone(tools.isArray(n)?n:[n]):[],s.data.simpleData.enable?data.nodeChildren(s,o,data.transformTozTreeFormat(s,n)):data.nodeChildren(s,o,n),data.initCache(s),event.unbindTree(s),event.bindTree(s),event.unbindEvent(s),event.bindEvent(s);var r={setting:s,addNodes:function(e,t,n,o){e=e||null;var a=data.nodeIsParent(s,e);if(e&&!a&&s.data.keep.leaf)return null;var r=parseInt(t,10);if(t=isNaN(r)?(o=!!n,n=t,-1):r,!n)return null;var d=tools.clone(tools.isArray(n)?n:[n]);function i(){view.addNodes(s,e,t,d,1==o)}return tools.canAsync(s,e)?view.asyncNode(s,e,o,i):i(),d},cancelSelectedNode:function(e){view.cancelPreSelectedNode(s,e)},destroy:function(){view.destroy(s)},expandAll:function(e){return e=!!e,view.expandCollapseSonNode(s,null,e,!0),e},expandNode:function(t,e,n,o,a){return t&&data.nodeIsParent(s,t)?(!0!==e&&!1!==e&&(e=!t.open),(a=!!a)&&e&&0==tools.apply(s.callback.beforeExpand,[s.treeId,t],!0)||a&&!e&&0==tools.apply(s.callback.beforeCollapse,[s.treeId,t],!0)?null:(e&&t.parentTId&&view.expandCollapseParentNode(s,t.getParentNode(),e,!1),e!==t.open||n?(data.getRoot(s).expandTriggerFlag=a,!tools.canAsync(s,t)&&n?view.expandCollapseSonNode(s,t,e,!0,r):(t.open=!e,view.switchNode(this.setting,t),r()),e):null)):null;function r(){var e=$$(t,s).get(0);e&&!1!==o&&view.scrollIntoView(s,e)}},getNodes:function(){return data.getNodes(s)},getNodeByParam:function(e,t,n){return e?data.getNodeByParam(s,n?data.nodeChildren(s,n):data.getNodes(s),e,t):null},getNodeByTId:function(e){return data.getNodeCache(s,e)},getNodesByParam:function(e,t,n){return e?data.getNodesByParam(s,n?data.nodeChildren(s,n):data.getNodes(s),e,t):null},getNodesByParamFuzzy:function(e,t,n){return e?data.getNodesByParamFuzzy(s,n?data.nodeChildren(s,n):data.getNodes(s),e,t):null},getNodesByFilter:function(e,t,n,o){return t=!!t,e&&"function"==typeof e?data.getNodesByFilter(s,n?data.nodeChildren(s,n):data.getNodes(s),e,t,o):t?null:[]},getNodeIndex:function(e){if(!e)return null;for(var t=e.parentTId?e.getParentNode():data.getRoot(s),n=data.nodeChildren(s,t),o=0,a=n.length;o<a;o++)if(n[o]==e)return o;return-1},getSelectedNodes:function(){for(var e=[],t=data.getRoot(s).curSelectedList,n=0,o=t.length;n<o;n++)e.push(t[n]);return e},isSelectedNode:function(e){return data.isSelectedNode(s,e)},reAsyncChildNodesPromise:function(n,o,a){return new Promise(function(e,t){try{r.reAsyncChildNodes(n,o,a,function(){e(n)})}catch(e){t(e)}})},reAsyncChildNodes:function(e,t,n,o){if(this.setting.async.enable){var a=!e;if(a&&(e=data.getRoot(s)),"refresh"==t){for(var r=data.nodeChildren(s,e),d=0,i=r?r.length:0;d<i;d++)data.removeNodeCache(s,r[d]);if(data.removeSelectedNode(s),data.nodeChildren(s,e,[]),a)this.setting.treeObj.empty();else $$(e,consts.id.UL,s).empty()}view.asyncNode(this.setting,a?null:e,!!n,o)}},refresh:function(){this.setting.treeObj.empty();var e=data.getRoot(s),t=data.nodeChildren(s,e);data.initRoot(s),data.nodeChildren(s,e,t),data.initCache(s),view.createNodes(s,0,data.nodeChildren(s,e),null,-1)},removeChildNodes:function(e){if(!e)return null;var t=data.nodeChildren(s,e);return view.removeChildNodes(s,e),t||null},removeNode:function(e,t){e&&((t=!!t)&&0==tools.apply(s.callback.beforeRemove,[s.treeId,e],!0)||(view.removeNode(s,e),t&&this.setting.treeObj.trigger(consts.event.REMOVE,[s.treeId,e])))},selectNode:function(t,e,n){if(t&&tools.uCanDo(s)){if(e=s.view.selectedMulti&&e,t.parentTId)view.expandCollapseParentNode(s,t.getParentNode(),!0,!1,function(){if(n)return;var e=$$(t,s).get(0);view.scrollIntoView(s,e)});else if(!n)try{$$(t,s).focus().blur()}catch(e){}view.selectNode(s,t,e)}},transformTozTreeNodes:function(e){return data.transformTozTreeFormat(s,e)},transformToArray:function(e){return data.transformToArrayFormat(s,e)},updateNode:function(e,t){e&&$$(e,s).get(0)&&tools.uCanDo(s)&&(view.setNodeName(s,e),view.setNodeTarget(s,e),view.setNodeUrl(s,e),view.setNodeLineIcos(s,e),view.setNodeFontCss(s,e),view.setNodeClasses(s,e))}};o.treeTools=r,data.setZTreeTools(s,r);var a=data.nodeChildren(s,o);return a&&0<a.length?view.createNodes(s,0,a,null,-1):s.async.enable&&s.async.url&&""!==s.async.url&&view.asyncNode(s),r}};var zt=$.fn.zTree,$$=tools.$,consts=zt.consts}(jQuery);