server {
    listen 80;
    server_name localhost;

    access_log /var/log/nginx/host.access.log main;
    error_log /var/log/nginx/error.log error;

    root /usr/share/nginx/html;

    # 处理静态文件的请求
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
    }

    # 禁止缓存 HTML 文件
    location ~* \.html$ {
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # 错误页面配置
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}