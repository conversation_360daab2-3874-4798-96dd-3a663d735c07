import Vue from 'vue'
import Router from 'vue-router'
import Layout from '@/layout'

Vue.use(Router)

/* Layout */

/* Router Modules */

// import demoRouter from './modules/demo'
// import nestedRouter from './modules/nested'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/redirect',
    component: () => import('@/views/redirect/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/home',
    meta: { title: '首页' },
    children: [
      {
        path: 'home',
        component: () => import('@/views/patient-home/index.vue'),
        name: 'PatientHome',
        meta: { title: '主页看板', affix: true },
        hidden: true,
        caiDanLX: 'C'
      },
      {
        path: '/patient-detail/:id',
        component: () => import('@/views/patient-detail/index'),
        name: 'PatientDetail',
        meta: {
          title: '病人详情',
          keepAlive: true, // 启用keep-alive
          usePathKey: true // 使用路径作为key
        }
      },
      {
        path: 'treat-medical-advice',
        component: () => import('@/views/system-maintenance/treat-medical-advice/index.vue'),
        name: 'TreatMedicalAdvice',
        meta: { title: '治疗医嘱模板' } //完成目录增删改查，组合未完成
      },
      {
        path: 'comprehensive-medical-advice',
        component: () =>
          import('@/views/system-maintenance/comprehensive-medical-advice/index.vue'),
        name: 'ComprehensiveMedicalAdvice',
        meta: { title: '综合医嘱模板' } //文档ok，页面复杂
      },
      {
        path: 'youchuang-model',
        component: () => import('@/views/system-maintenance/youchuang-model/index.vue'),
        name: 'YouChuangModel',
        meta: { title: '有创模板维护' } //文档404
      },
      //废弃
      // {
      //   path: 'daily-model',
      //   component: () => import('@/views/system-maintenance/daily-model/index.vue'),
      //   name: 'DailyModel',
      //   meta: { title: '日常模板维护' }//文档废弃
      // },
      {
        path: 'complication-model',
        component: () => import('@/views/system-maintenance/complication-model/index.vue'),
        name: 'ComplicationModel',
        meta: { title: '并发症模板维护' } //文档废弃
      },
      {
        path: 'treat-group/treat-group-maintenance',
        component: () =>
          import('@/views/system-maintenance/treat-group/treat-group-maintenance/index.vue'),
        name: 'TreatGroupMaintenance',
        meta: { title: '治疗组维护' } //文档404
      },
      {
        path: 'treat-group/treat-group-patient-list',
        component: () =>
          import('@/views/system-maintenance/treat-group/treat-group-patient-list/index.vue'),
        name: 'TreatGroupPatientList',
        meta: { title: '治疗组病人列表' } //文档不存在
      },
      {
        path: 'treat-group/treat-group-scheduling-review',
        component: () =>
          import('@/views/system-maintenance/treat-group/treat-group-scheduling-review/index.vue'),
        name: 'TreatGroupSchedulingReview',
        meta: { title: '治疗组排班审核' } //文档404
      },
      {
        path: 'treat-group/treat-group-scheduling-statistics',
        component: () =>
          import(
            '@/views/system-maintenance/treat-group/treat-group-scheduling-statistics/index.vue'
          ),
        name: 'TreatGroupSchedulingStatistics',
        meta: { title: '治疗组排班统计' } //文档404
      },
      {
        path: 'treat-group/treat-group-bed-maintenance',
        component: () =>
          import('@/views/system-maintenance/treat-group/treat-group-bed-maintenance/index.vue'),
        name: 'TreatGroupBedMaintenance',
        meta: { title: '治疗组床位维护' } //文档404
      },
      {
        path: 'special-patient',
        component: () => import('@/views/system-maintenance/special-patient/index.vue'),
        name: 'SpecialPatient',
        meta: { title: '特殊病人维护' } //文档404
      },
      {
        path: 'road-traffic-patient',
        component: () => import('@/views/system-maintenance/road-traffic-patient/index.vue'),
        name: 'RoadTrafficPatient',
        meta: { title: '道路交通患者维护' } //文档内容与页面不符
      },
      {
        path: 'inpatient-maintenance',
        component: () => import('@/views/system-maintenance/inpatient-maintenance/index.vue'),
        name: 'InpatientMaintenance',
        meta: { title: '住院总维护' } //文档404
      },
      {
        path: 'gastrointestinal-nutrition-meal',
        component: () =>
          import('@/views/system-maintenance/gastrointestinal-nutrition-meal/index.vue'),
        name: 'GastrointestinalNutritionMeal',
        meta: { title: '胃肠道营养餐维护' } //文档ok，接口已经对接完成
      },
      {
        path: 'medical-certificate-management',
        component: () =>
          import('@/views/system-maintenance/medical-certificate-management/index.vue'),
        name: 'MedicalCertificateManagement',
        meta: { title: '医疗证明管理' } //文档ok
      },
      {
        path: 'transfer-patient-list',
        component: () => import('@/views/system-maintenance/transfer-patient-list/index.vue'),
        name: 'TransferPatientList',
        meta: { title: '本专科转科病人列表' }
      },
      {
        path: 'pathological-result-remind',
        component: () => import('@/views/system-maintenance/pathological-result-remind/index.vue'),
        name: 'PathologicalResultRemind',
        meta: { title: '出院病人病理结果提醒' }
      },
      {
        path: 'emodialysis-advice-approve',
        component: () => import('@/views/system-maintenance/emodialysis-advice-approve/index.vue'),
        name: 'EmodialysisAdviceApprove',
        meta: { title: '血透医嘱审批' }
      },
      {
        path: 'inspection-report-query',
        component: () => import('@/views/system-maintenance/inspection-report-query/index.vue'),
        name: 'InspectionReportQuery',
        meta: { title: '专科本治疗组特检报告查询' }
      },
      {
        path: 'blood-application-approve',
        component: () => import('@/views/system-maintenance/blood-application-approve/index.vue'),
        name: 'BloodApplicationApprove',
        meta: { title: '输血申请单审批' }
      },
      {
        path: 'paperless-trial-patient',
        component: () => import('@/views/system-maintenance/paperless-trial-patient/index.vue'),
        name: 'PaperlessTrialPatient',
        meta: { title: '添加无纸化病人' }
      },
      {
        path: 'day-surgery',
        component: () => import('@/views/system-maintenance/day-surgery/index.vue'),
        name: 'DaySurgery',
        meta: { title: '日间手术与贯标国家码的对照维护' }
      },
      {
        path: 'case-filing',
        component: () => import('@/views/system-maintenance/case-filing/index.vue'),
        name: 'CaseFiling',
        meta: { title: '病例归档' }
      },
      {
        path: 'specialist-know-record',
        component: () => import('@/views/system-maintenance/specialist-know-record/index.vue'),
        name: 'SpecialistKnowRecord',
        meta: { title: '专科知情记录' }
      },
      {
        path: 'specialist-emergency',
        component: () => import('@/views/specialist-menus/specialist-emergency/index.vue'),
        name: 'SpecialistEmergency',
        meta: { title: '本专科危急值查询' }
      },
      {
        path: 'specialized-handover-record',
        component: () => import('@/views/specialist-menus/specialized-handover-record/index.vue'),
        name: 'SpecializedHandoverRecord',
        meta: { title: '专科交接班记录' }
      }
      // {
      //   path: 'case-correction-application-form',
      //   component: () =>
      //     import('@/views/system-maintenance/case-correction-application-form/index.vue'),
      //   name: 'CaseCorrectionApplicationForm',
      //   meta: { title: '转/跨科病历修正申请单管理' }
      // }

      // {
      //   path: 'patient-operate',
      //   component: () => import('@/views/patient-detail/index.vue'),
      //   name: 'PatientOperate',
      //   meta: { title: '病人操作' },
      //   caiDanLX: 'M',
      //   children: [
      //     {
      //       path: 'medical-record-index',
      //       component: () => import('@/views/my-menus/Demo.vue'),
      //       name: 'medicalRecordIndex',
      //       meta: { title: '病案首页接口' },
      //       caiDanLX: 'C'
      //     },
      //     {
      //       path: 'control-my-patient',
      //       component: () => import('@/views/my-menus/Demo.vue'),
      //       name: 'controlMyPatient',
      //       meta: { title: '移除我的病人' },
      //       caiDanLX: 'C'
      //     },
      //     {
      //       path: 'sense-monitor',
      //       component: () => import('@/views/my-menus/Demo.vue'),
      //       name: 'senseMonitor',
      //       meta: { title: '院感监控' },
      //       caiDanLX: 'C'
      //     },
      //     {
      //       path: 'Multidisciplinary-treatment',
      //       component: () => import('@/views/my-menus/Demo.vue'),
      //       name: 'PatientOperateDetail',
      //       meta: { title: '跨科治疗' },
      //       caiDanLX: 'C'
      //     },
      //     {
      //       path: 'patient-360',
      //       component: () => import('@/views/my-menus/Demo.vue'),
      //       name: 'Patient360',
      //       meta: { title: '患者360' },
      //       caiDanLX: 'C'
      //     }
      //   ]
      // }
    ]
  }
  // {
  //   path: '/my-menus',
  //   name: 'myMenus',
  //   component: Layout,
  //   meta: { title: '我的菜单' },
  //   children: [
  //     {
  //       path: 'hospital-drug-apply',
  //       component: () => import('@/views/my-menus/Demo.vue'),
  //       name: 'hospitalDrugApply',
  //       meta: { title: '住院外购药品申请' }
  //     },
  //     {
  //       path: 'specialty-wait-list',
  //       component: () => import('@/views/my-menus/Demo.vue'),
  //       name: 'specialtyWaitList',
  //       meta: { title: '专科等待入院病人列表' }
  //     }
  //   ]
  // },
  // {
  //   path: '/department-menus',
  //   component: Layout,
  //   name: 'departmentMenus',
  //   meta: { title: '专科菜单' },
  //   children: [
  //     {
  //       path: 'department-unwritten',
  //       component: () => import('@/views/my-menus/Demo.vue'),
  //       meta: '本专科未书写病程录'
  //     },
  //     {
  //       path: 'department-15-day',
  //       component: () => import('@/views/my-menus/Demo.vue'),
  //       meta: '专科15日出院病人列表'
  //     }
  //   ]
  // },
  // {
  //   path: '/medical-quality',
  //   component: Layout,
  //   name: 'medicalQuality',
  //   meta: { title: '科室医疗质量指标查询' }
  // },
  // {
  //   path: '/information',
  //   component: Layout,
  //   name: 'information',
  //   meta: { title: '信息查询' }
  // },
  // {
  //   path: '/system-maintenance',
  //   component: Layout,
  //   name: 'systemMaintenance',
  //   meta: { title: '系统维护' }
  // }
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  {
    path: '/permission',
    component: Layout,
    redirect: '/permission/page',
    alwaysShow: true, // will always show the root menu
    name: 'Permission',
    meta: {
      title: 'permission',
      icon: 'lock',
      roles: ['admin', 'editor'] // you can set roles in root nav
    },
    children: [
      {
        path: 'page',
        component: () => import('@/views/permission/page'),
        name: 'PagePermission',
        meta: {
          title: 'pagePermission',
          roles: ['admin'] // or you can only set roles in sub nav
        }
      },
      {
        path: 'directive',
        component: () => import('@/views/permission/directive'),
        name: 'DirectivePermission',
        meta: {
          title: 'directivePermission'
          // if do not set roles, means: this page does not require permission
        }
      },
      {
        path: 'role',
        component: () => import('@/views/permission/role'),
        name: 'RolePermission',
        meta: {
          title: 'rolePermission',
          roles: ['admin']
        }
      }
    ]
  },

  // {
  //   path: '/icon',
  //   component: Layout,
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/icons/index'),
  //       name: 'Icons',
  //       meta: { title: 'icons', icon: 'icon', noCache: true }
  //     }
  //   ]
  // },
  /** when your routing map is too long, you can split it into small modules **/
  // componentsRouter,
  // chartsRouter,
  // nestedRouter,
  // tableRouter,
  // {
  //   path: '/tab',
  //   component: Layout,
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/tab/index'),
  //       name: 'Tab',
  //       meta: { title: 'tab', icon: 'tab' }
  //     }
  //   ]
  // },

  {
    path: '/error',
    component: Layout,
    redirect: 'noRedirect',
    name: 'ErrorPages',
    meta: {
      title: 'errorPages',
      icon: '404'
    },
    children: [
      {
        path: '401',
        component: () => import('@/views/error-page/401'),
        name: 'Page401',
        meta: { title: 'page401', noCache: true }
      },
      {
        path: '404',
        component: () => import('@/views/error-page/404'),
        name: 'Page404',
        meta: { title: 'page404', noCache: true }
      }
    ]
  },

  {
    path: '/error-log',
    component: Layout,
    children: [
      {
        path: 'log',
        component: () => import('@/views/error-log/index'),
        name: 'ErrorLog',
        meta: { title: 'errorLog', icon: 'bug' }
      }
    ]
  },
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
