@import './variables';
@import './mixin';
@import './transition';
@import './element-ui';
@import './sidebar';
@import './reset';
@import './common';
@import './night/element-ui-night.scss';
@import './night/sidebar-night.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizelegibility;
  font: var(--font-regular);
  color: var(--font-color-regular);
}

label {
  font-weight: 700;
}

html {
  box-sizing: border-box;
  height: 100%;
}

#app {
  height: 100%;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
}

div:focus {
  outline: none;
}

.clearfix {
  &::after {
    display: block;
    height: 0;
    clear: both;
    font-size: 0;
    visibility: hidden;
    content: ' ';
  }
}

.color-danger {
  color: var(--color-danger);
}

.el-loading-spinner {
  font-size: 50px;
}

.other-menu-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

.other-menu-container {
  .el-select {
    width: 200px;
  }
}

// // main-container global css
// .app-container {
//   padding: 20px;
// }

.action-bar {
  position: relative;
  box-sizing: border-box;
  height: 42px;
  padding: 0 15px;
  text-align: right;
  border: 1px solid #f1e8e8;
  border-top: none;
  border-left: none;

  .delete-btn {
    color: #f56c6c;
  }
}

.action-item {
  position: relative;
  margin-right: 24px;
  font: var(--font-regular);
  font-size: $--font-size-regular;
  line-height: 17px;
  color: var(--link-color);
  letter-spacing: 0;
  cursor: pointer;

  &::after {
    position: absolute;
    top: 50%;
    right: -13px;
    width: 0;
    height: 16px;
    content: '';
    border: var(--border-base);
    opacity: 1;
    transform: translateY(-50%);
  }

  &:nth-last-of-type(1) {
    margin-right: 0;

    &::after {
      opacity: 0;
    }
  }

  &.danger {
    color: var(--color-danger);
  }

  &.disabled {
    color: var(--color-info);
    pointer-events: none;
  }
}

.label {
  margin-right: var(--label-value-margin);
  font-family: SourceHanSansCN-Regular, SourceHanSansCN;
  font-size: $--font-size-regular;
  font-weight: 400;
  line-height: 21px;
  color: var(--form-label-text-color);
  word-break: keep-all;
}

.text-medium {
  font: var(--font-medium);
  color: var(--font-color-medium);
}

.text-regular {
  font: var(--font-regular);
  color: var(--font-color-regular);
}

.title {
  font: var(--font-medium);
  font-size: $--font-size-regular;
  line-height: 21px;
  color: #171c28;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.flex-sb {
  display: flex;
  justify-content: space-between;
}

.flex-align-center {
  display: flex;
  align-items: center;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  padding: 0 4px;
  margin: calc((var(--content-fixed-height) - 130px) / 2) 16px 0;
  cursor: pointer;
  background: linear-gradient(90deg, #f5f6fa 0%, #fff 100%);
  border: 1px solid var(--border-color-base);
  border-radius: 4px;
}

.controls {
  &-item {
    &:not(:first-child) {
      margin-left: var(--common-margin);
    }

    display: flex;
    align-items: center;
    overflow: hidden;

    >* {
      width: auto !important;
    }
  }

  &-row {
    display: flex;
    align-items: center;
    width: 100%;
    overflow: hidden;

    &:not(:first-child) {
      margin-top: var(--common-margin);
    }

    &:last-of-type {
      margin-bottom: var(--common-margin);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 滚动条样式
// 轨道宽度
$scroll-width: 7px;
// 轨道颜色
$scroll-track-color: white;
// 滚动条颜色
$scroll-color: var(--color-info);

::-webkit-scrollbar {
  position: fixed;
  width: $scroll-width;
  height: $scroll-width;
  background-color: transparent;

  /* 滚动条轨道 */
  &-track {
    background-color: $scroll-track-color;
  }

  /* 竖直的滑块 */
  &-thumb {
    background-color: $scroll-color;
    border-radius: $scroll-width;

    /* 水平的滑块 */
    &:horizontal {
      background-color: $scroll-color;
    }
  }
}

// :hover::-webkit-scrollbar {
//   width: $scroll-width * 2;
//   height: $scroll-width * 2;
//   display: block;
// }

/*
 * 紧致布局
 */
.compact {
  --table-cell-padding-vertical: calc(var(--common-padding) / 4);
  --table-cell-padding-horizontal: calc(var(--common-padding) / 4);
  --common-margin: calc(var(--common-padding) / 2);
}

.mg-l {
  margin-left: var(--common-margin);
}

.mg-r {
  margin-right: var(--common-margin);
}

.mg-t {
  margin-top: var(--common-margin);
}

.mg-b {
  margin-bottom: var(--common-margin);
}

input,
textarea {
  &[readonly='readonly'] {
    pointer-events: none;
  }
}

.selectable-table {
  .el-table {
    tr {
      cursor: pointer;
    }

    tr.current-row {
      &>td {
        $border-style: 1px solid #2e55ed;

        background: rgb(46 85 237 / 5%);
        border-top: $border-style;
        border-bottom: $border-style;

        &:first-child {
          border-left: $border-style;
        }

        &:last-child {
          border-right: $border-style;
        }
      }
    }
  }
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.contextmenu_menu {

  .menu_item__available:hover,
  .menu_item_expand {
    color: var(--color-primary) !important;
  }
}

.no-data {
  position: absolute;
  top: 50%;
  left: 50%;
  font: var(--font-regular);
  transform: translate(-50%, -50%);
}

input,
textarea {
  &[readonly] {
    color: var(--font-color-regular);
    caret-color: transparent;
  }
}
