<template>
  <div style="height: calc(100% - 45px); width: 100%">
    <router-view v-if="!$route.meta.keepAlive" />
    <keep-alive>
      <router-view
        v-if="$route.meta.keepAlive"
        :key="$route.meta.usePathKey ? $route.fullPath : undefined"
      />
    </keep-alive>
  </div>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    // 需要缓存的页面 固钉
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.fullPath
    }
  }
}
</script>

<style lang="scss" scoped></style>
