<template>
  <div
    :style="{
      '--right-width': rightWidth,
      '--left-width': leftWidth,
      '--gap-width': gapWidth,
      '--left-background-color': leftBgColor,
      '--right-background-color': rightBgColor,
      '--padding': padding
    }"
    class="area"
  >
    <div class="left-area">
      <slot name="left"></slot>
    </div>
    <div class="gap-area" />
    <div class="right-area">
      <slot name="right"></slot>
    </div>
  </div>
</template>
<script>
export default {
  name: 'LeftRightLayout',
  props: {
    leftWidth: {
      type: String,
      default: '50%'
    },
    rightWidth: {
      type: String,
      default: '50%'
    },
    gapWidth: {
      type: String,
      default: '10px'
    },
    leftBgColor: {
      type: String,
      default: '#eff3fb'
    },
    rightBgColor: {
      type: String,
      default: '#eff3fb'
    },
    padding: {
      type: String,
      default: '10px'
    }
  }
}
</script>
<style lang="scss" scoped>
.area {
  display: flex;
  justify-content: space-between;
  height: 100%;
  font-family: 'Source Han Sans', sans-serif;
  border-radius: 4px 4px 0 0;
  width: 100%;
  overflow: hidden;

  .left-area,
  .right-area {
    display: flex;
    flex-direction: column;
    padding: 15px 17px;
    height: 100%;
  }

  .left-area {
    background-color: var(--left-background-color);
    flex: 1 1 var(--left-width);
    min-width: var(--left-width);
    padding: var(--padding);
  }

  .right-area {
    background-color: var(--right-background-color);
    flex: 1 1 var(--right-width);
    min-width: var(--right-width);
    padding: var(--padding);
  }

  .gap-area {
    width: var(--gap-width);
  }
}
</style>
