import store from '@/store'
import { confirm } from '@/global/dialog'

/**
 * 表单离开之前的弹出保存提示
 * @param {string} name 监听的data中的属性名
 * @param {string} callback 执行的methods中的函数名
 * @returns
 */
export const beforeFormChangeConfirm = (name, callback) => {
  return {
    computed: {
      // 观察目标，处理兼容watch-deep无法监听到新旧值
      observeTarget() {
        try {
          return JSON.parse(JSON.stringify(this[name]))
        } catch (error) {
          console.error(error)
        }
      }
    },
    watch: {
      observeTarget: {
        deep: true,
        handler(val, oldVal) {
          if (!name) {
            return
          }
          // 比对值减少值未发生改变误差
          if (JSON.stringify(val) !== JSON.stringify(oldVal)) {
            store.dispatch('app/setCurrentDataChange', {
              value: true,
              callback: this[callback] ? this[callback] : () => {}
            })
          }
        }
      }
    },
    methods: {
      // 关闭此次监听
      deleteFormWatcher() {
        //宏任务处理异步之后触发了监听
        setTimeout(() => {
          store.dispatch('app/setCurrentDataChange', { value: false })
        })
      },
      async beforeFormHidden() {
        if (store.state.app.isCurrentDataChange) {
          return new Promise((resolve, reject) => {
            confirm(
              '是否保存已修改的数据？',
              async () => {
                await store.dispatch('app/useCallback')
                await store.dispatch('app/setCurrentDataChange', { value: false })
                resolve()
              },
              {
                popType: 'save',
                closeCallback: async () => {
                  console.log('关闭')
                  await store.dispatch('app/setCurrentDataChange', { value: false })
                  // 变化,可以关闭tag
                  await store.dispatch('app/setTagClose', true)
                  resolve()
                }
              }
            )
          })
        } else {
          return new Promise((resolve) => {
            resolve()
          })
        }
      }
    }
  }
}

/**
 * beforeFormChangeConfirm    用例参考
 <template>
 <div>
 姓名  <el-input   v-model="form.name"></el-input>
 <div @click="$router.push('http://baidu.com1')">返回</div>
 </div>
 </template>
 <script>

 import {beforeFormChangeConfirm} from '@/utils/mixin/beforeFormChangeConfirm.mixin';
 export default {
 + mixins: [beforeFormChangeConfirm('form','submit')],//使用，form为监听的表单对象，submit为确定的执行方法
 data(){
 return{
 form:{name:''}
 }},
 methods:{
 submit(){
 +    this.deleteFormWatcher()//完成后清除监听
 }
 close(){
 +    this.deleteFormWatcher()//取消后清除监听
 }
 }
 }
 </script>
 *
 */
