
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).echarts={})}(this,(function(t){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,n)};function n(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}var i=function(){return(i=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)};function r(t,e,n){if(n||2===arguments.length)for(var i,r=0,o=e.length;r<o;r++)!i&&r in e||(i||(i=Array.prototype.slice.call(e,0,r)),i[r]=e[r]);return t.concat(i||e)}var o=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},a=new function(){this.browser=new o,this.node=!1,this.wxa=!1,this.worker=!1,this.canvasSupported=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(a.wxa=!0,a.canvasSupported=!0,a.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?(a.worker=!0,a.canvasSupported=!0):"undefined"==typeof navigator?(a.node=!0,a.canvasSupported=!0,a.svgSupported=!0):function(t,e){var n=e.browser,i=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge?\/([\d.]+)/),a=/micromessenger/i.test(t);i&&(n.firefox=!0,n.version=i[1]);r&&(n.ie=!0,n.version=r[1]);o&&(n.edge=!0,n.version=o[1],n.newEdge=+o[1].split(".")[0]>18);a&&(n.weChat=!0);e.canvasSupported=!!document.createElement("canvas").getContext,e.svgSupported="undefined"!=typeof SVGRect,e.touchEventsSupported="ontouchstart"in window&&!n.ie&&!n.edge,e.pointerEventsSupported="onpointerdown"in window&&(n.edge||n.ie&&+n.version>=11),e.domSupported="undefined"!=typeof document;var s=document.documentElement.style;e.transform3dSupported=(n.ie&&"transition"in s||n.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),e.transformSupported=e.transform3dSupported||n.ie&&+n.version>=9}(navigator.userAgent,a);var s={"[object Function]":!0,"[object RegExp]":!0,"[object Date]":!0,"[object Error]":!0,"[object CanvasGradient]":!0,"[object CanvasPattern]":!0,"[object Image]":!0,"[object Canvas]":!0},l={"[object Int8Array]":!0,"[object Uint8Array]":!0,"[object Uint8ClampedArray]":!0,"[object Int16Array]":!0,"[object Uint16Array]":!0,"[object Int32Array]":!0,"[object Uint32Array]":!0,"[object Float32Array]":!0,"[object Float64Array]":!0},u=Object.prototype.toString,h=Array.prototype,c=h.forEach,p=h.filter,f=h.slice,d=h.map,g=function(){}.constructor,y=g?g.prototype:null,v="__proto__",m={};function _(t,e){m[t]=e}var x=2311;function b(){return x++}function w(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function S(t){if(null==t||"object"!=typeof t)return t;var e=t,n=u.call(t);if("[object Array]"===n){if(!st(t)){e=[];for(var i=0,r=t.length;i<r;i++)e[i]=S(t[i])}}else if(l[n]){if(!st(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,r=t.length;i<r;i++)e[i]=S(t[i])}}}else if(!s[n]&&!st(t)&&!q(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==v&&(e[a]=S(t[a]));return e}function M(t,e,n){if(!U(e)||!U(t))return n?S(e):t;for(var i in e)if(e.hasOwnProperty(i)&&i!==v){var r=t[i],o=e[i];!U(o)||!U(r)||F(o)||F(r)||q(o)||q(r)||X(o)||X(r)||st(o)||st(r)?!n&&i in t||(t[i]=S(e[i])):M(r,o,n)}return t}function T(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==v&&(t[n]=e[n]);return t}function C(t,e,n){for(var i=E(e),r=0;r<i.length;r++){var o=i[r];(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}return t}var D=function(){return m.createCanvas()};function I(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function k(t,e){var n=t.prototype;function i(){}for(var r in i.prototype=e.prototype,t.prototype=new i,n)n.hasOwnProperty(r)&&(t.prototype[r]=n[r]);t.prototype.constructor=t,t.superClass=e}function A(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),r=0;r<i.length;r++){var o=i[r];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}else C(t,e,n)}function P(t){return!!t&&("string"!=typeof t&&"number"==typeof t.length)}function L(t,e,n){if(t&&e)if(t.forEach&&t.forEach===c)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function O(t,e,n){if(!t)return[];if(!e)return et(t);if(t.map&&t.map===d)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}function R(t,e,n,i){if(t&&e){for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function N(t,e,n){if(!t)return[];if(!e)return et(t);if(t.filter&&t.filter===p)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}function E(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);return e}m.createCanvas=function(){return document.createElement("canvas")};var z=y&&V(y.bind)?y.call.bind(y.bind):function(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return function(){return t.apply(e,n.concat(f.call(arguments)))}};function B(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(f.call(arguments)))}}function F(t){return Array.isArray?Array.isArray(t):"[object Array]"===u.call(t)}function V(t){return"function"==typeof t}function H(t){return"string"==typeof t}function G(t){return"[object String]"===u.call(t)}function W(t){return"number"==typeof t}function U(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function X(t){return!!s[u.call(t)]}function Y(t){return!!l[u.call(t)]}function q(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function j(t){return null!=t.colorStops}function Z(t){return null!=t.image}function K(t){return"[object RegExp]"===u.call(t)}function $(t){return t!=t}function J(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t.length;n<i;n++)if(null!=t[n])return t[n]}function Q(t,e){return null!=t?t:e}function tt(t,e,n){return null!=t?t:null!=e?e:n}function et(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return f.apply(t,e)}function nt(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function it(t,e){if(!t)throw new Error(e)}function rt(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var ot="__ec_primitive__";function at(t){t[ot]=!0}function st(t){return t[ot]}var lt=function(){function t(e){this.data={};var n=F(e);this.data={};var i=this;function r(t,e){n?i.set(t,e):i.set(e,t)}e instanceof t?e.each(r):e&&L(e,r)}return t.prototype.get=function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},t.prototype.set=function(t,e){return this.data[t]=e},t.prototype.each=function(t,e){for(var n in this.data)this.data.hasOwnProperty(n)&&t.call(e,this.data[n],n)},t.prototype.keys=function(){return E(this.data)},t.prototype.removeKey=function(t){delete this.data[t]},t}();function ut(t){return new lt(t)}function ht(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];var r=t.length;for(i=0;i<e.length;i++)n[i+r]=e[i];return n}function ct(t,e){var n;if(Object.create)n=Object.create(t);else{var i=function(){};i.prototype=t,n=new i}return e&&T(n,e),n}function pt(t,e){return t.hasOwnProperty(e)}function ft(){}var dt=Object.freeze({__proto__:null,$override:_,guid:b,logError:w,clone:S,merge:M,mergeAll:function(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=M(n,t[i],e);return n},extend:T,defaults:C,createCanvas:D,indexOf:I,inherits:k,mixin:A,isArrayLike:P,each:L,map:O,reduce:R,filter:N,find:function(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]},keys:E,bind:z,curry:B,isArray:F,isFunction:V,isString:H,isStringSafe:G,isNumber:W,isObject:U,isBuiltInObject:X,isTypedArray:Y,isDom:q,isGradientObject:j,isImagePatternObject:Z,isRegExp:K,eqNaN:$,retrieve:J,retrieve2:Q,retrieve3:tt,slice:et,normalizeCssArray:nt,assert:it,trim:rt,setAsPrimitive:at,isPrimitive:st,HashMap:lt,createHashMap:ut,concatArray:ht,createObject:ct,hasOwn:pt,noop:ft});function gt(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function yt(t){return[t[0],t[1]]}function vt(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function mt(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function _t(t){return Math.sqrt(bt(t))}var xt=_t;function bt(t){return t[0]*t[0]+t[1]*t[1]}var wt=bt;function St(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function Mt(t,e){var n=_t(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function Tt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var Ct=Tt;function Dt(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var It=Dt;function kt(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function At(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t}function Pt(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function Lt(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var Ot=Object.freeze({__proto__:null,create:gt,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},clone:yt,set:function(t,e,n){return t[0]=e,t[1]=n,t},add:vt,scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},sub:mt,len:_t,length:xt,lenSquare:bt,lengthSquare:wt,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:St,normalize:Mt,distance:Tt,dist:Ct,distanceSquare:Dt,distSquare:It,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:kt,applyTransform:At,min:Pt,max:Lt}),Rt=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},Nt=function(){function t(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return t.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new Rt(e,t),"dragstart",t.event))},t.prototype._drag=function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,i=t.offsetY,r=n-this._x,o=i-this._y;this._x=n,this._y=i,e.drift(r,o,t),this.handler.dispatchToElement(new Rt(e,t),"drag",t.event);var a=this.handler.findHover(n,i,e).target,s=this._dropTarget;this._dropTarget=a,e!==a&&(s&&a!==s&&this.handler.dispatchToElement(new Rt(s,t),"dragleave",t.event),a&&a!==s&&this.handler.dispatchToElement(new Rt(a,t),"dragenter",t.event))}},t.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new Rt(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new Rt(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},t}(),Et=function(){function t(t){t&&(this._$eventProcessor=t)}return t.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var r=this._$handlers;if("function"==typeof e&&(i=n,n=e,e=null),!n||!t)return this;var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),r[t]||(r[t]=[]);for(var a=0;a<r[t].length;a++)if(r[t][a].h===n)return this;var s={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},l=r[t].length-1,u=r[t][l];return u&&u.callAtLast?r[t].splice(l,0,s):r[t].push(s),this},t.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},t.prototype.off=function(t,e){var n=this._$handlers;if(!n)return this;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},t.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var l=i[s];if(!r||!r.filter||null==l.query||r.filter(t,l.query))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e)}}return r&&r.afterTrigger&&r.afterTrigger(t),this},t.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,l=0;l<s;l++){var u=i[l];if(!r||!r.filter||null==u.query||r.filter(t,u.query))switch(o){case 0:u.h.call(a);break;case 1:u.h.call(a,e[0]);break;case 2:u.h.call(a,e[0],e[1]);break;default:u.h.apply(a,e.slice(1,o-1))}}return r&&r.afterTrigger&&r.afterTrigger(t),this},t}(),zt=Math.log(2);function Bt(t,e,n,i,r,o){var a=i+"-"+r,s=t.length;if(o.hasOwnProperty(a))return o[a];if(1===e){var l=Math.round(Math.log((1<<s)-1&~r)/zt);return t[n][l]}for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,p=0,f=0;p<s;p++){var d=1<<p;d&r||(c+=(f%2?-1:1)*t[n][p]*Bt(t,e-1,h,u,r|d,o),f++)}return o[a]=c,c}function Ft(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=Bt(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*Bt(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}function Vt(t,e,n,i,r){if(e.getBoundingClientRect&&a.domSupported&&!Ht(e)){var o=e.___zrEVENTSAVED||(e.___zrEVENTSAVED={}),s=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=[],s=[],l=!0,u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,p=h.left,f=h.top;a.push(p,f),l=l&&o&&p===o[c]&&f===o[c+1],s.push(t[u].offsetLeft,t[u].offsetTop)}return l&&r?r:(e.srcCoords=a,e[i]=n?Ft(s,a):Ft(a,s))}(function(t,e){var n=e.markers;if(n)return n;n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=o%2,l=(o>>1)%2;a.style.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[s]+":0",r[l]+":0",i[1-s]+":auto",r[1-l]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}return n}(e,o),o,r);if(s)return s(t,n,i),!0}return!1}function Ht(t){return"CANVAS"===t.nodeName.toUpperCase()}var Gt="undefined"!=typeof window&&!!window.addEventListener,Wt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Ut=[];function Xt(t,e,n,i){return n=n||{},i||!a.canvasSupported?Yt(t,e,n):a.browser.firefox&&a.browser.version<"39"&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):Yt(t,e,n),n}function Yt(t,e,n){if(a.domSupported&&t.getBoundingClientRect){var i=e.clientX,r=e.clientY;if(Ht(t)){var o=t.getBoundingClientRect();return n.zrX=i-o.left,void(n.zrY=r-o.top)}if(Vt(Ut,t,i,r))return n.zrX=Ut[0],void(n.zrY=Ut[1])}n.zrX=n.zrY=0}function qt(t){return t||window.event}function jt(t,e,n){if(null!=(e=qt(e)).zrX)return e;var i=e.type;if(i&&i.indexOf("touch")>=0){var r="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];r&&Xt(t,r,e,n)}else{Xt(t,e,e,n);var o=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,i=t.deltaY;if(null==n||null==i)return e;return 3*(0!==i?Math.abs(i):Math.abs(n))*(i>0?-1:i<0?1:n>0?-1:1)}(e);e.zrDelta=o?o/120:-(e.detail||0)/3}var a=e.button;return null==e.which&&void 0!==a&&Wt.test(e.type)&&(e.which=1&a?1:2&a?3:4&a?2:0),e}var Zt=Gt?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0},Kt=function(){function t(){this._track=[]}return t.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},t.prototype.clear=function(){return this._track.length=0,this},t.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=Xt(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},t.prototype._recognize=function(t){for(var e in Jt)if(Jt.hasOwnProperty(e)){var n=Jt[e](this._track,t);if(n)return n}},t}();function $t(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}var Jt={pinch:function(t,e){var n=t.length;if(n){var i,r=(t[n-1]||{}).points,o=(t[n-2]||{}).points||r;if(o&&o.length>1&&r&&r.length>1){var a=$t(r)/$t(o);!isFinite(a)&&(a=1),e.pinchScale=a;var s=[((i=r)[0][0]+i[1][0])/2,(i[0][1]+i[1][1])/2];return e.pinchX=s[0],e.pinchY=s[1],{type:"pinch",target:t[0].target,event:e}}}}},Qt="silent";function te(){Zt(this.event)}var ee=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.handler=null,e}return n(e,t),e.prototype.dispose=function(){},e.prototype.setCursor=function(){},e}(Et),ne=function(t,e){this.x=t,this.y=e},ie=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],re=function(t){function e(e,n,i,r){var o=t.call(this)||this;return o._hovered=new ne(0,0),o.storage=e,o.painter=n,o.painterRoot=r,i=i||new ee,o.proxy=null,o.setHandlerProxy(i),o._draggingMgr=new Nt(o),o}return n(e,t),e.prototype.setHandlerProxy=function(t){this.proxy&&this.proxy.dispose(),t&&(L(ie,(function(e){t.on&&t.on(e,this[e],this)}),this),t.handler=this),this.proxy=t},e.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,i=ae(this,e,n),r=this._hovered,o=r.target;o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target);var a=this._hovered=i?new ne(e,n):this.findHover(e,n),s=a.target,l=this.proxy;l.setCursor&&l.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},e.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},e.prototype.resize=function(){this._hovered=new ne(0,0)},e.prototype.dispatch=function(t,e){var n=this[t];n&&n.call(this,e)},e.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},e.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},e.prototype.dispatchToElement=function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o=function(t,e,n){return{type:t,event:n,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:te}}(e,t,n);i&&(i[r]&&(o.cancelBubble=!!i[r].call(i,o)),i.trigger(e,o),i=i.__hostTarget?i.__hostTarget:i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer((function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)})))}},e.prototype.findHover=function(t,e,n){for(var i=this.storage.getDisplayList(),r=new ne(t,e),o=i.length-1;o>=0;o--){var a=void 0;if(i[o]!==n&&!i[o].ignore&&(a=oe(i[o],t,e))&&(!r.topTarget&&(r.topTarget=i[o]),a!==Qt)){r.target=i[o];break}}return r},e.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new Kt);var n=this._gestureMgr;"start"===e&&n.clear();var i=n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&n.clear(),i){var r=i.type;t.gestureEvent=r;var o=new ne;o.target=i.target,this.dispatchToElement(o,r,i.event)}},e}(Et);function oe(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i=t,r=void 0,o=!1;i;){if(i.ignoreClip&&(o=!0),!o){var a=i.getClipPath();if(a&&!a.contain(e,n))return!1;i.silent&&(r=!0)}var s=i.__hostTarget;i=s||i.parent}return!r||Qt}return!1}function ae(t,e,n){var i=t.painter;return e<0||e>i.getWidth()||n<0||n>i.getHeight()}L(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],(function(t){re.prototype[t]=function(e){var n,i,r=e.zrX,o=e.zrY,a=ae(this,r,o);if("mouseup"===t&&a||(i=(n=this.findHover(r,o)).target),"mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||Ct(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,t,e)}}));function se(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;!function(t,e,n){n--;for(;e<n;){var i=t[e];t[e++]=t[n],t[n--]=i}}(t,e,r)}else for(;r<n&&i(t[r],t[r-1])>=0;)r++;return r-e}function le(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=o+1;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;u>0;)t[s+u]=t[s+u-1],u--}t[s]=a}}function ue(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])>0){for(s=i-r;l<s&&o(t,e[n+r+l])>0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s);var u=a;a=r-l,l=r-u}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])>0?a=h+1:l=h}return l}function he(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s);var u=a;a=r-l,l=r-u}else{for(s=i-r;l<s&&o(t,e[n+r+l])>=0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function ce(t,e){var n,i,r=7,o=0;t.length;var a=[];function s(s){var l=n[s],u=i[s],h=n[s+1],c=i[s+1];i[s]=u+c,s===o-3&&(n[s+1]=n[s+2],i[s+1]=i[s+2]),o--;var p=he(t[h],t,l,u,0,e);l+=p,0!==(u-=p)&&0!==(c=ue(t[l+u-1],t,h,c,c-1,e))&&(u<=c?function(n,i,o,s){var l=0;for(l=0;l<i;l++)a[l]=t[n+l];var u=0,h=o,c=n;if(t[c++]=t[h++],0==--s){for(l=0;l<i;l++)t[c+l]=a[u+l];return}if(1===i){for(l=0;l<s;l++)t[c+l]=t[h+l];return void(t[c+s]=a[u])}var p,f,d,g=r;for(;;){p=0,f=0,d=!1;do{if(e(t[h],a[u])<0){if(t[c++]=t[h++],f++,p=0,0==--s){d=!0;break}}else if(t[c++]=a[u++],p++,f=0,1==--i){d=!0;break}}while((p|f)<g);if(d)break;do{if(0!==(p=he(t[h],a,u,i,0,e))){for(l=0;l<p;l++)t[c+l]=a[u+l];if(c+=p,u+=p,(i-=p)<=1){d=!0;break}}if(t[c++]=t[h++],0==--s){d=!0;break}if(0!==(f=ue(a[u],t,h,s,0,e))){for(l=0;l<f;l++)t[c+l]=t[h+l];if(c+=f,h+=f,0===(s-=f)){d=!0;break}}if(t[c++]=a[u++],1==--i){d=!0;break}g--}while(p>=7||f>=7);if(d)break;g<0&&(g=0),g+=2}if((r=g)<1&&(r=1),1===i){for(l=0;l<s;l++)t[c+l]=t[h+l];t[c+s]=a[u]}else{if(0===i)throw new Error;for(l=0;l<i;l++)t[c+l]=a[u+l]}}(l,u,h,c):function(n,i,o,s){var l=0;for(l=0;l<s;l++)a[l]=t[o+l];var u=n+i-1,h=s-1,c=o+s-1,p=0,f=0;if(t[c--]=t[u--],0==--i){for(p=c-(s-1),l=0;l<s;l++)t[p+l]=a[l];return}if(1===s){for(f=(c-=i)+1,p=(u-=i)+1,l=i-1;l>=0;l--)t[f+l]=t[p+l];return void(t[c]=a[h])}var d=r;for(;;){var g=0,y=0,v=!1;do{if(e(a[h],t[u])<0){if(t[c--]=t[u--],g++,y=0,0==--i){v=!0;break}}else if(t[c--]=a[h--],y++,g=0,1==--s){v=!0;break}}while((g|y)<d);if(v)break;do{if(0!==(g=i-he(a[h],t,n,i,i-1,e))){for(i-=g,f=(c-=g)+1,p=(u-=g)+1,l=g-1;l>=0;l--)t[f+l]=t[p+l];if(0===i){v=!0;break}}if(t[c--]=a[h--],1==--s){v=!0;break}if(0!==(y=s-ue(t[u],a,0,s,s-1,e))){for(s-=y,f=(c-=y)+1,p=(h-=y)+1,l=0;l<y;l++)t[f+l]=a[p+l];if(s<=1){v=!0;break}}if(t[c--]=t[u--],0==--i){v=!0;break}d--}while(g>=7||y>=7);if(v)break;d<0&&(d=0),d+=2}(r=d)<1&&(r=1);if(1===s){for(f=(c-=i)+1,p=(u-=i)+1,l=i-1;l>=0;l--)t[f+l]=t[p+l];t[c]=a[h]}else{if(0===s)throw new Error;for(p=c-(s-1),l=0;l<s;l++)t[p+l]=a[l]}}(l,u,h,c))}return n=[],i=[],{mergeRuns:function(){for(;o>1;){var t=o-2;if(t>=1&&i[t-1]<=i[t]+i[t+1]||t>=2&&i[t-2]<=i[t]+i[t-1])i[t-1]<i[t+1]&&t--;else if(i[t]>i[t+1])break;s(t)}},forceMergeRuns:function(){for(;o>1;){var t=o-2;t>0&&i[t-1]<i[t+1]&&t--,s(t)}},pushRun:function(t,e){n[o]=t,i[o]=e,o+=1}}}function pe(t,e,n,i){n||(n=0),i||(i=t.length);var r=i-n;if(!(r<2)){var o=0;if(r<32)le(t,n,i,n+(o=se(t,n,i,e)),e);else{var a=ce(t,e),s=function(t){for(var e=0;t>=32;)e|=1&t,t>>=1;return t+e}(r);do{if((o=se(t,n,i,e))<s){var l=r;l>s&&(l=s),le(t,n,n+l,n+o,e),o=l}a.pushRun(n,o),a.mergeRuns(),r-=o,n+=o}while(0!==r);a.forceMergeRuns()}}}var fe=!1;function de(){fe||(fe=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function ge(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var ye=function(){function t(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=ge}return t.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},t.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return!t&&n.length||this.updateDisplayList(e),n},t.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,a.canvasSupported&&pe(n,ge)},t.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),o=r,r=r.getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty|=1),this._updateAndAddDisplayable(l,e,n)}t.__dirty=0}else{var u=t;e&&e.length?u.__clipPaths=e:u.__clipPaths&&u.__clipPaths.length>0&&(u.__clipPaths=[]),isNaN(u.z)&&(de(),u.z=0),isNaN(u.z2)&&(de(),u.z2=0),isNaN(u.zlevel)&&(de(),u.zlevel=0),this._displayList[this._displayListLen++]=u}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,n);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,n);var p=t.getTextContent();p&&this._updateAndAddDisplayable(p,e,n)}},t.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},t.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);else{var i=I(this._roots,t);i>=0&&this._roots.splice(i,1)}},t.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},t.prototype.getRoots=function(){return this._roots},t.prototype.dispose=function(){this._displayList=null,this._roots=null},t}(),ve="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},me={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),-n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=i*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*-.5:n*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-me.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*me.bounceIn(2*t):.5*me.bounceOut(2*t-1)+.5}},_e=function(){function t(t){this._initialized=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=null!=t.loop&&t.loop,this.gap=t.gap||0,this.easing=t.easing||"linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart}return t.prototype.step=function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),!this._paused){var n=(t-this._startTime-this._pausedTime)/this._life;n<0&&(n=0),n=Math.min(n,1);var i=this.easing,r="string"==typeof i?me[i]:i,o="function"==typeof r?r(n):n;if(this.onframe&&this.onframe(o),1===n){if(!this.loop)return!0;this._restart(t),this.onrestart&&this.onrestart()}return!1}this._pausedTime+=e},t.prototype._restart=function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0},t.prototype.pause=function(){this._paused=!0},t.prototype.resume=function(){this._paused=!1},t}(),xe=function(t){this.value=t},be=function(){function t(){this._len=0}return t.prototype.insert=function(t){var e=new xe(t);return this.insertEntry(e),e},t.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},t.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},t.prototype.len=function(){return this._len},t.prototype.clear=function(){this.head=this.tail=null,this._len=0},t}(),we=function(){function t(t){this._list=new be,this._maxSize=10,this._map={},this._maxSize=t}return t.prototype.put=function(t,e){var n=this._list,i=this._map,r=null;if(null==i[t]){var o=n.len(),a=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var s=n.head;n.remove(s),delete i[s.key],r=s.value,this._lastRemovedEntry=s}a?a.value=e:a=new xe(e),a.key=t,n.insertEntry(a),i[t]=a}return r},t.prototype.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},t.prototype.clear=function(){this._list.clear(),this._map={}},t.prototype.len=function(){return this._list.len()},t}(),Se={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Me(t){return(t=Math.round(t))<0?0:t>255?255:t}function Te(t){return t<0?0:t>1?1:t}function Ce(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?Me(parseFloat(e)/100*255):Me(parseInt(e,10))}function De(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?Te(parseFloat(e)/100):Te(parseFloat(e))}function Ie(t,e,n){return n<0?n+=1:n>1&&(n-=1),6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function ke(t,e,n){return t+(e-t)*n}function Ae(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function Pe(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var Le=new we(20),Oe=null;function Re(t,e){Oe&&Pe(Oe,e),Oe=Le.put(t,Oe||e.slice())}function Ne(t,e){if(t){e=e||[];var n=Le.get(t);if(n)return Pe(e,n);var i=(t+="").replace(/ /g,"").toLowerCase();if(i in Se)return Pe(e,Se[i]),Re(t,e),e;var r,o=i.length;if("#"===i.charAt(0))return 4===o||5===o?(r=parseInt(i.slice(1,4),16))>=0&&r<=4095?(Ae(e,(3840&r)>>4|(3840&r)>>8,240&r|(240&r)>>4,15&r|(15&r)<<4,5===o?parseInt(i.slice(4),16)/15:1),Re(t,e),e):void Ae(e,0,0,0,1):7===o||9===o?(r=parseInt(i.slice(1,7),16))>=0&&r<=16777215?(Ae(e,(16711680&r)>>16,(65280&r)>>8,255&r,9===o?parseInt(i.slice(7),16)/255:1),Re(t,e),e):void Ae(e,0,0,0,1):void 0;var a=i.indexOf("("),s=i.indexOf(")");if(-1!==a&&s+1===o){var l=i.substr(0,a),u=i.substr(a+1,s-(a+1)).split(","),h=1;switch(l){case"rgba":if(4!==u.length)return 3===u.length?Ae(e,+u[0],+u[1],+u[2],1):Ae(e,0,0,0,1);h=De(u.pop());case"rgb":return 3!==u.length?void Ae(e,0,0,0,1):(Ae(e,Ce(u[0]),Ce(u[1]),Ce(u[2]),h),Re(t,e),e);case"hsla":return 4!==u.length?void Ae(e,0,0,0,1):(u[3]=De(u[3]),Ee(u,e),Re(t,e),e);case"hsl":return 3!==u.length?void Ae(e,0,0,0,1):(Ee(u,e),Re(t,e),e);default:return}}Ae(e,0,0,0,1)}}function Ee(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=De(t[1]),r=De(t[2]),o=r<=.5?r*(i+1):r+i-r*i,a=2*r-o;return Ae(e=e||[],Me(255*Ie(a,o,n+1/3)),Me(255*Ie(a,o,n)),Me(255*Ie(a,o,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function ze(t,e){var n=Ne(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,n[i]>255?n[i]=255:n[i]<0&&(n[i]=0);return Ge(n,4===n.length?"rgba":"rgb")}}function Be(t,e,n){if(e&&e.length&&t>=0&&t<=1){n=n||[];var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=e[r],s=e[o],l=i-r;return n[0]=Me(ke(a[0],s[0],l)),n[1]=Me(ke(a[1],s[1],l)),n[2]=Me(ke(a[2],s[2],l)),n[3]=Te(ke(a[3],s[3],l)),n}}var Fe=Be;function Ve(t,e,n){if(e&&e.length&&t>=0&&t<=1){var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=Ne(e[r]),s=Ne(e[o]),l=i-r,u=Ge([Me(ke(a[0],s[0],l)),Me(ke(a[1],s[1],l)),Me(ke(a[2],s[2],l)),Te(ke(a[3],s[3],l))],"rgba");return n?{color:u,leftIndex:r,rightIndex:o,value:i}:u}}var He=Ve;function Ge(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}}function We(t,e){var n=Ne(t);return n?(.299*n[0]+.587*n[1]+.114*n[2])*n[3]/255+(1-n[3])*e:0}var Ue=Object.freeze({__proto__:null,parse:Ne,lift:ze,toHex:function(t){var e=Ne(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)},fastLerp:Be,fastMapToColor:Fe,lerp:Ve,mapToColor:He,modifyHSL:function(t,e,n,i){var r=Ne(t);if(t)return r=function(t){if(t){var e,n,i=t[0]/255,r=t[1]/255,o=t[2]/255,a=Math.min(i,r,o),s=Math.max(i,r,o),l=s-a,u=(s+a)/2;if(0===l)e=0,n=0;else{n=u<.5?l/(s+a):l/(2-s-a);var h=((s-i)/6+l/2)/l,c=((s-r)/6+l/2)/l,p=((s-o)/6+l/2)/l;i===s?e=p-c:r===s?e=1/3+h-p:o===s&&(e=2/3+c-h),e<0&&(e+=1),e>1&&(e-=1)}var f=[360*e,n,u];return null!=t[3]&&f.push(t[3]),f}}(r),null!=e&&(r[0]=function(t){return(t=Math.round(t))<0?0:t>360?360:t}(e)),null!=n&&(r[1]=De(n)),null!=i&&(r[2]=De(i)),Ge(Ee(r),"rgba")},modifyAlpha:function(t,e){var n=Ne(t);if(n&&null!=e)return n[3]=Te(e),Ge(n,"rgba")},stringify:Ge,lum:We,random:function(){return"rgb("+Math.round(255*Math.random())+","+Math.round(255*Math.random())+","+Math.round(255*Math.random())+")"}}),Xe=Array.prototype.slice;function Ye(t,e,n){return(e-t)*n+t}function qe(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=Ye(e[o],n[o],i)}function je(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=e[o]+n[o]*i;return t}function Ze(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*i}return t}function Ke(t,e,n){var i=t,r=e;if(i.push&&r.push){var o=i.length,a=r.length;if(o!==a)if(o>a)i.length=a;else for(var s=o;s<a;s++)i.push(1===n?r[s]:Xe.call(r[s]));var l=i[0]&&i[0].length;for(s=0;s<i.length;s++)if(1===n)isNaN(i[s])&&(i[s]=r[s]);else for(var u=0;u<l;u++)isNaN(i[s][u])&&(i[s][u]=r[s][u])}}function $e(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}function Je(t,e,n,i,r,o,a){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*a+(-3*(e-n)-2*s-l)*o+s*r+e}function Qe(t,e,n,i,r,o,a,s){for(var l=e.length,u=0;u<l;u++)t[u]=Je(e[u],n[u],i[u],r[u],o,a,s)}function tn(t){if(P(t)){var e=t.length;if(P(t[0])){for(var n=[],i=0;i<e;i++)n.push(Xe.call(t[i]));return n}return Xe.call(t)}return t}function en(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}var nn,rn,on=[0,0,0,0],an=function(){function t(t){this.keyframes=[],this.maxTime=0,this.arrDim=0,this.interpolable=!0,this._needsSort=!1,this._isAllValueEqual=!0,this._lastFrame=0,this._lastFramePercent=0,this.propName=t}return t.prototype.isFinished=function(){return this._finished},t.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},t.prototype.needsAnimate=function(){return!this._isAllValueEqual&&this.keyframes.length>=2&&this.interpolable&&this.maxTime>0},t.prototype.getAdditiveTrack=function(){return this._additiveTrack},t.prototype.addKeyframe=function(t,e){t>=this.maxTime?this.maxTime=t:this._needsSort=!0;var n=this.keyframes,i=n.length;if(this.interpolable)if(P(e)){var r=function(t){return P(t&&t[0])?2:1}(e);if(i>0&&this.arrDim!==r)return void(this.interpolable=!1);if(1===r&&"number"!=typeof e[0]||2===r&&"number"!=typeof e[0][0])return void(this.interpolable=!1);if(i>0){var o=n[i-1];this._isAllValueEqual&&(1===r&&$e(e,o.value)||(this._isAllValueEqual=!1))}this.arrDim=r}else{if(this.arrDim>0)return void(this.interpolable=!1);if("string"==typeof e){var a=Ne(e);a?(e=a,this.isValueColor=!0):this.interpolable=!1}else if("number"!=typeof e||isNaN(e))return void(this.interpolable=!1);if(this._isAllValueEqual&&i>0){o=n[i-1];(this.isValueColor&&!$e(o.value,e)||o.value!==e)&&(this._isAllValueEqual=!1)}}var s={time:t,value:e,percent:0};return this.keyframes.push(s),s},t.prototype.prepare=function(t){var e=this.keyframes;this._needsSort&&e.sort((function(t,e){return t.time-e.time}));for(var n=this.arrDim,i=e.length,r=e[i-1],o=0;o<i;o++)e[o].percent=e[o].time/this.maxTime,n>0&&o!==i-1&&Ke(e[o].value,r.value,n);if(t&&this.needsAnimate()&&t.needsAnimate()&&n===t.arrDim&&this.isValueColor===t.isValueColor&&!t._finished){this._additiveTrack=t;var a=e[0].value;for(o=0;o<i;o++)0===n?this.isValueColor?e[o].additiveValue=je([],e[o].value,a,-1):e[o].additiveValue=e[o].value-a:1===n?e[o].additiveValue=je([],e[o].value,a,-1):2===n&&(e[o].additiveValue=Ze([],e[o].value,a,-1))}},t.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,i=null!=this._additiveTrack,r=i?"additiveValue":"value",o=this.keyframes,a=this.keyframes.length,s=this.propName,l=this.arrDim,u=this.isValueColor;if(e<0)n=0;else if(e<this._lastFramePercent){for(n=Math.min(this._lastFrame+1,a-1);n>=0&&!(o[n].percent<=e);n--);n=Math.min(n,a-2)}else{for(n=this._lastFrame;n<a&&!(o[n].percent>e);n++);n=Math.min(n-1,a-2)}var h=o[n+1],c=o[n];if(c&&h){this._lastFrame=n,this._lastFramePercent=e;var p=h.percent-c.percent;if(0!==p){var f=(e-c.percent)/p,d=i?this._additiveValue:u?on:t[s];if((l>0||u)&&!d&&(d=this._additiveValue=[]),this.useSpline){var g=o[n][r],y=o[0===n?n:n-1][r],v=o[n>a-2?a-1:n+1][r],m=o[n>a-3?a-1:n+2][r];if(l>0)1===l?Qe(d,y,g,v,m,f,f*f,f*f*f):function(t,e,n,i,r,o,a,s){for(var l=e.length,u=e[0].length,h=0;h<l;h++){t[h]||(t[1]=[]);for(var c=0;c<u;c++)t[h][c]=Je(e[h][c],n[h][c],i[h][c],r[h][c],o,a,s)}}(d,y,g,v,m,f,f*f,f*f*f);else if(u)Qe(d,y,g,v,m,f,f*f,f*f*f),i||(t[s]=en(d));else{var _=void 0;_=this.interpolable?Je(y,g,v,m,f,f*f,f*f*f):v,i?this._additiveValue=_:t[s]=_}}else if(l>0)1===l?qe(d,c[r],h[r],f):function(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=Ye(e[a][s],n[a][s],i)}}(d,c[r],h[r],f);else if(u)qe(d,c[r],h[r],f),i||(t[s]=en(d));else{_=void 0;_=this.interpolable?Ye(c[r],h[r],f):function(t,e,n){return n>.5?e:t}(c[r],h[r],f),i?this._additiveValue=_:t[s]=_}i&&this._addToTarget(t)}}}},t.prototype._addToTarget=function(t){var e=this.arrDim,n=this.propName,i=this._additiveValue;0===e?this.isValueColor?(Ne(t[n],on),je(on,on,i,1),t[n]=en(on)):t[n]=t[n]+i:1===e?je(t[n],t[n],i,1):2===e&&Ze(t[n],t[n],i,1)},t}(),sn=function(){function t(t,e,n){this._tracks={},this._trackKeys=[],this._delay=0,this._maxTime=0,this._paused=!1,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&n?w("Can' use additive animation on looped animation."):this._additiveAnimators=n}return t.prototype.getTarget=function(){return this._target},t.prototype.changeTarget=function(t){this._target=t},t.prototype.when=function(t,e){return this.whenWithKeys(t,e,E(e))},t.prototype.whenWithKeys=function(t,e,n){for(var i=this._tracks,r=0;r<n.length;r++){var o=n[r],a=i[o];if(!a){a=i[o]=new an(o);var s=void 0,l=this._getAdditiveTrack(o);if(l){var u=l.keyframes[l.keyframes.length-1];s=u&&u.value,l.isValueColor&&s&&(s=en(s))}else s=this._target[o];if(null==s)continue;0!==t&&a.addKeyframe(0,tn(s)),this._trackKeys.push(o)}a.addKeyframe(t,tn(e[o]))}return this._maxTime=Math.max(this._maxTime,t),this},t.prototype.pause=function(){this._clip.pause(),this._paused=!0},t.prototype.resume=function(){this._clip.resume(),this._paused=!1},t.prototype.isPaused=function(){return!!this._paused},t.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},t.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},t.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},t.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var r=n[i].getTrack(t);r&&(e=r)}return e},t.prototype.start=function(t,e){if(!(this._started>0)){this._started=1;for(var n=this,i=[],r=0;r<this._trackKeys.length;r++){var o=this._trackKeys[r],a=this._tracks[o],s=this._getAdditiveTrack(o),l=a.keyframes;if(a.prepare(s),a.needsAnimate())i.push(a);else if(!a.interpolable){var u=l[l.length-1];u&&(n._target[a.propName]=u.value)}}if(i.length||e){var h=new _e({life:this._maxTime,loop:this._loop,delay:this._delay,onframe:function(t){n._started=2;var e=n._additiveAnimators;if(e){for(var r=!1,o=0;o<e.length;o++)if(e[o]._clip){r=!0;break}r||(n._additiveAnimators=null)}for(o=0;o<i.length;o++)i[o].step(n._target,t);var a=n._onframeCbs;if(a)for(o=0;o<a.length;o++)a[o](n._target,t)},ondestroy:function(){n._doneCallback()}});this._clip=h,this.animation&&this.animation.addClip(h),t&&"spline"!==t&&(h.easing=t)}else this._doneCallback();return this}},t.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},t.prototype.delay=function(t){return this._delay=t,this},t.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},t.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},t.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},t.prototype.getClip=function(){return this._clip},t.prototype.getTrack=function(t){return this._tracks[t]},t.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,r=0;r<t.length;r++){var o=n[t[r]];o&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}var a=!0;for(r=0;r<i.length;r++)if(!n[i[r]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},t.prototype.saveFinalToTarget=function(t,e){if(t){e=e||this._trackKeys;for(var n=0;n<e.length;n++){var i=e[n],r=this._tracks[i];if(r&&!r.isFinished()){var o=r.keyframes,a=o[o.length-1];if(a){var s=tn(a.value);r.isValueColor&&(s=en(s)),t[i]=s}}}}},t.prototype.__changeFinalValue=function(t,e){e=e||E(t);for(var n=0;n<e.length;n++){var i=e[n],r=this._tracks[i];if(r){var o=r.keyframes;if(o.length>1){var a=o.pop();r.addKeyframe(a.time,t[i]),r.prepare(r.getAdditiveTrack())}}}},t}(),ln=function(t){function e(e){var n=t.call(this)||this;return n._running=!1,n._time=0,n._pausedTime=0,n._pauseStart=0,n._paused=!1,e=e||{},n.stage=e.stage||{},n.onframe=e.onframe||function(){},n}return n(e,t),e.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._clipsHead?(this._clipsTail.next=t,t.prev=this._clipsTail,t.next=null,this._clipsTail=t):this._clipsHead=this._clipsTail=t,t.animation=this},e.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},e.prototype.removeClip=function(t){if(t.animation){var e=t.prev,n=t.next;e?e.next=n:this._clipsHead=n,n?n.prev=e:this._clipsTail=e,t.next=t.prev=t.animation=null}},e.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},e.prototype.update=function(t){for(var e=(new Date).getTime()-this._pausedTime,n=e-this._time,i=this._clipsHead;i;){var r=i.next;i.step(e,n)?(i.ondestroy&&i.ondestroy(),this.removeClip(i),i=r):i=r}this._time=e,t||(this.onframe(n),this.trigger("frame",n),this.stage.update&&this.stage.update())},e.prototype._startLoop=function(){var t=this;this._running=!0,ve((function e(){t._running&&(ve(e),!t._paused&&t.update())}))},e.prototype.start=function(){this._running||(this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop())},e.prototype.stop=function(){this._running=!1},e.prototype.pause=function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},e.prototype.resume=function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},e.prototype.clear=function(){for(var t=this._clipsHead;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._clipsHead=this._clipsTail=null},e.prototype.isFinished=function(){return null==this._clipsHead},e.prototype.animate=function(t,e){e=e||{},this.start();var n=new sn(t,e.loop);return this.addAnimator(n),n},e}(Et),un=a.domSupported,hn=(rn={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:nn=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:O(nn,(function(t){var e=t.replace("mouse","pointer");return rn.hasOwnProperty(e)?e:t}))}),cn=["mousemove","mouseup"],pn=["pointermove","pointerup"],fn=!1;function dn(t){var e=t.pointerType;return"pen"===e||"touch"===e}function gn(t){t&&(t.zrByTouch=!0)}function yn(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}var vn=function(t,e){this.stopPropagation=ft,this.stopImmediatePropagation=ft,this.preventDefault=ft,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},mn={mousedown:function(t){t=jt(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=jt(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=jt(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){yn(this,(t=jt(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){fn=!0,t=jt(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){fn||(t=jt(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){gn(t=jt(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),mn.mousemove.call(this,t),mn.mousedown.call(this,t)},touchmove:function(t){gn(t=jt(this.dom,t)),this.handler.processGesture(t,"change"),mn.mousemove.call(this,t)},touchend:function(t){gn(t=jt(this.dom,t)),this.handler.processGesture(t,"end"),mn.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&mn.click.call(this,t)},pointerdown:function(t){mn.mousedown.call(this,t)},pointermove:function(t){dn(t)||mn.mousemove.call(this,t)},pointerup:function(t){mn.mouseup.call(this,t)},pointerout:function(t){dn(t)||mn.mouseout.call(this,t)}};L(["click","dblclick","contextmenu"],(function(t){mn[t]=function(e){e=jt(this.dom,e),this.trigger(t,e)}}));var _n={pointermove:function(t){dn(t)||_n.mousemove.call(this,t)},pointerup:function(t){_n.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function xn(t,e){var n=e.domHandlers;a.pointerEventsSupported?L(hn.pointer,(function(i){wn(e,i,(function(e){n[i].call(t,e)}))})):(a.touchEventsSupported&&L(hn.touch,(function(i){wn(e,i,(function(r){n[i].call(t,r),function(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout((function(){t.touching=!1,t.touchTimer=null}),700)}(e)}))})),L(hn.mouse,(function(i){wn(e,i,(function(r){r=qt(r),e.touching||n[i].call(t,r)}))})))}function bn(t,e){function n(n){wn(e,n,(function(i){i=qt(i),yn(t,i.target)||(i=function(t,e){return jt(t.dom,new vn(t,e),!0)}(t,i),e.domHandlers[n].call(t,i))}),{capture:!0})}a.pointerEventsSupported?L(pn,n):a.touchEventsSupported||L(cn,n)}function wn(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,function(t,e,n,i){Gt?t.addEventListener(e,n,i):t.attachEvent("on"+e,n)}(t.domTarget,e,n,i)}function Sn(t){var e,n,i,r,o=t.mounted;for(var a in o)o.hasOwnProperty(a)&&(e=t.domTarget,n=a,i=o[a],r=t.listenerOpts[a],Gt?e.removeEventListener(n,i,r):e.detachEvent("on"+n,i));t.mounted={}}var Mn=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},Tn=function(t){function e(e,n){var i=t.call(this)||this;return i.__pointerCapturing=!1,i.dom=e,i.painterRoot=n,i._localHandlerScope=new Mn(e,mn),un&&(i._globalHandlerScope=new Mn(document,_n)),xn(i,i._localHandlerScope),i}return n(e,t),e.prototype.dispose=function(){Sn(this._localHandlerScope),un&&Sn(this._globalHandlerScope)},e.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},e.prototype.__togglePointerCapture=function(t){if(this.__mayPointerCapture=null,un&&+this.__pointerCapturing^+t){this.__pointerCapturing=t;var e=this._globalHandlerScope;t?bn(this,e):Sn(e)}},e}(Et),Cn=1;"undefined"!=typeof window&&(Cn=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var Dn=Cn,In="#333",kn="#ccc";function An(){return[1,0,0,1,0,0]}function Pn(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function Ln(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function On(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],l=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=l,t}function Rn(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function Nn(t,e,n){var i=e[0],r=e[2],o=e[4],a=e[1],s=e[3],l=e[5],u=Math.sin(n),h=Math.cos(n);return t[0]=i*h+a*u,t[1]=-i*u+a*h,t[2]=r*h+s*u,t[3]=-r*u+h*s,t[4]=h*o+u*l,t[5]=h*l-u*o,t}function En(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t}function zn(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],s=e[5],l=n*a-o*i;return l?(l=1/l,t[0]=a*l,t[1]=-o*l,t[2]=-i*l,t[3]=n*l,t[4]=(i*s-a*r)*l,t[5]=(o*r-n*s)*l,t):null}var Bn=Object.freeze({__proto__:null,create:An,identity:Pn,copy:Ln,mul:On,translate:Rn,rotate:Nn,scale:En,invert:zn,clone:function(t){var e=[1,0,0,1,0,0];return Ln(e,t),e}}),Fn=Pn,Vn=5e-5;function Hn(t){return t>Vn||t<-5e-5}var Gn,Wn,Un=[],Xn=[],Yn=[1,0,0,1,0,0],qn=Math.abs,jn=function(){function t(){}return t.prototype.getLocalTransform=function(e){return t.getLocalTransform(this,e)},t.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},t.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},t.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},t.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},t.prototype.needLocalTransform=function(){return Hn(this.rotation)||Hn(this.x)||Hn(this.y)||Hn(this.scaleX-1)||Hn(this.scaleY-1)},t.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;e||t?(n=n||[1,0,0,1,0,0],e?this.getLocalTransform(n):Fn(n),t&&(e?On(n,t,n):Ln(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)):n&&Fn(n)},t.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(null!=e&&1!==e){this.getGlobalScale(Un);var n=Un[0]<0?-1:1,i=Un[1]<0?-1:1,r=((Un[0]-n)*e+n)/Un[0]||0,o=((Un[1]-i)*e+i)/Un[1]||0;t[0]*=r,t[1]*=r,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||[1,0,0,1,0,0],zn(this.invTransform,t)},t.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},t.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=Math.atan2(t[1],t[0]),r=Math.PI/2+i-Math.atan2(t[3],t[2]);n=Math.sqrt(n)*Math.cos(r),e=Math.sqrt(e),this.skewX=r,this.skewY=0,this.rotation=-i,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=n,this.originX=0,this.originY=0}},t.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(On(Xn,t.invTransform,e),e=Xn);var n=this.originX,i=this.originY;(n||i)&&(Yn[4]=n,Yn[5]=i,On(Xn,e,Yn),Xn[4]-=n,Xn[5]-=i,e=Xn),this.setLocalTransform(e)}},t.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},t.prototype.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&At(n,n,i),n},t.prototype.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&At(n,n,i),n},t.prototype.getLineScale=function(){var t=this.transform;return t&&qn(t[0]-1)>1e-10&&qn(t[3]-1)>1e-10?Math.sqrt(qn(t[0]*t[3]-t[2]*t[1])):1},t.prototype.copyTransform=function(t){for(var e=0;e<Zn.length;e++){var n=Zn[e];this[n]=t[n]}},t.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,r=t.scaleX,o=t.scaleY,a=t.rotation||0,s=t.x,l=t.y,u=t.skewX?Math.tan(t.skewX):0,h=t.skewY?Math.tan(-t.skewY):0;return n||i?(e[4]=-n*r-u*i*o,e[5]=-i*o-h*n*r):e[4]=e[5]=0,e[0]=r,e[3]=o,e[1]=h*r,e[2]=u*o,a&&Nn(e,e,a),e[4]+=n+s,e[5]+=i+l,e},t.initDefaultProps=function(){var e=t.prototype;e.x=0,e.y=0,e.scaleX=1,e.scaleY=1,e.originX=0,e.originY=0,e.skewX=0,e.skewY=0,e.rotation=0,e.globalScaleRatio=1}(),t}(),Zn=["x","y","originX","originY","rotation","scaleX","scaleY","skewX","skewY"],Kn=function(){function t(t,e){this.x=t||0,this.y=e||0}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},t.prototype.clone=function(){return new t(this.x,this.y)},t.prototype.set=function(t,e){return this.x=t,this.y=e,this},t.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},t.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},t.prototype.scale=function(t){this.x*=t,this.y*=t},t.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},t.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},t.prototype.dot=function(t){return this.x*t.x+this.y*t.y},t.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},t.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},t.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},t.prototype.distance=function(t){var e=this.x-t.x,n=this.y-t.y;return Math.sqrt(e*e+n*n)},t.prototype.distanceSquare=function(t){var e=this.x-t.x,n=this.y-t.y;return e*e+n*n},t.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},t.prototype.transform=function(t){if(t){var e=this.x,n=this.y;return this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this}},t.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},t.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},t.set=function(t,e,n){t.x=e,t.y=n},t.copy=function(t,e){t.x=e.x,t.y=e.y},t.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.lenSquare=function(t){return t.x*t.x+t.y*t.y},t.dot=function(t,e){return t.x*e.x+t.y*e.y},t.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},t.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},t.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},t.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},t.lerp=function(t,e,n,i){var r=1-i;t.x=r*e.x+i*n.x,t.y=r*e.y+i*n.y},t}(),$n=Math.min,Jn=Math.max,Qn=new Kn,ti=new Kn,ei=new Kn,ni=new Kn,ii=new Kn,ri=new Kn,oi=function(){function t(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}return t.prototype.union=function(t){var e=$n(t.x,this.x),n=$n(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=Jn(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=Jn(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},t.prototype.applyTransform=function(e){t.applyTransform(this,this,e)},t.prototype.calculateTransform=function(t){var e=this,n=t.width/e.width,i=t.height/e.height,r=[1,0,0,1,0,0];return Rn(r,r,[-e.x,-e.y]),En(r,r,[n,i]),Rn(r,r,[t.x,t.y]),r},t.prototype.intersect=function(e,n){if(!e)return!1;e instanceof t||(e=t.create(e));var i=this,r=i.x,o=i.x+i.width,a=i.y,s=i.y+i.height,l=e.x,u=e.x+e.width,h=e.y,c=e.y+e.height,p=!(o<l||u<r||s<h||c<a);if(n){var f=1/0,d=0,g=Math.abs(o-l),y=Math.abs(u-r),v=Math.abs(s-h),m=Math.abs(c-a),_=Math.min(g,y),x=Math.min(v,m);o<l||u<r?_>d&&(d=_,g<y?Kn.set(ri,-g,0):Kn.set(ri,y,0)):_<f&&(f=_,g<y?Kn.set(ii,g,0):Kn.set(ii,-y,0)),s<h||c<a?x>d&&(d=x,v<m?Kn.set(ri,0,-v):Kn.set(ri,0,m)):_<f&&(f=_,v<m?Kn.set(ii,0,v):Kn.set(ii,0,-m))}return n&&Kn.copy(n,p?ii:ri),p},t.prototype.contain=function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},t.prototype.clone=function(){return new t(this.x,this.y,this.width,this.height)},t.prototype.copy=function(e){t.copy(this,e)},t.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},t.prototype.isZero=function(){return 0===this.width||0===this.height},t.create=function(e){return new t(e.x,e.y,e.width,e.height)},t.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},t.applyTransform=function(e,n,i){if(i){if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var r=i[0],o=i[3],a=i[4],s=i[5];return e.x=n.x*r+a,e.y=n.y*o+s,e.width=n.width*r,e.height=n.height*o,e.width<0&&(e.x+=e.width,e.width=-e.width),void(e.height<0&&(e.y+=e.height,e.height=-e.height))}Qn.x=ei.x=n.x,Qn.y=ni.y=n.y,ti.x=ni.x=n.x+n.width,ti.y=ei.y=n.y+n.height,Qn.transform(i),ni.transform(i),ti.transform(i),ei.transform(i),e.x=$n(Qn.x,ti.x,ei.x,ni.x),e.y=$n(Qn.y,ti.y,ei.y,ni.y);var l=Jn(Qn.x,ti.x,ei.x,ni.x),u=Jn(Qn.y,ti.y,ei.y,ni.y);e.width=l-e.x,e.height=u-e.y}else e!==n&&t.copy(e,n)},t}(),ai={},si="12px sans-serif";var li={measureText:function(t,e){return Gn||(Gn=D().getContext("2d")),Wn!==e&&(Wn=Gn.font=e||si),Gn.measureText(t)}};function ui(t,e){var n=ai[e=e||si];n||(n=ai[e]=new we(500));var i=n.get(t);return null==i&&(i=li.measureText(t,e).width,n.put(t,i)),i}function hi(t,e,n,i){var r=ui(t,e),o=di(e),a=pi(0,r,n),s=fi(0,o,i);return new oi(a,s,r,o)}function ci(t,e,n,i){var r=((t||"")+"").split("\n");if(1===r.length)return hi(r[0],e,n,i);for(var o=new oi(0,0,0,0),a=0;a<r.length;a++){var s=hi(r[a],e,n,i);0===a?o.copy(s):o.union(s)}return o}function pi(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function fi(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function di(t){return ui("国",t)}function gi(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function yi(t,e,n){var i=e.position||"inside",r=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,l=n.x,u=n.y,h="left",c="top";if(i instanceof Array)l+=gi(i[0],n.width),u+=gi(i[1],n.height),h=null,c=null;else switch(i){case"left":l-=r,u+=s,h="right",c="middle";break;case"right":l+=r+a,u+=s,c="middle";break;case"top":l+=a/2,u-=r,h="center",c="bottom";break;case"bottom":l+=a/2,u+=o+r,h="center";break;case"inside":l+=a/2,u+=s,h="center",c="middle";break;case"insideLeft":l+=r,u+=s,c="middle";break;case"insideRight":l+=a-r,u+=s,h="right",c="middle";break;case"insideTop":l+=a/2,u+=r,h="center";break;case"insideBottom":l+=a/2,u+=o-r,h="center",c="bottom";break;case"insideTopLeft":l+=r,u+=r;break;case"insideTopRight":l+=a-r,u+=r,h="right";break;case"insideBottomLeft":l+=r,u+=o-r,c="bottom";break;case"insideBottomRight":l+=a-r,u+=o-r,h="right",c="bottom"}return(t=t||{}).x=l,t.y=u,t.align=h,t.verticalAlign=c,t}var vi="__zr_normal__",mi=["x","y","scaleX","scaleY","originX","originY","rotation","ignore"],_i={x:!0,y:!0,scaleX:!0,scaleY:!0,originX:!0,originY:!0,rotation:!0,ignore:!1},xi={},bi=new oi(0,0,0,0),wi=function(){function t(t){this.id=b(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return t.prototype._init=function(t){this.attr(t)},t.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},t.prototype.beforeUpdate=function(){},t.prototype.afterUpdate=function(){},t.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},t.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var n=this.textConfig,i=n.local,r=e.innerTransformable,o=void 0,a=void 0,s=!1;r.parent=i?this:null;var l=!1;if(r.copyTransform(e),null!=n.position){var u=bi;n.layoutRect?u.copy(n.layoutRect):u.copy(this.getBoundingRect()),i||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(xi,n,u):yi(xi,n,u),r.x=xi.x,r.y=xi.y,o=xi.align,a=xi.verticalAlign;var h=n.origin;if(h&&null!=n.rotation){var c=void 0,p=void 0;"center"===h?(c=.5*u.width,p=.5*u.height):(c=gi(h[0],u.width),p=gi(h[1],u.height)),l=!0,r.originX=-r.x+c+(i?0:u.x),r.originY=-r.y+p+(i?0:u.y)}}null!=n.rotation&&(r.rotation=n.rotation);var f=n.offset;f&&(r.x+=f[0],r.y+=f[1],l||(r.originX=-f[0],r.originY=-f[1]));var d=null==n.inside?"string"==typeof n.position&&n.position.indexOf("inside")>=0:n.inside,g=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),y=void 0,v=void 0,m=void 0;d&&this.canBeInsideText()?(y=n.insideFill,v=n.insideStroke,null!=y&&"auto"!==y||(y=this.getInsideTextFill()),null!=v&&"auto"!==v||(v=this.getInsideTextStroke(y),m=!0)):(y=n.outsideFill,v=n.outsideStroke,null!=y&&"auto"!==y||(y=this.getOutsideFill()),null!=v&&"auto"!==v||(v=this.getOutsideStroke(y),m=!0)),(y=y||"#000")===g.fill&&v===g.stroke&&m===g.autoStroke&&o===g.align&&a===g.verticalAlign||(s=!0,g.fill=y,g.stroke=v,g.autoStroke=m,g.align=o,g.verticalAlign=a,e.setDefaultTextStyle(g)),e.__dirty|=1,s&&e.dirtyStyle(!0)}},t.prototype.canBeInsideText=function(){return!0},t.prototype.getInsideTextFill=function(){return"#fff"},t.prototype.getInsideTextStroke=function(t){return"#000"},t.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?kn:In},t.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&Ne(e);n||(n=[255,255,255,1]);for(var i=n[3],r=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(r?0:255)*(1-i);return n[3]=1,Ge(n,"rgba")},t.prototype.traverse=function(t,e){},t.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},T(this.extra,e)):this[t]=e},t.prototype.hide=function(){this.ignore=!0,this.markRedraw()},t.prototype.show=function(){this.ignore=!1,this.markRedraw()},t.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(U(t))for(var n=E(t),i=0;i<n.length;i++){var r=n[i];this.attrKV(r,t[r])}return this.markRedraw(),this},t.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],r=i.__fromStateTransition;if(!r||r===vi){var o=i.targetName,a=o?e[o]:e;i.saveFinalToTarget(a)}}},t.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,mi)},t.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null==t[r]||r in e||(e[r]=this[r])}},t.prototype.hasState=function(){return this.currentStates.length>0},t.prototype.getState=function(t){return this.states[t]},t.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},t.prototype.clearStates=function(t){this.useState(vi,!1,t)},t.prototype.useState=function(t,e,n,i){var r=t===vi;if(this.hasState()||!r){var o=this.currentStates,a=this.stateTransition;if(!(I(o,t)>=0)||!e&&1!==o.length){var s;if(this.stateProxy&&!r&&(s=this.stateProxy(t)),s||(s=this.states&&this.states[t]),s||r){r||this.saveCurrentToNormalState(s);var l=!!(s&&s.hoverLayer||i);l&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,s,this._normalState,e,!n&&!this.__inHover&&a&&a.duration>0,a);var u=this._textContent,h=this._textGuide;return u&&u.useState(t,e,n,l),h&&h.useState(t,e,n,l),r?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!l&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2),s}w("State "+t+" not exists.")}}},t.prototype.useStates=function(t,e,n){if(t.length){var i=[],r=this.currentStates,o=t.length,a=o===r.length;if(a)for(var s=0;s<o;s++)if(t[s]!==r[s]){a=!1;break}if(a)return;for(s=0;s<o;s++){var l=t[s],u=void 0;this.stateProxy&&(u=this.stateProxy(l,t)),u||(u=this.states[l]),u&&i.push(u)}var h=i[o-1],c=!!(h&&h.hoverLayer||n);c&&this._toggleHoverLayerFlag(!0);var p=this._mergeStates(i),f=this.stateTransition;this.saveCurrentToNormalState(p),this._applyStateObj(t.join(","),p,this._normalState,!1,!e&&!this.__inHover&&f&&f.duration>0,f);var d=this._textContent,g=this._textGuide;d&&d.useStates(t,e,c),g&&g.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2)}else this.clearStates()},t.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},t.prototype.removeState=function(t){var e=I(this.currentStates,t);if(e>=0){var n=this.currentStates.slice();n.splice(e,1),this.useStates(n)}},t.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),r=I(i,t),o=I(i,e)>=0;r>=0?o?i.splice(r,1):i[r]=e:n&&!o&&i.push(e),this.useStates(i)},t.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},t.prototype._mergeStates=function(t){for(var e,n={},i=0;i<t.length;i++){var r=t[i];T(n,r),r.textConfig&&T(e=e||{},r.textConfig)}return e&&(n.textConfig=e),n},t.prototype._applyStateObj=function(t,e,n,i,r,o){var a=!(e&&i);e&&e.textConfig?(this.textConfig=T({},i?this.textConfig:n.textConfig),T(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig);for(var s={},l=!1,u=0;u<mi.length;u++){var h=mi[u],c=r&&_i[h];e&&null!=e[h]?c?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(c?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!r)for(u=0;u<this.animators.length;u++){var p=this.animators[u],f=p.targetName;p.__changeFinalValue(f?(e||n)[f]:e||n)}l&&this._transitionState(t,s,o)},t.prototype._attachComponent=function(t){if(t.__zr&&!t.__hostTarget)throw new Error("Text element has been added to zrender.");if(t===this)throw new Error("Recursive component attachment.");var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this},t.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},t.prototype.getClipPath=function(){return this._clipPath},t.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},t.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},t.prototype.getTextContent=function(){return this._textContent},t.prototype.setTextContent=function(t){var e=this._textContent;if(e!==t){if(e&&e!==t&&this.removeTextContent(),t.__zr&&!t.__hostTarget)throw new Error("Text element has been added to zrender.");t.innerTransformable=new jn,this._attachComponent(t),this._textContent=t,this.markRedraw()}},t.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),T(this.textConfig,t),this.markRedraw()},t.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},t.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},t.prototype.getTextGuideLine=function(){return this._textGuide},t.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},t.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},t.prototype.markRedraw=function(){this.__dirty|=1;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},t.prototype.dirty=function(){this.markRedraw()},t.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},t.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},t.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},t.prototype.animate=function(t,e){var n=t?this[t]:this;if(n){var i=new sn(n,e);return this.addAnimator(i,t),i}w('Property "'+t+'" is not existed in element '+this.id)},t.prototype.addAnimator=function(t,e){var n=this.__zr,i=this;t.during((function(){i.updateDuringAnimation(e)})).done((function(){var e=i.animators,n=I(e,t);n>=0&&e.splice(n,1)})),this.animators.push(t),n&&n.animation.addAnimator(t),n&&n.wakeUp()},t.prototype.updateDuringAnimation=function(t){this.markRedraw()},t.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,r=[],o=0;o<i;o++){var a=n[o];t&&t!==a.scope?r.push(a):a.stop(e)}return this.animators=r,this},t.prototype.animateTo=function(t,e,n){Si(this,t,e,n)},t.prototype.animateFrom=function(t,e,n){Si(this,t,e,n,!0)},t.prototype._transitionState=function(t,e,n,i){for(var r=Si(this,e,n,i),o=0;o<r.length;o++)r[o].__fromStateTransition=t},t.prototype.getBoundingRect=function(){return null},t.prototype.getPaintRect=function(){return null},t.initDefaultProps=function(){var e=t.prototype;e.type="element",e.name="",e.ignore=!1,e.silent=!1,e.isGroup=!1,e.draggable=!1,e.dragging=!1,e.ignoreClip=!1,e.__inHover=!1,e.__dirty=1;var n={};function i(t,e,i){n[t+e+i]||(console.warn("DEPRECATED: '"+t+"' has been deprecated. use '"+e+"', '"+i+"' instead"),n[t+e+i]=!0)}function r(t,n,r,o){function a(t,e){Object.defineProperty(e,0,{get:function(){return t[r]},set:function(e){t[r]=e}}),Object.defineProperty(e,1,{get:function(){return t[o]},set:function(e){t[o]=e}})}Object.defineProperty(e,t,{get:function(){(i(t,r,o),this[n])||a(this,this[n]=[]);return this[n]},set:function(e){i(t,r,o),this[r]=e[0],this[o]=e[1],this[n]=e,a(this,e)}})}Object.defineProperty&&(!a.browser.ie||a.browser.version>8)&&(r("position","_legacyPos","x","y"),r("scale","_legacyScale","scaleX","scaleY"),r("origin","_legacyOrigin","originX","originY"))}(),t}();function Si(t,e,n,i,r){var o=[];Ci(t,"",t,e,n=n||{},i,o,r);var a=o.length,s=!1,l=n.done,u=n.aborted,h=function(){s=!0,--a<=0&&(s?l&&l():u&&u())},c=function(){--a<=0&&(s?l&&l():u&&u())};a||l&&l(),o.length>0&&n.during&&o[0].during((function(t,e){n.during(e)}));for(var p=0;p<o.length;p++){var f=o[p];h&&f.done(h),c&&f.aborted(c),f.start(n.easing,n.force)}return o}function Mi(t,e,n){for(var i=0;i<n;i++)t[i]=e[i]}function Ti(t,e,n){if(P(e[n]))if(P(t[n])||(t[n]=[]),Y(e[n])){var i=e[n].length;t[n].length!==i&&(t[n]=new e[n].constructor(i),Mi(t[n],e[n],i))}else{var r=e[n],o=t[n],a=r.length;if(P(r[0]))for(var s=r[0].length,l=0;l<a;l++)o[l]?Mi(o[l],r[l],s):o[l]=Array.prototype.slice.call(r[l]);else Mi(o,r,a);o.length=r.length}else t[n]=e[n]}function Ci(t,e,n,i,r,o,a,s){for(var l=[],u=[],h=E(i),c=r.duration,p=r.delay,f=r.additive,d=r.setToFinal,g=!U(o),y=0;y<h.length;y++){if(null!=n[T=h[y]]&&null!=i[T]&&(g||o[T]))if(U(i[T])&&!P(i[T])){if(e){s||(n[T]=i[T],t.updateDuringAnimation(e));continue}Ci(t,T,n[T],i[T],r,o&&o[T],a,s)}else l.push(T),u.push(T);else s||(n[T]=i[T],t.updateDuringAnimation(e),u.push(T))}var v=l.length;if(v>0||r.force&&!a.length){for(var m=t.animators,_=[],x=0;x<m.length;x++)m[x].targetName===e&&_.push(m[x]);if(!f&&_.length)for(x=0;x<_.length;x++){if(_[x].stopTracks(u)){var b=I(m,_[x]);m.splice(b,1)}}var w=void 0,S=void 0,M=void 0;if(s){S={},d&&(w={});for(x=0;x<v;x++){S[T=l[x]]=n[T],d?w[T]=i[T]:n[T]=i[T]}}else if(d){M={};for(x=0;x<v;x++){var T;M[T=l[x]]=tn(n[T]),Ti(n,i,T)}}var C=new sn(n,!1,f?_:null);C.targetName=e,r.scope&&(C.scope=r.scope),d&&w&&C.whenWithKeys(0,w,l),M&&C.whenWithKeys(0,M,l),C.whenWithKeys(null==c?500:c,s?S:i,l).delay(p||0),t.addAnimator(C,e),a.push(C)}}A(wi,Et),A(wi,jn);var Di=function(t){function e(e){var n=t.call(this)||this;return n.isGroup=!0,n._children=[],n.attr(e),n}return n(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.children=function(){return this._children.slice()},e.prototype.childAt=function(t){return this._children[t]},e.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},e.prototype.childCount=function(){return this._children.length},e.prototype.add=function(t){if(t&&(t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),t.__hostTarget))throw"This elemenet has been used as an attachment";return this},e.prototype.addBefore=function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,i=n.indexOf(e);i>=0&&(n.splice(i,0,t),this._doAdd(t))}return this},e.prototype.replace=function(t,e){var n=I(this._children,t);return n>=0&&this.replaceAt(e,n),this},e.prototype.replaceAt=function(t,e){var n=this._children,i=n[e];if(t&&t!==this&&t.parent!==this&&t!==i){n[e]=t,i.parent=null;var r=this.__zr;r&&i.removeSelfFromZr(r),this._doAdd(t)}return this},e.prototype._doAdd=function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},e.prototype.remove=function(t){var e=this.__zr,n=this._children,i=I(n,t);return i<0||(n.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},e.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var i=t[n];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},e.prototype.eachChild=function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},e.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n],r=t.call(e,i);i.isGroup&&!r&&i.traverse(t,e)}return this},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++){this._children[n].addSelfToZr(e)}},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++){this._children[n].removeSelfFromZr(e)}},e.prototype.getBoundingRect=function(t){for(var e=new oi(0,0,0,0),n=t||this._children,i=[],r=null,o=0;o<n.length;o++){var a=n[o];if(!a.ignore&&!a.invisible){var s=a.getBoundingRect(),l=a.getLocalTransform(i);l?(oi.applyTransform(e,s,l),(r=r||e.clone()).union(e)):(r=r||s.clone()).union(s)}}return r||e},e}(wi);Di.prototype.type="group";
/*!
    * ZRender, a high performance 2d drawing library.
    *
    * Copyright (c) 2013, Baidu Inc.
    * All rights reserved.
    *
    * LICENSE
    * https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
    */
var Ii=!a.canvasSupported,ki={},Ai={};var Pi=function(){function t(t,e,n){var i=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t;var r=new ye,o=n.renderer||"canvas";if(Ii)throw new Error("IE8 support has been dropped since 5.0");if(ki[o]||(o=E(ki)[0]),!ki[o])throw new Error("Renderer '"+o+"' is not imported. Please import it first.");n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect;var s=new ki[o](e,r,n,t);this.storage=r,this.painter=s;var l=a.node||a.worker?null:new Tn(s.getViewportRoot(),s.root);this.handler=new re(r,s,l,s.root),this.animation=new ln({stage:{update:function(){return i._flush(!0)}}}),this.animation.start()}return t.prototype.add=function(t){t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},t.prototype.remove=function(t){t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},t.prototype.configLayer=function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh()},t.prototype.setBackgroundColor=function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(!t)return!1;if("string"==typeof t)return We(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,n=0,i=e.length,r=0;r<i;r++)n+=We(e[r].color,1);return(n/=i)<.4}return!1}(t)},t.prototype.getBackgroundColor=function(){return this._backgroundColor},t.prototype.setDarkMode=function(t){this._darkMode=t},t.prototype.isDarkMode=function(){return this._darkMode},t.prototype.refreshImmediately=function(t){t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},t.prototype.refresh=function(){this._needsRefresh=!0,this.animation.start()},t.prototype.flush=function(){this._flush(!1)},t.prototype._flush=function(t){var e,n=(new Date).getTime();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var i=(new Date).getTime();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:i-n})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},t.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},t.prototype.wakeUp=function(){this.animation.start(),this._stillFrameAccum=0},t.prototype.addHover=function(t){},t.prototype.removeHover=function(t){},t.prototype.clearHover=function(){},t.prototype.refreshHover=function(){this._needsRefreshHover=!0},t.prototype.refreshHoverImmediately=function(){this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover()},t.prototype.resize=function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},t.prototype.clearAnimation=function(){this.animation.clear()},t.prototype.getWidth=function(){return this.painter.getWidth()},t.prototype.getHeight=function(){return this.painter.getHeight()},t.prototype.pathToImage=function(t,e){if(this.painter.pathToImage)return this.painter.pathToImage(t,e)},t.prototype.setCursorStyle=function(t){this.handler.setCursorStyle(t)},t.prototype.findHover=function(t,e){return this.handler.findHover(t,e)},t.prototype.on=function(t,e,n){return this.handler.on(t,e,n),this},t.prototype.off=function(t,e){this.handler.off(t,e)},t.prototype.trigger=function(t,e){this.handler.trigger(t,e)},t.prototype.clear=function(){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Di&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()},t.prototype.dispose=function(){var t;this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,t=this.id,delete Ai[t]},t}();function Li(t,e){var n=new Pi(b(),t,e);return Ai[n.id]=n,n}function Oi(t,e){ki[t]=e}var Ri=Object.freeze({__proto__:null,init:Li,dispose:function(t){t.dispose()},disposeAll:function(){for(var t in Ai)Ai.hasOwnProperty(t)&&Ai[t].dispose();Ai={}},getInstance:function(t){return Ai[t]},registerPainter:Oi,version:"5.2.1"}),Ni=1e-4;function Ei(t,e,n,i){var r=e[0],o=e[1],a=n[0],s=n[1],l=o-r,u=s-a;if(0===l)return 0===u?a:(a+s)/2;if(i)if(l>0){if(t<=r)return a;if(t>=o)return s}else{if(t>=r)return a;if(t<=o)return s}else{if(t===r)return a;if(t===o)return s}return(t-r)/l*u+a}function zi(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?(n=t,n.replace(/^\s+|\s+$/g,"")).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t;var n}function Bi(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function Fi(t){if(t=+t,isNaN(t))return 0;if(t>1e-14)for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n;return Vi(t)}function Vi(t){var e=t.toString().toLowerCase(),n=e.indexOf("e"),i=n>0?+e.slice(n+1):0,r=n>0?n:e.length,o=e.indexOf("."),a=o<0?0:r-1-o;return Math.max(0,a-i)}function Hi(t,e){var n=Math.log,i=Math.LN10,r=Math.floor(n(t[1]-t[0])/i),o=Math.round(n(Math.abs(e[1]-e[0]))/i),a=Math.min(Math.max(-r+o,0),20);return isFinite(a)?a:20}function Gi(t,e,n){if(!t[e])return 0;var i=R(t,(function(t,e){return t+(isNaN(e)?0:e)}),0);if(0===i)return 0;for(var r=Math.pow(10,n),o=O(t,(function(t){return(isNaN(t)?0:t)/i*r*100})),a=100*r,s=O(o,(function(t){return Math.floor(t)})),l=R(s,(function(t,e){return t+e}),0),u=O(o,(function(t,e){return t-s[e]}));l<a;){for(var h=Number.NEGATIVE_INFINITY,c=null,p=0,f=u.length;p<f;++p)u[p]>h&&(h=u[p],c=p);++s[c],u[c]=0,++l}return s[e]/r}function Wi(t,e){var n=Math.max(Fi(t),Fi(e)),i=t+e;return n>20?i:Bi(i,n)}function Ui(t){var e=2*Math.PI;return(t%e+e)%e}function Xi(t){return t>-1e-4&&t<Ni}var Yi=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function qi(t){if(t instanceof Date)return t;if("string"==typeof t){var e=Yi.exec(t);if(!e)return new Date(NaN);if(e[8]){var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0)}return null==t?new Date(NaN):new Date(Math.round(t))}function ji(t){return Math.pow(10,Zi(t))}function Zi(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return t/Math.pow(10,e)>=10&&e++,e}function Ki(t,e){var n=Zi(t),i=Math.pow(10,n),r=t/i;return t=(e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10)*i,n>=-20?+t.toFixed(n<0?-n:0):t}function $i(t){var e=parseFloat(t);return e==t&&(0!==e||"string"!=typeof t||t.indexOf("x")<=0)?e:NaN}function Ji(t){return!isNaN($i(t))}function Qi(t,e){return 0===e?t:Qi(e,t%e)}function tr(t,e){return null==t?e:null==e?t:t*e/Qi(t,e)}"undefined"!=typeof console&&console.warn&&console.log;function er(t){0}function nr(t){throw new Error(t)}var ir="series\0";function rr(t){return t instanceof Array?t:null==t?[]:[t]}function or(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var ar=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function sr(t){return!U(t)||F(t)||t instanceof Date?t:t.value}function lr(t){return U(t)&&!(t instanceof Array)}function ur(t,e,n){var i="normalMerge"===n,r="replaceMerge"===n,o="replaceAll"===n;t=t||[],e=(e||[]).slice();var a=ut();L(e,(function(t,n){U(t)||(e[n]=null)}));var s,l,u=function(t,e,n){var i=[];if("replaceAll"===n)return i;for(var r=0;r<t.length;r++){var o=t[r];o&&null!=o.id&&e.set(o.id,r),i.push({existing:"replaceMerge"===n||dr(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return i}(t,a,n);return(i||r)&&function(t,e,n,i){L(i,(function(r,o){if(r&&null!=r.id){var a=cr(r.id),s=n.get(a);if(null!=s){var l=t[s];it(!l.newOption,'Duplicated option on id "'+a+'".'),l.newOption=r,l.existing=e[s],i[o]=null}}}))}(u,t,a,e),i&&function(t,e){L(e,(function(n,i){if(n&&null!=n.name)for(var r=0;r<t.length;r++){var o=t[r].existing;if(!t[r].newOption&&o&&(null==o.id||null==n.id)&&!dr(n)&&!dr(o)&&hr("name",o,n))return t[r].newOption=n,void(e[i]=null)}}))}(u,e),i||r?function(t,e,n){L(e,(function(e){if(e){for(var i,r=0;(i=t[r])&&(i.newOption||dr(i.existing)||i.existing&&null!=e.id&&!hr("id",e,i.existing));)r++;i?(i.newOption=e,i.brandNew=n):t.push({newOption:e,brandNew:n,existing:null,keyInfo:null}),r++}}))}(u,e,r):o&&function(t,e){L(e,(function(e){t.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})}))}(u,e),s=u,l=ut(),L(s,(function(t){var e=t.existing;e&&l.set(e.id,t)})),L(s,(function(t){var e=t.newOption;it(!e||null==e.id||!l.get(e.id)||l.get(e.id)===t,"id duplicates: "+(e&&e.id)),e&&null!=e.id&&l.set(e.id,t),!t.keyInfo&&(t.keyInfo={})})),L(s,(function(t,e){var n=t.existing,i=t.newOption,r=t.keyInfo;if(U(i)){if(r.name=null!=i.name?cr(i.name):n?n.name:ir+e,n)r.id=cr(n.id);else if(null!=i.id)r.id=cr(i.id);else{var o=0;do{r.id="\0"+r.name+"\0"+o++}while(l.get(r.id))}l.set(r.id,t)}})),u}function hr(t,e,n){var i=pr(e[t],null),r=pr(n[t],null);return null!=i&&null!=r&&i===r}function cr(t){return pr(t,"")}function pr(t,e){if(null==t)return e;var n=typeof t;return"string"===n?t:"number"===n||G(t)?t+"":e}function fr(t){var e=t.name;return!(!e||!e.indexOf(ir))}function dr(t){return t&&null!=t.id&&0===cr(t.id).indexOf("\0_ec_\0")}function gr(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?F(e.dataIndex)?O(e.dataIndex,(function(e){return t.indexOfRawIndex(e)})):t.indexOfRawIndex(e.dataIndex):null!=e.name?F(e.name)?O(e.name,(function(e){return t.indexOfName(e)})):t.indexOfName(e.name):void 0}function yr(){var t="__ec_inner_"+vr++;return function(e){return e[t]||(e[t]={})}}var vr=Math.round(9*Math.random());function mr(t,e,n){var i=_r(e,n),r=i.mainTypeSpecified,o=i.queryOptionMap,a=i.others,s=n?n.defaultMainType:null;return!r&&s&&o.set(s,{}),o.each((function(e,i){var r=br(t,i,e,{useDefault:s===i,enableAll:!n||null==n.enableAll||n.enableAll,enableNone:!n||null==n.enableNone||n.enableNone});a[i+"Models"]=r.models,a[i+"Model"]=r.models[0]})),a}function _r(t,e){var n;if(H(t)){var i={};i[t+"Index"]=0,n=i}else n=t;var r=ut(),o={},a=!1;return L(n,(function(t,n){if("dataIndex"!==n&&"dataIndexInside"!==n){var i=n.match(/^(\w+)(Index|Id|Name)$/)||[],s=i[1],l=(i[2]||"").toLowerCase();if(s&&l&&!(e&&e.includeMainTypes&&I(e.includeMainTypes,s)<0))a=a||!!s,(r.get(s)||r.set(s,{}))[l]=t}else o[n]=t})),{mainTypeSpecified:a,queryOptionMap:r,others:o}}var xr={useDefault:!0,enableAll:!1,enableNone:!1};function br(t,e,n,i){i=i||xr;var r=n.index,o=n.id,a=n.name,s={models:null,specified:null!=r||null!=o||null!=a};if(!s.specified){var l=void 0;return s.models=i.useDefault&&(l=t.getComponent(e))?[l]:[],s}return"none"===r||!1===r?(it(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),s.models=[],s):("all"===r&&(it(i.enableAll,'`"all"` is not a valid value on index option.'),r=o=a=null),s.models=t.queryComponents({mainType:e,index:r,id:o,name:a}),s)}function wr(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}var Sr="___EC__COMPONENT__CONTAINER___",Mr="___EC__EXTENDED_CLASS___";function Tr(t){var e={main:"",sub:""};if(t){var n=t.split(".");e.main=n[0]||"",e.sub=n[1]||""}return e}function Cr(t,e){t.$constructor=t,t.extend=function(t){var e=this;function n(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];if(t.$constructor)t.$constructor.apply(this,arguments);else{if(Dr(e)){var a=ct(n.prototype,new(e.bind.apply(e,r([void 0],i))));return a}e.apply(this,arguments)}}return n[Mr]=!0,T(n.prototype,t),n.extend=this.extend,n.superCall=Ar,n.superApply=Pr,k(n,this),n.superClass=e,n}}function Dr(t){return"function"==typeof t&&/^class\s/.test(Function.prototype.toString.call(t))}function Ir(t,e){t.extend=e.extend}var kr=Math.round(10*Math.random());function Ar(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return this.superClass.prototype[e].apply(t,n)}function Pr(t,e,n){return this.superClass.prototype[e].apply(t,n)}function Lr(t){var e={};t.registerClass=function(t){var n,i=t.type||t.prototype.type;if(i){it(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(n=i),'componentType "'+n+'" illegal'),t.prototype.type=i;var r=Tr(i);if(r.sub){if(r.sub!==Sr){(function(t){var n=e[t.main];n&&n[Sr]||((n=e[t.main]={})[Sr]=!0);return n}(r))[r.sub]=t}}else e[r.main]=t}return t},t.getClass=function(t,n,i){var r=e[t];if(r&&r[Sr]&&(r=n?r[n]:null),i&&!r)throw new Error(n?"Component "+t+"."+(n||"")+" is used but not imported.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){var n=Tr(t),i=[],r=e[n.main];return r&&r[Sr]?L(r,(function(t,e){e!==Sr&&i.push(t)})):i.push(r),i},t.hasClass=function(t){var n=Tr(t);return!!e[n.main]},t.getAllClassMainTypes=function(){var t=[];return L(e,(function(e,n){t.push(n)})),t},t.hasSubTypes=function(t){var n=Tr(t),i=e[n.main];return i&&i[Sr]}}function Or(t,e){for(var n=0;n<t.length;n++)t[n][1]||(t[n][1]=t[n][0]);return e=e||!1,function(n,i,r){for(var o={},a=0;a<t.length;a++){var s=t[a][1];if(!(i&&I(i,s)>=0||r&&I(r,s)<0)){var l=n.getShallow(s,e);null!=l&&(o[t[a][0]]=l)}}return o}}var Rr=Or([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),Nr=function(){function t(){}return t.prototype.getAreaStyle=function(t,e){return Rr(this,t,e)},t}(),Er=new we(50);function zr(t){if("string"==typeof t){var e=Er.get(t);return e&&e.image}return t}function Br(t,e,n,i,r){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!n)return e;var o=Er.get(t),a={hostEl:n,cb:i,cbPayload:r};return o?!Vr(e=o.image)&&o.pending.push(a):((e=new Image).onload=e.onerror=Fr,Er.put(t,e.__cachedImgObj={image:e,pending:[a]}),e.src=e.__zrImageSrc=t),e}return t}return e}function Fr(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function Vr(t){return t&&t.width&&t.height}var Hr=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function Gr(t,e,n,i,r){if(!e)return"";var o=(t+"").split("\n");r=Wr(e,n,i,r);for(var a=0,s=o.length;a<s;a++)o[a]=Ur(o[a],r);return o.join("\n")}function Wr(t,e,n,i){var r=T({},i=i||{});r.font=e,n=Q(n,"..."),r.maxIterations=Q(i.maxIterations,2);var o=r.minChar=Q(i.minChar,0);r.cnCharWidth=ui("国",e);var a=r.ascCharWidth=ui("a",e);r.placeholder=Q(i.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;l<o&&s>=a;l++)s-=a;var u=ui(n,e);return u>s&&(n="",u=0),s=t-u,r.ellipsis=n,r.ellipsisWidth=u,r.contentWidth=s,r.containerWidth=t,r}function Ur(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";var o=ui(t,i);if(o<=n)return t;for(var a=0;;a++){if(o<=r||a>=e.maxIterations){t+=e.ellipsis;break}var s=0===a?Xr(t,r,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*r/o):0;o=ui(t=t.substr(0,s),i)}return""===t&&(t=e.placeholder),t}function Xr(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}var Yr=function(){},qr=function(t){this.tokens=[],t&&(this.tokens=t)},jr=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]};function Zr(t,e,n,i,r){var o,a,s=""===e,l=r&&n.rich[r]||{},u=t.lines,h=l.font||n.font,c=!1;if(i){var p=l.padding,f=p?p[1]+p[3]:0;if(null!=l.width&&"auto"!==l.width){var d=gi(l.width,i.width)+f;u.length>0&&d+i.accumWidth>i.width&&(o=e.split("\n"),c=!0),i.accumWidth=d}else{var g=Jr(e,h,i.width,i.breakAll,i.accumWidth);i.accumWidth=g.accumWidth+f,a=g.linesWidths,o=g.lines}}else o=e.split("\n");for(var y=0;y<o.length;y++){var v=o[y],m=new Yr;if(m.styleName=r,m.text=v,m.isLineHolder=!v&&!s,"number"==typeof l.width?m.width=l.width:m.width=a?a[y]:ui(v,h),y||c)u.push(new qr([m]));else{var _=(u[u.length-1]||(u[0]=new qr)).tokens,x=_.length;1===x&&_[0].isLineHolder?_[0]=m:(v||!x||s)&&_.push(m)}}}var Kr=R(",&?/;] ".split(""),(function(t,e){return t[e]=!0,t}),{});function $r(t){return!function(t){var e=t.charCodeAt(0);return e>=33&&e<=255}(t)||!!Kr[t]}function Jr(t,e,n,i,r){for(var o=[],a=[],s="",l="",u=0,h=0,c=0;c<t.length;c++){var p=t.charAt(c);if("\n"!==p){var f=ui(p,e),d=!i&&!$r(p);(o.length?h+f>n:r+h+f>n)?h?(s||l)&&(d?(s||(s=l,l="",h=u=0),o.push(s),a.push(h-u),l+=p,s="",h=u+=f):(l&&(s+=l,h+=u,l="",u=0),o.push(s),a.push(h),s=p,h=f)):d?(o.push(l),a.push(u),l=p,u=f):(o.push(p),a.push(f)):(h+=f,d?(l+=p,u+=f):(l&&(s+=l,l="",u=0),s+=p))}else l&&(s+=l,h+=u),o.push(s),a.push(h),s="",l="",u=0,h=0}return o.length||s||(s=t,l="",u=0),l&&(s+=l),s&&(o.push(s),a.push(h)),1===o.length&&(h+=r),{accumWidth:h,lines:o,linesWidths:a}}var Qr="__zr_style_"+Math.round(10*Math.random()),to={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},eo={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};to[Qr]=!0;var no=["z","z2","invisible"],io=["invisible"],ro=function(t){function e(e){return t.call(this,e)||this}var i;return n(e,t),e.prototype._init=function(e){for(var n=E(e),i=0;i<n.length;i++){var r=n[i];"style"===r?this.useStyle(e[r]):t.prototype.attrKV.call(this,r,e[r])}this.style||this.useStyle({})},e.prototype.beforeBrush=function(){},e.prototype.afterBrush=function(){},e.prototype.innerBeforeBrush=function(){},e.prototype.innerAfterBrush=function(){},e.prototype.shouldBePainted=function(t,e,n,i){var r=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,n){oo.copy(t.getBoundingRect()),t.transform&&oo.applyTransform(t.transform);return ao.width=e,ao.height=n,!oo.intersect(ao)}(this,t,e)||r&&!r[0]&&!r[3])return!1;if(n&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},e.prototype.contain=function(t,e){return this.rectContain(t,e)},e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.rectContain=function(t,e){var n=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(n[0],n[1])},e.prototype.getPaintRect=function(){var t=this._paintRect;if(!this._paintRect||this.__dirty){var e=this.transform,n=this.getBoundingRect(),i=this.style,r=i.shadowBlur||0,o=i.shadowOffsetX||0,a=i.shadowOffsetY||0;t=this._paintRect||(this._paintRect=new oi(0,0,0,0)),e?oi.applyTransform(t,n,e):t.copy(n),(r||o||a)&&(t.width+=2*r+Math.abs(o),t.height+=2*r+Math.abs(a),t.x=Math.min(t.x,t.x+o-r),t.y=Math.min(t.y,t.y+a-r));var s=this.dirtyRectTolerance;t.isZero()||(t.x=Math.floor(t.x-s),t.y=Math.floor(t.y-s),t.width=Math.ceil(t.width+1+2*s),t.height=Math.ceil(t.height+1+2*s))}return t},e.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new oi(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},e.prototype.getPrevPaintRect=function(){return this._prevPaintRect},e.prototype.animateStyle=function(t){return this.animate("style",t)},e.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},e.prototype.attrKV=function(e,n){"style"!==e?t.prototype.attrKV.call(this,e,n):this.style?this.setStyle(n):this.useStyle(n)},e.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:T(this.style,t),this.dirtyStyle(),this},e.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},e.prototype.dirty=function(){this.dirtyStyle()},e.prototype.styleChanged=function(){return!!(2&this.__dirty)},e.prototype.styleUpdated=function(){this.__dirty&=-3},e.prototype.createStyle=function(t){return ct(to,t)},e.prototype.useStyle=function(t){t[Qr]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},e.prototype.isStyleObject=function(t){return t[Qr]},e.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.style&&!n.style&&(n.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,n,no)},e.prototype._applyStateObj=function(e,n,i,r,o,a){t.prototype._applyStateObj.call(this,e,n,i,r,o,a);var s,l=!(n&&r);if(n&&n.style?o?r?s=n.style:(s=this._mergeStyle(this.createStyle(),i.style),this._mergeStyle(s,n.style)):(s=this._mergeStyle(this.createStyle(),r?this.style:i.style),this._mergeStyle(s,n.style)):l&&(s=i.style),s)if(o){var u=this.style;if(this.style=this.createStyle(l?{}:u),l)for(var h=E(u),c=0;c<h.length;c++){(f=h[c])in s&&(s[f]=s[f],this.style[f]=u[f])}var p=E(s);for(c=0;c<p.length;c++){var f=p[c];this.style[f]=this.style[f]}this._transitionState(e,{style:s},a,this.getAnimationStyleProps())}else this.useStyle(s);var d=this.__inHover?io:no;for(c=0;c<d.length;c++){f=d[c];n&&null!=n[f]?this[f]=n[f]:l&&null!=i[f]&&(this[f]=i[f])}},e.prototype._mergeStates=function(e){for(var n,i=t.prototype._mergeStates.call(this,e),r=0;r<e.length;r++){var o=e[r];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(i.style=n),i},e.prototype._mergeStyle=function(t,e){return T(t,e),t},e.prototype.getAnimationStyleProps=function(){return eo},e.initDefaultProps=((i=e.prototype).type="displayable",i.invisible=!1,i.z=0,i.z2=0,i.zlevel=0,i.culling=!1,i.cursor="pointer",i.rectHover=!1,i.incremental=!1,i._rect=null,i.dirtyRectTolerance=0,void(i.__dirty=3)),e}(wi),oo=new oi(0,0,0,0),ao=new oi(0,0,0,0);var so=Math.pow,lo=Math.sqrt,uo=1e-8,ho=1e-4,co=lo(3),po=1/3,fo=gt(),go=gt(),yo=gt();function vo(t){return t>-1e-8&&t<uo}function mo(t){return t>uo||t<-1e-8}function _o(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function xo(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function bo(t,e,n,i,r,o){var a=i+3*(e-n)-t,s=3*(n-2*e+t),l=3*(e-t),u=t-r,h=s*s-3*a*l,c=s*l-9*a*u,p=l*l-3*s*u,f=0;if(vo(h)&&vo(c)){if(vo(s))o[0]=0;else(M=-l/s)>=0&&M<=1&&(o[f++]=M)}else{var d=c*c-4*h*p;if(vo(d)){var g=c/h,y=-g/2;(M=-s/a+g)>=0&&M<=1&&(o[f++]=M),y>=0&&y<=1&&(o[f++]=y)}else if(d>0){var v=lo(d),m=h*s+1.5*a*(-c+v),_=h*s+1.5*a*(-c-v);(M=(-s-((m=m<0?-so(-m,po):so(m,po))+(_=_<0?-so(-_,po):so(_,po))))/(3*a))>=0&&M<=1&&(o[f++]=M)}else{var x=(2*h*s-3*a*c)/(2*lo(h*h*h)),b=Math.acos(x)/3,w=lo(h),S=Math.cos(b),M=(-s-2*w*S)/(3*a),T=(y=(-s+w*(S+co*Math.sin(b)))/(3*a),(-s+w*(S-co*Math.sin(b)))/(3*a));M>=0&&M<=1&&(o[f++]=M),y>=0&&y<=1&&(o[f++]=y),T>=0&&T<=1&&(o[f++]=T)}}return f}function wo(t,e,n,i,r){var o=6*n-12*e+6*t,a=9*e+3*i-3*t-9*n,s=3*e-3*t,l=0;if(vo(a)){if(mo(o))(h=-s/o)>=0&&h<=1&&(r[l++]=h)}else{var u=o*o-4*a*s;if(vo(u))r[0]=-o/(2*a);else if(u>0){var h,c=lo(u),p=(-o-c)/(2*a);(h=(-o+c)/(2*a))>=0&&h<=1&&(r[l++]=h),p>=0&&p<=1&&(r[l++]=p)}}return l}function So(t,e,n,i,r,o){var a=(e-t)*r+t,s=(n-e)*r+e,l=(i-n)*r+n,u=(s-a)*r+a,h=(l-s)*r+s,c=(h-u)*r+u;o[0]=t,o[1]=a,o[2]=u,o[3]=c,o[4]=c,o[5]=h,o[6]=l,o[7]=i}function Mo(t,e,n,i,r,o,a,s,l){for(var u=t,h=e,c=0,p=1/l,f=1;f<=l;f++){var d=f*p,g=_o(t,n,r,a,d),y=_o(e,i,o,s,d),v=g-u,m=y-h;c+=Math.sqrt(v*v+m*m),u=g,h=y}return c}function To(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function Co(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function Do(t,e,n){var i=t+n-2*e;return 0===i?.5:(t-e)/i}function Io(t,e,n,i,r){var o=(e-t)*i+t,a=(n-e)*i+e,s=(a-o)*i+o;r[0]=t,r[1]=o,r[2]=s,r[3]=s,r[4]=a,r[5]=n}function ko(t,e,n,i,r,o,a){for(var s=t,l=e,u=0,h=1/a,c=1;c<=a;c++){var p=c*h,f=To(t,n,r,p),d=To(e,i,o,p),g=f-s,y=d-l;u+=Math.sqrt(g*g+y*y),s=f,l=d}return u}var Ao=Math.min,Po=Math.max,Lo=Math.sin,Oo=Math.cos,Ro=2*Math.PI,No=gt(),Eo=gt(),zo=gt();function Bo(t,e,n){if(0!==t.length){for(var i=t[0],r=i[0],o=i[0],a=i[1],s=i[1],l=1;l<t.length;l++)i=t[l],r=Ao(r,i[0]),o=Po(o,i[0]),a=Ao(a,i[1]),s=Po(s,i[1]);e[0]=r,e[1]=a,n[0]=o,n[1]=s}}function Fo(t,e,n,i,r,o){r[0]=Ao(t,n),r[1]=Ao(e,i),o[0]=Po(t,n),o[1]=Po(e,i)}var Vo=[],Ho=[];function Go(t,e,n,i,r,o,a,s,l,u){var h=wo,c=_o,p=h(t,n,r,a,Vo);l[0]=1/0,l[1]=1/0,u[0]=-1/0,u[1]=-1/0;for(var f=0;f<p;f++){var d=c(t,n,r,a,Vo[f]);l[0]=Ao(d,l[0]),u[0]=Po(d,u[0])}p=h(e,i,o,s,Ho);for(f=0;f<p;f++){var g=c(e,i,o,s,Ho[f]);l[1]=Ao(g,l[1]),u[1]=Po(g,u[1])}l[0]=Ao(t,l[0]),u[0]=Po(t,u[0]),l[0]=Ao(a,l[0]),u[0]=Po(a,u[0]),l[1]=Ao(e,l[1]),u[1]=Po(e,u[1]),l[1]=Ao(s,l[1]),u[1]=Po(s,u[1])}function Wo(t,e,n,i,r,o,a,s){var l=Do,u=To,h=Po(Ao(l(t,n,r),1),0),c=Po(Ao(l(e,i,o),1),0),p=u(t,n,r,h),f=u(e,i,o,c);a[0]=Ao(t,r,p),a[1]=Ao(e,o,f),s[0]=Po(t,r,p),s[1]=Po(e,o,f)}function Uo(t,e,n,i,r,o,a,s,l){var u=Pt,h=Lt,c=Math.abs(r-o);if(c%Ro<1e-4&&c>1e-4)return s[0]=t-n,s[1]=e-i,l[0]=t+n,void(l[1]=e+i);if(No[0]=Oo(r)*n+t,No[1]=Lo(r)*i+e,Eo[0]=Oo(o)*n+t,Eo[1]=Lo(o)*i+e,u(s,No,Eo),h(l,No,Eo),(r%=Ro)<0&&(r+=Ro),(o%=Ro)<0&&(o+=Ro),r>o&&!a?o+=Ro:r<o&&a&&(r+=Ro),a){var p=o;o=r,r=p}for(var f=0;f<o;f+=Math.PI/2)f>r&&(zo[0]=Oo(f)*n+t,zo[1]=Lo(f)*i+e,u(s,zo,s),h(l,zo,l))}var Xo={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Yo=[],qo=[],jo=[],Zo=[],Ko=[],$o=[],Jo=Math.min,Qo=Math.max,ta=Math.cos,ea=Math.sin,na=Math.sqrt,ia=Math.abs,ra=Math.PI,oa=2*ra,aa="undefined"!=typeof Float32Array,sa=[];function la(t){return Math.round(t/ra*1e8)/1e8%2*ra}function ua(t,e){var n=la(t[0]);n<0&&(n+=oa);var i=n-t[0],r=t[1];r+=i,!e&&r-n>=oa?r=n+oa:e&&n-r>=oa?r=n-oa:!e&&n>r?r=n+(oa-la(n-r)):e&&n<r&&(r=n-(oa-la(r-n))),t[0]=n,t[1]=r}var ha=function(){function t(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return t.prototype.increaseVersion=function(){this._version++},t.prototype.getVersion=function(){return this._version},t.prototype.setScale=function(t,e,n){(n=n||0)>0&&(this._ux=ia(n/Dn/t)||0,this._uy=ia(n/Dn/e)||0)},t.prototype.setDPR=function(t){this.dpr=t},t.prototype.setContext=function(t){this._ctx=t},t.prototype.getContext=function(){return this._ctx},t.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},t.prototype.reset=function(){this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},t.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Xo.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},t.prototype.lineTo=function(t,e){var n=ia(t-this._xi),i=ia(e-this._yi),r=n>this._ux||i>this._uy;if(this.addData(Xo.L,t,e),this._ctx&&r&&(this._needsDash?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),r)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=n*n+i*i;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},t.prototype.bezierCurveTo=function(t,e,n,i,r,o){return this._drawPendingPt(),this.addData(Xo.C,t,e,n,i,r,o),this._ctx&&(this._needsDash?this._dashedBezierTo(t,e,n,i,r,o):this._ctx.bezierCurveTo(t,e,n,i,r,o)),this._xi=r,this._yi=o,this},t.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(Xo.Q,t,e,n,i),this._ctx&&(this._needsDash?this._dashedQuadraticTo(t,e,n,i):this._ctx.quadraticCurveTo(t,e,n,i)),this._xi=n,this._yi=i,this},t.prototype.arc=function(t,e,n,i,r,o){this._drawPendingPt(),sa[0]=i,sa[1]=r,ua(sa,o),i=sa[0];var a=(r=sa[1])-i;return this.addData(Xo.A,t,e,n,n,i,a,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=ta(r)*n+t,this._yi=ea(r)*n+e,this},t.prototype.arcTo=function(t,e,n,i,r){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},t.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(Xo.R,t,e,n,i),this},t.prototype.closePath=function(){this._drawPendingPt(),this.addData(Xo.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&(this._needsDash&&this._dashedLineTo(e,n),t.closePath()),this._xi=e,this._yi=n,this},t.prototype.fill=function(t){t&&t.fill(),this.toStatic()},t.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},t.prototype.setLineDash=function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,n=0;n<t.length;n++)e+=t[n];this._dashSum=e,this._needsDash=!0}else this._lineDash=null,this._needsDash=!1;return this},t.prototype.setLineDashOffset=function(t){return this._dashOffset=t,this},t.prototype.len=function(){return this._len},t.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!aa||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},t.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();aa&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},t.prototype.addData=function(t,e,n,i,r,o,a,s,l){if(this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var h=0;h<arguments.length;h++)u[this._len++]=arguments[h]}},t.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},t.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},t.prototype._dashedLineTo=function(t,e){var n,i,r=this._dashSum,o=this._lineDash,a=this._ctx,s=this._dashOffset,l=this._xi,u=this._yi,h=t-l,c=e-u,p=na(h*h+c*c),f=l,d=u,g=o.length;for(s<0&&(s=r+s),f-=(s%=r)*(h/=p),d-=s*(c/=p);h>0&&f<=t||h<0&&f>=t||0===h&&(c>0&&d<=e||c<0&&d>=e);)f+=h*(n=o[i=this._dashIdx]),d+=c*n,this._dashIdx=(i+1)%g,h>0&&f<l||h<0&&f>l||c>0&&d<u||c<0&&d>u||a[i%2?"moveTo":"lineTo"](h>=0?Jo(f,t):Qo(f,t),c>=0?Jo(d,e):Qo(d,e));h=f-t,c=d-e,this._dashOffset=-na(h*h+c*c)},t.prototype._dashedBezierTo=function(t,e,n,i,r,o){var a,s,l,u,h,c=this._ctx,p=this._dashSum,f=this._dashOffset,d=this._lineDash,g=this._xi,y=this._yi,v=0,m=this._dashIdx,_=d.length,x=0;for(f<0&&(f=p+f),f%=p,a=0;a<1;a+=.1)s=_o(g,t,n,r,a+.1)-_o(g,t,n,r,a),l=_o(y,e,i,o,a+.1)-_o(y,e,i,o,a),v+=na(s*s+l*l);for(;m<_&&!((x+=d[m])>f);m++);for(a=(x-f)/v;a<=1;)u=_o(g,t,n,r,a),h=_o(y,e,i,o,a),m%2?c.moveTo(u,h):c.lineTo(u,h),a+=d[m]/v,m=(m+1)%_;m%2!=0&&c.lineTo(r,o),s=r-u,l=o-h,this._dashOffset=-na(s*s+l*l)},t.prototype._dashedQuadraticTo=function(t,e,n,i){var r=n,o=i;n=(n+2*t)/3,i=(i+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,n,i,r,o)},t.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,aa&&this._len>11&&(this.data=new Float32Array(t)))}},t.prototype.getBoundingRect=function(){jo[0]=jo[1]=Ko[0]=Ko[1]=Number.MAX_VALUE,Zo[0]=Zo[1]=$o[0]=$o[1]=-Number.MAX_VALUE;var t,e=this.data,n=0,i=0,r=0,o=0;for(t=0;t<this._len;){var a=e[t++],s=1===t;switch(s&&(r=n=e[t],o=i=e[t+1]),a){case Xo.M:n=r=e[t++],i=o=e[t++],Ko[0]=r,Ko[1]=o,$o[0]=r,$o[1]=o;break;case Xo.L:Fo(n,i,e[t],e[t+1],Ko,$o),n=e[t++],i=e[t++];break;case Xo.C:Go(n,i,e[t++],e[t++],e[t++],e[t++],e[t],e[t+1],Ko,$o),n=e[t++],i=e[t++];break;case Xo.Q:Wo(n,i,e[t++],e[t++],e[t],e[t+1],Ko,$o),n=e[t++],i=e[t++];break;case Xo.A:var l=e[t++],u=e[t++],h=e[t++],c=e[t++],p=e[t++],f=e[t++]+p;t+=1;var d=!e[t++];s&&(r=ta(p)*h+l,o=ea(p)*c+u),Uo(l,u,h,c,p,f,d,Ko,$o),n=ta(f)*h+l,i=ea(f)*c+u;break;case Xo.R:Fo(r=n=e[t++],o=i=e[t++],r+e[t++],o+e[t++],Ko,$o);break;case Xo.Z:n=r,i=o}Pt(jo,jo,Ko),Lt(Zo,Zo,$o)}return 0===t&&(jo[0]=jo[1]=Zo[0]=Zo[1]=0),new oi(jo[0],jo[1],Zo[0]-jo[0],Zo[1]-jo[1])},t.prototype._calculateLength=function(){var t=this.data,e=this._len,n=this._ux,i=this._uy,r=0,o=0,a=0,s=0;this._pathSegLen||(this._pathSegLen=[]);for(var l=this._pathSegLen,u=0,h=0,c=0;c<e;){var p=t[c++],f=1===c;f&&(a=r=t[c],s=o=t[c+1]);var d=-1;switch(p){case Xo.M:r=a=t[c++],o=s=t[c++];break;case Xo.L:var g=t[c++],y=(_=t[c++])-o;(ia(k=g-r)>n||ia(y)>i||c===e-1)&&(d=Math.sqrt(k*k+y*y),r=g,o=_);break;case Xo.C:var v=t[c++],m=t[c++],_=(g=t[c++],t[c++]),x=t[c++],b=t[c++];d=Mo(r,o,v,m,g,_,x,b,10),r=x,o=b;break;case Xo.Q:d=ko(r,o,v=t[c++],m=t[c++],g=t[c++],_=t[c++],10),r=g,o=_;break;case Xo.A:var w=t[c++],S=t[c++],M=t[c++],T=t[c++],C=t[c++],D=t[c++],I=D+C;c+=1;t[c++];f&&(a=ta(C)*M+w,s=ea(C)*T+S),d=Qo(M,T)*Jo(oa,Math.abs(D)),r=ta(I)*M+w,o=ea(I)*T+S;break;case Xo.R:a=r=t[c++],s=o=t[c++],d=2*t[c++]+2*t[c++];break;case Xo.Z:var k=a-r;y=s-o;d=Math.sqrt(k*k+y*y),r=a,o=s}d>=0&&(l[h++]=d,u+=d)}return this._pathLen=u,u},t.prototype.rebuildPath=function(t,e){var n,i,r,o,a,s,l,u,h,c,p=this.data,f=this._ux,d=this._uy,g=this._len,y=e<1,v=0,m=0,_=0;if(!y||(this._pathSegLen||this._calculateLength(),l=this._pathSegLen,u=e*this._pathLen))t:for(var x=0;x<g;){var b=p[x++],w=1===x;switch(w&&(n=r=p[x],i=o=p[x+1]),b!==Xo.L&&_>0&&(t.lineTo(h,c),_=0),b){case Xo.M:n=r=p[x++],i=o=p[x++],t.moveTo(r,o);break;case Xo.L:a=p[x++],s=p[x++];var S=ia(a-r),M=ia(s-o);if(S>f||M>d){if(y){if(v+(j=l[m++])>u){var T=(u-v)/j;t.lineTo(r*(1-T)+a*T,o*(1-T)+s*T);break t}v+=j}t.lineTo(a,s),r=a,o=s,_=0}else{var C=S*S+M*M;C>_&&(h=a,c=s,_=C)}break;case Xo.C:var D=p[x++],I=p[x++],k=p[x++],A=p[x++],P=p[x++],L=p[x++];if(y){if(v+(j=l[m++])>u){So(r,D,k,P,T=(u-v)/j,Yo),So(o,I,A,L,T,qo),t.bezierCurveTo(Yo[1],qo[1],Yo[2],qo[2],Yo[3],qo[3]);break t}v+=j}t.bezierCurveTo(D,I,k,A,P,L),r=P,o=L;break;case Xo.Q:D=p[x++],I=p[x++],k=p[x++],A=p[x++];if(y){if(v+(j=l[m++])>u){Io(r,D,k,T=(u-v)/j,Yo),Io(o,I,A,T,qo),t.quadraticCurveTo(Yo[1],qo[1],Yo[2],qo[2]);break t}v+=j}t.quadraticCurveTo(D,I,k,A),r=k,o=A;break;case Xo.A:var O=p[x++],R=p[x++],N=p[x++],E=p[x++],z=p[x++],B=p[x++],F=p[x++],V=!p[x++],H=N>E?N:E,G=ia(N-E)>.001,W=z+B,U=!1;if(y)v+(j=l[m++])>u&&(W=z+B*(u-v)/j,U=!0),v+=j;if(G&&t.ellipse?t.ellipse(O,R,N,E,F,z,W,V):t.arc(O,R,H,z,W,V),U)break t;w&&(n=ta(z)*N+O,i=ea(z)*E+R),r=ta(W)*N+O,o=ea(W)*E+R;break;case Xo.R:n=r=p[x],i=o=p[x+1],a=p[x++],s=p[x++];var X=p[x++],Y=p[x++];if(y){if(v+(j=l[m++])>u){var q=u-v;t.moveTo(a,s),t.lineTo(a+Jo(q,X),s),(q-=X)>0&&t.lineTo(a+X,s+Jo(q,Y)),(q-=Y)>0&&t.lineTo(a+Qo(X-q,0),s+Y),(q-=X)>0&&t.lineTo(a,s+Qo(Y-q,0));break t}v+=j}t.rect(a,s,X,Y);break;case Xo.Z:if(y){var j;if(v+(j=l[m++])>u){T=(u-v)/j;t.lineTo(r*(1-T)+n*T,o*(1-T)+i*T);break t}v+=j}t.closePath(),r=n,o=i}}},t.prototype.clone=function(){var e=new t,n=this.data;return e.data=n.slice?n.slice():Array.prototype.slice.call(n),e._len=this._len,e},t.CMD=Xo,t.initDefaultProps=function(){var e=t.prototype;e._saveData=!0,e._needsDash=!1,e._dashOffset=0,e._dashIdx=0,e._dashSum=0,e._ux=0,e._uy=0,e._pendingPtDist=0,e._version=0}(),t}();function ca(t,e,n,i,r,o,a){if(0===r)return!1;var s=r,l=0;if(a>e+s&&a>i+s||a<e-s&&a<i-s||o>t+s&&o>n+s||o<t-s&&o<n-s)return!1;if(t===n)return Math.abs(o-t)<=s/2;var u=(l=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n);return u*u/(l*l+1)<=s/2*s/2}function pa(t,e,n,i,r,o,a,s,l,u,h){if(0===l)return!1;var c=l;return!(h>e+c&&h>i+c&&h>o+c&&h>s+c||h<e-c&&h<i-c&&h<o-c&&h<s-c||u>t+c&&u>n+c&&u>r+c&&u>a+c||u<t-c&&u<n-c&&u<r-c&&u<a-c)&&function(t,e,n,i,r,o,a,s,l,u,h){var c,p,f,d,g,y=.005,v=1/0;fo[0]=l,fo[1]=u;for(var m=0;m<1;m+=.05)go[0]=_o(t,n,r,a,m),go[1]=_o(e,i,o,s,m),(d=It(fo,go))<v&&(c=m,v=d);v=1/0;for(var _=0;_<32&&!(y<ho);_++)p=c-y,f=c+y,go[0]=_o(t,n,r,a,p),go[1]=_o(e,i,o,s,p),d=It(go,fo),p>=0&&d<v?(c=p,v=d):(yo[0]=_o(t,n,r,a,f),yo[1]=_o(e,i,o,s,f),g=It(yo,fo),f<=1&&g<v?(c=f,v=g):y*=.5);return h&&(h[0]=_o(t,n,r,a,c),h[1]=_o(e,i,o,s,c)),lo(v)}(t,e,n,i,r,o,a,s,u,h,null)<=c/2}function fa(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var u=a;return!(l>e+u&&l>i+u&&l>o+u||l<e-u&&l<i-u&&l<o-u||s>t+u&&s>n+u&&s>r+u||s<t-u&&s<n-u&&s<r-u)&&function(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;fo[0]=a,fo[1]=s;for(var p=0;p<1;p+=.05)go[0]=To(t,n,r,p),go[1]=To(e,i,o,p),(y=It(fo,go))<c&&(u=p,c=y);c=1/0;for(var f=0;f<32&&!(h<ho);f++){var d=u-h,g=u+h;go[0]=To(t,n,r,d),go[1]=To(e,i,o,d);var y=It(go,fo);if(d>=0&&y<c)u=d,c=y;else{yo[0]=To(t,n,r,g),yo[1]=To(e,i,o,g);var v=It(yo,fo);g<=1&&v<c?(u=g,c=v):h*=.5}}return l&&(l[0]=To(t,n,r,u),l[1]=To(e,i,o,u)),lo(c)}(t,e,n,i,r,o,s,l,null)<=u/2}var da=2*Math.PI;function ga(t){return(t%=da)<0&&(t+=da),t}var ya=2*Math.PI;function va(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var u=a;s-=t,l-=e;var h=Math.sqrt(s*s+l*l);if(h-u>n||h+u<n)return!1;if(Math.abs(i-r)%ya<1e-4)return!0;if(o){var c=i;i=ga(r),r=ga(c)}else i=ga(i),r=ga(r);i>r&&(r+=ya);var p=Math.atan2(l,s);return p<0&&(p+=ya),p>=i&&p<=r||p+ya>=i&&p+ya<=r}function ma(t,e,n,i,r,o){if(o>e&&o>i||o<e&&o<i)return 0;if(i===e)return 0;var a=(o-e)/(i-e),s=i<e?1:-1;1!==a&&0!==a||(s=i<e?.5:-.5);var l=a*(n-t)+t;return l===r?1/0:l>r?s:0}var _a=ha.CMD,xa=2*Math.PI;var ba=[-1,-1,-1],wa=[-1,-1];function Sa(t,e,n,i,r,o,a,s,l,u){if(u>e&&u>i&&u>o&&u>s||u<e&&u<i&&u<o&&u<s)return 0;var h,c=bo(e,i,o,s,u,ba);if(0===c)return 0;for(var p=0,f=-1,d=void 0,g=void 0,y=0;y<c;y++){var v=ba[y],m=0===v||1===v?.5:1;_o(t,n,r,a,v)<l||(f<0&&(f=wo(e,i,o,s,wa),wa[1]<wa[0]&&f>1&&(h=void 0,h=wa[0],wa[0]=wa[1],wa[1]=h),d=_o(e,i,o,s,wa[0]),f>1&&(g=_o(e,i,o,s,wa[1]))),2===f?v<wa[0]?p+=d<e?m:-m:v<wa[1]?p+=g<d?m:-m:p+=s<g?m:-m:v<wa[0]?p+=d<e?m:-m:p+=s<d?m:-m)}return p}function Ma(t,e,n,i,r,o,a,s){if(s>e&&s>i&&s>o||s<e&&s<i&&s<o)return 0;var l=function(t,e,n,i,r){var o=t-2*e+n,a=2*(e-t),s=t-i,l=0;if(vo(o))mo(a)&&(h=-s/a)>=0&&h<=1&&(r[l++]=h);else{var u=a*a-4*o*s;if(vo(u))(h=-a/(2*o))>=0&&h<=1&&(r[l++]=h);else if(u>0){var h,c=lo(u),p=(-a-c)/(2*o);(h=(-a+c)/(2*o))>=0&&h<=1&&(r[l++]=h),p>=0&&p<=1&&(r[l++]=p)}}return l}(e,i,o,s,ba);if(0===l)return 0;var u=Do(e,i,o);if(u>=0&&u<=1){for(var h=0,c=To(e,i,o,u),p=0;p<l;p++){var f=0===ba[p]||1===ba[p]?.5:1;To(t,n,r,ba[p])<a||(ba[p]<u?h+=c<e?f:-f:h+=o<c?f:-f)}return h}f=0===ba[0]||1===ba[0]?.5:1;return To(t,n,r,ba[0])<a?0:o<e?f:-f}function Ta(t,e,n,i,r,o,a,s){if((s-=e)>n||s<-n)return 0;var l=Math.sqrt(n*n-s*s);ba[0]=-l,ba[1]=l;var u=Math.abs(i-r);if(u<1e-4)return 0;if(u>=xa-1e-4){i=0,r=xa;var h=o?1:-1;return a>=ba[0]+t&&a<=ba[1]+t?h:0}if(i>r){var c=i;i=r,r=c}i<0&&(i+=xa,r+=xa);for(var p=0,f=0;f<2;f++){var d=ba[f];if(d+t>a){var g=Math.atan2(s,d);h=o?1:-1;g<0&&(g=xa+g),(g>=i&&g<=r||g+xa>=i&&g+xa<=r)&&(g>Math.PI/2&&g<1.5*Math.PI&&(h=-h),p+=h)}}return p}function Ca(t,e,n,i,r){for(var o,a,s,l,u=t.data,h=t.len(),c=0,p=0,f=0,d=0,g=0,y=0;y<h;){var v=u[y++],m=1===y;switch(v===_a.M&&y>1&&(n||(c+=ma(p,f,d,g,i,r))),m&&(d=p=u[y],g=f=u[y+1]),v){case _a.M:p=d=u[y++],f=g=u[y++];break;case _a.L:if(n){if(ca(p,f,u[y],u[y+1],e,i,r))return!0}else c+=ma(p,f,u[y],u[y+1],i,r)||0;p=u[y++],f=u[y++];break;case _a.C:if(n){if(pa(p,f,u[y++],u[y++],u[y++],u[y++],u[y],u[y+1],e,i,r))return!0}else c+=Sa(p,f,u[y++],u[y++],u[y++],u[y++],u[y],u[y+1],i,r)||0;p=u[y++],f=u[y++];break;case _a.Q:if(n){if(fa(p,f,u[y++],u[y++],u[y],u[y+1],e,i,r))return!0}else c+=Ma(p,f,u[y++],u[y++],u[y],u[y+1],i,r)||0;p=u[y++],f=u[y++];break;case _a.A:var _=u[y++],x=u[y++],b=u[y++],w=u[y++],S=u[y++],M=u[y++];y+=1;var T=!!(1-u[y++]);o=Math.cos(S)*b+_,a=Math.sin(S)*w+x,m?(d=o,g=a):c+=ma(p,f,o,a,i,r);var C=(i-_)*w/b+_;if(n){if(va(_,x,w,S,S+M,T,e,C,r))return!0}else c+=Ta(_,x,w,S,S+M,T,C,r);p=Math.cos(S+M)*b+_,f=Math.sin(S+M)*w+x;break;case _a.R:if(d=p=u[y++],g=f=u[y++],o=d+u[y++],a=g+u[y++],n){if(ca(d,g,o,g,e,i,r)||ca(o,g,o,a,e,i,r)||ca(o,a,d,a,e,i,r)||ca(d,a,d,g,e,i,r))return!0}else c+=ma(o,g,o,a,i,r),c+=ma(d,a,d,g,i,r);break;case _a.Z:if(n){if(ca(p,f,d,g,e,i,r))return!0}else c+=ma(p,f,d,g,i,r);p=d,f=g}}return n||(s=f,l=g,Math.abs(s-l)<1e-4)||(c+=ma(p,f,d,g,i,r)||0),0!==c}var Da=C({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},to),Ia={style:C({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},eo.style)},ka=["x","y","rotation","scaleX","scaleY","originX","originY","invisible","culling","z","z2","zlevel","parent"],Aa=function(t){function e(e){return t.call(this,e)||this}var i;return n(e,t),e.prototype.update=function(){var n=this;t.prototype.update.call(this);var i=this.style;if(i.decal){var r=this._decalEl=this._decalEl||new e;r.buildPath===e.prototype.buildPath&&(r.buildPath=function(t){n.buildPath(t,n.shape)}),r.silent=!0;var o=r.style;for(var a in i)o[a]!==i[a]&&(o[a]=i[a]);o.fill=i.fill?i.decal:null,o.decal=null,o.shadowColor=null,i.strokeFirst&&(o.stroke=null);for(var s=0;s<ka.length;++s)r[ka[s]]=this[ka[s]];r.__dirty|=1}else this._decalEl&&(this._decalEl=null)},e.prototype.getDecalElement=function(){return this._decalEl},e.prototype._init=function(e){var n=E(e);this.shape=this.getDefaultShape();var i=this.getDefaultStyle();i&&this.useStyle(i);for(var r=0;r<n.length;r++){var o=n[r],a=e[o];"style"===o?this.style?T(this.style,a):this.useStyle(a):"shape"===o?T(this.shape,a):t.prototype.attrKV.call(this,o,a)}this.style||this.useStyle({})},e.prototype.getDefaultStyle=function(){return null},e.prototype.getDefaultShape=function(){return{}},e.prototype.canBeInsideText=function(){return this.hasFill()},e.prototype.getInsideTextFill=function(){var t=this.style.fill;if("none"!==t){if(H(t)){var e=We(t,0);return e>.5?In:e>.2?"#eee":kn}if(t)return kn}return In},e.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(H(e)){var n=this.__zr;if(!(!n||!n.isDarkMode())===We(t,0)<.4)return e}},e.prototype.buildPath=function(t,e,n){},e.prototype.pathUpdated=function(){this.__dirty&=-5},e.prototype.getUpdatedPathProxy=function(t){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},e.prototype.createPathProxy=function(){this.path=new ha(!1)},e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))},e.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},e.prototype.getBoundingRect=function(){var t=this._rect,e=this.style,n=!t;if(n){var i=!1;this.path||(i=!0,this.createPathProxy());var r=this.path;(i||4&this.__dirty)&&(r.beginPath(),this.buildPath(r,this.shape,!1),this.pathUpdated()),t=r.getBoundingRect()}if(this._rect=t,this.hasStroke()&&this.path&&this.path.len()>0){var o=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||n){o.copy(t);var a=e.strokeNoScale?this.getLineScale():1,s=e.lineWidth;if(!this.hasFill()){var l=this.strokeContainThreshold;s=Math.max(s,null==l?4:l)}a>1e-10&&(o.width+=s/a,o.height+=s/a,o.x-=s/a/2,o.y-=s/a/2)}return o}return t},e.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var o=this.path;if(this.hasStroke()){var a=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(this.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),function(t,e,n,i){return Ca(t,e,!0,n,i)}(o,a/s,t,e)))return!0}if(this.hasFill())return function(t,e,n){return Ca(t,0,!1,e,n)}(o,t,e)}return!1},e.prototype.dirtyShape=function(){this.__dirty|=4,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},e.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},e.prototype.animateShape=function(t){return this.animate("shape",t)},e.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},e.prototype.attrKV=function(e,n){"shape"===e?this.setShape(n):t.prototype.attrKV.call(this,e,n)},e.prototype.setShape=function(t,e){var n=this.shape;return n||(n=this.shape={}),"string"==typeof t?n[t]=e:T(n,t),this.dirtyShape(),this},e.prototype.shapeChanged=function(){return!!(4&this.__dirty)},e.prototype.createStyle=function(t){return ct(Da,t)},e.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.shape&&!n.shape&&(n.shape=T({},this.shape))},e.prototype._applyStateObj=function(e,n,i,r,o,a){t.prototype._applyStateObj.call(this,e,n,i,r,o,a);var s,l=!(n&&r);if(n&&n.shape?o?r?s=n.shape:(s=T({},i.shape),T(s,n.shape)):(s=T({},r?this.shape:i.shape),T(s,n.shape)):l&&(s=i.shape),s)if(o){this.shape=T({},this.shape);for(var u={},h=E(s),c=0;c<h.length;c++){var p=h[c];"object"==typeof s[p]?this.shape[p]=s[p]:u[p]=s[p]}this._transitionState(e,{shape:u},a)}else this.shape=s,this.dirtyShape()},e.prototype._mergeStates=function(e){for(var n,i=t.prototype._mergeStates.call(this,e),r=0;r<e.length;r++){var o=e[r];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(i.shape=n),i},e.prototype.getAnimationStyleProps=function(){return Ia},e.prototype.isZeroArea=function(){return!1},e.extend=function(t){var i=function(e){function i(n){var i=e.call(this,n)||this;return t.init&&t.init.call(i,n),i}return n(i,e),i.prototype.getDefaultStyle=function(){return S(t.style)},i.prototype.getDefaultShape=function(){return S(t.shape)},i}(e);for(var r in t)"function"==typeof t[r]&&(i.prototype[r]=t[r]);return i},e.initDefaultProps=((i=e.prototype).type="path",i.strokeContainThreshold=5,i.segmentIgnoreThreshold=0,i.subPixelOptimize=!1,i.autoBatch=!1,void(i.__dirty=7)),e}(ro),Pa=C({strokeFirst:!0,font:si,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},Da),La=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&t.lineWidth>0},e.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},e.prototype.createStyle=function(t){return ct(Pa,t)},e.prototype.setBoundingRect=function(t){this._rect=t},e.prototype.getBoundingRect=function(){var t=this.style;if(!this._rect){var e=t.text;null!=e?e+="":e="";var n=ci(e,t.font,t.textAlign,t.textBaseline);if(n.x+=t.x||0,n.y+=t.y||0,this.hasStroke()){var i=t.lineWidth;n.x-=i/2,n.y-=i/2,n.width+=i,n.height+=i}this._rect=n}return this._rect},e.initDefaultProps=void(e.prototype.dirtyRectTolerance=10),e}(ro);La.prototype.type="tspan";var Oa=C({x:0,y:0},to),Ra={style:C({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},eo.style)};var Na=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.createStyle=function(t){return ct(Oa,t)},e.prototype._getSize=function(t){var e=this.style,n=e[t];if(null!=n)return n;var i,r=(i=e.image)&&"string"!=typeof i&&i.width&&i.height?e.image:this.__image;if(!r)return 0;var o="width"===t?"height":"width",a=e[o];return null==a?r[t]:r[t]/r[o]*a},e.prototype.getWidth=function(){return this._getSize("width")},e.prototype.getHeight=function(){return this._getSize("height")},e.prototype.getAnimationStyleProps=function(){return Ra},e.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new oi(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},e}(ro);Na.prototype.type="image";var Ea=Math.round;function za(t,e,n){if(!e)return t;var i=Ea(2*t);return(i+Ea(e))%2==0?i/2:(i+(n?1:-1))/2}var Ba=function(){this.x=0,this.y=0,this.width=0,this.height=0},Fa={},Va=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Ba},e.prototype.buildPath=function(t,e){var n,i,r,o;if(this.subPixelOptimize){var a=function(t,e,n){if(e){var i=e.x,r=e.y,o=e.width,a=e.height;t.x=i,t.y=r,t.width=o,t.height=a;var s=n&&n.lineWidth;return s?(t.x=za(i,s,!0),t.y=za(r,s,!0),t.width=Math.max(za(i+o,s,!1)-t.x,0===o?0:1),t.height=Math.max(za(r+a,s,!1)-t.y,0===a?0:1),t):t}}(Fa,e,this.style);n=a.x,i=a.y,r=a.width,o=a.height,a.r=e.r,e=a}else n=e.x,i=e.y,r=e.width,o=e.height;e.r?function(t,e){var n,i,r,o,a,s=e.x,l=e.y,u=e.width,h=e.height,c=e.r;u<0&&(s+=u,u=-u),h<0&&(l+=h,h=-h),"number"==typeof c?n=i=r=o=c:c instanceof Array?1===c.length?n=i=r=o=c[0]:2===c.length?(n=r=c[0],i=o=c[1]):3===c.length?(n=c[0],i=o=c[1],r=c[2]):(n=c[0],i=c[1],r=c[2],o=c[3]):n=i=r=o=0,n+i>u&&(n*=u/(a=n+i),i*=u/a),r+o>u&&(r*=u/(a=r+o),o*=u/a),i+r>h&&(i*=h/(a=i+r),r*=h/a),n+o>h&&(n*=h/(a=n+o),o*=h/a),t.moveTo(s+n,l),t.lineTo(s+u-i,l),0!==i&&t.arc(s+u-i,l+i,i,-Math.PI/2,0),t.lineTo(s+u,l+h-r),0!==r&&t.arc(s+u-r,l+h-r,r,0,Math.PI/2),t.lineTo(s+o,l+h),0!==o&&t.arc(s+o,l+h-o,o,Math.PI/2,Math.PI),t.lineTo(s,l+n),0!==n&&t.arc(s+n,l+n,n,Math.PI,1.5*Math.PI)}(t,e):t.rect(n,i,r,o)},e.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},e}(Aa);Va.prototype.type="rect";var Ha={fill:"#000"},Ga={style:C({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},eo.style)},Wa=function(t){function e(e){var n=t.call(this)||this;return n.type="text",n._children=[],n._defaultStyle=Ha,n.attr(e),n}return n(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.update=function(){t.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var n=this._children[e];n.zlevel=this.zlevel,n.z=this.z,n.z2=this.z2,n.culling=this.culling,n.cursor=this.cursor,n.invisible=this.invisible}},e.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):t.prototype.updateTransform.call(this)},e.prototype.getLocalTransform=function(e){var n=this.innerTransformable;return n?n.getLocalTransform(e):t.prototype.getLocalTransform.call(this,e)},e.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),t.prototype.getComputedTransform.call(this)},e.prototype._updateSubTexts=function(){var t;this._childCursor=0,Ya(t=this.style),L(t.rich,Ya),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=e},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=null},e.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new oi(0,0,0,0),e=this._children,n=[],i=null,r=0;r<e.length;r++){var o=e[r],a=o.getBoundingRect(),s=o.getLocalTransform(n);s?(t.copy(a),t.applyTransform(s),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},e.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||Ha},e.prototype.setTextContent=function(t){throw new Error("Can't attach text on another text")},e.prototype._mergeStyle=function(t,e){if(!e)return t;var n=e.rich,i=t.rich||n&&{};return T(t,e),n&&i?(this._mergeRich(i,n),t.rich=i):i&&(t.rich=i),t},e.prototype._mergeRich=function(t,e){for(var n=E(e),i=0;i<n.length;i++){var r=n[i];t[r]=t[r]||{},T(t[r],e[r])}},e.prototype.getAnimationStyleProps=function(){return Ga},e.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),this._children[this._childCursor++]=e,e.__zr=this.__zr,e.parent=this,e},e.prototype._updatePlainTexts=function(){var t=this.style,e=t.font||si,n=t.padding,i=function(t,e){null!=t&&(t+="");var n,i=e.overflow,r=e.padding,o=e.font,a="truncate"===i,s=di(o),l=Q(e.lineHeight,s),u="truncate"===e.lineOverflow,h=e.width,c=(n=null!=h&&"break"===i||"breakAll"===i?t?Jr(t,e.font,h,"breakAll"===i,0).lines:[]:t?t.split("\n"):[]).length*l,p=Q(e.height,c);if(c>p&&u){var f=Math.floor(p/l);n=n.slice(0,f)}var d=p,g=h;if(r&&(d+=r[0]+r[2],null!=g&&(g+=r[1]+r[3])),t&&a&&null!=g)for(var y=Wr(h,o,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),v=0;v<n.length;v++)n[v]=Ur(n[v],y);if(null==h){var m=0;for(v=0;v<n.length;v++)m=Math.max(ui(n[v],o),m);h=m}return{lines:n,height:p,outerHeight:d,lineHeight:l,calculatedLineHeight:s,contentHeight:c,width:h}}(Ka(t),t),r=$a(t),o=!!t.backgroundColor,a=i.outerHeight,s=i.lines,l=i.lineHeight,u=this._defaultStyle,h=t.x||0,c=t.y||0,p=t.align||u.align||"left",f=t.verticalAlign||u.verticalAlign||"top",d=h,g=fi(c,i.contentHeight,f);if(r||n){var y=i.width;n&&(y+=n[1]+n[3]);var v=pi(h,y,p),m=fi(c,a,f);r&&this._renderBackground(t,t,v,m,y,a)}g+=l/2,n&&(d=Za(h,p,n),"top"===f?g+=n[0]:"bottom"===f&&(g-=n[2]));for(var _=0,x=!1,b=(ja("fill"in t?t.fill:(x=!0,u.fill))),w=(qa("stroke"in t?t.stroke:o||u.autoStroke&&!x?null:(_=2,u.stroke))),S=t.textShadowBlur>0,M=null!=t.width&&("truncate"===t.overflow||"break"===t.overflow||"breakAll"===t.overflow),T=i.calculatedLineHeight,C=0;C<s.length;C++){var D=this._getOrCreateChild(La),I=D.createStyle();D.useStyle(I),I.text=s[C],I.x=d,I.y=g,p&&(I.textAlign=p),I.textBaseline="middle",I.opacity=t.opacity,I.strokeFirst=!0,S&&(I.shadowBlur=t.textShadowBlur||0,I.shadowColor=t.textShadowColor||"transparent",I.shadowOffsetX=t.textShadowOffsetX||0,I.shadowOffsetY=t.textShadowOffsetY||0),w&&(I.stroke=w,I.lineWidth=t.lineWidth||_,I.lineDash=t.lineDash,I.lineDashOffset=t.lineDashOffset||0),b&&(I.fill=b),I.font=e,g+=l,M&&D.setBoundingRect(new oi(pi(I.x,t.width,I.textAlign),fi(I.y,T,I.textBaseline),t.width,T))}},e.prototype._updateRichTexts=function(){var t=this.style,e=function(t,e){var n=new jr;if(null!=t&&(t+=""),!t)return n;for(var i,r=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==r?null:{width:r,accumWidth:0,breakAll:"breakAll"===a},l=Hr.lastIndex=0;null!=(i=Hr.exec(t));){var u=i.index;u>l&&Zr(n,t.substring(l,u),e,s),Zr(n,i[2],e,s,i[1]),l=Hr.lastIndex}l<t.length&&Zr(n,t.substring(l,t.length),e,s);var h=[],c=0,p=0,f=e.padding,d="truncate"===a,g="truncate"===e.lineOverflow;function y(t,e,n){t.width=e,t.lineHeight=n,c+=n,p=Math.max(p,e)}t:for(var v=0;v<n.lines.length;v++){for(var m=n.lines[v],_=0,x=0,b=0;b<m.tokens.length;b++){var w=(L=m.tokens[b]).styleName&&e.rich[L.styleName]||{},S=L.textPadding=w.padding,M=S?S[1]+S[3]:0,T=L.font=w.font||e.font;L.contentHeight=di(T);var C=Q(w.height,L.contentHeight);if(L.innerHeight=C,S&&(C+=S[0]+S[2]),L.height=C,L.lineHeight=tt(w.lineHeight,e.lineHeight,C),L.align=w&&w.align||e.align,L.verticalAlign=w&&w.verticalAlign||"middle",g&&null!=o&&c+L.lineHeight>o){b>0?(m.tokens=m.tokens.slice(0,b),y(m,x,_),n.lines=n.lines.slice(0,v+1)):n.lines=n.lines.slice(0,v);break t}var D=w.width,I=null==D||"auto"===D;if("string"==typeof D&&"%"===D.charAt(D.length-1))L.percentWidth=D,h.push(L),L.contentWidth=ui(L.text,T);else{if(I){var k=w.backgroundColor,A=k&&k.image;A&&Vr(A=zr(A))&&(L.width=Math.max(L.width,A.width*C/A.height))}var P=d&&null!=r?r-x:null;null!=P&&P<L.width?!I||P<M?(L.text="",L.width=L.contentWidth=0):(L.text=Gr(L.text,P-M,T,e.ellipsis,{minChar:e.truncateMinChar}),L.width=L.contentWidth=ui(L.text,T)):L.contentWidth=ui(L.text,T)}L.width+=M,x+=L.width,w&&(_=Math.max(_,L.lineHeight))}y(m,x,_)}for(n.outerWidth=n.width=Q(r,p),n.outerHeight=n.height=Q(o,c),n.contentHeight=c,n.contentWidth=p,f&&(n.outerWidth+=f[1]+f[3],n.outerHeight+=f[0]+f[2]),v=0;v<h.length;v++){var L,O=(L=h[v]).percentWidth;L.width=parseInt(O,10)/100*n.width}return n}(Ka(t),t),n=e.width,i=e.outerWidth,r=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,l=this._defaultStyle,u=t.align||l.align,h=t.verticalAlign||l.verticalAlign,c=pi(a,i,u),p=fi(s,r,h),f=c,d=p;o&&(f+=o[3],d+=o[0]);var g=f+n;$a(t)&&this._renderBackground(t,t,c,p,i,r);for(var y=!!t.backgroundColor,v=0;v<e.lines.length;v++){for(var m=e.lines[v],_=m.tokens,x=_.length,b=m.lineHeight,w=m.width,S=0,M=f,T=g,C=x-1,D=void 0;S<x&&(!(D=_[S]).align||"left"===D.align);)this._placeToken(D,t,b,d,M,"left",y),w-=D.width,M+=D.width,S++;for(;C>=0&&"right"===(D=_[C]).align;)this._placeToken(D,t,b,d,T,"right",y),w-=D.width,T-=D.width,C--;for(M+=(n-(M-f)-(g-T)-w)/2;S<=C;)D=_[S],this._placeToken(D,t,b,d,M+D.width/2,"center",y),M+=D.width,S++;d+=b}},e.prototype._placeToken=function(t,e,n,i,r,o,a){var s=e.rich[t.styleName]||{};s.text=t.text;var l=t.verticalAlign,u=i+n/2;"top"===l?u=i+t.height/2:"bottom"===l&&(u=i+n-t.height/2),!t.isLineHolder&&$a(s)&&this._renderBackground(s,e,"right"===o?r-t.width:"center"===o?r-t.width/2:r,u-t.height/2,t.width,t.height);var h=!!s.backgroundColor,c=t.textPadding;c&&(r=Za(r,o,c),u-=t.height/2-c[0]-t.innerHeight/2);var p=this._getOrCreateChild(La),f=p.createStyle();p.useStyle(f);var d=this._defaultStyle,g=!1,y=0,v=ja("fill"in s?s.fill:"fill"in e?e.fill:(g=!0,d.fill)),m=qa("stroke"in s?s.stroke:"stroke"in e?e.stroke:h||a||d.autoStroke&&!g?null:(y=2,d.stroke)),_=s.textShadowBlur>0||e.textShadowBlur>0;f.text=t.text,f.x=r,f.y=u,_&&(f.shadowBlur=s.textShadowBlur||e.textShadowBlur||0,f.shadowColor=s.textShadowColor||e.textShadowColor||"transparent",f.shadowOffsetX=s.textShadowOffsetX||e.textShadowOffsetX||0,f.shadowOffsetY=s.textShadowOffsetY||e.textShadowOffsetY||0),f.textAlign=o,f.textBaseline="middle",f.font=t.font||si,f.opacity=tt(s.opacity,e.opacity,1),m&&(f.lineWidth=tt(s.lineWidth,e.lineWidth,y),f.lineDash=Q(s.lineDash,e.lineDash),f.lineDashOffset=e.lineDashOffset||0,f.stroke=m),v&&(f.fill=v);var x=t.contentWidth,b=t.contentHeight;p.setBoundingRect(new oi(pi(f.x,x,f.textAlign),fi(f.y,b,f.textBaseline),x,b))},e.prototype._renderBackground=function(t,e,n,i,r,o){var a,s,l,u=t.backgroundColor,h=t.borderWidth,c=t.borderColor,p=u&&u.image,f=u&&!p,d=t.borderRadius,g=this;if(f||t.lineHeight||h&&c){(a=this._getOrCreateChild(Va)).useStyle(a.createStyle()),a.style.fill=null;var y=a.shape;y.x=n,y.y=i,y.width=r,y.height=o,y.r=d,a.dirtyShape()}if(f)(l=a.style).fill=u||null,l.fillOpacity=Q(t.fillOpacity,1);else if(p){(s=this._getOrCreateChild(Na)).onload=function(){g.dirtyStyle()};var v=s.style;v.image=u.image,v.x=n,v.y=i,v.width=r,v.height=o}h&&c&&((l=a.style).lineWidth=h,l.stroke=c,l.strokeOpacity=Q(t.strokeOpacity,1),l.lineDash=t.borderDash,l.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill()&&a.hasStroke()&&(l.strokeFirst=!0,l.lineWidth*=2));var m=(a||s).style;m.shadowBlur=t.shadowBlur||0,m.shadowColor=t.shadowColor||"transparent",m.shadowOffsetX=t.shadowOffsetX||0,m.shadowOffsetY=t.shadowOffsetY||0,m.opacity=tt(t.opacity,e.opacity,1)},e.makeFont=function(t){var e="";if(t.fontSize||t.fontFamily||t.fontWeight){var n="";n="string"!=typeof t.fontSize||-1===t.fontSize.indexOf("px")&&-1===t.fontSize.indexOf("rem")&&-1===t.fontSize.indexOf("em")?isNaN(+t.fontSize)?"12px":t.fontSize+"px":t.fontSize,e=[t.fontStyle,t.fontWeight,n,t.fontFamily||"sans-serif"].join(" ")}return e&&rt(e)||t.textFont||t.font},e}(ro),Ua={left:!0,right:1,center:1},Xa={top:1,bottom:1,middle:1};function Ya(t){if(t){t.font=Wa.makeFont(t);var e=t.align;"middle"===e&&(e="center"),t.align=null==e||Ua[e]?e:"left";var n=t.verticalAlign;"center"===n&&(n="middle"),t.verticalAlign=null==n||Xa[n]?n:"top",t.padding&&(t.padding=nt(t.padding))}}function qa(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function ja(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Za(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Ka(t){var e=t.text;return null!=e&&(e+=""),e}function $a(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var Ja=yr(),Qa=1,ts={},es=yr(),ns=["emphasis","blur","select"],is=["normal","emphasis","blur","select"],rs="highlight",os="downplay",as="select",ss="unselect",ls="toggleSelect";function us(t){return null!=t&&"none"!==t}var hs=new we(100);function cs(t){if("string"!=typeof t)return t;var e=hs.get(t);return e||(e=ze(t,-.1),hs.put(t,e)),e}function ps(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function fs(t){ps(t,"emphasis",2)}function ds(t){2===t.hoverState&&ps(t,"normal",0)}function gs(t){ps(t,"blur",1)}function ys(t){1===t.hoverState&&ps(t,"normal",0)}function vs(t){t.selected=!0}function ms(t){t.selected=!1}function _s(t,e,n){e(t,n)}function xs(t,e,n){_s(t,e,n),t.isGroup&&t.traverse((function(t){_s(t,e,n)}))}function bs(t,e){switch(e){case"emphasis":t.hoverState=2;break;case"normal":t.hoverState=0;break;case"blur":t.hoverState=1;break;case"select":t.selected=!0}}function ws(t,e){var n=this.states[t];if(this.style){if("emphasis"===t)return function(t,e,n,i){var r=n&&I(n,"select")>=0,o=!1;if(t instanceof Aa){var a=es(t),s=r&&a.selectFill||a.normalFill,l=r&&a.selectStroke||a.normalStroke;if(us(s)||us(l)){var u=(i=i||{}).style||{};"inherit"===u.fill?(o=!0,i=T({},i),(u=T({},u)).fill=s):!us(u.fill)&&us(s)?(o=!0,i=T({},i),(u=T({},u)).fill=cs(s)):!us(u.stroke)&&us(l)&&(o||(i=T({},i),u=T({},u)),u.stroke=cs(l)),i.style=u}}if(i&&null==i.z2){o||(i=T({},i));var h=t.z2EmphasisLift;i.z2=t.z2+(null!=h?h:10)}return i}(this,0,e,n);if("blur"===t)return function(t,e,n){var i=I(t.currentStates,e)>=0,r=t.style.opacity,o=i?null:function(t,e,n,i){for(var r=t.style,o={},a=0;a<e.length;a++){var s=e[a],l=r[s];o[s]=null==l?i&&i[s]:l}for(a=0;a<t.animators.length;a++){var u=t.animators[a];u.__fromStateTransition&&u.__fromStateTransition.indexOf(n)<0&&"style"===u.targetName&&u.saveFinalToTarget(o,e)}return o}(t,["opacity"],e,{opacity:1}),a=(n=n||{}).style||{};return null==a.opacity&&(n=T({},n),a=T({opacity:i?r:.1*o.opacity},a),n.style=a),n}(this,t,n);if("select"===t)return function(t,e,n){if(n&&null==n.z2){n=T({},n);var i=t.z2SelectLift;n.z2=t.z2+(null!=i?i:9)}return n}(this,0,n)}return n}function Ss(t){t.stateProxy=ws;var e=t.getTextContent(),n=t.getTextGuideLine();e&&(e.stateProxy=ws),n&&(n.stateProxy=ws)}function Ms(t,e){!Ps(t,e)&&!t.__highByOuter&&xs(t,fs)}function Ts(t,e){!Ps(t,e)&&!t.__highByOuter&&xs(t,ds)}function Cs(t,e){t.__highByOuter|=1<<(e||0),xs(t,fs)}function Ds(t,e){!(t.__highByOuter&=~(1<<(e||0)))&&xs(t,ds)}function Is(t){xs(t,ys)}function ks(t){xs(t,vs)}function As(t){xs(t,ms)}function Ps(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Ls(t){t.getModel().eachComponent((function(e,n){("series"===e?t.getViewOfSeriesModel(n):t.getViewOfComponentModel(n)).group.traverse((function(t){ys(t)}))}))}function Os(t,e,n,i){var r=i.getModel();function o(t,e){for(var n=0;n<e.length;n++){var i=t.getItemGraphicEl(e[n]);i&&Is(i)}}if(n=n||"coordinateSystem",null!=t&&e&&"none"!==e){var a=r.getSeriesByIndex(t),s=a.coordinateSystem;s&&s.master&&(s=s.master);var l=[];r.eachSeries((function(t){var r=a===t,u=t.coordinateSystem;if(u&&u.master&&(u=u.master),!("series"===n&&!r||"coordinateSystem"===n&&!(u&&s?u===s:r)||"series"===e&&r)){if(i.getViewOfSeriesModel(t).group.traverse((function(t){gs(t)})),P(e))o(t.getData(),e);else if(U(e))for(var h=E(e),c=0;c<h.length;c++)o(t.getData(h[c]),e[h[c]]);l.push(t)}})),r.eachComponent((function(t,e){if("series"!==t){var n=i.getViewOfComponentModel(e);n&&n.blurSeries&&n.blurSeries(l,r)}}))}}function Rs(t,e,n){if(null!=t&&null!=e){var i=n.getModel().getComponent(t,e);if(i){var r=n.getViewOfComponentModel(i);r&&r.focusBlurEnabled&&r.group.traverse((function(t){gs(t)}))}}}function Ns(t,e,n,i){var r={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return r;var o=i.getModel().getComponent(t,e);if(!o)return r;var a=i.getViewOfComponentModel(o);if(!a||!a.findHighDownDispatchers)return r;for(var s,l=a.findHighDownDispatchers(n),u=0;u<l.length;u++)if("self"===Ja(l[u]).focus){s=!0;break}return{focusSelf:s,dispatchers:l}}function Es(t){L(t.getAllData(),(function(e){var n=e.data,i=e.type;n.eachItemGraphicEl((function(e,n){t.isSelected(n,i)?ks(e):As(e)}))}))}function zs(t){var e=[];return t.eachSeries((function(t){L(t.getAllData(),(function(n){n.data;var i=n.type,r=t.getSelectedDataIndices();if(r.length>0){var o={dataIndex:r,seriesIndex:t.seriesIndex};null!=i&&(o.dataType=i),e.push(o)}}))})),e}function Bs(t,e,n){!function(t,e){var n=!1===e,i=t;t.highDownSilentOnTouch&&(i.__highDownSilentOnTouch=t.highDownSilentOnTouch);n&&!i.__highDownDispatcher||(i.__highByOuter=i.__highByOuter||0,i.__highDownDispatcher=!n)}(t,!0),xs(t,Ss),function(t,e,n){var i=Ja(t);null!=e?(i.focus=e,i.blurScope=n):i.focus&&(i.focus=null)}(t,e,n)}var Fs=["emphasis","blur","select"],Vs={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function Hs(t,e,n,i){n=n||"itemStyle";for(var r=0;r<Fs.length;r++){var o=Fs[r],a=e.getModel([o,n]);t.ensureState(o).style=i?i(a):a[Vs[n]]()}}function Gs(t){return!(!t||!t.__highDownDispatcher)}function Ws(t){var e=t.type;return e===as||e===ss||e===ls}function Us(t){var e=t.type;return e===rs||e===os}var Xs=ha.CMD,Ys=[[],[],[]],qs=Math.sqrt,js=Math.atan2;var Zs=Math.sqrt,Ks=Math.sin,$s=Math.cos,Js=Math.PI;function Qs(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function tl(t,e){return(t[0]*e[0]+t[1]*e[1])/(Qs(t)*Qs(e))}function el(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(tl(t,e))}function nl(t,e,n,i,r,o,a,s,l,u,h){var c=l*(Js/180),p=$s(c)*(t-n)/2+Ks(c)*(e-i)/2,f=-1*Ks(c)*(t-n)/2+$s(c)*(e-i)/2,d=p*p/(a*a)+f*f/(s*s);d>1&&(a*=Zs(d),s*=Zs(d));var g=(r===o?-1:1)*Zs((a*a*(s*s)-a*a*(f*f)-s*s*(p*p))/(a*a*(f*f)+s*s*(p*p)))||0,y=g*a*f/s,v=g*-s*p/a,m=(t+n)/2+$s(c)*y-Ks(c)*v,_=(e+i)/2+Ks(c)*y+$s(c)*v,x=el([1,0],[(p-y)/a,(f-v)/s]),b=[(p-y)/a,(f-v)/s],w=[(-1*p-y)/a,(-1*f-v)/s],S=el(b,w);if(tl(b,w)<=-1&&(S=Js),tl(b,w)>=1&&(S=0),S<0){var M=Math.round(S/Js*1e6)/1e6;S=2*Js+M%2*Js}h.addData(u,m,_,a,s,x,S,c,o)}var il=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,rl=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;var ol=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.applyTransform=function(t){},e}(Aa);function al(t){return null!=t.setData}function sl(t,e){var n=function(t){var e=new ha;if(!t)return e;var n,i=0,r=0,o=i,a=r,s=ha.CMD,l=t.match(il);if(!l)return e;for(var u=0;u<l.length;u++){for(var h=l[u],c=h.charAt(0),p=void 0,f=h.match(rl)||[],d=f.length,g=0;g<d;g++)f[g]=parseFloat(f[g]);for(var y=0;y<d;){var v=void 0,m=void 0,_=void 0,x=void 0,b=void 0,w=void 0,S=void 0,M=i,T=r,C=void 0,D=void 0;switch(c){case"l":i+=f[y++],r+=f[y++],p=s.L,e.addData(p,i,r);break;case"L":i=f[y++],r=f[y++],p=s.L,e.addData(p,i,r);break;case"m":i+=f[y++],r+=f[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="l";break;case"M":i=f[y++],r=f[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="L";break;case"h":i+=f[y++],p=s.L,e.addData(p,i,r);break;case"H":i=f[y++],p=s.L,e.addData(p,i,r);break;case"v":r+=f[y++],p=s.L,e.addData(p,i,r);break;case"V":r=f[y++],p=s.L,e.addData(p,i,r);break;case"C":p=s.C,e.addData(p,f[y++],f[y++],f[y++],f[y++],f[y++],f[y++]),i=f[y-2],r=f[y-1];break;case"c":p=s.C,e.addData(p,f[y++]+i,f[y++]+r,f[y++]+i,f[y++]+r,f[y++]+i,f[y++]+r),i+=f[y-2],r+=f[y-1];break;case"S":v=i,m=r,C=e.len(),D=e.data,n===s.C&&(v+=i-D[C-4],m+=r-D[C-3]),p=s.C,M=f[y++],T=f[y++],i=f[y++],r=f[y++],e.addData(p,v,m,M,T,i,r);break;case"s":v=i,m=r,C=e.len(),D=e.data,n===s.C&&(v+=i-D[C-4],m+=r-D[C-3]),p=s.C,M=i+f[y++],T=r+f[y++],i+=f[y++],r+=f[y++],e.addData(p,v,m,M,T,i,r);break;case"Q":M=f[y++],T=f[y++],i=f[y++],r=f[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"q":M=f[y++]+i,T=f[y++]+r,i+=f[y++],r+=f[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"T":v=i,m=r,C=e.len(),D=e.data,n===s.Q&&(v+=i-D[C-4],m+=r-D[C-3]),i=f[y++],r=f[y++],p=s.Q,e.addData(p,v,m,i,r);break;case"t":v=i,m=r,C=e.len(),D=e.data,n===s.Q&&(v+=i-D[C-4],m+=r-D[C-3]),i+=f[y++],r+=f[y++],p=s.Q,e.addData(p,v,m,i,r);break;case"A":_=f[y++],x=f[y++],b=f[y++],w=f[y++],S=f[y++],nl(M=i,T=r,i=f[y++],r=f[y++],w,S,_,x,b,p=s.A,e);break;case"a":_=f[y++],x=f[y++],b=f[y++],w=f[y++],S=f[y++],nl(M=i,T=r,i+=f[y++],r+=f[y++],w,S,_,x,b,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,r=a),n=p}return e.toStatic(),e}(t),i=T({},e);return i.buildPath=function(t){if(al(t)){t.setData(n.data),(e=t.getContext())&&t.rebuildPath(e,1)}else{var e=t;n.rebuildPath(e,1)}},i.applyTransform=function(t){!function(t,e){if(e){var n,i,r,o,a,s,l=t.data,u=t.len(),h=Xs.M,c=Xs.C,p=Xs.L,f=Xs.R,d=Xs.A,g=Xs.Q;for(r=0,o=0;r<u;){switch(n=l[r++],o=r,i=0,n){case h:case p:i=1;break;case c:i=3;break;case g:i=2;break;case d:var y=e[4],v=e[5],m=qs(e[0]*e[0]+e[1]*e[1]),_=qs(e[2]*e[2]+e[3]*e[3]),x=js(-e[1]/_,e[0]/m);l[r]*=m,l[r++]+=y,l[r]*=_,l[r++]+=v,l[r++]*=m,l[r++]*=_,l[r++]+=x,l[r++]+=x,o=r+=2;break;case f:s[0]=l[r++],s[1]=l[r++],At(s,s,e),l[o++]=s[0],l[o++]=s[1],s[0]+=l[r++],s[1]+=l[r++],At(s,s,e),l[o++]=s[0],l[o++]=s[1]}for(a=0;a<i;a++){var b=Ys[a];b[0]=l[r++],b[1]=l[r++],At(b,b,e),l[o++]=b[0],l[o++]=b[1]}}t.increaseVersion()}}(n,t),this.dirtyShape()},i}function ll(t,e){return new ol(sl(t,e))}var ul=function(){this.cx=0,this.cy=0,this.r=0},hl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new ul},e.prototype.buildPath=function(t,e,n){n&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},e}(Aa);hl.prototype.type="circle";var cl=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},pl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new cl},e.prototype.buildPath=function(t,e){var n=.5522848,i=e.cx,r=e.cy,o=e.rx,a=e.ry,s=o*n,l=a*n;t.moveTo(i-o,r),t.bezierCurveTo(i-o,r-l,i-s,r-a,i,r-a),t.bezierCurveTo(i+s,r-a,i+o,r-l,i+o,r),t.bezierCurveTo(i+o,r+l,i+s,r+a,i,r+a),t.bezierCurveTo(i-s,r+a,i-o,r+l,i-o,r),t.closePath()},e}(Aa);pl.prototype.type="ellipse";var fl=Math.PI,dl=2*fl,gl=Math.sin,yl=Math.cos,vl=Math.acos,ml=Math.atan2,_l=Math.abs,xl=Math.sqrt,bl=Math.max,wl=Math.min,Sl=1e-4;function Ml(t,e,n,i,r,o,a){var s=t-n,l=e-i,u=(a?o:-o)/xl(s*s+l*l),h=u*l,c=-u*s,p=t+h,f=e+c,d=n+h,g=i+c,y=(p+d)/2,v=(f+g)/2,m=d-p,_=g-f,x=m*m+_*_,b=r-o,w=p*g-d*f,S=(_<0?-1:1)*xl(bl(0,b*b*x-w*w)),M=(w*_-m*S)/x,T=(-w*m-_*S)/x,C=(w*_+m*S)/x,D=(-w*m+_*S)/x,I=M-y,k=T-v,A=C-y,P=D-v;return I*I+k*k>A*A+P*P&&(M=C,T=D),{cx:M,cy:T,x01:-h,y01:-c,x11:M*(r/b-1),y11:T*(r/b-1)}}function Tl(t,e){var n=bl(e.r,0),i=bl(e.r0||0,0),r=n>0;if(r||i>0){if(r||(n=i,i=0),i>n){var o=n;n=i,i=o}var a,s=!!e.clockwise,l=e.startAngle,u=e.endAngle;if(l===u)a=0;else{var h=[l,u];ua(h,!s),a=_l(h[0]-h[1])}var c=e.cx,p=e.cy,f=e.cornerRadius||0,d=e.innerCornerRadius||0;if(n>Sl)if(a>dl-Sl)t.moveTo(c+n*yl(l),p+n*gl(l)),t.arc(c,p,n,l,u,!s),i>Sl&&(t.moveTo(c+i*yl(u),p+i*gl(u)),t.arc(c,p,i,u,l,s));else{var g=_l(n-i)/2,y=wl(g,f),v=wl(g,d),m=v,_=y,x=n*yl(l),b=n*gl(l),w=i*yl(u),S=i*gl(u),M=void 0,T=void 0,C=void 0,D=void 0;if((y>Sl||v>Sl)&&(M=n*yl(u),T=n*gl(u),C=i*yl(l),D=i*gl(l),a<fl)){var I=function(t,e,n,i,r,o,a,s){var l=n-t,u=i-e,h=a-r,c=s-o,p=c*l-h*u;if(!(p*p<Sl))return[t+(p=(h*(e-o)-c*(t-r))/p)*l,e+p*u]}(x,b,C,D,M,T,w,S);if(I){var k=x-I[0],A=b-I[1],P=M-I[0],L=T-I[1],O=1/gl(vl((k*P+A*L)/(xl(k*k+A*A)*xl(P*P+L*L)))/2),R=xl(I[0]*I[0]+I[1]*I[1]);m=wl(v,(i-R)/(O-1)),_=wl(y,(n-R)/(O+1))}}if(a>Sl)if(_>Sl){var N=Ml(C,D,x,b,n,_,s),E=Ml(M,T,w,S,n,_,s);t.moveTo(c+N.cx+N.x01,p+N.cy+N.y01),_<y?t.arc(c+N.cx,p+N.cy,_,ml(N.y01,N.x01),ml(E.y01,E.x01),!s):(t.arc(c+N.cx,p+N.cy,_,ml(N.y01,N.x01),ml(N.y11,N.x11),!s),t.arc(c,p,n,ml(N.cy+N.y11,N.cx+N.x11),ml(E.cy+E.y11,E.cx+E.x11),!s),t.arc(c+E.cx,p+E.cy,_,ml(E.y11,E.x11),ml(E.y01,E.x01),!s))}else t.moveTo(c+x,p+b),t.arc(c,p,n,l,u,!s);else t.moveTo(c+x,p+b);if(i>Sl&&a>Sl)if(m>Sl){N=Ml(w,S,M,T,i,-m,s),E=Ml(x,b,C,D,i,-m,s);t.lineTo(c+N.cx+N.x01,p+N.cy+N.y01),m<v?t.arc(c+N.cx,p+N.cy,m,ml(N.y01,N.x01),ml(E.y01,E.x01),!s):(t.arc(c+N.cx,p+N.cy,m,ml(N.y01,N.x01),ml(N.y11,N.x11),!s),t.arc(c,p,i,ml(N.cy+N.y11,N.cx+N.x11),ml(E.cy+E.y11,E.cx+E.x11),s),t.arc(c+E.cx,p+E.cy,m,ml(E.y11,E.x11),ml(E.y01,E.x01),!s))}else t.lineTo(c+w,p+S),t.arc(c,p,i,u,l,s);else t.lineTo(c+w,p+S)}else t.moveTo(c,p);t.closePath()}}var Cl=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0,this.innerCornerRadius=0},Dl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Cl},e.prototype.buildPath=function(t,e){Tl(t,e)},e.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},e}(Aa);Dl.prototype.type="sector";var Il=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},kl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Il},e.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)},e}(Aa);function Al(t,e,n,i,r,o,a){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*a+(-3*(e-n)-2*s-l)*o+s*r+e}function Pl(t,e,n){var i=e.smooth,r=e.points;if(r&&r.length>=2){if(i&&"spline"!==i){var o=function(t,e,n,i){var r,o,a,s,l=[],u=[],h=[],c=[];if(i){a=[1/0,1/0],s=[-1/0,-1/0];for(var p=0,f=t.length;p<f;p++)Pt(a,a,t[p]),Lt(s,s,t[p]);Pt(a,a,i[0]),Lt(s,s,i[1])}for(p=0,f=t.length;p<f;p++){var d=t[p];if(n)r=t[p?p-1:f-1],o=t[(p+1)%f];else{if(0===p||p===f-1){l.push(yt(t[p]));continue}r=t[p-1],o=t[p+1]}mt(u,o,r),St(u,u,e);var g=Tt(d,r),y=Tt(d,o),v=g+y;0!==v&&(g/=v,y/=v),St(h,u,-g),St(c,u,y);var m=vt([],d,h),_=vt([],d,c);i&&(Lt(m,m,a),Pt(m,m,s),Lt(_,_,a),Pt(_,_,s)),l.push(m),l.push(_)}return n&&l.push(l.shift()),l}(r,i,n,e.smoothConstraint);t.moveTo(r[0][0],r[0][1]);for(var a=r.length,s=0;s<(n?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=r[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}}else{"spline"===i&&(r=function(t,e){for(var n=t.length,i=[],r=0,o=1;o<n;o++)r+=Tt(t[o-1],t[o]);var a=r/2;for(a=a<n?n:a,o=0;o<a;o++){var s=o/(a-1)*(e?n:n-1),l=Math.floor(s),u=s-l,h=void 0,c=t[l%n],p=void 0,f=void 0;e?(h=t[(l-1+n)%n],p=t[(l+1)%n],f=t[(l+2)%n]):(h=t[0===l?l:l-1],p=t[l>n-2?n-1:l+1],f=t[l>n-3?n-1:l+2]);var d=u*u,g=u*d;i.push([Al(h[0],c[0],p[0],f[0],u,d,g),Al(h[1],c[1],p[1],f[1],u,d,g)])}return i}(r,n)),t.moveTo(r[0][0],r[0][1]);s=1;for(var c=r.length;s<c;s++)t.lineTo(r[s][0],r[s][1])}n&&t.closePath()}}kl.prototype.type="ring";var Ll=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},Ol=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Ll},e.prototype.buildPath=function(t,e){Pl(t,e,!0)},e}(Aa);Ol.prototype.type="polygon";var Rl=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},Nl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Rl},e.prototype.buildPath=function(t,e){Pl(t,e,!1)},e}(Aa);Nl.prototype.type="polyline";var El={},zl=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},Bl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new zl},e.prototype.buildPath=function(t,e){var n,i,r,o;if(this.subPixelOptimize){var a=function(t,e,n){if(e){var i=e.x1,r=e.x2,o=e.y1,a=e.y2;t.x1=i,t.x2=r,t.y1=o,t.y2=a;var s=n&&n.lineWidth;return s?(Ea(2*i)===Ea(2*r)&&(t.x1=t.x2=za(i,s,!0)),Ea(2*o)===Ea(2*a)&&(t.y1=t.y2=za(o,s,!0)),t):t}}(El,e,this.style);n=a.x1,i=a.y1,r=a.x2,o=a.y2}else n=e.x1,i=e.y1,r=e.x2,o=e.y2;var s=e.percent;0!==s&&(t.moveTo(n,i),s<1&&(r=n*(1-s)+r*s,o=i*(1-s)+o*s),t.lineTo(r,o))},e.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},e}(Aa);Bl.prototype.type="line";var Fl=[],Vl=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function Hl(t,e,n){var i=t.cpx2,r=t.cpy2;return null===i||null===r?[(n?xo:_o)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?xo:_o)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?Co:To)(t.x1,t.cpx1,t.x2,e),(n?Co:To)(t.y1,t.cpy1,t.y2,e)]}var Gl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Vl},e.prototype.buildPath=function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,h=e.percent;0!==h&&(t.moveTo(n,i),null==l||null==u?(h<1&&(Io(n,a,r,h,Fl),a=Fl[1],r=Fl[2],Io(i,s,o,h,Fl),s=Fl[1],o=Fl[2]),t.quadraticCurveTo(a,s,r,o)):(h<1&&(So(n,a,l,r,h,Fl),a=Fl[1],l=Fl[2],r=Fl[3],So(i,s,u,o,h,Fl),s=Fl[1],u=Fl[2],o=Fl[3]),t.bezierCurveTo(a,s,l,u,r,o)))},e.prototype.pointAt=function(t){return Hl(this.shape,t,!1)},e.prototype.tangentAt=function(t){var e=Hl(this.shape,t,!0);return Mt(e,e)},e}(Aa);Gl.prototype.type="bezier-curve";var Wl=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},Ul=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Wl},e.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,l=Math.cos(o),u=Math.sin(o);t.moveTo(l*r+n,u*r+i),t.arc(n,i,r,o,a,!s)},e}(Aa);Ul.prototype.type="arc";var Xl=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="compound",e}return n(e,t),e.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},e.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},e.prototype.buildPath=function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},e.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},e.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),Aa.prototype.getBoundingRect.call(this)},e}(Aa),Yl=function(){function t(t){this.colorStops=t||[]}return t.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},t}(),ql=function(t){function e(e,n,i,r,o,a){var s=t.call(this,o)||this;return s.x=null==e?0:e,s.y=null==n?0:n,s.x2=null==i?1:i,s.y2=null==r?0:r,s.type="linear",s.global=a||!1,s}return n(e,t),e}(Yl),jl=function(t){function e(e,n,i,r,o){var a=t.call(this,r)||this;return a.x=null==e?.5:e,a.y=null==n?.5:n,a.r=null==i?.5:i,a.type="radial",a.global=o||!1,a}return n(e,t),e}(Yl),Zl=[0,0],Kl=[0,0],$l=new Kn,Jl=new Kn,Ql=function(){function t(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new Kn;for(n=0;n<2;n++)this._axes[n]=new Kn;t&&this.fromBoundingRect(t,e)}return t.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,r=t.x,o=t.y,a=r+t.width,s=o+t.height;if(n[0].set(r,o),n[1].set(a,o),n[2].set(a,s),n[3].set(r,s),e)for(var l=0;l<4;l++)n[l].transform(e);Kn.sub(i[0],n[1],n[0]),Kn.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(l=0;l<2;l++)this._origin[l]=i[l].dot(n[0])},t.prototype.intersect=function(t,e){var n=!0,i=!e;return $l.set(1/0,1/0),Jl.set(0,0),!this._intersectCheckOneSide(this,t,$l,Jl,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,$l,Jl,i,-1)&&(n=!1,i)||i||Kn.copy(e,n?$l:Jl),n},t.prototype._intersectCheckOneSide=function(t,e,n,i,r,o){for(var a=!0,s=0;s<2;s++){var l=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,Zl),this._getProjMinMaxOnAxis(s,e._corners,Kl),Zl[1]<Kl[0]||Zl[0]>Kl[1]){if(a=!1,r)return a;var u=Math.abs(Kl[0]-Zl[1]),h=Math.abs(Zl[0]-Kl[1]);Math.min(u,h)>i.len()&&(u<h?Kn.scale(i,l,-u*o):Kn.scale(i,l,h*o))}else if(n){u=Math.abs(Kl[0]-Zl[1]),h=Math.abs(Zl[0]-Kl[1]);Math.min(u,h)<n.len()&&(u<h?Kn.scale(n,l,u*o):Kn.scale(n,l,-h*o))}}return a},t.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],r=this._origin,o=e[0].dot(i)+r[t],a=o,s=o,l=1;l<e.length;l++){var u=e[l].dot(i)+r[t];a=Math.min(u,a),s=Math.max(u,s)}n[0]=a,n[1]=s},t}(),tu=[],eu=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return n(e,t),e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.useStyle=function(){this.style={}},e.prototype.getCursor=function(){return this._cursor},e.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},e.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},e.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},e.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},e.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},e.prototype.getDisplayables=function(){return this._displayables},e.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},e.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},e.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){(e=this._displayables[t]).parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){var e;(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null}},e.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new oi(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(tu)),t.union(i)}this._rect=t}return this._rect},e.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++){if(this._displayables[i].contain(t,e))return!0}return!1},e}(ro),nu=yr();function iu(t,e,n,i,r,o,a){var s,l=!1;"function"==typeof r?(a=o,o=r,r=null):U(r)&&(o=r.cb,a=r.during,l=r.isFrom,s=r.removeOpt,r=r.dataIndex);var u="remove"===t;u||e.stopAnimation("remove");var h=function(t,e,n,i,r){var o;if(e&&e.ecModel){var a=e.ecModel.getUpdatePayload();o=a&&a.animation}var s="update"===t;if(e&&e.isAnimationEnabled()){var l=void 0,u=void 0,h=void 0;return i?(l=Q(i.duration,200),u=Q(i.easing,"cubicOut"),h=0):(l=e.getShallow(s?"animationDurationUpdate":"animationDuration"),u=e.getShallow(s?"animationEasingUpdate":"animationEasing"),h=e.getShallow(s?"animationDelayUpdate":"animationDelay")),o&&(null!=o.duration&&(l=o.duration),null!=o.easing&&(u=o.easing),null!=o.delay&&(h=o.delay)),"function"==typeof h&&(h=h(n,r)),"function"==typeof l&&(l=l(n)),{duration:l||0,delay:h,easing:u}}return null}(t,i,r,u?s||{}:null,i&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null);if(h&&h.duration>0){var c={duration:h.duration,delay:h.delay||0,easing:h.easing,done:o,force:!!o||!!a,setToFinal:!u,scope:t,during:a};l?e.animateFrom(n,c):e.animateTo(n,c)}else e.stopAnimation(),!l&&e.attr(n),a&&a(1),o&&o()}function ru(t,e,n,i,r,o){iu("update",t,e,n,i,r,o)}function ou(t,e,n,i,r,o){iu("init",t,e,n,i,r,o)}function au(t){if(!t.__zr)return!0;for(var e=0;e<t.animators.length;e++){if("remove"===t.animators[e].scope)return!0}return!1}function su(t,e,n,i,r,o){au(t)||iu("remove",t,e,n,i,r,o)}function lu(t,e,n,i){t.removeTextContent(),t.removeTextGuideLine(),su(t,{style:{opacity:0}},e,n,i)}function uu(t,e,n){function i(){t.parent&&t.parent.remove(t)}t.isGroup?t.traverse((function(t){t.isGroup||lu(t,e,n,i)})):lu(t,e,n,i)}function hu(t){nu(t).oldStyle=t.style}var cu=Math.max,pu=Math.min,fu={};var du=function(t,e){var i=sl(t,e);return function(t){function e(e){var n=t.call(this,e)||this;return n.applyTransform=i.applyTransform,n.buildPath=i.buildPath,n}return n(e,t),e}(ol)};function gu(t,e){fu[t]=e}function yu(t,e,n,i){var r=ll(t,e);return n&&("center"===i&&(n=mu(n,r.getBoundingRect())),xu(r,n)),r}function vu(t,e,n){var i=new Na({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===n){var r={width:t.width,height:t.height};i.setStyle(mu(e,r))}}});return i}function mu(t,e){var n,i=e.width/e.height,r=t.height*i;return n=r<=t.width?t.height:(r=t.width)/i,{x:t.x+t.width/2-r/2,y:t.y+t.height/2-n/2,width:r,height:n}}var _u=function(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];n.push(o.getUpdatedPathProxy(!0))}var a=new Aa(e);return a.createPathProxy(),a.buildPath=function(t){if(al(t)){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e,1)}},a};function xu(t,e){if(t.applyTransform){var n=t.getBoundingRect().calculateTransform(e);t.applyTransform(n)}}function bu(t){return!t.isGroup}gu("circle",hl),gu("ellipse",pl),gu("sector",Dl),gu("ring",kl),gu("polygon",Ol),gu("polyline",Nl),gu("rect",Va),gu("line",Bl),gu("bezierCurve",Gl),gu("arc",Ul);var wu={};function Su(t,e,n){var i,r=t.labelFetcher,o=t.labelDataIndex,a=t.labelDimIndex,s=e.normal;r&&(i=r.getFormattedLabel(o,"normal",null,a,s&&s.get("formatter"),null!=n?{interpolatedValue:n}:null)),null==i&&(i=V(t.defaultText)?t.defaultText(o,t,n):t.defaultText);for(var l={normal:i},u=0;u<ns.length;u++){var h=ns[u],c=e[h];l[h]=Q(r?r.getFormattedLabel(o,h,null,a,c&&c.get("formatter")):null,i)}return l}function Mu(t,e,n,i){n=n||wu;for(var r=t instanceof Wa,o=!1,a=0;a<is.length;a++){if((p=e[is[a]])&&p.getShallow("show")){o=!0;break}}var s=r?t:t.getTextContent();if(o){r||(s||(s=new Wa,t.setTextContent(s)),t.stateProxy&&(s.stateProxy=t.stateProxy));var l=Su(n,e),u=e.normal,h=!!u.getShallow("show"),c=Cu(u,i&&i.normal,n,!1,!r);c.text=l.normal,r||t.setTextConfig(Du(u,n,!1));for(a=0;a<ns.length;a++){var p,f=ns[a];if(p=e[f]){var d=s.ensureState(f),g=!!Q(p.getShallow("show"),h);if(g!==h&&(d.ignore=!g),d.style=Cu(p,i&&i[f],n,!0,!r),d.style.text=l[f],!r)t.ensureState(f).textConfig=Du(p,n,!0)}}s.silent=!!u.getShallow("silent"),null!=s.style.x&&(c.x=s.style.x),null!=s.style.y&&(c.y=s.style.y),s.ignore=!h,s.useStyle(c),s.dirty(),n.enableTextSetter&&(Lu(s).setLabelText=function(t){var i=Su(n,e,t);!function(t,e){for(var n=0;n<ns.length;n++){var i=ns[n],r=e[i],o=t.ensureState(i);o.style=o.style||{},o.style.text=r}var a=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(a,!0)}(s,i)})}else s&&(s.ignore=!0);t.dirty()}function Tu(t,e){e=e||"label";for(var n={normal:t.getModel(e)},i=0;i<ns.length;i++){var r=ns[i];n[r]=t.getModel([r,e])}return n}function Cu(t,e,n,i,r){var o={};return function(t,e,n,i,r){n=n||wu;var o,a=e.ecModel,s=a&&a.option.textStyle,l=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||wu).rich;if(n){e=e||{};for(var i=E(n),r=0;r<i.length;r++){e[i[r]]=1}}t=t.parentModel}return e}(e);if(l)for(var u in o={},l)if(l.hasOwnProperty(u)){var h=e.getModel(["rich",u]);Pu(o[u]={},h,s,n,i,r,!1,!0)}o&&(t.rich=o);var c=e.get("overflow");c&&(t.overflow=c);var p=e.get("minMargin");null!=p&&(t.margin=p);Pu(t,e,s,n,i,r,!0,!1)}(o,t,n,i,r),e&&T(o,e),o}function Du(t,e,n){e=e||{};var i,r={},o=t.getShallow("rotate"),a=Q(t.getShallow("distance"),n?null:5),s=t.getShallow("offset");return"outside"===(i=t.getShallow("position")||(n?null:"inside"))&&(i=e.defaultOutsidePosition||"top"),null!=i&&(r.position=i),null!=s&&(r.offset=s),null!=o&&(o*=Math.PI/180,r.rotation=o),null!=a&&(r.distance=a),r.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",r}var Iu=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],ku=["align","lineHeight","width","height","tag","verticalAlign"],Au=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function Pu(t,e,n,i,r,o,a,s){n=!r&&n||wu;var l=i&&i.inheritColor,u=e.getShallow("color"),h=e.getShallow("textBorderColor"),c=Q(e.getShallow("opacity"),n.opacity);"inherit"!==u&&"auto"!==u||(u=l||null),"inherit"!==h&&"auto"!==h||(h=l||null),o||(u=u||n.color,h=h||n.textBorderColor),null!=u&&(t.fill=u),null!=h&&(t.stroke=h);var p=Q(e.getShallow("textBorderWidth"),n.textBorderWidth);null!=p&&(t.lineWidth=p);var f=Q(e.getShallow("textBorderType"),n.textBorderType);null!=f&&(t.lineDash=f);var d=Q(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset);null!=d&&(t.lineDashOffset=d),r||null!=c||s||(c=i&&i.defaultOpacity),null!=c&&(t.opacity=c),r||o||null==t.fill&&i.inheritColor&&(t.fill=i.inheritColor);for(var g=0;g<Iu.length;g++){var y=Iu[g];null!=(m=Q(e.getShallow(y),n[y]))&&(t[y]=m)}for(g=0;g<ku.length;g++){y=ku[g];null!=(m=e.getShallow(y))&&(t[y]=m)}if(null==t.verticalAlign){var v=e.getShallow("baseline");null!=v&&(t.verticalAlign=v)}if(!a||!i.disableBox){for(g=0;g<Au.length;g++){var m;y=Au[g];null!=(m=e.getShallow(y))&&(t[y]=m)}var _=e.getShallow("borderType");null!=_&&(t.borderDash=_),"auto"!==t.backgroundColor&&"inherit"!==t.backgroundColor||!l||(t.backgroundColor=l),"auto"!==t.borderColor&&"inherit"!==t.borderColor||!l||(t.borderColor=l)}}var Lu=yr();var Ou,Ru,Nu=["textStyle","color"],Eu=new Wa,zu=function(){function t(){}return t.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(Nu):null)},t.prototype.getFont=function(){return t={fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},e=this.ecModel,n=e&&e.getModel("textStyle"),rt([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "));var t,e,n},t.prototype.getTextRect=function(t){return Eu.useStyle({text:t,fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily"),verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline"),padding:this.getShallow("padding"),lineHeight:this.getShallow("lineHeight"),rich:this.getShallow("rich")}),Eu.update(),Eu.getBoundingRect()},t}(),Bu=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],Fu=Or(Bu),Vu=function(){function t(){}return t.prototype.getLineStyle=function(t){return Fu(this,t)},t}(),Hu=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],Gu=Or(Hu),Wu=function(){function t(){}return t.prototype.getItemStyle=function(t,e){return Gu(this,t,e)},t}(),Uu=function(){function t(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}return t.prototype.init=function(t,e,n){for(var i=[],r=3;r<arguments.length;r++)i[r-3]=arguments[r]},t.prototype.mergeOption=function(t,e){M(this.option,t,!0)},t.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},t.prototype.getShallow=function(t,e){var n=this.option,i=null==n?n:n[t];if(null==i&&!e){var r=this.parentModel;r&&(i=r.getShallow(t))}return i},t.prototype.getModel=function(e,n){var i=null!=e,r=i?this.parsePath(e):null;return new t(i?this._doGet(r):this.option,n=n||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(r)),this.ecModel)},t.prototype.isEmpty=function(){return null==this.option},t.prototype.restoreData=function(){},t.prototype.clone=function(){return new(0,this.constructor)(S(this.option))},t.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},t.prototype.resolveParentPath=function(t){return t},t.prototype.isAnimationEnabled=function(){if(!a.node&&this.option){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},t.prototype._doGet=function(t,e){var n=this.option;if(!t)return n;for(var i=0;i<t.length&&(!t[i]||null!=(n=n&&"object"==typeof n?n[t[i]]:null));i++);return null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel)),n},t}();Cr(Uu),Ou=Uu,Ru=["__\0is_clz",kr++].join("_"),Ou.prototype[Ru]=!0,Ou.isInstance=function(t){return!(!t||!t[Ru])},A(Uu,Vu),A(Uu,Wu),A(Uu,Nr),A(Uu,zu);var Xu=Math.round(10*Math.random());function Yu(t){return[t||"",Xu++].join("_")}var qu="ZH",ju="EN",Zu=ju,Ku={},$u={},Ju=a.domSupported&&(document.documentElement.lang||navigator.language||navigator.browserLanguage).toUpperCase().indexOf(qu)>-1?qu:Zu;function Qu(t,e){t=t.toUpperCase(),$u[t]=new Uu(e),Ku[t]=e}Qu(ju,{time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}),Qu(qu,{time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}});var th=1e3,eh=6e4,nh=36e5,ih=864e5,rh=31536e6,oh={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},ah="{yyyy}-{MM}-{dd}",sh={year:"{yyyy}",month:"{yyyy}-{MM}",day:ah,hour:"{yyyy}-{MM}-{dd} "+oh.hour,minute:"{yyyy}-{MM}-{dd} "+oh.minute,second:"{yyyy}-{MM}-{dd} "+oh.second,millisecond:oh.none},lh=["year","month","day","hour","minute","second","millisecond"],uh=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function hh(t,e){return"0000".substr(0,e-(t+="").length)+t}function ch(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function ph(t){return t===ch(t)}function fh(t,e,n,i){var r=qi(t),o=r[yh(n)](),a=r[vh(n)]()+1,s=Math.floor((a-1)/4)+1,l=r[mh(n)](),u=r["get"+(n?"UTC":"")+"Day"](),h=r[_h(n)](),c=(h-1)%12+1,p=r[xh(n)](),f=r[bh(n)](),d=r[wh(n)](),g=(i instanceof Uu?i:function(t){return $u[t]}(i||Ju)||$u.EN).getModel("time"),y=g.get("month"),v=g.get("monthAbbr"),m=g.get("dayOfWeek"),_=g.get("dayOfWeekAbbr");return(e||"").replace(/{yyyy}/g,o+"").replace(/{yy}/g,o%100+"").replace(/{Q}/g,s+"").replace(/{MMMM}/g,y[a-1]).replace(/{MMM}/g,v[a-1]).replace(/{MM}/g,hh(a,2)).replace(/{M}/g,a+"").replace(/{dd}/g,hh(l,2)).replace(/{d}/g,l+"").replace(/{eeee}/g,m[u]).replace(/{ee}/g,_[u]).replace(/{e}/g,u+"").replace(/{HH}/g,hh(h,2)).replace(/{H}/g,h+"").replace(/{hh}/g,hh(c+"",2)).replace(/{h}/g,c+"").replace(/{mm}/g,hh(p,2)).replace(/{m}/g,p+"").replace(/{ss}/g,hh(f,2)).replace(/{s}/g,f+"").replace(/{SSS}/g,hh(d,3)).replace(/{S}/g,d+"")}function dh(t,e){var n=qi(t),i=n[vh(e)]()+1,r=n[mh(e)](),o=n[_h(e)](),a=n[xh(e)](),s=n[bh(e)](),l=0===n[wh(e)](),u=l&&0===s,h=u&&0===a,c=h&&0===o,p=c&&1===r;return p&&1===i?"year":p?"month":c?"day":h?"hour":u?"minute":l?"second":"millisecond"}function gh(t,e,n){var i="number"==typeof t?qi(t):t;switch(e=e||dh(t,n)){case"year":return i[yh(n)]();case"half-year":return i[vh(n)]()>=6?1:0;case"quarter":return Math.floor((i[vh(n)]()+1)/4);case"month":return i[vh(n)]();case"day":return i[mh(n)]();case"half-day":return i[_h(n)]()/24;case"hour":return i[_h(n)]();case"minute":return i[xh(n)]();case"second":return i[bh(n)]();case"millisecond":return i[wh(n)]()}}function yh(t){return t?"getUTCFullYear":"getFullYear"}function vh(t){return t?"getUTCMonth":"getMonth"}function mh(t){return t?"getUTCDate":"getDate"}function _h(t){return t?"getUTCHours":"getHours"}function xh(t){return t?"getUTCMinutes":"getMinutes"}function bh(t){return t?"getUTCSeconds":"getSeconds"}function wh(t){return t?"getUTCMilliseconds":"getMilliseconds"}function Sh(t){return t?"setUTCFullYear":"setFullYear"}function Mh(t){return t?"setUTCMonth":"setMonth"}function Th(t){return t?"setUTCDate":"setDate"}function Ch(t){return t?"setUTCHours":"setHours"}function Dh(t){return t?"setUTCMinutes":"setMinutes"}function Ih(t){return t?"setUTCSeconds":"setSeconds"}function kh(t){return t?"setUTCMilliseconds":"setMilliseconds"}function Ah(t){if(!Ji(t))return H(t)?t:"-";var e=(t+"").split(".");return e[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(e.length>1?"."+e[1]:"")}var Ph=nt,Lh=/([&<>"'])/g,Oh={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Rh(t){return null==t?"":(t+"").replace(Lh,(function(t,e){return Oh[e]}))}var Nh=["a","b","c","d","e","f","g"],Eh=function(t,e){return"{"+t+(null==e?"":e)+"}"};function zh(t,e,n){F(e)||(e=[e]);var i=e.length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=Nh[o];t=t.replace(Eh(a),Eh(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(Eh(Nh[l],s),n?Rh(u):u)}return t}function Bh(t,e){return e=e||"transparent",H(t)?t:U(t)&&t.colorStops&&(t.colorStops[0]||{}).color||e}var Fh=L,Vh=["left","right","top","bottom","width","height"],Hh=[["width","left","right"],["height","top","bottom"]];function Gh(t,e,n,i,r){var o=0,a=0;null==i&&(i=1/0),null==r&&(r=1/0);var s=0;e.eachChild((function(l,u){var h,c,p=l.getBoundingRect(),f=e.childAt(u+1),d=f&&f.getBoundingRect();if("horizontal"===t){var g=p.width+(d?-d.x+p.x:0);(h=o+g)>i||l.newline?(o=0,h=g,a+=s+n,s=p.height):s=Math.max(s,p.height)}else{var y=p.height+(d?-d.y+p.y:0);(c=a+y)>r||l.newline?(o+=s+n,a=0,c=y,s=p.width):s=Math.max(s,p.width)}l.newline||(l.x=o,l.y=a,l.markRedraw(),"horizontal"===t?o=h+n:a=c+n)}))}B(Gh,"vertical"),B(Gh,"horizontal");function Wh(t,e,n){n=Ph(n||0);var i=e.width,r=e.height,o=zi(t.left,i),a=zi(t.top,r),s=zi(t.right,i),l=zi(t.bottom,r),u=zi(t.width,i),h=zi(t.height,r),c=n[2]+n[0],p=n[1]+n[3],f=t.aspect;switch(isNaN(u)&&(u=i-s-p-o),isNaN(h)&&(h=r-l-c-a),null!=f&&(isNaN(u)&&isNaN(h)&&(f>i/r?u=.8*i:h=.8*r),isNaN(u)&&(u=f*h),isNaN(h)&&(h=u/f)),isNaN(o)&&(o=i-s-u-p),isNaN(a)&&(a=r-l-h-c),t.left||t.right){case"center":o=i/2-u/2-n[3];break;case"right":o=i-u-p}switch(t.top||t.bottom){case"middle":case"center":a=r/2-h/2-n[0];break;case"bottom":a=r-h-c}o=o||0,a=a||0,isNaN(u)&&(u=i-p-o-(s||0)),isNaN(h)&&(h=r-c-a-(l||0));var d=new oi(o+n[3],a+n[0],u,h);return d.margin=n,d}function Uh(t){var e=t.layoutMode||t.constructor.layoutMode;return U(e)?e:e?{type:e}:null}function Xh(t,e,n){var i=n&&n.ignoreSize;!F(i)&&(i=[i,i]);var r=a(Hh[0],0),o=a(Hh[1],1);function a(n,r){var o={},a=0,u={},h=0;if(Fh(n,(function(e){u[e]=t[e]})),Fh(n,(function(t){s(e,t)&&(o[t]=u[t]=e[t]),l(o,t)&&a++,l(u,t)&&h++})),i[r])return l(e,n[1])?u[n[2]]=null:l(e,n[2])&&(u[n[1]]=null),u;if(2!==h&&a){if(a>=2)return o;for(var c=0;c<n.length;c++){var p=n[c];if(!s(o,p)&&s(t,p)){o[p]=t[p];break}}return o}return u}function s(t,e){return t.hasOwnProperty(e)}function l(t,e){return null!=t[e]&&"auto"!==t[e]}function u(t,e,n){Fh(t,(function(t){e[t]=n[t]}))}u(Hh[0],t,r),u(Hh[1],t,o)}function Yh(t){return function(t,e){return e&&t&&Fh(Vh,(function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t}({},t)}var qh=yr(),jh=function(t){function e(e,n,i){var r=t.call(this,e,n,i)||this;return r.uid=Yu("ec_cpt_model"),r}return n(e,t),e.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},e.prototype.mergeDefaultAndTheme=function(t,e){var n=Uh(this),i=n?Yh(t):{};M(t,e.getTheme().get(this.mainType)),M(t,this.getDefaultOption()),n&&Xh(t,i,n)},e.prototype.mergeOption=function(t,e){M(this.option,t,!0);var n=Uh(this);n&&Xh(this.option,t,n)},e.prototype.optionUpdated=function(t,e){},e.prototype.getDefaultOption=function(){var t=this.constructor;if(!function(t){return!(!t||!t[Mr])}(t))return t.defaultOption;var e=qh(this);if(!e.defaultOption){for(var n=[],i=t;i;){var r=i.prototype.defaultOption;r&&n.push(r),i=i.superClass}for(var o={},a=n.length-1;a>=0;a--)o=M(o,n[a],!0);e.defaultOption=o}return e.defaultOption},e.prototype.getReferringComponents=function(t,e){var n=t+"Index",i=t+"Id";return br(this.ecModel,t,{index:this.get(n,!0),id:this.get(i,!0)},e)},e.prototype.getBoxLayoutParams=function(){var t=this;return{left:t.get("left"),top:t.get("top"),right:t.get("right"),bottom:t.get("bottom"),width:t.get("width"),height:t.get("height")}},e.protoInitialize=function(){var t=e.prototype;t.type="component",t.id="",t.name="",t.mainType="",t.subType="",t.componentIndex=0}(),e}(Uu);Ir(jh,Uu),Lr(jh),function(t){var e={};t.registerSubTypeDefaulter=function(t,n){var i=Tr(t);e[i.main]=n},t.determineSubType=function(n,i){var r=i.type;if(!r){var o=Tr(n).main;t.hasSubTypes(n)&&e[o]&&(r=e[o](i))}return r}}(jh),function(t,e){function n(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}t.topologicalTravel=function(t,i,r,o){if(t.length){var a=function(t){var i={},r=[];return L(t,(function(o){var a=n(i,o),s=function(t,e){var n=[];return L(t,(function(t){I(e,t)>=0&&n.push(t)})),n}(a.originalDeps=e(o),t);a.entryCount=s.length,0===a.entryCount&&r.push(o),L(s,(function(t){I(a.predecessor,t)<0&&a.predecessor.push(t);var e=n(i,t);I(e.successor,t)<0&&e.successor.push(o)}))})),{graph:i,noEntryList:r}}(i),s=a.graph,l=a.noEntryList,u={};for(L(t,(function(t){u[t]=!0}));l.length;){var h=l.pop(),c=s[h],p=!!u[h];p&&(r.call(o,h,c.originalDeps.slice()),delete u[h]),L(c.successor,p?d:f)}L(u,(function(){var t="";throw new Error(t)}))}function f(t){s[t].entryCount--,0===s[t].entryCount&&l.push(t)}function d(t){u[t]=!0,f(t)}}}(jh,(function(t){var e=[];L(jh.getClassesByMainType(t),(function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])})),e=O(e,(function(t){return Tr(t).main})),"dataset"!==t&&I(e,"dataset")<=0&&e.unshift("dataset");return e}));var Zh="";"undefined"!=typeof navigator&&(Zh=navigator.platform||"");var Kh="rgba(0, 0, 0, 0.2)",$h={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:Kh,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:Kh,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:Kh,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:Kh,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:Kh,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:Kh,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:Zh.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},Jh=ut(["tooltip","label","itemName","itemId","itemGroupId","seriesName"]),Qh="original",tc="arrayRows",ec="objectRows",nc="keyedColumns",ic="typedArray",rc="unknown",oc="column",ac="row",sc=1,lc=2,uc=3,hc=yr();function cc(t,e,n){var i={},r=fc(e);if(!r||!t)return i;var o,a,s=[],l=[],u=e.ecModel,h=hc(u).datasetMap,c=r.uid+"_"+n.seriesLayoutBy;L(t=t.slice(),(function(e,n){var r=U(e)?e:t[n]={name:e};"ordinal"===r.type&&null==o&&(o=n,a=d(r)),i[r.name]=[]}));var p=h.get(c)||h.set(c,{categoryWayDim:a,valueWayDim:0});function f(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function d(t){var e=t.dimsDef;return e?e.length:1}return L(t,(function(t,e){var n=t.name,r=d(t);if(null==o){var a=p.valueWayDim;f(i[n],a,r),f(l,a,r),p.valueWayDim+=r}else if(o===e)f(i[n],0,r),f(s,0,r);else{a=p.categoryWayDim;f(i[n],a,r),f(l,a,r),p.categoryWayDim+=r}})),s.length&&(i.itemName=s),l.length&&(i.seriesName=l),i}function pc(t,e,n){var i={};if(!fc(t))return i;var r,o=e.sourceFormat,a=e.dimensionsDefine;o!==ec&&o!==nc||L(a,(function(t,e){"name"===(U(t)?t.name:t)&&(r=e)}));var s=function(){for(var t={},i={},s=[],l=0,u=Math.min(5,n);l<u;l++){var h=gc(e.data,o,e.seriesLayoutBy,a,e.startIndex,l);s.push(h);var c=h===uc;if(c&&null==t.v&&l!==r&&(t.v=l),(null==t.n||t.n===t.v||!c&&s[t.n]===uc)&&(t.n=l),p(t)&&s[t.n]!==uc)return t;c||(h===lc&&null==i.v&&l!==r&&(i.v=l),null!=i.n&&i.n!==i.v||(i.n=l))}function p(t){return null!=t.v&&null!=t.n}return p(t)?t:p(i)?i:null}();if(s){i.value=[s.v];var l=null!=r?r:s.n;i.itemName=[l],i.seriesName=[l]}return i}function fc(t){if(!t.get("data",!0))return br(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},xr).models[0]}function dc(t,e){return gc(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function gc(t,e,n,i,r,o){var a,s,l;if(Y(t))return uc;if(i){var u=i[o];U(u)?(s=u.name,l=u.type):H(u)&&(s=u)}if(null!=l)return"ordinal"===l?sc:uc;if(e===tc){var h=t;if(n===ac){for(var c=h[o],p=0;p<(c||[]).length&&p<5;p++)if(null!=(a=m(c[r+p])))return a}else for(p=0;p<h.length&&p<5;p++){var f=h[r+p];if(f&&null!=(a=m(f[o])))return a}}else if(e===ec){var d=t;if(!s)return uc;for(p=0;p<d.length&&p<5;p++){if((y=d[p])&&null!=(a=m(y[s])))return a}}else if(e===nc){if(!s)return uc;if(!(c=t[s])||Y(c))return uc;for(p=0;p<c.length&&p<5;p++)if(null!=(a=m(c[p])))return a}else if(e===Qh){var g=t;for(p=0;p<g.length&&p<5;p++){var y,v=sr(y=g[p]);if(!F(v))return uc;if(null!=(a=m(v[o])))return a}}function m(t){var e=H(t);return null!=t&&isFinite(t)&&""!==t?e?lc:uc:e&&"-"!==t?sc:void 0}return uc}var yc=ut();var vc,mc,_c,xc=yr(),bc=yr(),wc=function(){function t(){}return t.prototype.getColorFromPalette=function(t,e,n){var i=rr(this.get("color",!0)),r=this.get("colorLayer",!0);return Mc(this,xc,i,r,t,e,n)},t.prototype.clearColorPalette=function(){!function(t,e){e(t).paletteIdx=0,e(t).paletteNameMap={}}(this,xc)},t}();function Sc(t,e,n,i){var r=rr(t.get(["aria","decal","decals"]));return Mc(t,bc,r,null,e,n,i)}function Mc(t,e,n,i,r,o,a){var s=e(o=o||t),l=s.paletteIdx||0,u=s.paletteNameMap=s.paletteNameMap||{};if(u.hasOwnProperty(r))return u[r];var h=null!=a&&i?function(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}(i,a):n;if((h=h||n)&&h.length){var c=h[l];return r&&(u[r]=c),s.paletteIdx=(l+1)%h.length,c}}var Tc=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.init=function(t,e,n,i,r,o){i=i||{},this.option=null,this._theme=new Uu(i),this._locale=new Uu(r),this._optionManager=o},e.prototype.setOption=function(t,e,n){var i=Ic(e);this._optionManager.setOption(t,n,i),this._resetOption(null,i)},e.prototype.resetOption=function(t,e){return this._resetOption(t,Ic(e))},e.prototype._resetOption=function(t,e){var n=!1,i=this._optionManager;if(!t||"recreate"===t){var r=i.mountOption("recreate"===t);0,this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(r,e)):_c(this,r),n=!0}if("timeline"!==t&&"media"!==t||this.restoreData(),!t||"recreate"===t||"timeline"===t){var o=i.getTimelineOption(this);o&&(n=!0,this._mergeOption(o,e))}if(!t||"recreate"===t||"media"===t){var a=i.getMediaOption(this);a.length&&L(a,(function(t){n=!0,this._mergeOption(t,e)}),this)}return n},e.prototype.mergeOption=function(t){this._mergeOption(t,null)},e.prototype._mergeOption=function(t,e){var n=this.option,i=this._componentsMap,r=this._componentsCount,o=[],a=ut(),s=e&&e.replaceMergeMainTypeMap;hc(this).datasetMap=ut(),L(t,(function(t,e){null!=t&&(jh.hasClass(e)?e&&(o.push(e),a.set(e,!0)):n[e]=null==n[e]?S(t):M(n[e],t,!0))})),s&&s.each((function(t,e){jh.hasClass(e)&&!a.get(e)&&(o.push(e),a.set(e,!0))})),jh.topologicalTravel(o,jh.getAllClassMainTypes(),(function(e){var o=function(t,e,n){var i=yc.get(e);if(!i)return n;var r=i(t);return r?n.concat(r):n}(this,e,rr(t[e])),a=i.get(e),l=a?s&&s.get(e)?"replaceMerge":"normalMerge":"replaceAll",u=ur(a,o,l);(function(t,e,n){L(t,(function(t){var i=t.newOption;U(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=function(t,e,n,i){return e.type?e.type:n?n.subType:i.determineSubType(t,e)}(e,i,t.existing,n))}))})(u,e,jh),n[e]=null,i.set(e,null),r.set(e,0);var h=[],c=[],p=0;L(u,(function(t,n){var i=t.existing,r=t.newOption;if(r){var o="series"===e,a=jh.getClass(e,t.keyInfo.subType,!o);if(!a)return;if(i&&i.constructor===a)i.name=t.keyInfo.name,i.mergeOption(r,this),i.optionUpdated(r,!1);else{var s=T({componentIndex:n},t.keyInfo);T(i=new a(r,this,this,s),s),t.brandNew&&(i.__requireNewView=!0),i.init(r,this,this),i.optionUpdated(null,!0)}}else i&&(i.mergeOption({},this),i.optionUpdated({},!1));i?(h.push(i.option),c.push(i),p++):(h.push(void 0),c.push(void 0))}),this),n[e]=h,i.set(e,c),r.set(e,p),"series"===e&&vc(this)}),this),this._seriesIndices||vc(this)},e.prototype.getOption=function(){var t=S(this.option);return L(t,(function(e,n){if(jh.hasClass(n)){for(var i=rr(e),r=i.length,o=!1,a=r-1;a>=0;a--)i[a]&&!dr(i[a])?o=!0:(i[a]=null,!o&&r--);i.length=r,t[n]=i}})),delete t["\0_ec_inner"],t},e.prototype.getTheme=function(){return this._theme},e.prototype.getLocaleModel=function(){return this._locale},e.prototype.setUpdatePayload=function(t){this._payload=t},e.prototype.getUpdatePayload=function(){return this._payload},e.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){var i=n[e||0];if(i)return i;if(null==e)for(var r=0;r<n.length;r++)if(n[r])return n[r]}},e.prototype.queryComponents=function(t){var e=t.mainType;if(!e)return[];var n,i=t.index,r=t.id,o=t.name,a=this._componentsMap.get(e);return a&&a.length?(null!=i?(n=[],L(rr(i),(function(t){a[t]&&n.push(a[t])}))):n=null!=r?Cc("id",r,a):null!=o?Cc("name",o,a):N(a,(function(t){return!!t})),Dc(n,t)):[]},e.prototype.findComponents=function(t){var e,n,i,r,o,a=t.query,s=t.mainType,l=(n=s+"Index",i=s+"Id",r=s+"Name",!(e=a)||null==e[n]&&null==e[i]&&null==e[r]?null:{mainType:s,index:e[n],id:e[i],name:e[r]}),u=l?this.queryComponents(l):N(this._componentsMap.get(s),(function(t){return!!t}));return o=Dc(u,t),t.filter?N(o,t.filter):o},e.prototype.eachComponent=function(t,e,n){var i=this._componentsMap;if(V(t)){var r=e,o=t;i.each((function(t,e){for(var n=0;t&&n<t.length;n++){var i=t[n];i&&o.call(r,e,i,i.componentIndex)}}))}else for(var a=H(t)?i.get(t):U(t)?this.findComponents(t):null,s=0;a&&s<a.length;s++){var l=a[s];l&&e.call(n,l,l.componentIndex)}},e.prototype.getSeriesByName=function(t){var e=pr(t,null);return N(this._componentsMap.get("series"),(function(t){return!!t&&null!=e&&t.name===e}))},e.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},e.prototype.getSeriesByType=function(t){return N(this._componentsMap.get("series"),(function(e){return!!e&&e.subType===t}))},e.prototype.getSeries=function(){return N(this._componentsMap.get("series"),(function(t){return!!t}))},e.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},e.prototype.eachSeries=function(t,e){mc(this),L(this._seriesIndices,(function(n){var i=this._componentsMap.get("series")[n];t.call(e,i,n)}),this)},e.prototype.eachRawSeries=function(t,e){L(this._componentsMap.get("series"),(function(n){n&&t.call(e,n,n.componentIndex)}))},e.prototype.eachSeriesByType=function(t,e,n){mc(this),L(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];r.subType===t&&e.call(n,r,i)}),this)},e.prototype.eachRawSeriesByType=function(t,e,n){return L(this.getSeriesByType(t),e,n)},e.prototype.isSeriesFiltered=function(t){return mc(this),null==this._seriesIndicesMap.get(t.componentIndex)},e.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},e.prototype.filterSeries=function(t,e){mc(this);var n=[];L(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];t.call(e,r,i)&&n.push(i)}),this),this._seriesIndices=n,this._seriesIndicesMap=ut(n)},e.prototype.restoreData=function(t){vc(this);var e=this._componentsMap,n=[];e.each((function(t,e){jh.hasClass(e)&&n.push(e)})),jh.topologicalTravel(n,jh.getAllClassMainTypes(),(function(n){L(e.get(n),(function(e){!e||"series"===n&&function(t,e){if(e){var n=e.seriesIndex,i=e.seriesId,r=e.seriesName;return null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=r&&t.name!==r}}(e,t)||e.restoreData()}))}))},e.internalField=(vc=function(t){var e=t._seriesIndices=[];L(t._componentsMap.get("series"),(function(t){t&&e.push(t.componentIndex)})),t._seriesIndicesMap=ut(e)},mc=function(t){},void(_c=function(t,e){t.option={},t.option["\0_ec_inner"]=1,t._componentsMap=ut({series:[]}),t._componentsCount=ut();var n=e.aria;U(n)&&null==n.enabled&&(n.enabled=!0),function(t,e){var n=t.color&&!t.colorLayer;L(e,(function(e,i){"colorLayer"===i&&n||jh.hasClass(i)||("object"==typeof e?t[i]=t[i]?M(t[i],e,!1):S(e):null==t[i]&&(t[i]=e))}))}(e,t._theme.option),M(e,$h,!1),t._mergeOption(e,null)})),e}(Uu);function Cc(t,e,n){if(F(e)){var i=ut();return L(e,(function(t){null!=t&&(null!=pr(t,null)&&i.set(t,!0))})),N(n,(function(e){return e&&i.get(e[t])}))}var r=pr(e,null);return N(n,(function(e){return e&&null!=r&&e[t]===r}))}function Dc(t,e){return e.hasOwnProperty("subType")?N(t,(function(t){return t&&t.subType===e.subType})):t}function Ic(t){var e=ut();return t&&L(rr(t.replaceMerge),(function(t){e.set(t,!0)})),{replaceMergeMainTypeMap:e}}A(Tc,wc);var kc=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],Ac=function(t){L(kc,(function(e){this[e]=z(t[e],t)}),this)},Pc={},Lc=function(){function t(){this._coordinateSystems=[]}return t.prototype.create=function(t,e){var n=[];L(Pc,(function(i,r){var o=i.create(t,e);n=n.concat(o||[])})),this._coordinateSystems=n},t.prototype.update=function(t,e){L(this._coordinateSystems,(function(n){n.update&&n.update(t,e)}))},t.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},t.register=function(t,e){Pc[t]=e},t.get=function(t){return Pc[t]},t}(),Oc=/^(min|max)?(.+)$/,Rc=function(){function t(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return t.prototype.setOption=function(t,e,n){t&&(L(rr(t.series),(function(t){t&&t.data&&Y(t.data)&&at(t.data)})),L(rr(t.dataset),(function(t){t&&t.source&&Y(t.source)&&at(t.source)}))),t=S(t);var i=this._optionBackup,r=function(t,e,n){var i,r,o=[],a=t.baseOption,s=t.timeline,l=t.options,u=t.media,h=!!t.media,c=!!(l||s||a&&a.timeline);a?(r=a).timeline||(r.timeline=s):((c||h)&&(t.options=t.media=null),r=t);h&&F(u)&&L(u,(function(t){t&&t.option&&(t.query?o.push(t):i||(i=t))}));function p(t){L(e,(function(e){e(t,n)}))}return p(r),L(l,(function(t){return p(t)})),L(o,(function(t){return p(t.option)})),{baseOption:r,timelineOptions:l||[],mediaDefault:i,mediaList:o}}(t,e,!i);this._newBaseOption=r.baseOption,i?(r.timelineOptions.length&&(i.timelineOptions=r.timelineOptions),r.mediaList.length&&(i.mediaList=r.mediaList),r.mediaDefault&&(i.mediaDefault=r.mediaDefault)):this._optionBackup=r},t.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],S(t?e.baseOption:this._newBaseOption)},t.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=S(n[i.getCurrentIndex()]))}return e},t.prototype.getMediaOption=function(t){var e,n,i=this._api.getWidth(),r=this._api.getHeight(),o=this._mediaList,a=this._mediaDefault,s=[],l=[];if(!o.length&&!a)return l;for(var u=0,h=o.length;u<h;u++)Nc(o[u].query,i,r)&&s.push(u);return!s.length&&a&&(s=[-1]),s.length&&(e=s,n=this._currentMediaIndices,e.join(",")!==n.join(","))&&(l=O(s,(function(t){return S(-1===t?a.option:o[t].option)}))),this._currentMediaIndices=s,l},t}();function Nc(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return L(t,(function(t,e){var n=e.match(Oc);if(n&&n[1]&&n[2]){var o=n[1],a=n[2].toLowerCase();(function(t,e,n){return"min"===n?t>=e:"max"===n?t<=e:t===e})(i[a],t,o)||(r=!1)}})),r}var Ec=L,zc=U,Bc=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function Fc(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=Bc.length;n<i;n++){var r=Bc[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?M(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?M(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function Vc(t,e,n){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var i=t[e].normal,r=t[e].emphasis;i&&(n?(t[e].normal=t[e].emphasis=null,C(t[e],i)):t[e]=i),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r,r.focus&&(t.emphasis.focus=r.focus),r.blurScope&&(t.emphasis.blurScope=r.blurScope))}}function Hc(t){Vc(t,"itemStyle"),Vc(t,"lineStyle"),Vc(t,"areaStyle"),Vc(t,"label"),Vc(t,"labelLine"),Vc(t,"upperLabel"),Vc(t,"edgeLabel")}function Gc(t,e){var n=zc(t)&&t[e],i=zc(n)&&n.textStyle;if(i){0;for(var r=0,o=ar.length;r<o;r++){var a=ar[r];i.hasOwnProperty(a)&&(n[a]=i[a])}}}function Wc(t){t&&(Hc(t),Gc(t,"label"),t.emphasis&&Gc(t.emphasis,"label"))}function Uc(t){return F(t)?t:t?[t]:[]}function Xc(t){return(F(t)?t[0]:t)||{}}function Yc(t,e){Ec(Uc(t.series),(function(t){zc(t)&&function(t){if(zc(t)){Fc(t),Hc(t),Gc(t,"label"),Gc(t,"upperLabel"),Gc(t,"edgeLabel"),t.emphasis&&(Gc(t.emphasis,"label"),Gc(t.emphasis,"upperLabel"),Gc(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(Fc(e),Wc(e));var n=t.markLine;n&&(Fc(n),Wc(n));var i=t.markArea;i&&Wc(i);var r=t.data;if("graph"===t.type){r=r||t.nodes;var o=t.links||t.edges;if(o&&!Y(o))for(var a=0;a<o.length;a++)Wc(o[a]);L(t.categories,(function(t){Hc(t)}))}if(r&&!Y(r))for(a=0;a<r.length;a++)Wc(r[a]);if((e=t.markPoint)&&e.data){var s=e.data;for(a=0;a<s.length;a++)Wc(s[a])}if((n=t.markLine)&&n.data){var l=n.data;for(a=0;a<l.length;a++)F(l[a])?(Wc(l[a][0]),Wc(l[a][1])):Wc(l[a])}"gauge"===t.type?(Gc(t,"axisLabel"),Gc(t,"title"),Gc(t,"detail")):"treemap"===t.type?(Vc(t.breadcrumb,"itemStyle"),L(t.levels,(function(t){Hc(t)}))):"tree"===t.type&&Hc(t.leaves)}}(t)}));var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),Ec(n,(function(e){Ec(Uc(t[e]),(function(t){t&&(Gc(t,"axisLabel"),Gc(t.axisPointer,"label"))}))})),Ec(Uc(t.parallel),(function(t){var e=t&&t.parallelAxisDefault;Gc(e,"axisLabel"),Gc(e&&e.axisPointer,"label")})),Ec(Uc(t.calendar),(function(t){Vc(t,"itemStyle"),Gc(t,"dayLabel"),Gc(t,"monthLabel"),Gc(t,"yearLabel")})),Ec(Uc(t.radar),(function(t){Gc(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)})),Ec(Uc(t.geo),(function(t){zc(t)&&(Wc(t),Ec(Uc(t.regions),(function(t){Wc(t)})))})),Ec(Uc(t.timeline),(function(t){Wc(t),Vc(t,"label"),Vc(t,"itemStyle"),Vc(t,"controlStyle",!0);var e=t.data;F(e)&&L(e,(function(t){U(t)&&(Vc(t,"label"),Vc(t,"itemStyle"))}))})),Ec(Uc(t.toolbox),(function(t){Vc(t,"iconStyle"),Ec(t.feature,(function(t){Vc(t,"iconStyle")}))})),Gc(Xc(t.axisPointer),"label"),Gc(Xc(t.tooltip).axisPointer,"label")}function qc(t){t&&L(jc,(function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])}))}var jc=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Zc=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Kc=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function $c(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<Kc.length;n++){var i=Kc[n][1],r=Kc[n][0];null!=e[i]&&(e[r]=e[i])}}function Jc(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function Qc(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function tp(t,e){if(t)for(var n=0;n<t.length;n++)e(t[n]),t[n]&&tp(t[n].children,e)}function ep(t,e){Yc(t,e),t.series=rr(t.series),L(t.series,(function(t){if(U(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e){if(null!=t.clockWise&&(t.clockwise=t.clockWise),Jc(t.label),(r=t.data)&&!Y(r))for(var n=0;n<r.length;n++)Jc(r[n]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},(t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset))}else if("gauge"===e){var i=function(t,e){for(var n=e.split(","),i=t,r=0;r<n.length&&null!=(i=i&&i[n[r]]);r++);return i}(t,"pointer.color");null!=i&&function(t,e,n,i){for(var r,o=e.split(","),a=t,s=0;s<o.length-1;s++)null==a[r=o[s]]&&(a[r]={}),a=a[r];(i||null==a[o[s]])&&(a[o[s]]=n)}(t,"itemStyle.color",i)}else if("bar"===e){var r;if($c(t),$c(t.backgroundStyle),$c(t.emphasis),(r=t.data)&&!Y(r))for(n=0;n<r.length;n++)"object"==typeof r[n]&&($c(r[n]),$c(r[n]&&r[n].emphasis))}else if("sunburst"===e){var o=t.highlightPolicy;o&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=o)),Qc(t),tp(t.data,Qc)}else"graph"===e||"sankey"===e?function(t){t&&null!=t.focusNodeAdjacency&&(t.emphasis=t.emphasis||{},null==t.emphasis.focus&&(t.emphasis.focus="adjacency"))}(t):"map"===e&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation&&C(t,t.mapLocation));null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation)),qc(t)}})),t.dataRange&&(t.visualMap=t.dataRange),L(Zc,(function(e){var n=t[e];n&&(F(n)||(n=[n]),L(n,(function(t){qc(t)})))}))}function np(t){L(t,(function(e,n){var i=[],r=[NaN,NaN],o=[e.stackResultDimension,e.stackedOverDimension],a=e.data,s=e.isStackedByIndex;a.modify(o,(function(o,l,u){var h,c,p=a.get(e.stackedDimension,u);if(isNaN(p))return r;s?c=a.getRawIndex(u):h=a.get(e.stackedByDimension,u);for(var f=NaN,d=n-1;d>=0;d--){var g=t[d];if(s||(c=g.data.rawIndexOf(g.stackedByDimension,h)),c>=0){var y=g.data.getByRawIndex(g.stackResultDimension,c);if(p>=0&&y>0||p<=0&&y<0){p=Wi(p,y),f=y;break}}}return i[0]=p,i[1]=f,i}))}))}var ip,rp,op,ap,sp,lp=function(t){this.data=t.data||(t.sourceFormat===nc?{}:[]),this.sourceFormat=t.sourceFormat||rc,this.seriesLayoutBy=t.seriesLayoutBy||oc,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];null==i.type&&dc(this,n)===sc&&(i.type="ordinal")}};function up(t){return t instanceof lp}function hp(t,e,n){n=n||pp(t);var i=e.seriesLayoutBy,r=function(t,e,n,i,r){var o,a;if(!t)return{dimensionsDefine:fp(r),startIndex:a,dimensionsDetectedCount:o};if(e===tc){var s=t;"auto"===i||null==i?dp((function(t){null!=t&&"-"!==t&&(H(t)?null==a&&(a=1):a=0)}),n,s,10):a=W(i)?i:i?1:0,r||1!==a||(r=[],dp((function(t,e){r[e]=null!=t?t+"":""}),n,s,1/0)),o=r?r.length:n===ac?s.length:s[0]?s[0].length:null}else if(e===ec)r||(r=function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););if(e){var i=[];return L(e,(function(t,e){i.push(e)})),i}}(t));else if(e===nc)r||(r=[],L(t,(function(t,e){r.push(e)})));else if(e===Qh){var l=sr(t[0]);o=F(l)&&l.length||1}return{startIndex:a,dimensionsDefine:fp(r),dimensionsDetectedCount:o}}(t,n,i,e.sourceHeader,e.dimensions);return new lp({data:t,sourceFormat:n,seriesLayoutBy:i,dimensionsDefine:r.dimensionsDefine,startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount,metaRawOption:S(e)})}function cp(t){return new lp({data:t,sourceFormat:Y(t)?ic:Qh})}function pp(t){var e=rc;if(Y(t))e=ic;else if(F(t)){0===t.length&&(e=tc);for(var n=0,i=t.length;n<i;n++){var r=t[n];if(null!=r){if(F(r)){e=tc;break}if(U(r)){e=ec;break}}}}else if(U(t))for(var o in t)if(pt(t,o)&&P(t[o])){e=nc;break}return e}function fp(t){if(t){var e=ut();return O(t,(function(t,n){var i={name:(t=U(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};if(null==i.name)return i;i.name+="",null==i.displayName&&(i.displayName=i.name);var r=e.get(i.name);return r?i.name+="-"+r.count++:e.set(i.name,{count:1}),i}))}}function dp(t,e,n,i){if(e===ac)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else{var o=n[0]||[];for(r=0;r<o.length&&r<i;r++)t(o[r],r)}}function gp(t){var e=t.sourceFormat;return e===ec||e===nc}var yp=function(){function t(t,e){var n=up(t)?t:cp(t);this._source=n;var i=this._data=n.data;n.sourceFormat===ic&&(this._offset=0,this._dimSize=e,this._data=i),sp(this,i,n)}return t.prototype.getSource=function(){return this._source},t.prototype.count=function(){return 0},t.prototype.getItem=function(t,e){},t.prototype.appendData=function(t){},t.prototype.clean=function(){},t.protoInitialize=function(){var e=t.prototype;e.pure=!1,e.persistent=!0}(),t.internalField=function(){var t;sp=function(t,r,o){var a=o.sourceFormat,s=o.seriesLayoutBy,l=o.startIndex,u=o.dimensionsDefine,h=ap[Cp(a,s)];if(T(t,h),a===ic)t.getItem=e,t.count=i,t.fillStorage=n;else{var c=_p(a,s);t.getItem=z(c,null,r,l,u);var p=wp(a,s);t.count=z(p,null,r,l,u)}};var e=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,i=this._dimSize,r=i*t,o=0;o<i;o++)e[o]=n[r+o];return e},n=function(t,e,n,i){for(var r=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=i[a],l=null==s[0]?1/0:s[0],u=null==s[1]?-1/0:s[1],h=e-t,c=n[a],p=0;p<h;p++){var f=r[p*o+a];c[t+p]=f,f<l&&(l=f),f>u&&(u=f)}s[0]=l,s[1]=u}},i=function(){return this._data?this._data.length/this._dimSize:0};function r(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}(t={}).arrayRows_column={pure:!0,appendData:r},t.arrayRows_row={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t.objectRows={pure:!0,appendData:r},t.keyedColumns={pure:!0,appendData:function(t){var e=this._data;L(t,(function(t,n){for(var i=e[n]||(e[n]=[]),r=0;r<(t||[]).length;r++)i.push(t[r])}))}},t.original={appendData:r},t.typedArray={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},ap=t}(),t}(),vp=function(t,e,n,i){return t[i]},mp=((ip={}).arrayRows_column=function(t,e,n,i){return t[i+e]},ip.arrayRows_row=function(t,e,n,i,r){i+=e;for(var o=r||[],a=t,s=0;s<a.length;s++){var l=a[s];o[s]=l?l[i]:null}return o},ip.objectRows=vp,ip.keyedColumns=function(t,e,n,i,r){for(var o=r||[],a=0;a<n.length;a++){var s=n[a].name;0;var l=t[s];o[a]=l?l[i]:null}return o},ip.original=vp,ip);function _p(t,e){var n=mp[Cp(t,e)];return n}var xp=function(t,e,n){return t.length},bp=((rp={}).arrayRows_column=function(t,e,n){return Math.max(0,t.length-e)},rp.arrayRows_row=function(t,e,n){var i=t[0];return i?Math.max(0,i.length-e):0},rp.objectRows=xp,rp.keyedColumns=function(t,e,n){var i=n[0].name;var r=t[i];return r?r.length:0},rp.original=xp,rp);function wp(t,e){var n=bp[Cp(t,e)];return n}var Sp=function(t,e,n){return t[e]},Mp=((op={}).arrayRows=Sp,op.objectRows=function(t,e,n){return t[n]},op.keyedColumns=Sp,op.original=function(t,e,n){var i=sr(t);return i instanceof Array?i[e]:i},op.typedArray=Sp,op);function Tp(t){var e=Mp[t];return e}function Cp(t,e){return t===tc?t+"_"+e:t}function Dp(t,e,n){if(t){var i=t.getRawDataItem(e);if(null!=i){var r=t.getStore(),o=r.getSource().sourceFormat;if(null!=n){var a=t.getDimensionIndex(n),s=r.getDimensionProperty(a);return Tp(o)(i,a,s)}var l=i;return o===Qh&&(l=sr(i)),l}}}var Ip=/\{@(.+?)\}/g,kp=function(){function t(){}return t.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),l=s&&s[n.getItemVisual(t,"drawType")||"fill"],u=s&&s.stroke,h=this.mainType,c="series"===h,p=n.userOutput&&n.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:c?this.subType:null,seriesIndex:this.seriesIndex,seriesId:c?this.id:null,seriesName:c?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:l,borderColor:u,dimensionNames:p?p.fullDimensions:null,encode:p?p.encode:null,$vars:["seriesName","name","value"]}},t.prototype.getFormattedLabel=function(t,e,n,i,r,o){e=e||"normal";var a=this.getData(n),s=this.getDataParams(t,n);(o&&(s.value=o.interpolatedValue),null!=i&&F(s.value)&&(s.value=s.value[i]),r)||(r=a.getItemModel(t).get("normal"===e?["label","formatter"]:[e,"label","formatter"]));return"function"==typeof r?(s.status=e,s.dimensionIndex=i,r(s)):"string"==typeof r?zh(r,s).replace(Ip,(function(e,n){var i=n.length,r=n;"["===r.charAt(0)&&"]"===r.charAt(i-1)&&(r=+r.slice(1,i-1));var s=Dp(a,t,r);if(o&&F(o.interpolatedValue)){var l=a.getDimensionIndex(r);l>=0&&(s=o.interpolatedValue[l])}return null!=s?s+"":""})):void 0},t.prototype.getRawValue=function(t,e){return Dp(this.getData(e),t)},t.prototype.formatTooltip=function(t,e,n){},t}();function Ap(t){return new Pp(t)}var Pp=function(){function t(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return t.prototype.perform=function(t){var e,n=this._upstream,i=t&&t.skip;if(this._dirty&&n){var r=this.context;r.data=r.outputData=n.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!i&&(e=this._plan(this.context));var o,a=h(this._modBy),s=this._modDataCount||0,l=h(t&&t.modBy),u=t&&t.modDataCount||0;function h(t){return!(t>=1)&&(t=1),t}a===l&&s===u||(e="reset"),(this._dirty||"reset"===e)&&(this._dirty=!1,o=this._doReset(i)),this._modBy=l,this._modDataCount=u;var c=t&&t.step;if(this._dueEnd=n?n._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var p=this._dueIndex,f=Math.min(null!=c?this._dueIndex+c:1/0,this._dueEnd);if(!i&&(o||p<f)){var d=this._progress;if(F(d))for(var g=0;g<d.length;g++)this._doProgress(d[g],p,f,l,u);else this._doProgress(d,p,f,l,u)}this._dueIndex=f;var y=null!=this._settedOutputEnd?this._settedOutputEnd:f;0,this._outputDueEnd=y}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},t.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},t.prototype._doProgress=function(t,e,n,i,r){Lp.reset(e,n,i,r),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:Lp.next},this.context)},t.prototype._doReset=function(t){var e,n;this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),F(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var i=this._downstream;return i&&i.dirty(),n},t.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},t.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},t.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},t.prototype.getUpstream=function(){return this._upstream},t.prototype.getDownstream=function(){return this._downstream},t.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},t}(),Lp=function(){var t,e,n,i,r,o={reset:function(l,u,h,c){e=l,t=u,n=h,i=c,r=Math.ceil(i/n),o.next=n>1&&i>0?s:a}};return o;function a(){return e<t?e++:null}function s(){var o=e%r*n+Math.ceil(e/r),a=e>=t?null:o<i?o:e;return e++,a}}();function Op(t,e){var n=e&&e.type;return"ordinal"===n?t:("time"===n&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+qi(t)),null==t||""===t?NaN:+t)}ut({number:function(t){return parseFloat(t)},time:function(t){return+qi(t)},trim:function(t){return"string"==typeof t?rt(t):t}});var Rp=function(){function t(){}return t.prototype.getRawData=function(){throw new Error("not supported")},t.prototype.getRawDataItem=function(t){throw new Error("not supported")},t.prototype.cloneRawData=function(){},t.prototype.getDimensionInfo=function(t){},t.prototype.cloneAllDimensionInfo=function(){},t.prototype.count=function(){},t.prototype.retrieveValue=function(t,e){},t.prototype.retrieveValueFromItem=function(t,e){},t.prototype.convertValue=function(t,e){return Op(t,e)},t}();function Np(t){var e=t.sourceFormat;if(!Hp(e)){var n="";0,nr(n)}return t.data}function Ep(t){var e=t.sourceFormat,n=t.data;if(!Hp(e)){var i="";0,nr(i)}if(e===tc){for(var r=[],o=0,a=n.length;o<a;o++)r.push(n[o].slice());return r}if(e===ec){for(r=[],o=0,a=n.length;o<a;o++)r.push(T({},n[o]));return r}}function zp(t,e,n){if(null!=n)return"number"==typeof n||!isNaN(n)&&!pt(e,n)?t[n]:pt(e,n)?e[n]:void 0}function Bp(t){return S(t)}var Fp=ut();function Vp(t,e,n,i){var r="";e.length||nr(r),U(t)||nr(r);var o=t.type,a=Fp.get(o);a||nr(r);var s=O(e,(function(t){return function(t,e){var n=new Rp,i=t.data,r=n.sourceFormat=t.sourceFormat,o=t.startIndex,a="";t.seriesLayoutBy!==oc&&nr(a);var s=[],l={},u=t.dimensionsDefine;if(u)L(u,(function(t,e){var n=t.name,i={index:e,name:n,displayName:t.displayName};if(s.push(i),null!=n){var r="";pt(l,n)&&nr(r),l[n]=i}}));else for(var h=0;h<t.dimensionsDetectedCount;h++)s.push({index:h});var c=_p(r,oc);e.__isBuiltIn&&(n.getRawDataItem=function(t){return c(i,o,s,t)},n.getRawData=z(Np,null,t)),n.cloneRawData=z(Ep,null,t);var p=wp(r,oc);n.count=z(p,null,i,o,s);var f=Tp(r);n.retrieveValue=function(t,e){var n=c(i,o,s,t);return d(n,e)};var d=n.retrieveValueFromItem=function(t,e){if(null!=t){var n=s[e];return n?f(t,e,n.name):void 0}};return n.getDimensionInfo=z(zp,null,s,l),n.cloneAllDimensionInfo=z(Bp,null,s),n}(t,a)})),l=rr(a.transform({upstream:s[0],upstreamList:s,config:S(t.config)}));return O(l,(function(t,n){var i,r="";U(t)||nr(r),t.data||nr(r),Hp(pp(t.data))||nr(r);var o=e[0];if(o&&0===n&&!t.dimensions){var a=o.startIndex;a&&(t.data=o.data.slice(0,a).concat(t.data)),i={seriesLayoutBy:oc,sourceHeader:a,dimensions:o.metaRawOption.dimensions}}else i={seriesLayoutBy:oc,sourceHeader:0,dimensions:t.dimensions};return hp(t.data,i,null)}))}function Hp(t){return t===tc||t===ec}var Gp,Wp="undefined",Up=typeof Uint32Array===Wp?Array:Uint32Array,Xp=typeof Uint16Array===Wp?Array:Uint16Array,Yp=typeof Int32Array===Wp?Array:Int32Array,qp=typeof Float64Array===Wp?Array:Float64Array,jp={float:qp,int:Yp,ordinal:Array,number:Array,time:qp};function Zp(t){return t>65535?Up:Xp}function Kp(t,e,n,i,r){var o=jp[n||"float"];if(r){var a=t[e],s=a&&a.length;if(s!==i){for(var l=new o(i),u=0;u<s;u++)l[u]=a[u];t[e]=l}}else t[e]=new o(i)}var $p=function(){function t(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=ut()}return t.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),r=this.defaultDimValueGetter=Gp[i.sourceFormat];this._dimValueGetter=n||r,this._rawExtent=[];gp(i);this._dimensions=O(e,(function(t){return{type:t.type,property:t.property}})),this._initDataFromProvider(0,t.count())},t.prototype.getProvider=function(){return this._provider},t.prototype.getSource=function(){return this._provider.getSource()},t.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,r=n.get(t);if(null!=r){if(i[r].type===e)return r}else r=i.length;return i[r]={type:e},n.set(t,r),this._chunks[r]=new jp[e||"float"](this._rawCount),this._rawExtent[r]=[1/0,-1/0],r},t.prototype.collectOrdinalMeta=function(t,e){var n=this._chunks[t],i=this._dimensions[t],r=this._rawExtent,o=i.ordinalOffset||0,a=n.length;0===o&&(r[t]=[1/0,-1/0]);for(var s=r[t],l=o;l<a;l++){var u=n[l]=e.parseAndCollect(n[l]);s[0]=Math.min(u,s[0]),s[1]=Math.max(u,s[1])}i.ordinalMeta=e,i.ordinalOffset=a,i.type="ordinal"},t.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},t.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},t.prototype.appendData=function(t){var e=this._provider,n=this.count();e.appendData(t);var i=e.count();return e.persistent||(i+=n),n<i&&this._initDataFromProvider(n,i,!0),[n,i]},t.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,r=i.length,o=this._rawExtent,a=this.count(),s=a+Math.max(t.length,e||0),l=0;l<r;l++){Kp(n,l,(f=i[l]).type,s,!0)}for(var u=[],h=a;h<s;h++)for(var c=h-a,p=0;p<r;p++){var f=i[p],d=Gp.arrayRows.call(this,t[c]||u,f.property,c,p);n[p][h]=d;var g=o[p];d<g[0]&&(g[0]=d),d>g[1]&&(g[1]=d)}return this._rawCount=this._count=s,{start:a,end:s}},t.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,r=this._chunks,o=this._dimensions,a=o.length,s=this._rawExtent,l=O(o,(function(t){return t.property})),u=0;u<a;u++){var h=o[u];s[u]||(s[u]=[1/0,-1/0]),Kp(r,u,h.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,r,s);else for(var c=[],p=t;p<e;p++){c=i.getItem(p,c);for(var f=0;f<a;f++){var d=r[f],g=this._dimValueGetter(c,l[f],p,f);d[p]=g;var y=s[f];g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},t.prototype.count=function(){return this._count},t.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var n=this._chunks[t];return n?n[this.getRawIndex(e)]:NaN},t.prototype.getValues=function(t,e){var n=[],i=[];if(null==e){e=t,t=[];for(var r=0;r<this._dimensions.length;r++)i.push(r)}else i=t;r=0;for(var o=i.length;r<o;r++)n.push(this.get(i[r],e));return n},t.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var n=this._chunks[t];return n?n[e]:NaN},t.prototype.getSum=function(t){var e=0;if(this._chunks[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},t.prototype.getMedian=function(t){var e=[];this.each([t],(function(t){isNaN(t)||e.push(t)}));var n=e.sort((function(t,e){return t-e})),i=this.count();return 0===i?0:i%2==1?n[(i-1)/2]:(n[i/2]+n[i/2-1])/2},t.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=o+1;else{if(!(e[o]>t))return o;r=o-1}}return-1},t.prototype.indicesOfNearest=function(t,e,n){var i=this._chunks[t],r=[];if(!i)return r;null==n&&(n=1/0);for(var o=1/0,a=-1,s=0,l=0,u=this.count();l<u;l++){var h=e-i[this.getRawIndex(l)],c=Math.abs(h);c<=n&&((c<o||c===o&&h>=0&&a<0)&&(o=c,a=h,s=0),h===a&&(r[s++]=l))}return r.length=s,r},t.prototype.getIndices=function(){var t,e=this._indices;if(e){var n=e.constructor,i=this._count;if(n===Array){t=new n(i);for(var r=0;r<i;r++)t[r]=e[r]}else t=new n(e.buffer,0,i)}else{t=new(n=Zp(this._rawCount))(this.count());for(r=0;r<t.length;r++)t[r]=r}return t},t.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),r=new(Zp(n._rawCount))(i),o=[],a=t.length,s=0,l=t[0],u=n._chunks,h=0;h<i;h++){var c=void 0,p=n.getRawIndex(h);if(0===a)c=e(h);else if(1===a){c=e(u[l][p],h)}else{for(var f=0;f<a;f++)o[f]=u[t[f]][p];o[f]=h,c=e.apply(null,o)}c&&(r[s++]=p)}return s<i&&(n._indices=r),n._count=s,n._extent=[],n._updateGetRawIdx(),n},t.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=E(t),r=i.length;if(!r)return this;var o=e.count(),a=new(Zp(e._rawCount))(o),s=0,l=i[0],u=t[l][0],h=t[l][1],c=e._chunks,p=!1;if(!e._indices){var f=0;if(1===r){for(var d=c[i[0]],g=0;g<n;g++){((_=d[g])>=u&&_<=h||isNaN(_))&&(a[s++]=f),f++}p=!0}else if(2===r){d=c[i[0]];var y=c[i[1]],v=t[i[1]][0],m=t[i[1]][1];for(g=0;g<n;g++){var _=d[g],x=y[g];(_>=u&&_<=h||isNaN(_))&&(x>=v&&x<=m||isNaN(x))&&(a[s++]=f),f++}p=!0}}if(!p)if(1===r)for(g=0;g<o;g++){var b=e.getRawIndex(g);((_=c[i[0]][b])>=u&&_<=h||isNaN(_))&&(a[s++]=b)}else for(g=0;g<o;g++){for(var w=!0,S=(b=e.getRawIndex(g),0);S<r;S++){var M=i[S];((_=c[M][b])<t[M][0]||_>t[M][1])&&(w=!1)}w&&(a[s++]=e.getRawIndex(g))}return s<o&&(e._indices=a),e._count=s,e._extent=[],e._updateGetRawIdx(),e},t.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},t.prototype.modify=function(t,e){this._updateDims(this,t,e)},t.prototype._updateDims=function(t,e,n){for(var i=t._chunks,r=[],o=e.length,a=t.count(),s=[],l=t._rawExtent,u=0;u<e.length;u++)l[e[u]]=[1/0,-1/0];for(var h=0;h<a;h++){for(var c=t.getRawIndex(h),p=0;p<o;p++)s[p]=i[e[p]][c];s[o]=h;var f=n&&n.apply(null,s);if(null!=f){"object"!=typeof f&&(r[0]=f,f=r);for(u=0;u<f.length;u++){var d=e[u],g=f[u],y=l[d],v=i[d];v&&(v[c]=g),g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}}},t.prototype.lttbDownSample=function(t,e){var n,i,r,o=this.clone([t],!0),a=o._chunks[t],s=this.count(),l=0,u=Math.floor(1/e),h=this.getRawIndex(0),c=new(Zp(this._rawCount))(Math.ceil(s/u)+2);c[l++]=h;for(var p=1;p<s-1;p+=u){for(var f=Math.min(p+u,s-1),d=Math.min(p+2*u,s),g=(d+f)/2,y=0,v=f;v<d;v++){var m=a[S=this.getRawIndex(v)];isNaN(m)||(y+=m)}y/=d-f;var _=p,x=Math.min(p+u,s),b=p-1,w=a[h];n=-1,r=_;for(v=_;v<x;v++){var S;m=a[S=this.getRawIndex(v)];isNaN(m)||(i=Math.abs((b-g)*(m-w)-(b-v)*(y-w)))>n&&(n=i,r=S)}c[l++]=r,h=r}return c[l++]=this.getRawIndex(s-1),o._count=l,o._indices=c,o.getRawIndex=this._getRawIdx,o},t.prototype.downSample=function(t,e,n,i){for(var r=this.clone([t],!0),o=r._chunks,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=r._rawExtent[t]=[1/0,-1/0],c=new(Zp(this._rawCount))(Math.ceil(u/s)),p=0,f=0;f<u;f+=s){s>u-f&&(s=u-f,a.length=s);for(var d=0;d<s;d++){var g=this.getRawIndex(f+d);a[d]=l[g]}var y=n(a),v=this.getRawIndex(Math.min(f+i(a,y)||0,u-1));l[v]=y,y<h[0]&&(h[0]=y),y>h[1]&&(h[1]=y),c[p++]=v}return r._count=p,r._indices=c,r._updateGetRawIdx(),r},t.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,r=0,o=this.count();r<o;r++){var a=this.getRawIndex(r);switch(n){case 0:e(r);break;case 1:e(i[t[0]][a],r);break;case 2:e(i[t[0]][a],i[t[1]][a],r);break;default:for(var s=0,l=[];s<n;s++)l[s]=i[t[s]][a];l[s]=r,e.apply(null,l)}}},t.prototype.getDataExtent=function(t){var e=this._chunks[t],n=[1/0,-1/0];if(!e)return n;var i,r=this.count();if(!this._indices)return this._rawExtent[t].slice();if(i=this._extent[t])return i.slice();for(var o=(i=n)[0],a=i[1],s=0;s<r;s++){var l=e[this.getRawIndex(s)];l<o&&(o=l),l>a&&(a=l)}return i=[o,a],this._extent[t]=i,i},t.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,r=0;r<i.length;r++)n.push(i[r][e]);return n},t.prototype.clone=function(e,n){var i,r,o=new t,a=this._chunks,s=e&&R(e,(function(t,e){return t[e]=!0,t}),{});if(s)for(var l=0;l<a.length;l++)o._chunks[l]=s[l]?(i=a[l],r=void 0,(r=i.constructor)===Array?i.slice():new r(i)):a[l];else o._chunks=a;return this._copyCommonProps(o),n||(o._indices=this._cloneIndices()),o._updateGetRawIdx(),o},t.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=S(this._extent),t._rawExtent=S(this._rawExtent)},t.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var n=this._indices.length;e=new t(n);for(var i=0;i<n;i++)e[i]=this._indices[i]}else e=new t(this._indices);return e}return null},t.prototype._getRawIdxIdentity=function(t){return t},t.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},t.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},t.internalField=function(){function t(t,e,n,i){return Op(t[i],this._dimensions[i])}Gp={arrayRows:t,objectRows:function(t,e,n,i){return Op(t[e],this._dimensions[i])},keyedColumns:t,original:function(t,e,n,i){var r=t&&(null==t.value?t:t.value);return Op(r instanceof Array?r[i]:r,this._dimensions[i])},typedArray:function(t,e,n,i){return t[i]}}}(),t}(),Jp=function(){function t(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return t.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},t.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},t.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},t.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},t.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n=this._sourceHost,i=this._getUpstreamSourceManagers(),r=!!i.length;if(tf(n)){var o=n,a=void 0,s=void 0,l=void 0;if(r){var u=i[0];u.prepareSource(),a=(l=u.getSource()).data,s=l.sourceFormat,e=[u._getVersionSign()]}else s=Y(a=o.get("data",!0))?ic:Qh,e=[];var h=this._getSourceMetaRawOption()||{},c=l&&l.metaRawOption||{},p=Q(h.seriesLayoutBy,c.seriesLayoutBy)||null,f=Q(h.sourceHeader,c.sourceHeader)||null,d=Q(h.dimensions,c.dimensions);t=p!==c.seriesLayoutBy||!!f!=!!c.sourceHeader||d?[hp(a,{seriesLayoutBy:p,sourceHeader:f,dimensions:d},s)]:[]}else{var g=n;if(r){var y=this._applyTransform(i);t=y.sourceList,e=y.upstreamSignList}else{t=[hp(g.get("source",!0),this._getSourceMetaRawOption(),null)],e=[]}}this._setLocalSource(t,e)},t.prototype._applyTransform=function(t){var e,n=this._sourceHost,i=n.get("transform",!0),r=n.get("fromTransformResult",!0);if(null!=r){var o="";1!==t.length&&ef(o)}var a,s=[],l=[];return L(t,(function(t){t.prepareSource();var e=t.getSource(r||0),n="";null==r||e||ef(n),s.push(e),l.push(t._getVersionSign())})),i?e=function(t,e,n){var i=rr(t),r=i.length,o="";r||nr(o);for(var a=0,s=r;a<s;a++)e=Vp(i[a],e),a!==s-1&&(e.length=Math.max(e.length,1));return e}(i,s,n.componentIndex):null!=r&&(e=[(a=s[0],new lp({data:a.data,sourceFormat:a.sourceFormat,seriesLayoutBy:a.seriesLayoutBy,dimensionsDefine:S(a.dimensionsDefine),startIndex:a.startIndex,dimensionsDetectedCount:a.dimensionsDetectedCount}))]),{sourceList:e,upstreamSignList:l}},t.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},t.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var n=this._getUpstreamSourceManagers();return n[0]&&n[0].getSource(t)}return e},t.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},t.prototype._innerGetDataStore=function(t,e,n){var i=this._storeList,r=i[0];r||(r=i[0]={});var o=r[n];if(!o){var a=this._getUpstreamSourceManagers()[0];tf(this._sourceHost)&&a?o=a._innerGetDataStore(t,e,n):(o=new $p).initData(new yp(e,t.length),t),r[n]=o}return o},t.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(tf(t)){var e=fc(t);return e?[e.getSourceManager()]:[]}return O(function(t){return t.get("transform",!0)||t.get("fromTransformResult",!0)?br(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},xr).models:[]}(t),(function(t){return t.getSourceManager()}))},t.prototype._getSourceMetaRawOption=function(){var t,e,n,i=this._sourceHost;if(tf(i))t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var r=i;t=r.get("seriesLayoutBy",!0),e=r.get("sourceHeader",!0),n=r.get("dimensions",!0)}return{seriesLayoutBy:t,sourceHeader:e,dimensions:n}},t}();function Qp(t){t.option.transform&&at(t.option.transform)}function tf(t){return"series"===t.mainType}function ef(t){throw new Error(t)}function nf(t,e){return e.type=t,e}function rf(t){var e,n,i,r,o=t.series,a=t.dataIndex,s=t.multipleSeries,l=o.getData(),u=l.mapDimensionsAll("defaultedTooltip"),h=u.length,c=o.getRawValue(a),p=F(c),f=function(t,e){return Bh(t.getData().getItemVisual(e,"style")[t.visualDrawType])}(o,a);if(h>1||p&&!h){var d=function(t,e,n,i,r){var o=e.getData(),a=R(t,(function(t,e,n){var i=o.getDimensionInfo(n);return t||i&&!1!==i.tooltip&&null!=i.displayName}),!1),s=[],l=[],u=[];function h(t,e){var n=o.getDimensionInfo(e);n&&!1!==n.otherDims.tooltip&&(a?u.push(nf("nameValue",{markerType:"subItem",markerColor:r,name:n.displayName,value:t,valueType:n.type})):(s.push(t),l.push(n.type)))}return i.length?L(i,(function(t){h(Dp(o,n,t),t)})):L(t,h),{inlineValues:s,inlineValueTypes:l,blocks:u}}(c,o,a,u,f);e=d.inlineValues,n=d.inlineValueTypes,i=d.blocks,r=d.inlineValues[0]}else if(h){var g=l.getDimensionInfo(u[0]);r=e=Dp(l,a,u[0]),n=g.type}else r=e=p?c[0]:c;var y=fr(o),v=y&&o.name||"",m=l.getName(a),_=s?v:m;return nf("section",{header:v,noHeader:s||!y,sortParam:r,blocks:[nf("nameValue",{markerType:"item",markerColor:f,name:_,noName:!rt(_),value:e,valueType:n})].concat(i||[])})}var of=yr();function af(t,e){return t.getName(e)||t.getId(e)}var sf=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}return n(e,t),e.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=Ap({count:uf,reset:hf}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),(of(this).sourceManager=new Jp(this)).prepareSource();var i=this.getInitialData(t,n);pf(i,this),this.dataTask.context.data=i,of(this).dataBeforeProcessed=i,lf(this),this._initSelectedMapFromData(i)},e.prototype.mergeDefaultAndTheme=function(t,e){var n=Uh(this),i=n?Yh(t):{},r=this.subType;jh.hasClass(r)&&(r+="Series"),M(t,e.getTheme().get(this.subType)),M(t,this.getDefaultOption()),or(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&Xh(t,i,n)},e.prototype.mergeOption=function(t,e){t=M(this.option,t,!0),this.fillDataTextStyle(t.data);var n=Uh(this);n&&Xh(this.option,t,n);var i=of(this).sourceManager;i.dirty(),i.prepareSource();var r=this.getInitialData(t,e);pf(r,this),this.dataTask.dirty(),this.dataTask.context.data=r,of(this).dataBeforeProcessed=r,lf(this),this._initSelectedMapFromData(r)},e.prototype.fillDataTextStyle=function(t){if(t&&!Y(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&or(t[n],"label",e)},e.prototype.getInitialData=function(t,e){},e.prototype.appendData=function(t){this.getRawData().appendData(t.data)},e.prototype.getData=function(t){var e=df(this);if(e){var n=e.context.data;return null==t?n:n.getLinkedData(t)}return of(this).data},e.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},e.prototype.setData=function(t){var e=df(this);if(e){var n=e.context;n.outputData=t,e!==this.dataTask&&(n.data=t)}of(this).data=t},e.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return ut(t)},e.prototype.getSourceManager=function(){return of(this).sourceManager},e.prototype.getSource=function(){return this.getSourceManager().getSource()},e.prototype.getRawData=function(){return of(this).dataBeforeProcessed},e.prototype.getColorBy=function(){return this.get("colorBy")||"series"},e.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},e.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},e.prototype.formatTooltip=function(t,e,n){return rf({series:this,dataIndex:t,multipleSeries:e})},e.prototype.isAnimationEnabled=function(){if(a.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),!!t},e.prototype.restoreData=function(){this.dataTask.dirty()},e.prototype.getColorFromPalette=function(t,e,n){var i=this.ecModel,r=wc.prototype.getColorFromPalette.call(this,t,e,n);return r||(r=i.getColorFromPalette(t,e,n)),r},e.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},e.prototype.getProgressive=function(){return this.get("progressive")},e.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},e.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},e.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n)for(var i=this.getData(e),r=0;r<t.length;r++){var o=af(i,t[r]);n[o]=!1,this._selectedDataIndicesMap[o]=-1}},e.prototype.toggleSelect=function(t,e){for(var n=[],i=0;i<t.length;i++)n[0]=t[i],this.isSelected(t[i],e)?this.unselect(n,e):this.select(n,e)},e.prototype.getSelectedDataIndices=function(){for(var t=this._selectedDataIndicesMap,e=E(t),n=[],i=0;i<e.length;i++){var r=t[e[i]];r>=0&&n.push(r)}return n},e.prototype.isSelected=function(t,e){var n=this.option.selectedMap;return n&&n[af(this.getData(e),t)]||!1},e.prototype.isUniversalTransitionEnabled=function(){if(this.__universalTransitionEnabled)return!0;var t=this.option.universalTransition;return!!t&&(!0===t||t&&t.enabled)},e.prototype._innerSelect=function(t,e){var n,i,r=this.option.selectedMode,o=e.length;if(r&&o)if("multiple"===r)for(var a=this.option.selectedMap||(this.option.selectedMap={}),s=0;s<o;s++){var l=e[s];a[h=af(t,l)]=!0,this._selectedDataIndicesMap[h]=t.getRawIndex(l)}else if("single"===r||!0===r){var u=e[o-1],h=af(t,u);this.option.selectedMap=((n={})[h]=!0,n),this._selectedDataIndicesMap=((i={})[h]=t.getRawIndex(u),i)}},e.prototype._initSelectedMapFromData=function(t){if(!this.option.selectedMap){var e=[];t.hasItemOption&&t.each((function(n){var i=t.getRawDataItem(n);i&&i.selected&&e.push(n)})),e.length>0&&this._innerSelect(t,e)}},e.registerClass=function(t){return jh.registerClass(t)},e.protoInitialize=function(){var t=e.prototype;t.type="series.__base__",t.seriesIndex=0,t.ignoreStyleOnData=!1,t.hasSymbolVisual=!1,t.defaultSymbol="circle",t.visualStyleAccessPath="itemStyle",t.visualDrawType="fill"}(),e}(jh);function lf(t){var e=t.name;fr(t)||(t.name=function(t){var e=t.getRawData(),n=e.mapDimensionsAll("seriesName"),i=[];return L(n,(function(t){var n=e.getDimensionInfo(t);n.displayName&&i.push(n.displayName)})),i.join(" ")}(t)||e)}function uf(t){return t.model.getRawData().count()}function hf(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),cf}function cf(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function pf(t,e){L(ht(t.CHANGABLE_METHODS,t.DOWNSAMPLE_METHODS),(function(n){t.wrapMethod(n,B(ff,e))}))}function ff(t,e){var n=df(t);return n&&n.setOutputEnd((e||this).count()),e}function df(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var i=n.currentTask;if(i){var r=i.agentStubMap;r&&(i=r.get(t.uid))}return i}}A(sf,kp),A(sf,wc),Ir(sf,jh);var gf=function(){function t(){this.group=new Di,this.uid=Yu("viewComponent")}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,i){},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,i){},t.prototype.updateLayout=function(t,e,n,i){},t.prototype.updateVisual=function(t,e,n,i){},t.prototype.blurSeries=function(t,e){},t}();function yf(){var t=yr();return function(e){var n=t(e),i=e.pipelineContext,r=!!n.large,o=!!n.progressiveRender,a=n.large=!(!i||!i.large),s=n.progressiveRender=!(!i||!i.progressiveRender);return!(r===a&&o===s)&&"reset"}}Cr(gf),Lr(gf);var vf=yr(),mf=yf(),_f=function(){function t(){this.group=new Di,this.uid=Yu("viewChart"),this.renderTask=Ap({plan:wf,reset:Sf}),this.renderTask.context={view:this}}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,i){},t.prototype.highlight=function(t,e,n,i){bf(t.getData(),i,"emphasis")},t.prototype.downplay=function(t,e,n,i){bf(t.getData(),i,"normal")},t.prototype.remove=function(t,e){this.group.removeAll()},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},t.markUpdateMethod=function(t,e){vf(t).updateMethod=e},t.protoInitialize=void(t.prototype.type="chart"),t}();function xf(t,e,n){t&&("emphasis"===e?Cs:Ds)(t,n)}function bf(t,e,n){var i=gr(t,e),r=e&&null!=e.highlightKey?function(t){var e=ts[t];return null==e&&Qa<=32&&(e=ts[t]=Qa++),e}(e.highlightKey):null;null!=i?L(rr(i),(function(e){xf(t.getItemGraphicEl(e),n,r)})):t.eachItemGraphicEl((function(t){xf(t,n,r)}))}function wf(t){return mf(t.model)}function Sf(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,a=t.view,s=r&&vf(r).updateMethod,l=o?"incrementalPrepareRender":s&&a[s]?s:"render";return"render"!==l&&a[l](e,n,i,r),Mf[l]}Cr(_f),Lr(_f);var Mf={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}};function Tf(t,e,n){var i,r,o,a,s,l=0,u=0,h=null;function c(){u=(new Date).getTime(),h=null,t.apply(o,a||[])}e=e||0;var p=function(){for(var t=[],p=0;p<arguments.length;p++)t[p]=arguments[p];i=(new Date).getTime(),o=this,a=t;var f=s||e,d=s||n;s=null,r=i-(d?l:u)-f,clearTimeout(h),d?h=setTimeout(c,f):r>=0?c():h=setTimeout(c,-r),l=i};return p.clear=function(){h&&(clearTimeout(h),h=null)},p.debounceNextCall=function(t){s=t},p}var Cf=yr(),Df={itemStyle:Or(Hu,!0),lineStyle:Or(Bu,!0)},If={lineStyle:"stroke",itemStyle:"fill"};function kf(t,e){var n=t.visualStyleMapper||Df[e];return n||(console.warn("Unkown style type '"+e+"'."),Df.itemStyle)}function Af(t,e){var n=t.visualDrawType||If[e];return n||(console.warn("Unkown style type '"+e+"'."),"fill")}var Pf={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=t.getModel(i),o=kf(t,i)(r),a=r.getShallow("decal");a&&(n.setVisual("decal",a),a.dirty=!0);var s=Af(t,i),l=o[s],u=V(l)?l:null,h="auto"===o.fill||"auto"===o.stroke;if(!o[s]||u||h){var c=t.getColorFromPalette(t.name,null,e.getSeriesCount());o[s]||(o[s]=c,n.setVisual("colorFromPalette",!0)),o.fill="auto"===o.fill||"function"==typeof o.fill?c:o.fill,o.stroke="auto"===o.stroke||"function"==typeof o.stroke?c:o.stroke}if(n.setVisual("style",o),n.setVisual("drawType",s),!e.isSeriesFiltered(t)&&u)return n.setVisual("colorFromPalette",!1),{dataEach:function(e,n){var i=t.getDataParams(n),r=T({},o);r[s]=u(i),e.setItemVisual(n,"style",r)}}}},Lf=new Uu,Of={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(!t.ignoreStyleOnData&&!e.isSeriesFiltered(t)){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=kf(t,i),o=n.getVisual("drawType");return{dataEach:n.hasItemOption?function(t,e){var n=t.getRawDataItem(e);if(n&&n[i]){Lf.option=n[i];var a=r(Lf);T(t.ensureUniqueItemVisual(e,"style"),a),Lf.option.decal&&(t.setItemVisual(e,"decal",Lf.option.decal),Lf.option.decal.dirty=!0),o in a&&t.setItemVisual(e,"colorFromPalette",!1)}}:null}}}},Rf={performRawSeries:!0,overallReset:function(t){var e=ut();t.eachSeries((function(t){var n=t.getColorBy();if(!t.isColorBySeries()){var i=t.type+"-"+n,r=e.get(i);r||(r={},e.set(i,r)),Cf(t).scope=r}})),t.eachSeries((function(e){if(!e.isColorBySeries()&&!t.isSeriesFiltered(e)){var n=e.getRawData(),i={},r=e.getData(),o=Cf(e).scope,a=e.visualStyleAccessPath||"itemStyle",s=Af(e,a);r.each((function(t){var e=r.getRawIndex(t);i[e]=t})),n.each((function(t){var a=i[t];if(r.getItemVisual(a,"colorFromPalette")){var l=r.ensureUniqueItemVisual(a,"style"),u=n.getName(t)||t+"",h=n.count();l[s]=e.getColorFromPalette(u,o,h)}}))}}))}},Nf=Math.PI;var Ef=function(){function t(t,e,n,i){this._stageTaskMap=ut(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}return t.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each((function(t){var e=t.overallTask;e&&e.dirty()}))},t.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context,r=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,o=i&&i.modDataCount;return{step:r,modBy:null!=o?Math.ceil(o/r):null,modDataCount:o}}},t.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},t.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),r=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,o=t.get("large")&&i>=t.get("largeThreshold"),a="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:r,modDataCount:a,large:o}},t.prototype.restorePipelines=function(t){var e=this,n=e._pipelineMap=ut();t.eachSeries((function(t){var i=t.getProgressive(),r=t.uid;n.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:i&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(i||700),count:0}),e._pipe(t,t.dataTask)}))},t.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),n=this.api;L(this._allHandlers,(function(i){var r=t.get(i.uid)||t.set(i.uid,{}),o="";it(!(i.reset&&i.overallReset),o),i.reset&&this._createSeriesStageTask(i,r,e,n),i.overallReset&&this._createOverallStageTask(i,r,e,n)}),this)},t.prototype.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,this._pipe(e,r)},t.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},t.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},t.prototype._performStageTasks=function(t,e,n,i){i=i||{};var r=!1,o=this;function a(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}L(t,(function(t,s){if(!i.visualType||i.visualType===t.visualType){var l=o._stageTaskMap.get(t.uid),u=l.seriesTaskMap,h=l.overallTask;if(h){var c,p=h.agentStubMap;p.each((function(t){a(i,t)&&(t.dirty(),c=!0)})),c&&h.dirty(),o.updatePayload(h,n);var f=o.getPerformArgs(h,i.block);p.each((function(t){t.perform(f)})),h.perform(f)&&(r=!0)}else u&&u.each((function(s,l){a(i,s)&&s.dirty();var u=o.getPerformArgs(s,i.block);u.skip=!t.performRawSeries&&e.isSeriesFiltered(s.context.model),o.updatePayload(s,n),s.perform(u)&&(r=!0)}))}})),this.unfinished=r||this.unfinished},t.prototype.performSeriesTasks=function(t){var e;t.eachSeries((function(t){e=t.dataTask.perform()||e})),this.unfinished=e||this.unfinished},t.prototype.plan=function(){this._pipelineMap.each((function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)}))},t.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},t.prototype._createSeriesStageTask=function(t,e,n,i){var r=this,o=e.seriesTaskMap,a=e.seriesTaskMap=ut(),s=t.seriesType,l=t.getTargetSeries;function u(e){var s=e.uid,l=a.set(s,o&&o.get(s)||Ap({plan:Hf,reset:Gf,count:Xf}));l.context={model:e,ecModel:n,api:i,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:r},r._pipe(e,l)}t.createOnAllSeries?n.eachRawSeries(u):s?n.eachRawSeriesByType(s,u):l&&l(n,i).each(u)},t.prototype._createOverallStageTask=function(t,e,n,i){var r=this,o=e.overallTask=e.overallTask||Ap({reset:zf});o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:r};var a=o.agentStubMap,s=o.agentStubMap=ut(),l=t.seriesType,u=t.getTargetSeries,h=!0,c=!1,p="";function f(t){var e=t.uid,n=s.set(e,a&&a.get(e)||(c=!0,Ap({reset:Bf,onDirty:Vf})));n.context={model:t,overallProgress:h},n.agent=o,n.__block=h,r._pipe(t,n)}it(!t.createOnAllSeries,p),l?n.eachRawSeriesByType(l,f):u?u(n,i).each(f):(h=!1,L(n.getSeries(),f)),c&&o.dirty()},t.prototype._pipe=function(t,e){var n=t.uid,i=this._pipelineMap.get(n);!i.head&&(i.head=e),i.tail&&i.tail.pipe(e),i.tail=e,e.__idxInPipeline=i.count++,e.__pipeline=i},t.wrapStageHandler=function(t,e){return V(t)&&(t={overallReset:t,seriesType:Yf(t)}),t.uid=Yu("stageHandler"),e&&(t.visualType=e),t},t}();function zf(t){t.overallReset(t.ecModel,t.api,t.payload)}function Bf(t){return t.overallProgress&&Ff}function Ff(){this.agent.dirty(),this.getDownstream().dirty()}function Vf(){this.agent&&this.agent.dirty()}function Hf(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function Gf(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=rr(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?O(e,(function(t,e){return Uf(e)})):Wf}var Wf=Uf(0);function Uf(t){return function(e,n){var i=n.data,r=n.resetDefines[t];if(r&&r.dataEach)for(var o=e.start;o<e.end;o++)r.dataEach(i,o);else r&&r.progress&&r.progress(e,i)}}function Xf(t){return t.data.count()}function Yf(t){qf=null;try{t(jf,Zf)}catch(t){}return qf}var qf,jf={},Zf={};function Kf(t,e){for(var n in e.prototype)t[n]=ft}Kf(jf,Tc),Kf(Zf,Ac),jf.eachSeriesByType=jf.eachRawSeriesByType=function(t){qf=t},jf.eachComponent=function(t){"series"===t.mainType&&t.subType&&(qf=t.subType)};var $f=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],Jf={color:$f,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],$f]},Qf="#B9B8CE",td="#100C2A",ed=function(){return{axisLine:{lineStyle:{color:Qf}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},nd=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],id={darkMode:!0,color:nd,backgroundColor:td,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:Qf}},textStyle:{color:Qf},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:Qf}},dataZoom:{borderColor:"#71708A",textStyle:{color:Qf},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:Qf}},timeline:{lineStyle:{color:Qf},label:{color:Qf},controlStyle:{color:Qf,borderColor:Qf}},calendar:{itemStyle:{color:td},dayLabel:{color:Qf},monthLabel:{color:Qf},yearLabel:{color:Qf}},timeAxis:ed(),logAxis:ed(),valueAxis:ed(),categoryAxis:ed(),line:{symbol:"circle"},graph:{color:nd},gauge:{title:{color:Qf},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:Qf},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};id.categoryAxis.splitLine.show=!1;var rd=function(){function t(){}return t.prototype.normalizeQuery=function(t){var e={},n={},i={};if(H(t)){var r=Tr(t);e.mainType=r.main||null,e.subType=r.sub||null}else{var o=["Index","Name","Id"],a={name:1,dataIndex:1,dataType:1};L(t,(function(t,r){for(var s=!1,l=0;l<o.length;l++){var u=o[l],h=r.lastIndexOf(u);if(h>0&&h===r.length-u.length){var c=r.slice(0,h);"data"!==c&&(e.mainType=c,e[u.toLowerCase()]=t,s=!0)}}a.hasOwnProperty(r)&&(n[r]=t,s=!0),s||(i[r]=t)}))}return{cptQuery:e,dataQuery:n,otherQuery:i}},t.prototype.filter=function(t,e){var n=this.eventInfo;if(!n)return!0;var i=n.targetEl,r=n.packedEvent,o=n.model,a=n.view;if(!o||!a)return!0;var s=e.cptQuery,l=e.dataQuery;return u(s,o,"mainType")&&u(s,o,"subType")&&u(s,o,"index","componentIndex")&&u(s,o,"name")&&u(s,o,"id")&&u(l,r,"name")&&u(l,r,"dataIndex")&&u(l,r,"dataType")&&(!a.filterForExposedEvent||a.filterForExposedEvent(t,e.otherQuery,i,r));function u(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},t.prototype.afterTrigger=function(){this.eventInfo=null},t}(),od={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData();if(t.legendIcon&&n.setVisual("legendIcon",t.legendIcon),t.hasSymbolVisual){var i=t.get("symbol"),r=t.get("symbolSize"),o=t.get("symbolKeepAspect"),a=t.get("symbolRotate"),s=t.get("symbolOffset"),l=V(i),u=V(r),h=V(a),c=V(s),p=l||u||h||c,f=!l&&i?i:t.defaultSymbol,d=u?null:r,g=h?null:a,y=c?null:s;if(n.setVisual({legendIcon:t.legendIcon||f,symbol:f,symbolSize:d,symbolKeepAspect:o,symbolRotate:g,symbolOffset:y}),!e.isSeriesFiltered(t))return{dataEach:p?function(e,n){var o=t.getRawValue(n),p=t.getDataParams(n);l&&e.setItemVisual(n,"symbol",i(o,p)),u&&e.setItemVisual(n,"symbolSize",r(o,p)),h&&e.setItemVisual(n,"symbolRotate",a(o,p)),c&&e.setItemVisual(n,"symbolOffset",s(o,p))}:null}}}};function ad(t,e,n,i,r){var o=t+e;n.isSilent(o)||i.eachComponent({mainType:"series",subType:"pie"},(function(t){for(var e=t.seriesIndex,i=r.selected,a=0;a<i.length;a++)if(i[a].seriesIndex===e){var s=t.getData(),l=gr(s,r.fromActionPayload);n.trigger(o,{type:o,seriesId:t.id,name:F(l)?s.getName(l[0]):s.getName(l),selected:T({},t.option.selectedMap)})}}))}function sd(t,e,n){for(var i;t&&(!e(t)||(i=t,!n));)t=t.__hostTarget||t.parent;return i}var ld=Math.round(9*Math.random()),ud="function"==typeof Object.defineProperty,hd=function(){function t(){this._id="__ec_inner_"+ld++}return t.prototype.get=function(t){return this._guard(t)[this._id]},t.prototype.set=function(t,e){var n=this._guard(t);return ud?Object.defineProperty(n,this._id,{value:e,enumerable:!1,configurable:!0}):n[this._id]=e,this},t.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},t.prototype.has=function(t){return!!this._guard(t)[this._id]},t.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},t}(),cd=Aa.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i+o),t.lineTo(n-r,i+o),t.closePath()}}),pd=Aa.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i),t.lineTo(n,i+o),t.lineTo(n-r,i),t.closePath()}}),fd=Aa.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,o=Math.max(r,e.height),a=r/2,s=a*a/(o-a),l=i-o+a+s,u=Math.asin(s/a),h=Math.cos(u)*a,c=Math.sin(u),p=Math.cos(u),f=.6*a,d=.7*a;t.moveTo(n-h,l+s),t.arc(n,l,a,Math.PI-u,2*Math.PI+u),t.bezierCurveTo(n+h-c*f,l+s+p*f,n,i-d,n,i),t.bezierCurveTo(n,i-d,n-h+c*f,l+s+p*f,n-h,l+s),t.closePath()}}),dd=Aa.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,o=e.y,a=i/3*2;t.moveTo(r,o),t.lineTo(r+a,o+n),t.lineTo(r,o+n/4*3),t.lineTo(r-a,o+n),t.lineTo(r,o),t.closePath()}}),gd={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var o=Math.min(n,i);r.x=t,r.y=e,r.width=o,r.height=o},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},yd={};L({line:Bl,rect:Va,roundRect:Va,square:Va,circle:hl,diamond:pd,pin:fd,arrow:dd,triangle:cd},(function(t,e){yd[e]=new t}));var vd=Aa.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var i=yi(t,e,n),r=this.shape;return r&&"pin"===r.symbolType&&"inside"===e.position&&(i.y=n.y+.4*n.height),i},buildPath:function(t,e,n){var i=e.symbolType;if("none"!==i){var r=yd[i];r||(r=yd[i="rect"]),gd[i](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,n)}}});function md(t,e){if("image"!==this.type){var n=this.style;this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw()}}function _d(t,e,n,i,r,o,a){var s,l=0===t.indexOf("empty");return l&&(t=t.substr(5,1).toLowerCase()+t.substr(6)),(s=0===t.indexOf("image://")?vu(t.slice(8),new oi(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?yu(t.slice(7),{},new oi(e,n,i,r),a?"center":"cover"):new vd({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=l,s.setColor=md,o&&s.setColor(o),s}function xd(t,e){if(null!=t)return F(t)||(t=[t,t]),[zi(t[0],e[0])||0,zi(Q(t[1],t[0]),e[1])||0]}function bd(t,e,n){for(var i="radial"===e.type?function(t,e,n){var i=n.width,r=n.height,o=Math.min(i,r),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;return e.global||(a=a*i+n.x,s=s*r+n.y,l*=o),t.createRadialGradient(a,s,0,a,s,l)}(t,e,n):function(t,e,n){var i=null==e.x?0:e.x,r=null==e.x2?1:e.x2,o=null==e.y?0:e.y,a=null==e.y2?0:e.y2;return e.global||(i=i*n.width+n.x,r=r*n.width+n.x,o=o*n.height+n.y,a=a*n.height+n.y),i=isNaN(i)?0:i,r=isNaN(r)?1:r,o=isNaN(o)?0:o,a=isNaN(a)?0:a,t.createLinearGradient(i,o,r,a)}(t,e,n),r=e.colorStops,o=0;o<r.length;o++)i.addColorStop(r[o].offset,r[o].color);return i}function wd(t,e){return t&&"solid"!==t&&e>0?(e=e||1,"dashed"===t?[4*e,2*e]:"dotted"===t?[e]:W(t)?[t]:F(t)?t:null):null}var Sd=new ha(!0);function Md(t){var e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))}function Td(t){return"string"==typeof t&&"none"!==t}function Cd(t){var e=t.fill;return null!=e&&"none"!==e}function Dd(t,e){if(null!=e.fillOpacity&&1!==e.fillOpacity){var n=t.globalAlpha;t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n}else t.fill()}function Id(t,e){if(null!=e.strokeOpacity&&1!==e.strokeOpacity){var n=t.globalAlpha;t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n}else t.stroke()}function kd(t,e,n){var i=Br(e.image,e.__image,n);if(Vr(i)){var r=t.createPattern(i,e.repeat||"repeat");if("function"==typeof DOMMatrix&&r.setTransform){var o=new DOMMatrix;o.rotateSelf(0,0,(e.rotation||0)/Math.PI*180),o.scaleSelf(e.scaleX||1,e.scaleY||1),o.translateSelf(e.x||0,e.y||0),r.setTransform(o)}return r}}var Ad=["shadowBlur","shadowOffsetX","shadowOffsetY"],Pd=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Ld(t,e,n,i,r){var o=!1;if(!i&&e===(n=n||{}))return!1;if(i||e.opacity!==n.opacity){o||(Nd(t,r),o=!0);var a=Math.max(Math.min(e.opacity,1),0);t.globalAlpha=isNaN(a)?to.opacity:a}(i||e.blend!==n.blend)&&(o||(Nd(t,r),o=!0),t.globalCompositeOperation=e.blend||to.blend);for(var s=0;s<Ad.length;s++){var l=Ad[s];(i||e[l]!==n[l])&&(o||(Nd(t,r),o=!0),t[l]=t.dpr*(e[l]||0))}return(i||e.shadowColor!==n.shadowColor)&&(o||(Nd(t,r),o=!0),t.shadowColor=e.shadowColor||to.shadowColor),o}function Od(t,e,n,i,r){var o=Ed(e,r.inHover),a=i?null:n&&Ed(n,r.inHover)||{};if(o===a)return!1;var s=Ld(t,o,a,i,r);if((i||o.fill!==a.fill)&&(s||(Nd(t,r),s=!0),Td(o.fill)&&(t.fillStyle=o.fill)),(i||o.stroke!==a.stroke)&&(s||(Nd(t,r),s=!0),Td(o.stroke)&&(t.strokeStyle=o.stroke)),(i||o.opacity!==a.opacity)&&(s||(Nd(t,r),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()){var l=o.lineWidth/(o.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1);t.lineWidth!==l&&(s||(Nd(t,r),s=!0),t.lineWidth=l)}for(var u=0;u<Pd.length;u++){var h=Pd[u],c=h[0];(i||o[c]!==a[c])&&(s||(Nd(t,r),s=!0),t[c]=o[c]||h[1])}return s}function Rd(t,e){var n=e.transform,i=t.dpr||1;n?t.setTransform(i*n[0],i*n[1],i*n[2],i*n[3],i*n[4],i*n[5]):t.setTransform(i,0,0,i,0,0)}function Nd(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function Ed(t,e){return e&&t.__hoverStyle||t.style}function zd(t,e){Bd(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Bd(t,e,n,i){var r=e.transform;if(!e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1))return e.__dirty&=-2,void(e.__isRendered=!1);var o=e.__clipPaths,a=n.prevElClipPaths,s=!1,l=!1;if(a&&!function(t,e){if(t===e||!t&&!e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0;return!1}(o,a)||(a&&a.length&&(Nd(t,n),t.restore(),l=s=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),o&&o.length&&(Nd(t,n),t.save(),function(t,e,n){for(var i=!1,r=0;r<t.length;r++){var o=t[r];i=i||o.isZeroArea(),Rd(e,o),e.beginPath(),o.buildPath(e,o.shape),e.clip()}n.allClipped=i}(o,t,n),s=!0),n.prevElClipPaths=o),n.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var u=n.prevEl;u||(l=s=!0);var h,c,p=e instanceof Aa&&e.autoBatch&&function(t){var e=Cd(t),n=Md(t);return!(t.lineDash||!(+e^+n)||e&&"string"!=typeof t.fill||n&&"string"!=typeof t.stroke||t.strokePercent<1||t.strokeOpacity<1||t.fillOpacity<1)}(e.style);s||(h=r,c=u.transform,h&&c?h[0]!==c[0]||h[1]!==c[1]||h[2]!==c[2]||h[3]!==c[3]||h[4]!==c[4]||h[5]!==c[5]:h||c)?(Nd(t,n),Rd(t,e)):p||Nd(t,n);var f=Ed(e,n.inHover);e instanceof Aa?(1!==n.lastDrawType&&(l=!0,n.lastDrawType=1),Od(t,e,u,l,n),p&&(n.batchFill||n.batchStroke)||t.beginPath(),function(t,e,n,i){var r=Md(n),o=Cd(n),a=n.strokePercent,s=a<1,l=!e.path;e.silent&&!s||!l||e.createPathProxy();var u=e.path||Sd;if(!i){var h=n.fill,c=n.stroke,p=o&&!!h.colorStops,f=r&&!!c.colorStops,d=o&&!!h.image,g=r&&!!c.image,y=void 0,v=void 0,m=void 0,_=void 0,x=void 0;(p||f)&&(x=e.getBoundingRect()),p&&(y=e.__dirty?bd(t,h,x):e.__canvasFillGradient,e.__canvasFillGradient=y),f&&(v=e.__dirty?bd(t,c,x):e.__canvasStrokeGradient,e.__canvasStrokeGradient=v),d&&(m=e.__dirty||!e.__canvasFillPattern?kd(t,h,e):e.__canvasFillPattern,e.__canvasFillPattern=m),g&&(_=e.__dirty||!e.__canvasStrokePattern?kd(t,c,e):e.__canvasStrokePattern,e.__canvasStrokePattern=m),p?t.fillStyle=y:d&&(m?t.fillStyle=m:o=!1),f?t.strokeStyle=v:g&&(_?t.strokeStyle=_:r=!1)}var b=n.lineDash&&n.lineWidth>0&&wd(n.lineDash,n.lineWidth),w=n.lineDashOffset,S=!!t.setLineDash,M=e.getGlobalScale();if(u.setScale(M[0],M[1],e.segmentIgnoreThreshold),b){var T=n.strokeNoScale&&e.getLineScale?e.getLineScale():1;T&&1!==T&&(b=O(b,(function(t){return t/T})),w/=T)}var C=!0;(l||4&e.__dirty||b&&!S&&r)&&(u.setDPR(t.dpr),s?u.setContext(null):(u.setContext(t),C=!1),u.reset(),b&&!S&&(u.setLineDash(b),u.setLineDashOffset(w)),e.buildPath(u,e.shape,i),u.toStatic(),e.pathUpdated()),C&&u.rebuildPath(t,s?a:1),b&&S&&(t.setLineDash(b),t.lineDashOffset=w),i||(n.strokeFirst?(r&&Id(t,n),o&&Dd(t,n)):(o&&Dd(t,n),r&&Id(t,n))),b&&S&&t.setLineDash([])}(t,e,f,p),p&&(n.batchFill=f.fill||"",n.batchStroke=f.stroke||"")):e instanceof La?(3!==n.lastDrawType&&(l=!0,n.lastDrawType=3),Od(t,e,u,l,n),function(t,e,n){var i=n.text;if(null!=i&&(i+=""),i){t.font=n.font||si,t.textAlign=n.textAlign,t.textBaseline=n.textBaseline;var r=void 0;if(t.setLineDash){var o=n.lineDash&&n.lineWidth>0&&wd(n.lineDash,n.lineWidth),a=n.lineDashOffset;if(o){var s=n.strokeNoScale&&e.getLineScale?e.getLineScale():1;s&&1!==s&&(o=O(o,(function(t){return t/s})),a/=s),t.setLineDash(o),t.lineDashOffset=a,r=!0}}n.strokeFirst?(Md(n)&&t.strokeText(i,n.x,n.y),Cd(n)&&t.fillText(i,n.x,n.y)):(Cd(n)&&t.fillText(i,n.x,n.y),Md(n)&&t.strokeText(i,n.x,n.y)),r&&t.setLineDash([])}}(t,e,f)):e instanceof Na?(2!==n.lastDrawType&&(l=!0,n.lastDrawType=2),function(t,e,n,i,r){Ld(t,Ed(e,r.inHover),n&&Ed(n,r.inHover),i,r)}(t,e,u,l,n),function(t,e,n){var i=e.__image=Br(n.image,e.__image,e,e.onload);if(i&&Vr(i)){var r=n.x||0,o=n.y||0,a=e.getWidth(),s=e.getHeight(),l=i.width/i.height;if(null==a&&null!=s?a=s*l:null==s&&null!=a?s=a/l:null==a&&null==s&&(a=i.width,s=i.height),n.sWidth&&n.sHeight){var u=n.sx||0,h=n.sy||0;t.drawImage(i,u,h,n.sWidth,n.sHeight,r,o,a,s)}else if(n.sx&&n.sy){var c=a-(u=n.sx),p=s-(h=n.sy);t.drawImage(i,u,h,c,p,r,o,a,s)}else t.drawImage(i,r,o,a,s)}}(t,e,f)):e instanceof eu&&(4!==n.lastDrawType&&(l=!0,n.lastDrawType=4),function(t,e,n){var i=e.getDisplayables(),r=e.getTemporalDisplayables();t.save();var o,a,s={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:n.viewWidth,viewHeight:n.viewHeight,inHover:n.inHover};for(o=e.getCursor(),a=i.length;o<a;o++){(h=i[o]).beforeBrush&&h.beforeBrush(),h.innerBeforeBrush(),Bd(t,h,s,o===a-1),h.innerAfterBrush(),h.afterBrush&&h.afterBrush(),s.prevEl=h}for(var l=0,u=r.length;l<u;l++){var h;(h=r[l]).beforeBrush&&h.beforeBrush(),h.innerBeforeBrush(),Bd(t,h,s,l===u-1),h.innerAfterBrush(),h.afterBrush&&h.afterBrush(),s.prevEl=h}e.clearTemporalDisplayables(),e.notClear=!0,t.restore()}(t,e,n)),p&&i&&Nd(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),n.prevEl=e,e.__dirty=0,e.__isRendered=!0}}var Fd,Vd=new hd,Hd=new we(100),Gd=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Wd(t,e){if("none"===t)return null;var n=e.getDevicePixelRatio(),i=e.getZr(),r="svg"===i.painter.type;t.dirty&&Vd.delete(t);var o=Vd.get(t);if(o)return o;var a=C(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});"none"===a.backgroundColor&&(a.backgroundColor=null);var s={repeat:"repeat"};return function(t){for(var e,o=[n],s=!0,l=0;l<Gd.length;++l){var u=a[Gd[l]],h=typeof u;if(null!=u&&!F(u)&&"string"!==h&&"number"!==h&&"boolean"!==h){s=!1;break}o.push(u)}if(s){e=o.join(",")+(r?"-svg":"");var c=Hd.get(e);c&&(r?t.svgElement=c:t.image=c)}var p,f=Xd(a.dashArrayX),d=function(t){if(!t||"object"==typeof t&&0===t.length)return[0,0];if("number"==typeof t){var e=Math.ceil(t);return[e,e]}var n=O(t,(function(t){return Math.ceil(t)}));return t.length%2?n.concat(n):n}(a.dashArrayY),g=Ud(a.symbol),y=(b=f,O(b,(function(t){return Yd(t)}))),v=Yd(d),m=!r&&D(),_=r&&i.painter.createSVGElement("g"),x=function(){for(var t=1,e=0,n=y.length;e<n;++e)t=tr(t,y[e]);var i=1;for(e=0,n=g.length;e<n;++e)i=tr(i,g[e].length);t*=i;var r=v*y.length*g.length;return{width:Math.max(1,Math.min(t,a.maxTileWidth)),height:Math.max(1,Math.min(r,a.maxTileHeight))}}();var b;m&&(m.width=x.width*n,m.height=x.height*n,p=m.getContext("2d"));(function(){p&&(p.clearRect(0,0,m.width,m.height),a.backgroundColor&&(p.fillStyle=a.backgroundColor,p.fillRect(0,0,m.width,m.height)));for(var t=0,e=0;e<d.length;++e)t+=d[e];if(t<=0)return;var o=-v,s=0,l=0,u=0;for(;o<x.height;){if(s%2==0){for(var h=l/2%g.length,c=0,y=0,b=0;c<2*x.width;){var w=0;for(e=0;e<f[u].length;++e)w+=f[u][e];if(w<=0)break;if(y%2==0){var S=.5*(1-a.symbolSize),M=c+f[u][y]*S,T=o+d[s]*S,C=f[u][y]*a.symbolSize,D=d[s]*a.symbolSize,I=b/2%g[h].length;k(M,T,C,D,g[h][I])}c+=f[u][y],++b,++y===f[u].length&&(y=0)}++u===f.length&&(u=0)}o+=d[s],++l,++s===d.length&&(s=0)}function k(t,e,o,s,l){var u=r?1:n,h=_d(l,t*u,e*u,o*u,s*u,a.color,a.symbolKeepAspect);r?_.appendChild(i.painter.paintOne(h)):zd(p,h)}})(),s&&Hd.put(e,m||_);t.image=m,t.svgElement=_,t.svgWidth=x.width,t.svgHeight=x.height}(s),s.rotation=a.rotation,s.scaleX=s.scaleY=r?1:1/n,Vd.set(t,s),t.dirty=!1,s}function Ud(t){if(!t||0===t.length)return[["rect"]];if("string"==typeof t)return[[t]];for(var e=!0,n=0;n<t.length;++n)if("string"!=typeof t[n]){e=!1;break}if(e)return Ud([t]);var i=[];for(n=0;n<t.length;++n)"string"==typeof t[n]?i.push([t[n]]):i.push(t[n]);return i}function Xd(t){if(!t||0===t.length)return[[0,0]];if("number"==typeof t)return[[r=Math.ceil(t),r]];for(var e=!0,n=0;n<t.length;++n)if("number"!=typeof t[n]){e=!1;break}if(e)return Xd([t]);var i=[];for(n=0;n<t.length;++n)if("number"==typeof t[n]){var r=Math.ceil(t[n]);i.push([r,r])}else{(r=O(t[n],(function(t){return Math.ceil(t)}))).length%2==1?i.push(r.concat(r)):i.push(r)}return i}function Yd(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}function qd(t){H(t)&&(t=(new DOMParser).parseFromString(t,"text/xml"));var e=t;for(9===e.nodeType&&(e=e.firstChild);"svg"!==e.nodeName.toLowerCase()||1!==e.nodeType;)e=e.nextSibling;return e}var jd={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},Zd=E(jd),Kd={"alignment-baseline":"textBaseline","stop-color":"stopColor"},$d=E(Kd),Jd=function(){function t(){this._defs={},this._root=null}return t.prototype.parse=function(t,e){e=e||{};var n=qd(t);if(!n)throw new Error("Illegal svg");this._defsUsePending=[];var i=new Di;this._root=i;var r=[],o=n.getAttribute("viewBox")||"",a=parseFloat(n.getAttribute("width")||e.width),s=parseFloat(n.getAttribute("height")||e.height);isNaN(a)&&(a=null),isNaN(s)&&(s=null),rg(n,i,null,!0,!1);for(var l,u,h=n.firstChild;h;)this._parseNode(h,i,r,null,!1,!1),h=h.nextSibling;if(function(t,e){for(var n=0;n<e.length;n++){var i=e[n];i[0].style[i[1]]=t[i[2]]}}(this._defs,this._defsUsePending),this._defsUsePending=[],o){var c=lg(o);c.length>=4&&(l={x:parseFloat(c[0]||0),y:parseFloat(c[1]||0),width:parseFloat(c[2]),height:parseFloat(c[3])})}if(l&&null!=a&&null!=s&&(u=fg(l,{x:0,y:0,width:a,height:s}),!e.ignoreViewBox)){var p=i;(i=new Di).add(p),p.scaleX=p.scaleY=u.scale,p.x=u.x,p.y=u.y}return e.ignoreRootClip||null==a||null==s||i.setClipPath(new Va({shape:{x:0,y:0,width:a,height:s}})),{root:i,width:a,height:s,viewBoxRect:l,viewBoxTransform:u,named:r}},t.prototype._parseNode=function(t,e,n,i,r,o){var a,s=t.nodeName.toLowerCase(),l=i;if("defs"===s&&(r=!0),"text"===s&&(o=!0),"defs"===s||"switch"===s)a=e;else{if(!r){var u=Fd[s];if(u&&pt(Fd,s)){a=u.call(this,t,e);var h=t.getAttribute("name");if(h){var c={name:h,namedFrom:null,svgNodeTagLower:s,el:a};n.push(c),"g"===s&&(l=c)}else i&&n.push({name:i.name,namedFrom:i,svgNodeTagLower:s,el:a});e.add(a)}}var p=Qd[s];if(p&&pt(Qd,s)){var f=p.call(this,t),d=t.getAttribute("id");d&&(this._defs[d]=f)}}if(a&&a.isGroup)for(var g=t.firstChild;g;)1===g.nodeType?this._parseNode(g,a,n,l,r,o):3===g.nodeType&&o&&this._parseText(g,a),g=g.nextSibling},t.prototype._parseText=function(t,e){var n=new La({style:{text:t.textContent},silent:!0,x:this._textX||0,y:this._textY||0});ng(e,n),rg(t,n,this._defsUsePending,!1,!1),function(t,e){var n=e.__selfStyle;if(n){var i=n.textBaseline,r=i;i&&"auto"!==i?"baseline"===i?r="alphabetic":"before-edge"===i||"text-before-edge"===i?r="top":"after-edge"===i||"text-after-edge"===i?r="bottom":"central"!==i&&"mathematical"!==i||(r="middle"):r="alphabetic",t.style.textBaseline=r}var o=e.__inheritedStyle;if(o){var a=o.textAlign,s=a;a&&("middle"===a&&(s="center"),t.style.textAlign=s)}}(n,e);var i=n.style,r=i.fontSize;r&&r<9&&(i.fontSize=9,n.scaleX*=r/9,n.scaleY*=r/9);var o=(i.fontSize||i.fontFamily)&&[i.fontStyle,i.fontWeight,(i.fontSize||12)+"px",i.fontFamily||"sans-serif"].join(" ");i.font=o;var a=n.getBoundingRect();return this._textX+=a.width,e.add(n),n},t.internalField=void(Fd={g:function(t,e){var n=new Di;return ng(e,n),rg(t,n,this._defsUsePending,!1,!1),n},rect:function(t,e){var n=new Va;return ng(e,n),rg(t,n,this._defsUsePending,!1,!1),n.setShape({x:parseFloat(t.getAttribute("x")||"0"),y:parseFloat(t.getAttribute("y")||"0"),width:parseFloat(t.getAttribute("width")||"0"),height:parseFloat(t.getAttribute("height")||"0")}),n.silent=!0,n},circle:function(t,e){var n=new hl;return ng(e,n),rg(t,n,this._defsUsePending,!1,!1),n.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),r:parseFloat(t.getAttribute("r")||"0")}),n.silent=!0,n},line:function(t,e){var n=new Bl;return ng(e,n),rg(t,n,this._defsUsePending,!1,!1),n.setShape({x1:parseFloat(t.getAttribute("x1")||"0"),y1:parseFloat(t.getAttribute("y1")||"0"),x2:parseFloat(t.getAttribute("x2")||"0"),y2:parseFloat(t.getAttribute("y2")||"0")}),n.silent=!0,n},ellipse:function(t,e){var n=new pl;return ng(e,n),rg(t,n,this._defsUsePending,!1,!1),n.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),rx:parseFloat(t.getAttribute("rx")||"0"),ry:parseFloat(t.getAttribute("ry")||"0")}),n.silent=!0,n},polygon:function(t,e){var n,i=t.getAttribute("points");i&&(n=ig(i));var r=new Ol({shape:{points:n||[]},silent:!0});return ng(e,r),rg(t,r,this._defsUsePending,!1,!1),r},polyline:function(t,e){var n,i=t.getAttribute("points");i&&(n=ig(i));var r=new Nl({shape:{points:n||[]},silent:!0});return ng(e,r),rg(t,r,this._defsUsePending,!1,!1),r},image:function(t,e){var n=new Na;return ng(e,n),rg(t,n,this._defsUsePending,!1,!1),n.setStyle({image:t.getAttribute("xlink:href")||t.getAttribute("href"),x:+t.getAttribute("x"),y:+t.getAttribute("y"),width:+t.getAttribute("width"),height:+t.getAttribute("height")}),n.silent=!0,n},text:function(t,e){var n=t.getAttribute("x")||"0",i=t.getAttribute("y")||"0",r=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0";this._textX=parseFloat(n)+parseFloat(r),this._textY=parseFloat(i)+parseFloat(o);var a=new Di;return ng(e,a),rg(t,a,this._defsUsePending,!1,!0),a},tspan:function(t,e){var n=t.getAttribute("x"),i=t.getAttribute("y");null!=n&&(this._textX=parseFloat(n)),null!=i&&(this._textY=parseFloat(i));var r=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0",a=new Di;return ng(e,a),rg(t,a,this._defsUsePending,!1,!0),this._textX+=parseFloat(r),this._textY+=parseFloat(o),a},path:function(t,e){var n=ll(t.getAttribute("d")||"");return ng(e,n),rg(t,n,this._defsUsePending,!1,!1),n.silent=!0,n}}),t}(),Qd={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||"0",10),n=parseInt(t.getAttribute("y1")||"0",10),i=parseInt(t.getAttribute("x2")||"10",10),r=parseInt(t.getAttribute("y2")||"0",10),o=new ql(e,n,i,r);return tg(t,o),eg(t,o),o},radialgradient:function(t){var e=parseInt(t.getAttribute("cx")||"0",10),n=parseInt(t.getAttribute("cy")||"0",10),i=parseInt(t.getAttribute("r")||"0",10),r=new jl(e,n,i);return tg(t,r),eg(t,r),r}};function tg(t,e){"userSpaceOnUse"===t.getAttribute("gradientUnits")&&(e.global=!0)}function eg(t,e){for(var n=t.firstChild;n;){if(1===n.nodeType&&"stop"===n.nodeName.toLocaleLowerCase()){var i=n.getAttribute("offset"),r=void 0;r=i&&i.indexOf("%")>0?parseInt(i,10)/100:i?parseFloat(i):0;var o={};pg(n,o,o);var a=o.stopColor||n.getAttribute("stop-color")||"#000000";e.colorStops.push({offset:r,color:a})}n=n.nextSibling}}function ng(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),C(e.__inheritedStyle,t.__inheritedStyle))}function ig(t){for(var e=lg(t),n=[],i=0;i<e.length;i+=2){var r=parseFloat(e[i]),o=parseFloat(e[i+1]);n.push([r,o])}return n}function rg(t,e,n,i,r){var o=e,a=o.__inheritedStyle=o.__inheritedStyle||{},s={};1===t.nodeType&&(function(t,e){var n=t.getAttribute("transform");if(n){n=n.replace(/,/g," ");var i=[],r=null;n.replace(ug,(function(t,e,n){return i.push(e,n),""}));for(var o=i.length-1;o>0;o-=2){var a=i[o],s=i[o-1],l=lg(a);switch(r=r||[1,0,0,1,0,0],s){case"translate":Rn(r,r,[parseFloat(l[0]),parseFloat(l[1]||"0")]);break;case"scale":En(r,r,[parseFloat(l[0]),parseFloat(l[1]||l[0])]);break;case"rotate":Nn(r,r,-parseFloat(l[0])*hg);break;case"skewX":On(r,[1,0,Math.tan(parseFloat(l[0])*hg),1,0,0],r);break;case"skewY":On(r,[1,Math.tan(parseFloat(l[0])*hg),0,1,0,0],r);break;case"matrix":r[0]=parseFloat(l[0]),r[1]=parseFloat(l[1]),r[2]=parseFloat(l[2]),r[3]=parseFloat(l[3]),r[4]=parseFloat(l[4]),r[5]=parseFloat(l[5])}}e.setLocalTransform(r)}}(t,e),pg(t,a,s),i||function(t,e,n){for(var i=0;i<Zd.length;i++){var r=Zd[i];null!=(o=t.getAttribute(r))&&(e[jd[r]]=o)}for(i=0;i<$d.length;i++){var o;r=$d[i];null!=(o=t.getAttribute(r))&&(n[Kd[r]]=o)}}(t,a,s)),o.style=o.style||{},null!=a.fill&&(o.style.fill=ag(o,"fill",a.fill,n)),null!=a.stroke&&(o.style.stroke=ag(o,"stroke",a.stroke,n)),L(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],(function(t){null!=a[t]&&(o.style[t]=parseFloat(a[t]))})),L(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],(function(t){null!=a[t]&&(o.style[t]=a[t])})),r&&(o.__selfStyle=s),a.lineDash&&(o.style.lineDash=O(lg(a.lineDash),(function(t){return parseFloat(t)}))),"hidden"!==a.visibility&&"collapse"!==a.visibility||(o.invisible=!0),"none"===a.display&&(o.ignore=!0)}var og=/^url\(\s*#(.*?)\)/;function ag(t,e,n,i){var r=n&&n.match(og);if(!r)return"none"===n&&(n=null),n;var o=rt(r[1]);i.push([t,e,o])}var sg=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function lg(t){return t.match(sg)||[]}var ug=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,hg=Math.PI/180;var cg=/([^\s:;]+)\s*:\s*([^:;]+)/g;function pg(t,e,n){var i,r=t.getAttribute("style");if(r)for(cg.lastIndex=0;null!=(i=cg.exec(r));){var o=i[1],a=pt(jd,o)?jd[o]:null;a&&(e[a]=i[2]);var s=pt(Kd,o)?Kd[o]:null;s&&(n[s]=i[2])}}function fg(t,e){var n=e.width/t.width,i=e.height/t.height,r=Math.min(n,i);return{scale:r,x:-(t.x+t.width/2)*r+(e.x+e.width/2),y:-(t.y+t.height/2)*r+(e.y+e.height/2)}}function dg(t,e){return Math.abs(t-e)<1e-8}function gg(t,e,n){var i=0,r=t[0];if(!r)return!1;for(var o=1;o<t.length;o++){var a=t[o];i+=ma(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return dg(r[0],s[0])&&dg(r[1],s[1])||(i+=ma(r[0],r[1],s[0],s[1],e,n)),0!==i}var yg=[],vg=function(){function t(t){this.name=t}return t.prototype.getCenter=function(){},t}(),mg=function(t){function e(e,n,i){var r=t.call(this,e)||this;if(r.type="geoJSON",r.geometries=n,i)i=[i[0],i[1]];else{var o=r.getBoundingRect();i=[o.x+o.width/2,o.y+o.height/2]}return r._center=i,r}return n(e,t),e.prototype.getBoundingRect=function(){var t=this._rect;if(t)return t;for(var e=Number.MAX_VALUE,n=[e,e],i=[-e,-e],r=[],o=[],a=this.geometries,s=0;s<a.length;s++){if("polygon"===a[s].type)Bo(a[s].exterior,r,o),Pt(n,n,r),Lt(i,i,o)}return 0===s&&(n[0]=n[1]=i[0]=i[1]=0),this._rect=new oi(n[0],n[1],i[0]-n[0],i[1]-n[1])},e.prototype.contain=function(t){var e=this.getBoundingRect(),n=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var i=0,r=n.length;i<r;i++)if("polygon"===n[i].type){var o=n[i].exterior,a=n[i].interiors;if(gg(o,t[0],t[1])){for(var s=0;s<(a?a.length:0);s++)if(gg(a[s],t[0],t[1]))continue t;return!0}}return!1},e.prototype.transformTo=function(t,e,n,i){var r=this.getBoundingRect(),o=r.width/r.height;n?i||(i=n/o):n=o*i;for(var a=new oi(t,e,n,i),s=r.calculateTransform(a),l=this.geometries,u=0;u<l.length;u++)if("polygon"===l[u].type){for(var h=l[u].exterior,c=l[u].interiors,p=0;p<h.length;p++)At(h[p],h[p],s);for(var f=0;f<(c?c.length:0);f++)for(p=0;p<c[f].length;p++)At(c[f][p],c[f][p],s)}(r=this._rect).copy(a),this._center=[r.x+r.width/2,r.y+r.height/2]},e.prototype.cloneShallow=function(t){null==t&&(t=this.name);var n=new e(t,this.geometries,this._center);return n._rect=this._rect,n.transformTo=null,n},e.prototype.getCenter=function(){return this._center},e.prototype.setCenter=function(t){this._center=t},e}(vg),_g=function(t){function e(e,n){var i=t.call(this,e)||this;return i.type="geoSVG",i._elOnlyForCalculate=n,i}return n(e,t),e.prototype.getCenter=function(){var t=this._center;return t||(t=this._center=this._calculateCenter()),t},e.prototype._calculateCenter=function(){for(var t=this._elOnlyForCalculate,e=t.getBoundingRect(),n=[e.x+e.width/2,e.y+e.height/2],i=Pn(yg),r=t;r&&!r.isGeoSVGGraphicRoot;)On(i,r.getLocalTransform(),i),r=r.parent;return zn(i,i),At(n,n,i),n},e}(vg),xg=ut(["rect","circle","line","ellipse","polygon","polyline","path","text","tspan","g"]),bg=function(){function t(t,e){this.type="geoSVG",this._usedGraphicMap=ut(),this._freedGraphics=[],this._mapName=t,this._parsedXML=qd(e)}return t.prototype.load=function(){var t=this._firstGraphic;if(!t){t=this._firstGraphic=this._buildGraphic(this._parsedXML),this._freedGraphics.push(t),this._boundingRect=this._firstGraphic.boundingRect.clone();var e=function(t){var e=[],n=ut();return L(t,(function(t){if(null==t.namedFrom){var i=new _g(t.name,t.el);e.push(i),n.set(t.name,i)}})),{regions:e,regionsMap:n}}(t.named),n=e.regions,i=e.regionsMap;this._regions=n,this._regionsMap=i}return{boundingRect:this._boundingRect,regions:this._regions,regionsMap:this._regionsMap}},t.prototype._buildGraphic=function(t){var e,n,i,r;try{it(null!=(n=(e=t&&(i=t,r={ignoreViewBox:!0,ignoreRootClip:!0},(new Jd).parse(i,r))||{}).root))}catch(t){throw new Error("Invalid svg format\n"+t.message)}var o=new Di;o.add(n),o.isGeoSVGGraphicRoot=!0;var a=e.width,s=e.height,l=e.viewBoxRect,u=this._boundingRect;if(!u){var h=void 0,c=void 0,p=void 0,f=void 0;if(null!=a?(h=0,p=a):l&&(h=l.x,p=l.width),null!=s?(c=0,f=s):l&&(c=l.y,f=l.height),null==h||null==c){var d=n.getBoundingRect();null==h&&(h=d.x,p=d.width),null==c&&(c=d.y,f=d.height)}u=this._boundingRect=new oi(h,c,p,f)}if(l){var g=fg(l,u);n.scaleX=n.scaleY=g.scale,n.x=g.x,n.y=g.y}o.setClipPath(new Va({shape:u.plain()}));var y=[];return L(e.named,(function(t){var e;null!=xg.get(t.svgNodeTagLower)&&(y.push(t),(e=t.el).silent=!1,e.isGroup&&e.traverse((function(t){t.silent=!1})))})),{root:o,boundingRect:u,named:y}},t.prototype.useGraphic=function(t){var e=this._usedGraphicMap,n=e.get(t);return n||(n=this._freedGraphics.pop()||this._buildGraphic(this._parsedXML),e.set(t,n),n)},t.prototype.freeGraphic=function(t){var e=this._usedGraphicMap,n=e.get(t);n&&(e.removeKey(t),this._freedGraphics.push(n))},t}();function wg(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=t.charCodeAt(a)-64,l=t.charCodeAt(a+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),r=s+=r,o=l+=o,i.push([s/n,l/n])}return i}function Sg(t,e){return O(N((t=function(t){if(!t.UTF8Encoding)return t;var e=t,n=e.UTF8Scale;null==n&&(n=1024);for(var i=e.features,r=0;r<i.length;r++){var o=i[r].geometry;if("Polygon"===o.type)for(var a=o.coordinates,s=0;s<a.length;s++)a[s]=wg(a[s],o.encodeOffsets[s],n);else if("MultiPolygon"===o.type)for(a=o.coordinates,s=0;s<a.length;s++)for(var l=a[s],u=0;u<l.length;u++)l[u]=wg(l[u],o.encodeOffsets[s][u],n)}return e.UTF8Encoding=!1,e}(t)).features,(function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0})),(function(t){var n=t.properties,i=t.geometry,r=[];if("Polygon"===i.type){var o=i.coordinates;r.push({type:"polygon",exterior:o[0],interiors:o.slice(1)})}"MultiPolygon"===i.type&&L(o=i.coordinates,(function(t){t[0]&&r.push({type:"polygon",exterior:t[0],interiors:t.slice(1)})}));var a=new mg(n[e||"name"],r,n.cp);return a.properties=n,a}))}for(var Mg=[126,25],Tg="南海诸岛",Cg=[[[0,3.5],[7,11.2],[15,11.9],[30,7],[42,.7],[52,.7],[56,7.7],[59,.7],[64,.7],[64,0],[5,0],[0,3.5]],[[13,16.1],[19,14.7],[16,21.7],[11,23.1],[13,16.1]],[[12,32.2],[14,38.5],[15,38.5],[13,32.2],[12,32.2]],[[16,47.6],[12,53.2],[13,53.2],[18,47.6],[16,47.6]],[[6,64.4],[8,70],[9,70],[8,64.4],[6,64.4]],[[23,82.6],[29,79.8],[30,79.8],[25,82.6],[23,82.6]],[[37,70.7],[43,62.3],[44,62.3],[39,70.7],[37,70.7]],[[48,51.1],[51,45.5],[53,45.5],[50,51.1],[48,51.1]],[[51,35],[51,28.7],[53,28.7],[53,35],[51,35]],[[52,22.4],[55,17.5],[56,17.5],[53,22.4],[52,22.4]],[[58,12.6],[62,7],[63,7],[60,12.6],[58,12.6]],[[0,3.5],[0,93.1],[64,93.1],[64,0],[63,0],[63,92.4],[1,92.4],[1,3.5],[0,3.5]]],Dg=0;Dg<Cg.length;Dg++)for(var Ig=0;Ig<Cg[Dg].length;Ig++)Cg[Dg][Ig][0]/=10.5,Cg[Dg][Ig][1]/=-14,Cg[Dg][Ig][0]+=Mg[0],Cg[Dg][Ig][1]+=Mg[1];var kg={"南海诸岛":[32,80],"广东":[0,-10],"香港":[10,5],"澳门":[-10,10],"天津":[5,5]};var Ag={Russia:[100,60],"United States":[-99,38],"United States of America":[-99,38]};var Pg=[[[123.45165252685547,25.73527164402261],[123.49731445312499,25.73527164402261],[123.49731445312499,25.750734064600884],[123.45165252685547,25.750734064600884],[123.45165252685547,25.73527164402261]]];var Lg=function(){function t(t,e,n){var i;this.type="geoJSON",this._parsedMap=ut(),this._mapName=t,this._specialAreas=n,this._geoJSON=H(i=e)?"undefined"!=typeof JSON&&JSON.parse?JSON.parse(i):new Function("return ("+i+");")():i}return t.prototype.load=function(t,e){e=e||"name";var n=this._parsedMap.get(e);if(!n){var i=this._parseToRegions(e);n=this._parsedMap.set(e,{regions:i,boundingRect:Og(i)})}var r=ut(),o=[];return L(n.regions,(function(e){var n=e.name;t&&t.hasOwnProperty(n)&&(e=e.cloneShallow(n=t[n])),o.push(e),r.set(n,e)})),{regions:o,boundingRect:n.boundingRect||new oi(0,0,0,0),regionsMap:r}},t.prototype._parseToRegions=function(t){var e,n=this._mapName,i=this._geoJSON;try{e=i?Sg(i,t):[]}catch(t){throw new Error("Invalid geoJson format\n"+t.message)}return function(t,e){if("china"===t){for(var n=0;n<e.length;n++)if(e[n].name===Tg)return;e.push(new mg(Tg,O(Cg,(function(t){return{type:"polygon",exterior:t}})),Mg))}}(n,e),L(e,(function(t){var e=t.name;!function(t,e){if("china"===t){var n=kg[e.name];if(n){var i=e.getCenter();i[0]+=n[0]/10.5,i[1]+=-n[1]/14,e.setCenter(i)}}}(n,t),function(t,e){if("world"===t){var n=Ag[e.name];if(n){var i=[n[0],n[1]];e.setCenter(i)}}}(n,t),function(t,e){"china"===t&&"台湾"===e.name&&e.geometries.push({type:"polygon",exterior:Pg[0]})}(n,t);var i=this._specialAreas&&this._specialAreas[e];i&&t.transformTo(i.left,i.top,i.width,i.height)}),this),e},t.prototype.getMapForUser=function(){return{geoJson:this._geoJSON,geoJSON:this._geoJSON,specialAreas:this._specialAreas}},t}();function Og(t){for(var e,n=0;n<t.length;n++){var i=t[n].getBoundingRect();(e=e||i.clone()).union(i)}return e}var Rg=ut(),Ng=function(t,e,n){if(e.svg){var i=new bg(t,e.svg);Rg.set(t,i)}else{var r=e.geoJson||e.geoJSON;r&&!e.features?n=e.specialAreas:r=e;i=new Lg(t,r,n);Rg.set(t,i)}},Eg=function(t){var e=Rg.get(t);return e&&"geoJSON"===e.type&&e.getMapForUser()},zg=new Et,Bg="undefined"!=typeof window,Fg=2e3,Vg=4500,Hg={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:Fg,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:Vg,ARIA:6e3,DECAL:7e3}},Gg=/^[a-zA-Z0-9_]+$/,Wg="__connectUpdateStatus";function Ug(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(!this.isDisposed())return Yg(this,t,e);vy(this.id)}}function Xg(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return Yg(this,t,e)}}function Yg(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),Et.prototype[e].apply(t,n)}var qg,jg,Zg,Kg,$g,Jg,Qg,ty,ey,ny,iy,ry,oy,ay,sy,ly,uy,hy,cy,py=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(Et),fy=py.prototype;fy.on=Xg("on"),fy.off=Xg("off");var dy=function(t){function e(e,n,i){var r=t.call(this,new rd)||this;r._chartsViews=[],r._chartsMap={},r._componentsViews=[],r._componentsMap={},r._pendingActions=[],i=i||{},"string"==typeof n&&(n=Sy[n]),r._dom=e;var o="canvas",a=!1,s=r._zr=Li(e,{renderer:i.renderer||o,devicePixelRatio:i.devicePixelRatio,width:i.width,height:i.height,useDirtyRect:null==i.useDirtyRect?a:i.useDirtyRect});r._throttledZrFlush=Tf(z(s.flush,s),17),(n=S(n))&&ep(n,!0),r._theme=n,r._locale=function(t){if(H(t)){var e=Ku[t.toUpperCase()]||{};return t===qu||t===ju?S(e):M(S(e),S(Ku.EN),!1)}return M(S(t),S(Ku.EN),!1)}(i.locale||Ju),r._coordSysMgr=new Lc;var l=r._api=ly(r);function u(t,e){return t.__prio-e.__prio}return pe(wy,u),pe(xy,u),r._scheduler=new Ef(r,l,xy,wy),r._messageCenter=new py,r._initEvents(),r.resize=z(r.resize,r),s.animation.on("frame",r._onframe,r),ny(s,r),iy(s,r),at(r),r}return n(e,t),e.prototype._onframe=function(){if(!this._disposed){cy(this);var t=this._scheduler;if(this.__pendingUpdate){var e=this.__pendingUpdate.silent;this.__flagInMainProcess=!0,qg(this),Kg.update.call(this,null,this.__pendingUpdate.updateParams),this._zr.flush(),this.__flagInMainProcess=!1,this.__pendingUpdate=null,ty.call(this,e),ey.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date;t.performSeriesTasks(i),t.performDataProcessorTasks(i),Jg(this,i),t.performVisualTasks(i),sy(this,this._model,r,"remain",{}),n-=+new Date-o}while(n>0&&t.unfinished);t.unfinished||this._zr.flush()}}},e.prototype.getDom=function(){return this._dom},e.prototype.getId=function(){return this.id},e.prototype.getZr=function(){return this._zr},e.prototype.setOption=function(t,e,n){if(this._disposed)vy(this.id);else{var i,r,o;if(U(e)&&(n=e.lazyUpdate,i=e.silent,r=e.replaceMerge,o=e.transition,e=e.notMerge),this.__flagInMainProcess=!0,!this._model||e){var a=new Rc(this._api),s=this._theme,l=this._model=new Tc;l.scheduler=this._scheduler,l.init(null,null,null,s,this._locale,a)}this._model.setOption(t,{replaceMerge:r},by);var u={seriesTransition:o,optionChanged:!0};n?(this.__pendingUpdate={silent:i,updateParams:u},this.__flagInMainProcess=!1,this.getZr().wakeUp()):(qg(this),Kg.update.call(this,null,u),this._zr.flush(),this.__pendingUpdate=null,this.__flagInMainProcess=!1,ty.call(this,i),ey.call(this,i))}},e.prototype.setTheme=function(){console.error("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},e.prototype.getModel=function(){return this._model},e.prototype.getOption=function(){return this._model&&this._model.getOption()},e.prototype.getWidth=function(){return this._zr.getWidth()},e.prototype.getHeight=function(){return this._zr.getHeight()},e.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||Bg&&window.devicePixelRatio||1},e.prototype.getRenderedCanvas=function(t){if(a.canvasSupported)return t=t||{},this._zr.painter.getRenderedCanvas({backgroundColor:t.backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},e.prototype.getSvgDataURL=function(){if(a.svgSupported){var t=this._zr;return L(t.storage.getDisplayList(),(function(t){t.stopAnimation(null,!0)})),t.painter.toDataURL()}},e.prototype.getDataURL=function(t){if(!this._disposed){var e=(t=t||{}).excludeComponents,n=this._model,i=[],r=this;L(e,(function(t){n.eachComponent({mainType:t},(function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)}))}));var o="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return L(i,(function(t){t.group.ignore=!1})),o}vy(this.id)},e.prototype.getConnectedDataURL=function(t){if(this._disposed)vy(this.id);else if(a.canvasSupported){var e="svg"===t.type,n=this.group,i=Math.min,r=Math.max,o=1/0;if(Cy[n]){var s=o,l=o,u=-1/0,h=-1/0,c=[],p=t&&t.pixelRatio||this.getDevicePixelRatio();L(Ty,(function(o,a){if(o.group===n){var p=e?o.getZr().painter.getSvgDom().innerHTML:o.getRenderedCanvas(S(t)),f=o.getDom().getBoundingClientRect();s=i(f.left,s),l=i(f.top,l),u=r(f.right,u),h=r(f.bottom,h),c.push({dom:p,left:f.left,top:f.top})}}));var f=(u*=p)-(s*=p),d=(h*=p)-(l*=p),g=D(),y=Li(g,{renderer:e?"svg":"canvas"});if(y.resize({width:f,height:d}),e){var v="";return L(c,(function(t){var e=t.left-s,n=t.top-l;v+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"})),y.painter.getSvgRoot().innerHTML=v,t.connectedBackgroundColor&&y.painter.setBackgroundColor(t.connectedBackgroundColor),y.refreshImmediately(),y.painter.toDataURL()}return t.connectedBackgroundColor&&y.add(new Va({shape:{x:0,y:0,width:f,height:d},style:{fill:t.connectedBackgroundColor}})),L(c,(function(t){var e=new Na({style:{x:t.left*p-s,y:t.top*p-l,image:t.dom}});y.add(e)})),y.refreshImmediately(),g.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},e.prototype.convertToPixel=function(t,e){return $g(this,"convertToPixel",t,e)},e.prototype.convertFromPixel=function(t,e){return $g(this,"convertFromPixel",t,e)},e.prototype.containPixel=function(t,e){var n;if(!this._disposed)return L(mr(this._model,t),(function(t,i){i.indexOf("Models")>=0&&L(t,(function(t){var r=t.coordinateSystem;if(r&&r.containPoint)n=n||!!r.containPoint(e);else if("seriesModels"===i){var o=this._chartsMap[t.__viewId];o&&o.containPoint&&(n=n||o.containPoint(e,t))}else 0}),this)}),this),!!n;vy(this.id)},e.prototype.getVisual=function(t,e){var n=mr(this._model,t,{defaultMainType:"series"}),i=n.seriesModel;var r=i.getData(),o=n.hasOwnProperty("dataIndexInside")?n.dataIndexInside:n.hasOwnProperty("dataIndex")?r.indexOfRawIndex(n.dataIndex):null;return null!=o?function(t,e,n){switch(n){case"color":return t.getItemVisual(e,"style")[t.getVisual("drawType")];case"opacity":return t.getItemVisual(e,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getItemVisual(e,n)}}(r,o,e):function(t,e){switch(e){case"color":return t.getVisual("style")[t.getVisual("drawType")];case"opacity":return t.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getVisual(e)}}(r,e)},e.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},e.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},e.prototype._initEvents=function(){var t,e,n,i=this;L(yy,(function(t){var e=function(e){var n,r=i.getModel(),o=e.target,a="globalout"===t;if(a?n={}:o&&sd(o,(function(t){var e=Ja(t);if(e&&null!=e.dataIndex){var i=e.dataModel||r.getSeriesByIndex(e.seriesIndex);return n=i&&i.getDataParams(e.dataIndex,e.dataType)||{},!0}if(e.eventData)return n=T({},e.eventData),!0}),!0),n){var s=n.componentType,l=n.componentIndex;"markLine"!==s&&"markPoint"!==s&&"markArea"!==s||(s="series",l=n.seriesIndex);var u=s&&null!=l&&r.getComponent(s,l),h=u&&i["series"===u.mainType?"_chartsMap":"_componentsMap"][u.__viewId];0,n.event=e,n.type=t,i._$eventProcessor.eventInfo={targetEl:o,packedEvent:n,model:u,view:h},i.trigger(t,n)}};e.zrEventfulCallAtLast=!0,i._zr.on(t,e,i)})),L(_y,(function(t,e){i._messageCenter.on(e,(function(t){this.trigger(e,t)}),i)})),L(["selectchanged"],(function(t){i._messageCenter.on(t,(function(e){this.trigger(t,e)}),i)})),t=this._messageCenter,e=this,n=this._api,t.on("selectchanged",(function(t){var i=n.getModel();t.isFromClick?(ad("map","selectchanged",e,i,t),ad("pie","selectchanged",e,i,t)):"select"===t.fromAction?(ad("map","selected",e,i,t),ad("pie","selected",e,i,t)):"unselect"===t.fromAction&&(ad("map","unselected",e,i,t),ad("pie","unselected",e,i,t))}))},e.prototype.isDisposed=function(){return this._disposed},e.prototype.clear=function(){this._disposed?vy(this.id):this.setOption({series:[]},!0)},e.prototype.dispose=function(){if(this._disposed)vy(this.id);else{this._disposed=!0,wr(this.getDom(),ky,"");var t=this,e=t._api,n=t._model;L(t._componentsViews,(function(t){t.dispose(n,e)})),L(t._chartsViews,(function(t){t.dispose(n,e)})),t._zr.dispose(),t._dom=t._model=t._chartsMap=t._componentsMap=t._chartsViews=t._componentsViews=t._scheduler=t._api=t._zr=t._throttledZrFlush=t._theme=t._coordSysMgr=t._messageCenter=null,delete Ty[t.id]}},e.prototype.resize=function(t){if(this._disposed)vy(this.id);else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var n=e.resetOption("media"),i=t&&t.silent;this.__pendingUpdate&&(null==i&&(i=this.__pendingUpdate.silent),n=!0,this.__pendingUpdate=null),this.__flagInMainProcess=!0,n&&qg(this),Kg.update.call(this,{type:"resize",animation:T({duration:0},t&&t.animation)}),this.__flagInMainProcess=!1,ty.call(this,i),ey.call(this,i)}}},e.prototype.showLoading=function(t,e){if(this._disposed)vy(this.id);else if(U(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),My[t]){var n=My[t](this._api,e),i=this._zr;this._loadingFX=n,i.add(n)}},e.prototype.hideLoading=function(){this._disposed?vy(this.id):(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},e.prototype.makeActionFromEvent=function(t){var e=T({},t);return e.type=_y[t.type],e},e.prototype.dispatchAction=function(t,e){if(this._disposed)vy(this.id);else if(U(e)||(e={silent:!!e}),my[t.type]&&this._model)if(this.__flagInMainProcess)this._pendingActions.push(t);else{var n=e.silent;Qg.call(this,t,n);var i=e.flush;i?this._zr.flush():!1!==i&&a.browser.weChat&&this._throttledZrFlush(),ty.call(this,n),ey.call(this,n)}},e.prototype.updateLabelLayout=function(){zg.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},e.prototype.appendData=function(t){if(this._disposed)vy(this.id);else{var e=t.seriesIndex,n=this.getModel().getSeriesByIndex(e);0,n.appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp()}},e.internalField=function(){function t(t){for(var e=[],n=t.currentStates,i=0;i<n.length;i++){var r=n[i];"emphasis"!==r&&"blur"!==r&&"select"!==r&&e.push(r)}t.selected&&t.states.select&&e.push("select"),2===t.hoverState&&t.states.emphasis?e.push("emphasis"):1===t.hoverState&&t.states.blur&&e.push("blur"),t.useStates(e)}function e(t,e){t.preventAutoZ||i(e.group,t.get("z")||0,t.get("zlevel")||0,-1/0)}function i(t,e,n,r){var o=t.getTextContent(),a=t.getTextGuideLine();if(t.isGroup)for(var s=t.childrenRef(),l=0;l<s.length;l++)r=Math.max(i(s[l],e,n,r),r);else t.z=e,t.zlevel=n,r=Math.max(t.z2,r);if(o&&(o.z=e,o.zlevel=n,isFinite(r)&&(o.z2=r+2)),a){var u=t.textGuideLineConfig;a.z=e,a.zlevel=n,isFinite(r)&&(a.z2=r+(u&&u.showAbove?1:-1))}return r}function r(t,e){e.group.traverse((function(t){if(!au(t)){var e=t.getTextContent(),n=t.getTextGuideLine();t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null)}}))}function o(e,n){var i=e.getModel("stateAnimation"),r=e.isAnimationEnabled(),o=i.get("duration"),a=o>0?{duration:o,delay:i.get("delay"),easing:i.get("easing")}:null;n.group.traverse((function(e){if(e.states&&e.states.emphasis){if(au(e))return;if(e instanceof Aa&&function(t){var e=es(t);e.normalFill=t.style.fill,e.normalStroke=t.style.stroke;var n=t.states.select||{};e.selectFill=n.style&&n.style.fill||null,e.selectStroke=n.style&&n.style.stroke||null}(e),e.__dirty){var n=e.prevStates;n&&e.useStates(n)}if(r){e.stateTransition=a;var i=e.getTextContent(),o=e.getTextGuideLine();i&&(i.stateTransition=a),o&&(o.stateTransition=a)}e.__dirty&&t(e)}}))}qg=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),jg(t,!0),jg(t,!1),e.plan()},jg=function(t,e){for(var n=t._model,i=t._scheduler,r=e?t._componentsViews:t._chartsViews,o=e?t._componentsMap:t._chartsMap,a=t._zr,s=t._api,l=0;l<r.length;l++)r[l].__alive=!1;function u(t){var l=t.__requireNewView;t.__requireNewView=!1;var u="_ec_"+t.id+"_"+t.type,h=!l&&o[u];if(!h){var c=Tr(t.type),p=e?gf.getClass(c.main,c.sub):_f.getClass(c.sub);0,(h=new p).init(n,s),o[u]=h,r.push(h),a.add(h.group)}t.__viewId=h.__id=u,h.__alive=!0,h.__model=t,h.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!e&&i.prepareView(h,t,n,s)}e?n.eachComponent((function(t,e){"series"!==t&&u(e)})):n.eachSeries(u);for(l=0;l<r.length;){var h=r[l];h.__alive?l++:(!e&&h.renderTask.dispose(),a.remove(h.group),h.dispose(n,s),r.splice(l,1),o[h.__id]===h&&delete o[h.__id],h.__id=h.group.__ecComponentInfo=null)}},Zg=function(t,e,n,i,r){var o=t._model;if(o.setUpdatePayload(n),i){var a={};a[i+"Id"]=n[i+"Id"],a[i+"Index"]=n[i+"Index"],a[i+"Name"]=n[i+"Name"];var s={mainType:i,query:a};r&&(s.subType=r);var l,u=n.excludeSeriesId;null!=u&&(l=ut(),L(rr(u),(function(t){var e=pr(t,null);null!=e&&l.set(e,!0)}))),Us(n)&&Ls(t._api),o&&o.eachComponent(s,(function(e){if(!(l&&null!==l.get(e.id)))if(Us(n))if(e instanceof sf)n.type!==rs||n.notBlur||function(t,e,n){var i=t.seriesIndex,r=t.getData(e.dataType),o=gr(r,e);o=(F(o)?o[0]:o)||0;var a=r.getItemGraphicEl(o);if(!a)for(var s=r.count(),l=0;!a&&l<s;)a=r.getItemGraphicEl(l++);if(a){var u=Ja(a);Os(i,u.focus,u.blurScope,n)}else{var h=t.get(["emphasis","focus"]),c=t.get(["emphasis","blurScope"]);null!=h&&Os(i,h,c,n)}}(e,n,t._api);else{var i=Ns(e.mainType,e.componentIndex,n.name,t._api),r=i.focusSelf,o=i.dispatchers;n.type===rs&&r&&!n.notBlur&&Rs(e.mainType,e.componentIndex,t._api),o&&L(o,(function(t){n.type===rs?Cs(t):Ds(t)}))}else Ws(n)&&e instanceof sf&&(!function(t,e,n){if(Ws(e)){var i=e.dataType,r=gr(t.getData(i),e);F(r)||(r=[r]),t[e.type===ls?"toggleSelect":e.type===as?"select":"unselect"](r,i)}}(e,n,t._api),Es(e),hy(t))}),t),o&&o.eachComponent(s,(function(e){l&&null!==l.get(e.id)||h(t["series"===i?"_chartsMap":"_componentsMap"][e.__viewId])}),t)}else L([].concat(t._componentsViews).concat(t._chartsViews),h);function h(i){i&&i.__alive&&i[e]&&i[e](i.__model,o,t._api,n)}},Kg={prepareAndUpdate:function(t){qg(this),Kg.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(t,e){var n=this._model,i=this._api,r=this._zr,o=this._coordSysMgr,s=this._scheduler;if(n){n.setUpdatePayload(t),s.restoreData(n,t),s.performSeriesTasks(n),o.create(n,i),s.performDataProcessorTasks(n,t),Jg(this,n),o.update(n,i),ry(n),s.performVisualTasks(n,t),oy(this,n,i,t,e);var l=n.get("backgroundColor")||"transparent",u=n.get("darkMode");if(a.canvasSupported)r.setBackgroundColor(l),null!=u&&"auto"!==u&&r.setDarkMode(u);else{var h=Ne(l);l=Ge(h,"rgb"),0===h[3]&&(l="transparent")}zg.trigger("afterupdate",n,i)}},updateTransform:function(t){var e=this,n=this._model,i=this._api;if(n){n.setUpdatePayload(t);var r=[];n.eachComponent((function(o,a){if("series"!==o){var s=e.getViewOfComponentModel(a);if(s&&s.__alive)if(s.updateTransform){var l=s.updateTransform(a,n,i,t);l&&l.update&&r.push(s)}else r.push(s)}}));var o=ut();n.eachSeries((function(r){var a=e._chartsMap[r.__viewId];if(a.updateTransform){var s=a.updateTransform(r,n,i,t);s&&s.update&&o.set(r.uid,1)}else o.set(r.uid,1)})),ry(n),this._scheduler.performVisualTasks(n,t,{setDirty:!0,dirtyMap:o}),sy(this,n,i,t,{},o),zg.trigger("afterupdate",n,i)}},updateView:function(t){var e=this._model;e&&(e.setUpdatePayload(t),_f.markUpdateMethod(t,"updateView"),ry(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),oy(this,e,this._api,t,{}),zg.trigger("afterupdate",e,this._api))},updateVisual:function(t){var e=this,n=this._model;n&&(n.setUpdatePayload(t),n.eachSeries((function(t){t.getData().clearAllVisual()})),_f.markUpdateMethod(t,"updateVisual"),ry(n),this._scheduler.performVisualTasks(n,t,{visualType:"visual",setDirty:!0}),n.eachComponent((function(i,r){if("series"!==i){var o=e.getViewOfComponentModel(r);o&&o.__alive&&o.updateVisual(r,n,e._api,t)}})),n.eachSeries((function(i){e._chartsMap[i.__viewId].updateVisual(i,n,e._api,t)})),zg.trigger("afterupdate",n,this._api))},updateLayout:function(t){Kg.update.call(this,t)}},$g=function(t,e,n,i){if(t._disposed)vy(t.id);else{for(var r,o=t._model,a=t._coordSysMgr.getCoordinateSystems(),s=mr(o,n),l=0;l<a.length;l++){var u=a[l];if(u[e]&&null!=(r=u[e](o,s,i)))return r}0}},Jg=function(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries((function(t){i.updateStreamModes(t,n[t.__viewId])}))},Qg=function(t,e){var n=this,i=this.getModel(),r=t.type,o=t.escapeConnect,a=my[r],s=a.actionInfo,l=(s.update||"update").split(":"),u=l.pop(),h=null!=l[0]&&Tr(l[0]);this.__flagInMainProcess=!0;var c=[t],p=!1;t.batch&&(p=!0,c=O(t.batch,(function(e){return(e=C(T({},e),t)).batch=null,e})));var f,d=[],g=Ws(t),y=Us(t);if(L(c,(function(e){if((f=(f=a.action(e,n._model,n._api))||T({},e)).type=s.event||f.type,d.push(f),y){var i=_r(t),r=i.queryOptionMap,o=i.mainTypeSpecified?r.keys()[0]:"series";Zg(n,u,e,o),hy(n)}else g?(Zg(n,u,e,"series"),hy(n)):h&&Zg(n,u,e,h.main,h.sub)})),"none"===u||y||g||h||(this.__pendingUpdate?(qg(this),Kg.update.call(this,t),this.__pendingUpdate=null):Kg[u].call(this,t)),f=p?{type:s.event||r,escapeConnect:o,batch:d}:d[0],this.__flagInMainProcess=!1,!e){var v=this._messageCenter;if(v.trigger(f.type,f),g){var m={type:"selectchanged",escapeConnect:o,selected:zs(i),isFromClick:t.isFromClick||!1,fromAction:t.type,fromActionPayload:t};v.trigger(m.type,m)}}},ty=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();Qg.call(this,n,t)}},ey=function(t){!t&&this.trigger("updated")},ny=function(t,e){t.on("rendered",(function(n){e.trigger("rendered",n),!t.animation.isFinished()||e.__pendingUpdate||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")}))},iy=function(t,e){t.on("mouseover",(function(t){var n=sd(t.target,Gs);n&&(!function(t,e,n){var i=Ja(t),r=Ns(i.componentMainType,i.componentIndex,i.componentHighDownName,n),o=r.dispatchers,a=r.focusSelf;o?(a&&Rs(i.componentMainType,i.componentIndex,n),L(o,(function(t){return Ms(t,e)}))):(Os(i.seriesIndex,i.focus,i.blurScope,n),"self"===i.focus&&Rs(i.componentMainType,i.componentIndex,n),Ms(t,e))}(n,t,e._api),hy(e))})).on("mouseout",(function(t){var n=sd(t.target,Gs);n&&(!function(t,e,n){Ls(n);var i=Ja(t),r=Ns(i.componentMainType,i.componentIndex,i.componentHighDownName,n).dispatchers;r?L(r,(function(t){return Ts(t,e)})):Ts(t,e)}(n,t,e._api),hy(e))})).on("click",(function(t){var n=sd(t.target,(function(t){return null!=Ja(t).dataIndex}),!0);if(n){var i=n.selected?"unselect":"select",r=Ja(n);e._api.dispatchAction({type:i,dataType:r.dataType,dataIndexInside:r.dataIndex,seriesIndex:r.seriesIndex,isFromClick:!0})}}))},ry=function(t){t.clearColorPalette(),t.eachSeries((function(t){t.clearColorPalette()}))},oy=function(t,e,n,i,r){ay(t,e,n,i,r),L(t._chartsViews,(function(t){t.__alive=!1})),sy(t,e,n,i,r),L(t._chartsViews,(function(t){t.__alive||t.remove(e,n)}))},ay=function(t,n,i,a,s,l){L(l||t._componentsViews,(function(t){var s=t.__model;r(s,t),t.render(s,n,i,a),e(s,t),o(s,t)}))},sy=function(t,n,i,s,l,u){var h=t._scheduler;l=T(l||{},{updatedSeries:n.getSeries()}),zg.trigger("series:beforeupdate",n,i,l);var c=!1;n.eachSeries((function(e){var n=t._chartsMap[e.__viewId];n.__alive=!0;var i=n.renderTask;h.updatePayload(i,s),r(e,n),u&&u.get(e.uid)&&i.dirty(),i.perform(h.getPerformArgs(i))&&(c=!0),n.group.silent=!!e.get("silent"),function(t,e){var n=t.get("blendMode")||null;0;e.group.traverse((function(t){t.isGroup||(t.style.blend=n),t.eachPendingDisplayable&&t.eachPendingDisplayable((function(t){t.style.blend=n}))}))}(e,n),Es(e)})),h.unfinished=c||h.unfinished,zg.trigger("series:layoutlabels",n,i,l),zg.trigger("series:transition",n,i,l),n.eachSeries((function(n){var i=t._chartsMap[n.__viewId];e(n,i),o(n,i)})),function(t,e){var n=t._zr.storage,i=0;n.traverse((function(t){t.isGroup||i++})),i>e.get("hoverLayerThreshold")&&!a.node&&!a.worker&&e.eachSeries((function(e){if(!e.preventUsingHoverLayer){var n=t._chartsMap[e.__viewId];n.__alive&&n.group.traverse((function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)}))}}))}(t,n),zg.trigger("series:afterupdate",n,i,l)},hy=function(t){t.__needsUpdateStatus=!0,t.getZr().wakeUp()},cy=function(e){e.__needsUpdateStatus&&(e.getZr().storage.traverse((function(e){au(e)||t(e)})),e.__needsUpdateStatus=!1)},ly=function(t){return new(function(e){function i(){return null!==e&&e.apply(this,arguments)||this}return n(i,e),i.prototype.getCoordinateSystems=function(){return t._coordSysMgr.getCoordinateSystems()},i.prototype.getComponentByElement=function(e){for(;e;){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}},i.prototype.enterEmphasis=function(e,n){Cs(e,n),hy(t)},i.prototype.leaveEmphasis=function(e,n){Ds(e,n),hy(t)},i.prototype.enterBlur=function(e){!function(t){xs(t,gs)}(e),hy(t)},i.prototype.leaveBlur=function(e){Is(e),hy(t)},i.prototype.enterSelect=function(e){ks(e),hy(t)},i.prototype.leaveSelect=function(e){As(e),hy(t)},i.prototype.getModel=function(){return t.getModel()},i.prototype.getViewOfComponentModel=function(e){return t.getViewOfComponentModel(e)},i.prototype.getViewOfSeriesModel=function(e){return t.getViewOfSeriesModel(e)},i}(Ac))(t)},uy=function(t){function e(t,e){for(var n=0;n<t.length;n++){t[n][Wg]=e}}L(_y,(function(n,i){t._messageCenter.on(i,(function(n){if(Cy[t.group]&&0!==t[Wg]){if(n&&n.escapeConnect)return;var i=t.makeActionFromEvent(n),r=[];L(Ty,(function(e){e!==t&&e.group===t.group&&r.push(e)})),e(r,0),L(r,(function(t){1!==t[Wg]&&t.dispatchAction(i)})),e(r,2)}}))}))}}(),e}(Et),gy=dy.prototype;gy.on=Ug("on"),gy.off=Ug("off"),gy.one=function(t,e,n){var i=this;er(),this.on.call(this,t,(function n(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];e&&e.apply&&e.apply(this,r),i.off(t,n)}),n)};var yy=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];function vy(t){0}var my={},_y={},xy=[],by=[],wy=[],Sy={},My={},Ty={},Cy={},Dy=+new Date-0,Iy=+new Date-0,ky="_echarts_instance_";function Ay(t){Cy[t]=!1}var Py=Ay;function Ly(t){return Ty[function(t,e){return t.getAttribute?t.getAttribute(e):t[e]}(t,ky)]}function Oy(t,e){Sy[t]=e}function Ry(t){I(by,t)<0&&by.push(t)}function Ny(t,e){Uy(xy,t,e,2e3)}function Ey(t){By("afterinit",t)}function zy(t){By("afterupdate",t)}function By(t,e){zg.on(t,e)}function Fy(t,e,n){"function"==typeof e&&(n=e,e="");var i=U(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,_y[e]||(it(Gg.test(i)&&Gg.test(e)),my[i]||(my[i]={action:n,actionInfo:t}),_y[e]=i)}function Vy(t,e){Lc.register(t,e)}function Hy(t,e){Uy(wy,t,e,1e3,"layout")}function Gy(t,e){Uy(wy,t,e,3e3,"visual")}var Wy=[];function Uy(t,e,n,i,r){if((V(e)||U(e))&&(n=e,e=i),!(I(Wy,n)>=0)){Wy.push(n);var o=Ef.wrapStageHandler(n,r);o.__prio=e,o.__raw=n,t.push(o)}}function Xy(t,e){My[t]=e}function Yy(t,e,n){Ng(t,e,n)}var qy=function(t){var e=(t=S(t)).type,n="";e||nr(n);var i=e.split(":");2!==i.length&&nr(n);var r=!1;"echarts"===i[0]&&(e=i[1],r=!0),t.__isBuiltIn=r,Fp.set(e,t)};Gy(Fg,Pf),Gy(Vg,Of),Gy(Vg,Rf),Gy(Fg,od),Gy(Vg,{createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t))return{dataEach:t.getData().hasItemOption?function(t,e){var n=t.getItemModel(e),i=n.getShallow("symbol",!0),r=n.getShallow("symbolSize",!0),o=n.getShallow("symbolRotate",!0),a=n.getShallow("symbolOffset",!0),s=n.getShallow("symbolKeepAspect",!0);null!=i&&t.setItemVisual(e,"symbol",i),null!=r&&t.setItemVisual(e,"symbolSize",r),null!=o&&t.setItemVisual(e,"symbolRotate",o),null!=a&&t.setItemVisual(e,"symbolOffset",a),null!=s&&t.setItemVisual(e,"symbolKeepAspect",s)}:null}}}),Gy(7e3,(function(t,e){t.eachRawSeries((function(n){if(!t.isSeriesFiltered(n)){var i=n.getData();i.hasItemVisual()&&i.each((function(t){var n=i.getItemVisual(t,"decal");n&&(i.ensureUniqueItemVisual(t,"style").decal=Wd(n,e))}));var r=i.getVisual("decal");if(r)i.getVisual("style").decal=Wd(r,e)}}))})),Ry(ep),Ny(900,(function(t){var e=ut();t.eachSeries((function(t){var n=t.get("stack");if(n){var i=e.get(n)||e.set(n,[]),r=t.getData(),o={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};if(!o.stackedDimension||!o.isStackedByIndex&&!o.stackedByDimension)return;i.length&&r.setCalculationInfo("stackedOnSeries",i[i.length-1].seriesModel),i.push(o)}})),e.each(np)})),Xy("default",(function(t,e){C(e=e||{},{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var n=new Di,i=new Va({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4});n.add(i);var r,o=new Wa({style:{text:e.text,fill:e.textColor,fontSize:e.fontSize,fontWeight:e.fontWeight,fontStyle:e.fontStyle,fontFamily:e.fontFamily},zlevel:e.zlevel,z:10001}),a=new Va({style:{fill:"none"},textContent:o,textConfig:{position:"right",distance:10},zlevel:e.zlevel,z:10001});return n.add(a),e.showSpinner&&((r=new Ul({shape:{startAngle:-Nf/2,endAngle:-Nf/2+.1,r:e.spinnerRadius},style:{stroke:e.color,lineCap:"round",lineWidth:e.lineWidth},zlevel:e.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*Nf/2}).start("circularInOut"),r.animateShape(!0).when(1e3,{startAngle:3*Nf/2}).delay(300).start("circularInOut"),n.add(r)),n.resize=function(){var n=o.getBoundingRect().width,s=e.showSpinner?e.spinnerRadius:0,l=(t.getWidth()-2*s-(e.showSpinner&&n?10:0)-n)/2-(e.showSpinner&&n?0:5+n/2)+(e.showSpinner?0:n/2)+(n?0:s),u=t.getHeight()/2;e.showSpinner&&r.setShape({cx:l,cy:u}),a.setShape({x:l-s,y:u-s,width:2*s,height:2*s}),i.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},n.resize(),n})),Fy({type:rs,event:rs,update:rs},ft),Fy({type:os,event:os,update:os},ft),Fy({type:as,event:as,update:as},ft),Fy({type:ss,event:ss,update:ss},ft),Fy({type:ls,event:ls,update:ls},ft),Oy("light",Jf),Oy("dark",id);var jy=[],Zy={registerPreprocessor:Ry,registerProcessor:Ny,registerPostInit:Ey,registerPostUpdate:zy,registerUpdateLifecycle:By,registerAction:Fy,registerCoordinateSystem:Vy,registerLayout:Hy,registerVisual:Gy,registerTransform:qy,registerLoading:Xy,registerMap:Yy,PRIORITY:Hg,ComponentModel:jh,ComponentView:gf,SeriesModel:sf,ChartView:_f,registerComponentModel:function(t){jh.registerClass(t)},registerComponentView:function(t){gf.registerClass(t)},registerSeriesModel:function(t){sf.registerClass(t)},registerChartView:function(t){_f.registerClass(t)},registerSubTypeDefaulter:function(t,e){jh.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){Oi(t,e)}};function Ky(t){F(t)?L(t,(function(t){Ky(t)})):I(jy,t)>=0||(jy.push(t),V(t)&&(t={install:t}),t.install(Zy))}function $y(t){return null==t?0:t.length||1}function Jy(t){return t}var Qy=function(){function t(t,e,n,i,r,o){this._old=t,this._new=e,this._oldKeyGetter=n||Jy,this._newKeyGetter=i||Jy,this.context=r,this._diffModeMultiple="multiple"===o}return t.prototype.add=function(t){return this._add=t,this},t.prototype.update=function(t){return this._update=t,this},t.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},t.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},t.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},t.prototype.remove=function(t){return this._remove=t,this},t.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},t.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=new Array(t.length),r=new Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,r,"_newKeyGetter");for(var o=0;o<t.length;o++){var a=i[o],s=n[a],l=$y(s);if(l>1){var u=s.shift();1===s.length&&(n[a]=s[0]),this._update&&this._update(u,o)}else 1===l?(n[a]=null,this._update&&this._update(s,o)):this._remove&&this._remove(o)}this._performRestAdd(r,n)},t.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},r=[],o=[];this._initIndexMap(t,n,r,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var a=0;a<r.length;a++){var s=r[a],l=n[s],u=i[s],h=$y(l),c=$y(u);if(h>1&&1===c)this._updateManyToOne&&this._updateManyToOne(u,l),i[s]=null;else if(1===h&&c>1)this._updateOneToMany&&this._updateOneToMany(u,l),i[s]=null;else if(1===h&&1===c)this._update&&this._update(u,l),i[s]=null;else if(h>1&&c>1)this._updateManyToMany&&this._updateManyToMany(u,l),i[s]=null;else if(h>1)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(o,i)},t.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],r=e[i],o=$y(r);if(o>1)for(var a=0;a<o;a++)this._add&&this._add(r[a]);else 1===o&&this._add&&this._add(r);e[i]=null}},t.prototype._initIndexMap=function(t,e,n,i){for(var r=this._diffModeMultiple,o=0;o<t.length;o++){var a="_ec_"+this[i](t[o],o);if(r||(n[o]=a),e){var s=e[a],l=$y(s);0===l?(e[a]=o,r&&n.push(a)):1===l?e[a]=[s,o]:s.push(o)}}},t}(),tv=function(){function t(t,e){this._encode=t,this._schema=e}return t.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},t.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},t}();function ev(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}var nv=function(t){this.otherDims={},null!=t&&T(this,t)},iv=yr(),rv={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},ov=function(){function t(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return t.prototype.isDimensionOmitted=function(){return this._dimOmitted},t.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=lv(this.source)))},t.prototype.getSourceDimensionIndex=function(t){return Q(this._dimNameMap.get(t),-1)},t.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},t.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=gp(this.source),n=!uv(t),i="",r=[],o=0,a=0;o<t;o++){var s=void 0,l=void 0,u=void 0,h=this.dimensions[a];if(h&&h.storeDimIndex===o)s=e?h.name:null,l=h.type,u=h.ordinalMeta,a++;else{var c=this.getSourceDimension(o);c&&(s=e?c.name:null,l=c.type)}r.push({property:s,type:l,ordinalMeta:u}),!e||null==s||h&&h.isCalculationCoord||(i+=n?s.replace(/\`/g,"`1").replace(/\$/g,"`2"):s),i+="$",i+=rv[l]||"f",u&&(i+=u.uid),i+="$"}var p=this.source;return{dimensions:r,hash:[p.seriesLayoutBy,p.startIndex,i].join("$$")}},t.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,r=this.dimensions[n];if(r&&r.storeDimIndex===e)r.isCalculationCoord||(i=r.name),n++;else{var o=this.getSourceDimension(e);o&&(i=o.name)}t.push(i)}return t},t.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},t}();function av(t){return t instanceof ov}function sv(t){for(var e=ut(),n=0;n<(t||[]).length;n++){var i=t[n],r=U(i)?i.name:i;null!=r&&null==e.get(r)&&e.set(r,n)}return e}function lv(t){var e=iv(t);return e.dimNameMap||(e.dimNameMap=sv(t.dimensionsDefine))}function uv(t){return t>30}var hv,cv,pv,fv,dv,gv,yv,vv=U,mv=O,_v="undefined"==typeof Int32Array?Array:Int32Array,xv=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],bv=["_approximateExtent"],wv=function(){function t(t,e){var n;this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","lttbDownSample"];var i=!1;av(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(i=!0,n=t),n=n||["x","y"];for(var r={},o=[],a={},s=!1,l={},u=0;u<n.length;u++){var h=n[u],c=H(h)?new nv({name:h}):h instanceof nv?h:new nv(h),p=c.name;c.type=c.type||"float",c.coordDim||(c.coordDim=p,c.coordDimIndex=0);var f=c.otherDims=c.otherDims||{};o.push(p),r[p]=c,null!=l[p]&&(s=!0),c.createInvertedIndices&&(a[p]=[]),0===f.itemName&&(this._nameDimIdx=u),0===f.itemId&&(this._idDimIdx=u),i&&(c.storeDimIndex=u)}if(this.dimensions=o,this._dimInfos=r,this._initGetDimensionInfo(s),this.hostModel=e,this._invertedIndicesMap=a,this._dimOmitted){var d=this._dimIdxToName=ut();L(o,(function(t){d.set(r[t].storeDimIndex,t)}))}}return t.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(null==e)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var n=this._dimIdxToName.get(e);if(null!=n)return n;var i=this._schema.getSourceDimension(e);return i?i.name:void 0},t.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(null!=e)return e;if(null==t)return-1;var n=this._getDimInfo(t);return n?n.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},t.prototype._recognizeDimIndex=function(t){if("number"==typeof t||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},t.prototype._getStoreDimIndex=function(t){var e=this.getDimensionIndex(t);return e},t.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},t.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},t.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},t.prototype.mapDimension=function(t,e){var n=this._dimSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var i=n.encode[t];return i?i[e]:null},t.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},t.prototype.getStore=function(){return this._store},t.prototype.initData=function(t,e,n){var i,r=this;if(t instanceof $p&&(i=t),!i){var o=this.dimensions,a=up(t)||P(t)?new yp(t,o.length):t;i=new $p;var s=mv(o,(function(t){return{type:r._dimInfos[t].type,property:t}}));i.initData(a,s,n)}this._store=i,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=function(t,e){var n={},i=n.encode={},r=ut(),o=[],a=[],s={};L(t.dimensions,(function(e){var n,l=t.getDimensionInfo(e),u=l.coordDim;if(u){var h=l.coordDimIndex;ev(i,u)[h]=e,l.isExtraCoord||(r.set(u,1),"ordinal"!==(n=l.type)&&"time"!==n&&(o[0]=e),ev(s,u)[h]=t.getDimensionIndex(l.name)),l.defaultTooltip&&a.push(e)}Jh.each((function(t,e){var n=ev(i,e),r=l.otherDims[e];null!=r&&!1!==r&&(n[r]=l.name)}))}));var l=[],u={};r.each((function(t,e){var n=i[e];u[e]=n[0],l=l.concat(n)})),n.dataDimsOnCoord=l,n.dataDimIndicesOnCoord=O(l,(function(e){return t.getDimensionInfo(e).storeDimIndex})),n.encodeFirstDimNotExtra=u;var h=i.label;h&&h.length&&(o=h.slice());var c=i.tooltip;return c&&c.length?a=c.slice():a.length||(a=o.slice()),i.defaultedLabel=o,i.defaultedTooltip=a,n.userOutput=new tv(s,e),n}(this,this._schema),this.userOutput=this._dimSummary.userOutput},t.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},t.prototype.appendValues=function(t,e){var n=this._store.appendValues(t,e.length),i=n.start,r=n.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var a=i;a<r;a++){var s=a-i;this._nameList[a]=e[s],o&&yv(this,a)}},t.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},t.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==ic&&!t.fillStorage},t.prototype._doInit=function(t,e){if(!(t>=e)){var n=this._store.getProvider();this._updateOrdinalMeta();var i=this._nameList,r=this._idList;if(n.getSource().sourceFormat===Qh&&!n.pure)for(var o=[],a=t;a<e;a++){var s=n.getItem(a,o);if(!this.hasItemOption&&lr(s)&&(this.hasItemOption=!0),s){var l=s.name;null==i[a]&&null!=l&&(i[a]=pr(l,null));var u=s.id;null==r[a]&&null!=u&&(r[a]=pr(u,null))}}if(this._shouldMakeIdFromName())for(a=t;a<e;a++)yv(this,a);hv(this)}},t.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},t.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},t.prototype.setCalculationInfo=function(t,e){vv(t)?T(this._calculationInfo,t):this._calculationInfo[t]=e},t.prototype.getName=function(t){var e=this.getRawIndex(t),n=this._nameList[e];return null==n&&null!=this._nameDimIdx&&(n=pv(this,this._nameDimIdx,e)),null==n&&(n=""),n},t.prototype._getCategory=function(t,e){var n=this._store.get(t,e),i=this._store.getOrdinalMeta(t);return i?i.categories[n]:n},t.prototype.getId=function(t){return cv(this,this.getRawIndex(t))},t.prototype.count=function(){return this._store.count()},t.prototype.get=function(t,e){var n=this._store,i=this._dimInfos[t];if(i)return n.get(i.storeDimIndex,e)},t.prototype.getByRawIndex=function(t,e){var n=this._store,i=this._dimInfos[t];if(i)return n.getByRawIndex(i.storeDimIndex,e)},t.prototype.getIndices=function(){return this._store.getIndices()},t.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},t.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},t.prototype.getValues=function(t,e){var n=this,i=this._store;return F(t)?i.getValues(mv(t,(function(t){return n._getStoreDimIndex(t)})),e):i.getValues(t)},t.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},t.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},t.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},t.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},t.prototype.rawIndexOf=function(t,e){var n=t&&this._invertedIndicesMap[t];var i=n[e];return null==i||isNaN(i)?-1:i},t.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},t.prototype.each=function(t,e,n){"function"==typeof t&&(n=e,e=t,t=[]);var i=n||this,r=mv(fv(t),this._getStoreDimIndex,this);this._store.each(r,i?z(e,i):e)},t.prototype.filterSelf=function(t,e,n){"function"==typeof t&&(n=e,e=t,t=[]);var i=n||this,r=mv(fv(t),this._getStoreDimIndex,this);return this._store=this._store.filter(r,i?z(e,i):e),this},t.prototype.selectRange=function(t){var e=this,n={};return L(E(t),(function(i){var r=e._getStoreDimIndex(i);n[r]=t[i]})),this._store=this._store.selectRange(n),this},t.prototype.mapArray=function(t,e,n){"function"==typeof t&&(n=e,e=t,t=[]),n=n||this;var i=[];return this.each(t,(function(){i.push(e&&e.apply(this,arguments))}),n),i},t.prototype.map=function(t,e,n,i){var r=n||i||this,o=mv(fv(t),this._getStoreDimIndex,this),a=gv(this);return a._store=this._store.map(o,r?z(e,r):e),a},t.prototype.modify=function(t,e,n,i){var r=n||i||this;var o=mv(fv(t),this._getStoreDimIndex,this);this._store.modify(o,r?z(e,r):e)},t.prototype.downSample=function(t,e,n,i){var r=gv(this);return r._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),r},t.prototype.lttbDownSample=function(t,e){var n=gv(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},t.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},t.prototype.getItemModel=function(t){var e=this.hostModel,n=this.getRawDataItem(t);return new Uu(n,e,e&&e.ecModel)},t.prototype.diff=function(t){var e=this;return new Qy(t?t.getStore().getIndices():[],this.getStore().getIndices(),(function(e){return cv(t,e)}),(function(t){return cv(e,t)}))},t.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},t.prototype.setVisual=function(t,e){this._visual=this._visual||{},vv(t)?T(this._visual,t):this._visual[t]=e},t.prototype.getItemVisual=function(t,e){var n=this._itemVisuals[t],i=n&&n[e];return null==i?this.getVisual(e):i},t.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},t.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t];i||(i=n[t]={});var r=i[e];return null==r&&(F(r=this.getVisual(e))?r=r.slice():vv(r)&&(r=T({},r)),i[e]=r),r},t.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,vv(e)?T(i,e):i[e]=n},t.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},t.prototype.setLayout=function(t,e){if(vv(t))for(var n in t)t.hasOwnProperty(n)&&this.setLayout(n,t[n]);else this._layout[t]=e},t.prototype.getLayout=function(t){return this._layout[t]},t.prototype.getItemLayout=function(t){return this._itemLayouts[t]},t.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?T(this._itemLayouts[t]||{},e):e},t.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},t.prototype.setItemGraphicEl=function(t,e){!function(t,e,n,i){if(i){var r=Ja(i);r.dataIndex=n,r.dataType=e,r.seriesIndex=t,"group"===i.type&&i.traverse((function(i){var r=Ja(i);r.seriesIndex=t,r.dataIndex=n,r.dataType=e}))}}(this.hostModel&&this.hostModel.seriesIndex,this.dataType,t,e),this._graphicEls[t]=e},t.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},t.prototype.eachItemGraphicEl=function(t,e){L(this._graphicEls,(function(n,i){n&&t&&t.call(e,n,i)}))},t.prototype.cloneShallow=function(e){return e||(e=new t(this._schema?this._schema:mv(this.dimensions,this._getDimInfo,this),this.hostModel)),dv(e,this),e._store=this._store,e},t.prototype.wrapMethod=function(t,e){var n=this[t];"function"==typeof n&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(et(arguments)))})},t.internalField=(hv=function(t){var e=t._invertedIndicesMap;L(e,(function(n,i){var r=t._dimInfos[i],o=r.ordinalMeta,a=t._store;if(o){n=e[i]=new _v(o.categories.length);for(var s=0;s<n.length;s++)n[s]=-1;for(s=0;s<a.count();s++)n[a.get(r.storeDimIndex,s)]=s}}))},pv=function(t,e,n){return pr(t._getCategory(e,n),null)},cv=function(t,e){var n=t._idList[e];return null==n&&null!=t._idDimIdx&&(n=pv(t,t._idDimIdx,e)),null==n&&(n="e\0\0"+e),n},fv=function(t){return F(t)||(t=null!=t?[t]:[]),t},gv=function(e){var n=new t(e._schema?e._schema:mv(e.dimensions,e._getDimInfo,e),e.hostModel);return dv(n,e),n},dv=function(t,e){L(xv.concat(e.__wrappedMethods||[]),(function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t.__wrappedMethods=e.__wrappedMethods,L(bv,(function(n){t[n]=S(e[n])})),t._calculationInfo=T({},e._calculationInfo)},void(yv=function(t,e){var n=t._nameList,i=t._idList,r=t._nameDimIdx,o=t._idDimIdx,a=n[e],s=i[e];if(null==a&&null!=r&&(n[e]=a=pv(t,r,e)),null==s&&null!=o&&(i[e]=s=pv(t,o,e)),null==s&&null!=a){var l=t._nameRepeatCount,u=l[a]=(l[a]||0)+1;s=a,u>1&&(s+="__ec__"+u),i[e]=s}})),t}();function Sv(t,e){up(t)||(t=cp(t));var n=(e=e||{}).coordDimensions||[],i=e.dimensionsDefine||t.dimensionsDefine||[],r=ut(),o=[],a=function(t,e,n,i){var r=Math.max(t.dimensionsDetectedCount||1,e.length,n.length,i||0);return L(e,(function(t){var e;U(t)&&(e=t.dimsDef)&&(r=Math.max(r,e.length))})),r}(t,n,i,e.dimensionsCount),s=e.canOmitUnusedDimensions&&uv(a),l=i===t.dimensionsDefine,u=l?lv(t):sv(i),h=e.encodeDefine;!h&&e.encodeDefaulter&&(h=e.encodeDefaulter(t,a));for(var c=ut(h),p=new Yp(a),f=0;f<p.length;f++)p[f]=-1;function d(t){var e=p[t];if(e<0){var n=i[t],r=U(n)?n:{name:n},a=new nv,s=r.name;null!=s&&null!=u.get(s)&&(a.name=a.displayName=s),null!=r.type&&(a.type=r.type),null!=r.displayName&&(a.displayName=r.displayName);var l=o.length;return p[t]=l,a.storeDimIndex=t,o.push(a),a}return o[e]}if(!s)for(f=0;f<a;f++)d(f);c.each((function(t,e){var n=rr(t).slice();if(1===n.length&&!H(n[0])&&n[0]<0)c.set(e,!1);else{var i=c.set(e,[]);L(n,(function(t,n){var r=H(t)?u.get(t):t;null!=r&&r<a&&(i[n]=r,y(d(r),e,n))}))}}));var g=0;function y(t,e,n){null!=Jh.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,r.set(e,!0))}L(n,(function(t){var e,n,i,r;if(H(t))e=t,r={};else{e=(r=t).name;var o=r.ordinalMeta;r.ordinalMeta=null,(r=T({},r)).ordinalMeta=o,n=r.dimsDef,i=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null}var s=c.get(e);if(!1!==s){if(!(s=rr(s)).length)for(var u=0;u<(n&&n.length||1);u++){for(;g<a&&null!=d(g).coordDim;)g++;g<a&&s.push(g++)}L(s,(function(t,o){var a=d(t);if(l&&null!=r.type&&(a.type=r.type),y(C(a,r),e,o),null==a.name&&n){var s=n[o];!U(s)&&(s={name:s}),a.name=a.displayName=s.name,a.defaultTooltip=s.defaultTooltip}i&&C(a.otherDims,i)}))}}));var v=e.generateCoord,m=e.generateCoordCount,_=null!=m;m=v?m||1:0;var x=v||"value";function b(t){null==t.name&&(t.name=t.coordDim)}if(s)L(o,(function(t){b(t)})),o.sort((function(t,e){return t.storeDimIndex-e.storeDimIndex}));else for(var w=0;w<a;w++){var S=d(w);null==S.coordDim&&(S.coordDim=Mv(x,r,_),S.coordDimIndex=0,(!v||m<=0)&&(S.isExtraCoord=!0),m--),b(S),null!=S.type||dc(t,w)!==sc&&(!S.isExtraCoord||null==S.otherDims.itemName&&null==S.otherDims.seriesName)||(S.type="ordinal")}return function(t){for(var e=ut(),n=0;n<t.length;n++){var i=t[n],r=i.name,o=e.get(r)||0;o>0&&(i.name=r+(o-1)),o++,e.set(r,o)}}(o),new ov({source:t,dimensions:o,fullDimensionCount:a,dimensionOmitted:s})}function Mv(t,e,n){var i=e.data;if(n||i.hasOwnProperty(t)){for(var r=0;i.hasOwnProperty(t+r);)r++;t+=r}return e.set(t,!0),t}var Tv=function(t){this.coordSysDims=[],this.axisMap=ut(),this.categoryAxisMap=ut(),this.coordSysName=t};var Cv={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis",xr).models[0],o=t.getReferringComponents("yAxis",xr).models[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",o),Dv(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),Dv(o)&&(i.set("y",o),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},singleAxis:function(t,e,n,i){var r=t.getReferringComponents("singleAxis",xr).models[0];e.coordSysDims=["single"],n.set("single",r),Dv(r)&&(i.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var r=t.getReferringComponents("polar",xr).models[0],o=r.findAxisModel("radiusAxis"),a=r.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",o),n.set("angle",a),Dv(o)&&(i.set("radius",o),e.firstCategoryDimIndex=0),Dv(a)&&(i.set("angle",a),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,e,n,i){var r=t.ecModel,o=r.getComponent("parallel",t.get("parallelIndex")),a=e.coordSysDims=o.dimensions.slice();L(o.parallelAxisIndex,(function(t,o){var s=r.getComponent("parallelAxis",t),l=a[o];n.set(l,s),Dv(s)&&(i.set(l,s),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=o))}))}};function Dv(t){return"category"===t.get("type")}function Iv(t,e,n){var i,r,o,a=(n=n||{}).byIndex,s=n.stackedCoordDimension;!function(t){return!av(t.schema)}(e)?(r=e.schema,i=r.dimensions,o=e.store):i=e;var l,u,h,c,p=!(!t||!t.get("stack"));if(L(i,(function(t,e){H(t)&&(i[e]=t={name:t}),p&&!t.isExtraCoord&&(a||l||!t.ordinalMeta||(l=t),u||"ordinal"===t.type||"time"===t.type||s&&s!==t.coordDim||(u=t))})),!u||a||l||(a=!0),u){h="__\0ecstackresult_"+t.id,c="__\0ecstackedover_"+t.id,l&&(l.createInvertedIndices=!0);var f=u.coordDim,d=u.type,g=0;L(i,(function(t){t.coordDim===f&&g++}));var y={name:h,coordDim:f,coordDimIndex:g,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},v={name:c,coordDim:c,coordDimIndex:g+1,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1};r?(o&&(y.storeDimIndex=o.ensureCalculationDimension(c,d),v.storeDimIndex=o.ensureCalculationDimension(h,d)),r.appendCalculationDimension(y),r.appendCalculationDimension(v)):(i.push(y),i.push(v))}return{stackedDimension:u&&u.name,stackedByDimension:l&&l.name,isStackedByIndex:a,stackedOverDimension:c,stackResultDimension:h}}function kv(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function Av(t,e){return kv(t,e)?t.getCalculationInfo("stackResultDimension"):e}function Pv(t,e,n){n=n||{};var i,r=e.getSourceManager(),o=!1;t?(o=!0,i=cp(t)):o=(i=r.getSource()).sourceFormat===Qh;var a=function(t){var e=t.get("coordinateSystem"),n=new Tv(e),i=Cv[e];if(i)return i(t,n,n.axisMap,n.categoryAxisMap),n}(e),s=function(t,e){var n,i=t.get("coordinateSystem"),r=Lc.get(i);return e&&e.coordSysDims&&(n=O(e.coordSysDims,(function(t){var n={name:t},i=e.axisMap.get(t);if(i){var r=i.get("type");n.type=function(t){return"category"===t?"ordinal":"time"===t?"time":"float"}(r)}return n}))),n||(n=r&&(r.getDimensionsInfo?r.getDimensionsInfo():r.dimensions.slice())||["x","y"]),n}(e,a),l=n.useEncodeDefaulter,u=V(l)?l:l?B(cc,s,e):null,h=Sv(i,{coordDimensions:s,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:u,canOmitUnusedDimensions:!o}),c=function(t,e,n){var i,r;return n&&L(t,(function(t,o){var a=t.coordDim,s=n.categoryAxisMap.get(a);s&&(null==i&&(i=o),t.ordinalMeta=s.getOrdinalMeta(),e&&(t.createInvertedIndices=!0)),null!=t.otherDims.itemName&&(r=!0)})),r||null==i||(t[i].otherDims.itemName=0),i}(h.dimensions,n.createInvertedIndices,a),p=o?null:r.getSharedDataStore(h),f=Iv(e,{schema:h,store:p}),d=new wv(h,e);d.setCalculationInfo(f);var g=null!=c&&function(t){if(t.sourceFormat===Qh){var e=function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(t.data||[]);return null!=e&&!F(sr(e))}}(i)?function(t,e,n,i){return i===c?n:this.defaultDimValueGetter(t,e,n,i)}:null;return d.hasItemOption=!1,d.initData(o?i:p,null,g),d}var Lv=function(){function t(t){this._setting=t||{},this._extent=[1/0,-1/0]}return t.prototype.getSetting=function(t){return this._setting[t]},t.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},t.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},t.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},t.prototype.isBlank=function(){return this._isBlank},t.prototype.setBlank=function(t){this._isBlank=t},t}();Lr(Lv);var Ov=0,Rv=function(){function t(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++Ov}return t.createByAxisModel=function(e){var n=e.option,i=n.data,r=i&&O(i,Nv);return new t({categories:r,needCollect:!r,deduplication:!1!==n.dedplication})},t.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},t.prototype.parseAndCollect=function(t){var e,n=this._needCollect;if("string"!=typeof t&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var i=this._getOrCreateMap();return null==(e=i.get(t))&&(n?(e=this.categories.length,this.categories[e]=t,i.set(t,e)):e=NaN),e},t.prototype._getOrCreateMap=function(){return this._map||(this._map=ut(this.categories))},t}();function Nv(t){return U(t)&&null!=t.value?t.value:t+""}var Ev=Bi;function zv(t,e,n,i){var r={},o=t[1]-t[0],a=r.interval=Ki(o/e,!0);null!=n&&a<n&&(a=r.interval=n),null!=i&&a>i&&(a=r.interval=i);var s=r.intervalPrecision=Bv(a);return function(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),Fv(t,0,e),Fv(t,1,e),t[0]>t[1]&&(t[0]=t[1])}(r.niceTickExtent=[Ev(Math.ceil(t[0]/a)*a,s),Ev(Math.floor(t[1]/a)*a,s)],t),r}function Bv(t){return Fi(t)+2}function Fv(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function Vv(t,e){return t>=e[0]&&t<=e[1]}function Hv(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function Gv(t,e){return t*(e[1]-e[0])+e[0]}var Wv=function(t){function e(e){var n=t.call(this,e)||this;n.type="ordinal";var i=n.getSetting("ordinalMeta");return i||(i=new Rv({})),F(i)&&(i=new Rv({categories:O(i,(function(t){return U(t)?t.value:t}))})),n._ordinalMeta=i,n._extent=n.getSetting("extent")||[0,i.categories.length-1],n}return n(e,t),e.prototype.parse=function(t){return"string"==typeof t?this._ordinalMeta.getOrdinal(t):Math.round(t)},e.prototype.contain=function(t){return Vv(t=this.parse(t),this._extent)&&null!=this._ordinalMeta.categories[t]},e.prototype.normalize=function(t){return Hv(t=this._getTickNumber(this.parse(t)),this._extent)},e.prototype.scale=function(t){return t=Math.round(Gv(t,this._extent)),this.getRawOrdinalNumber(t)},e.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},e.prototype.getMinorTicks=function(t){},e.prototype.setSortInfo=function(t){if(null!=t){for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],r=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);r<a;++r){var s=e[r];n[r]=s,i[s]=r}for(var l=0;r<o;++r){for(;null!=i[l];)l++;n.push(l),i[l]=r}}else this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null},e.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&t>=0&&t<e.length?e[t]:t},e.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&t>=0&&t<e.length?e[t]:t},e.prototype.getLabel=function(t){if(!this.isBlank()){var e=this.getRawOrdinalNumber(t.value),n=this._ordinalMeta.categories[e];return null==n?"":n+""}},e.prototype.count=function(){return this._extent[1]-this._extent[0]+1},e.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},e.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},e.prototype.getOrdinalMeta=function(){return this._ordinalMeta},e.prototype.niceTicks=function(){},e.prototype.niceExtent=function(){},e.type="ordinal",e}(Lv);Lv.registerClass(Wv);var Uv=Bi,Xv=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return n(e,t),e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return Vv(t,this._extent)},e.prototype.normalize=function(t){return Hv(t,this._extent)},e.prototype.scale=function(t){return Gv(t,this._extent)},e.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},e.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},e.prototype.getInterval=function(){return this._interval},e.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=Bv(t)},e.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(!e)return o;n[0]<i[0]&&(t?o.push({value:Uv(i[0]-e,r)}):o.push({value:n[0]}));for(var a=i[0];a<=i[1]&&(o.push({value:a}),(a=Uv(a+e,r))!==o[o.length-1].value);)if(o.length>1e4)return[];var s=o.length?o[o.length-1].value:i[1];return n[1]>s&&(t?o.push({value:Uv(s+e,r)}):o.push({value:n[1]})),o},e.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=(o.value-a.value)/t;s<t-1;){var h=Uv(a.value+(s+1)*u);h>i[0]&&h<i[1]&&l.push(h),s++}n.push(l)}return n},e.prototype.getLabel=function(t,e){if(null==t)return"";var n=e&&e.precision;return null==n?n=Fi(t.value)||0:"auto"===n&&(n=this._intervalPrecision),Ah(Uv(t.value,n,!0))},e.prototype.niceTicks=function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];if(isFinite(r)){r<0&&(r=-r,i.reverse());var o=zv(i,t,e,n);this._intervalPrecision=o.intervalPrecision,this._interval=o.interval,this._niceExtent=o.niceTickExtent}},e.prototype.niceExtent=function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var n=e[0];t.fixMax||(e[1]+=n/2),e[0]-=n/2}else e[1]=1;var i=e[1]-e[0];isFinite(i)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=Uv(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=Uv(Math.ceil(e[1]/r)*r))},e.type="interval",e}(Lv);Lv.registerClass(Xv);var Yv="undefined"!=typeof Float32Array?Float32Array:Array;function qv(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function jv(t){return t.dim+t.index}function Zv(t,e){var n=[];return e.eachSeriesByType(t,(function(t){tm(t)&&!em(t)&&n.push(t)})),n}function Kv(t){var e=function(t){var e={};L(t,(function(t){var n=t.coordinateSystem.getBaseAxis();if("time"===n.type||"value"===n.type)for(var i=t.getData(),r=n.dim+"_"+n.index,o=i.getDimensionIndex(i.mapDimension(n.dim)),a=i.getStore(),s=0,l=a.count();s<l;++s){var u=a.get(o,s);e[r]?e[r].push(u):e[r]=[u]}}));var n={};for(var i in e)if(e.hasOwnProperty(i)){var r=e[i];if(r){r.sort((function(t,e){return t-e}));for(var o=null,a=1;a<r.length;++a){var s=r[a]-r[a-1];s>0&&(o=null===o?s:Math.min(o,s))}n[i]=o}}return n}(t),n=[];return L(t,(function(t){var i,r=t.coordinateSystem.getBaseAxis(),o=r.getExtent();if("category"===r.type)i=r.getBandWidth();else if("value"===r.type||"time"===r.type){var a=r.dim+"_"+r.index,s=e[a],l=Math.abs(o[1]-o[0]),u=r.scale.getExtent(),h=Math.abs(u[1]-u[0]);i=s?l/h*s:l}else{var c=t.getData();i=Math.abs(o[1]-o[0])/c.count()}var p=zi(t.get("barWidth"),i),f=zi(t.get("barMaxWidth"),i),d=zi(t.get("barMinWidth")||1,i),g=t.get("barGap"),y=t.get("barCategoryGap");n.push({bandWidth:i,barWidth:p,barMaxWidth:f,barMinWidth:d,barGap:g,barCategoryGap:y,axisKey:jv(r),stackId:qv(t)})})),function(t){var e={};L(t,(function(t,n){var i=t.axisKey,r=t.bandWidth,o=e[i]||{bandWidth:r,remainedWidth:r,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},a=o.stacks;e[i]=o;var s=t.stackId;a[s]||o.autoWidthCount++,a[s]=a[s]||{width:0,maxWidth:0};var l=t.barWidth;l&&!a[s].width&&(a[s].width=l,l=Math.min(o.remainedWidth,l),o.remainedWidth-=l);var u=t.barMaxWidth;u&&(a[s].maxWidth=u);var h=t.barMinWidth;h&&(a[s].minWidth=h);var c=t.barGap;null!=c&&(o.gap=c);var p=t.barCategoryGap;null!=p&&(o.categoryGap=p)}));var n={};return L(e,(function(t,e){n[e]={};var i=t.stacks,r=t.bandWidth,o=t.categoryGap;if(null==o){var a=E(i).length;o=Math.max(35-4*a,15)+"%"}var s=zi(o,r),l=zi(t.gap,1),u=t.remainedWidth,h=t.autoWidthCount,c=(u-s)/(h+(h-1)*l);c=Math.max(c,0),L(i,(function(t){var e=t.maxWidth,n=t.minWidth;if(t.width){i=t.width;e&&(i=Math.min(i,e)),n&&(i=Math.max(i,n)),t.width=i,u-=i+l*i,h--}else{var i=c;e&&e<i&&(i=Math.min(e,u)),n&&n>i&&(i=n),i!==c&&(t.width=i,u-=i+l*i,h--)}})),c=(u-s)/(h+(h-1)*l),c=Math.max(c,0);var p,f=0;L(i,(function(t,e){t.width||(t.width=c),p=t,f+=t.width*(1+l)})),p&&(f-=p.width*l);var d=-f/2;L(i,(function(t,i){n[e][i]=n[e][i]||{bandWidth:r,offset:d,width:t.width},d+=t.width*(1+l)}))})),n}(n)}function $v(t,e,n){if(t&&e){var i=t[jv(e)];return null!=i&&null!=n?i[qv(n)]:i}}function Jv(t,e){var n=Zv(t,e),i=Kv(n),r={};L(n,(function(t){var e=t.getData(),n=t.coordinateSystem,o=n.getBaseAxis(),a=qv(t),s=i[jv(o)][a],l=s.offset,u=s.width,h=n.getOtherAxis(o),c=t.get("barMinHeight")||0;r[a]=r[a]||[],e.setLayout({bandWidth:s.bandWidth,offset:l,size:u});for(var p=e.mapDimension(h.dim),f=e.mapDimension(o.dim),d=kv(e,p),g=h.isHorizontal(),y=nm(o,h),v=e.getStore(),m=e.getDimensionIndex(p),_=e.getDimensionIndex(f),x=0,b=v.count();x<b;x++){var w=v.get(m,x),S=v.get(_,x),M=w>=0?"p":"n",T=y;d&&(r[a][S]||(r[a][S]={p:y,n:y}),T=r[a][S][M]);var C,D=void 0,I=void 0,k=void 0,A=void 0;if(g)D=T,I=(C=n.dataToPoint([w,S]))[1]+l,k=C[0]-y,A=u,Math.abs(k)<c&&(k=(k<0?-1:1)*c),isNaN(k)||d&&(r[a][S][M]+=k);else D=(C=n.dataToPoint([S,w]))[0]+l,I=T,k=u,A=C[1]-y,Math.abs(A)<c&&(A=(A<=0?-1:1)*c),isNaN(A)||d&&(r[a][S][M]+=A);e.setItemLayout(x,{x:D,y:I,width:k,height:A})}}))}var Qv={seriesType:"bar",plan:yf(),reset:function(t){if(tm(t)&&em(t)){var e=t.getData(),n=t.coordinateSystem,i=n.master.getRect(),r=n.getBaseAxis(),o=n.getOtherAxis(r),a=e.getDimensionIndex(e.mapDimension(o.dim)),s=e.getDimensionIndex(e.mapDimension(r.dim)),l=o.isHorizontal(),u=l?0:1,h=$v(Kv([t]),r,t).width;return h>.5||(h=.5),{progress:function(t,e){for(var c,p=t.count,f=new Yv(2*p),d=new Yv(2*p),g=new Yv(p),y=[],v=[],m=0,_=0,x=e.getStore();null!=(c=t.next());)v[u]=x.get(a,c),v[1-u]=x.get(s,c),y=n.dataToPoint(v,null),d[m]=l?i.x+i.width:y[0],f[m++]=y[0],d[m]=l?y[1]:i.y+i.height,f[m++]=y[1],g[_++]=c;e.setLayout({largePoints:f,largeDataIndices:g,largeBackgroundPoints:d,barWidth:h,valueAxisStart:nm(r,o),backgroundStart:l?i.x:i.y,valueAxisHorizontal:l})}}}}};function tm(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function em(t){return t.pipelineContext&&t.pipelineContext.large}function nm(t,e,n){return e.toGlobalCoord(e.dataToCoord("log"===e.type?1:0))}var im=function(t){function e(e){var n=t.call(this,e)||this;return n.type="time",n}return n(e,t),e.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return fh(t.value,sh[function(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}(ch(this._minLevelUnit))]||sh.second,e,this.getSetting("locale"))},e.prototype.getFormattedLabel=function(t,e,n){var i=this.getSetting("useUTC");return function(t,e,n,i,r){var o=null;if("string"==typeof n)o=n;else if("function"==typeof n)o=n(t.value,e,{level:t.level});else{var a=T({},oh);if(t.level>0)for(var s=0;s<lh.length;++s)a[lh[s]]="{primary|"+a[lh[s]]+"}";var l=n?!1===n.inherit?n:C(n,a):a,u=dh(t.value,r);if(l[u])o=l[u];else if(l.inherit){for(s=uh.indexOf(u)-1;s>=0;--s)if(l[u]){o=l[u];break}o=o||a.none}if(F(o)){var h=null==t.level?0:t.level>=0?t.level:o.length+t.level;o=o[h=Math.min(h,o.length-1)]}}return fh(new Date(t.value),o,r,i)}(t,e,n,this.getSetting("locale"),i)},e.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=[];if(!e)return i;i.push({value:n[0],level:0});var r=this.getSetting("useUTC"),o=function(t,e,n,i){var r=1e4,o=uh,a=0;function s(t,e,n,r,o,a,s){for(var l=new Date(e),u=e,h=l[r]();u<n&&u<=i[1];)s.push({value:u}),h+=t,l[o](h),u=l.getTime();s.push({value:u,notAdd:!0})}function l(t,r,o){var a=[],l=!r.length;if(!function(t,e,n,i){var r=qi(e),o=qi(n),a=function(t){return gh(r,t,i)===gh(o,t,i)},s=function(){return a("year")},l=function(){return s()&&a("month")},u=function(){return l()&&a("day")},h=function(){return u()&&a("hour")},c=function(){return h()&&a("minute")},p=function(){return c()&&a("second")},f=function(){return p()&&a("millisecond")};switch(t){case"year":return s();case"month":return l();case"day":return u();case"hour":return h();case"minute":return c();case"second":return p();case"millisecond":return f()}}(ch(t),i[0],i[1],n)){l&&(r=[{value:hm(new Date(i[0]),t,n)},{value:i[1]}]);for(var u=0;u<r.length-1;u++){var h=r[u].value,c=r[u+1].value;if(h!==c){var p=void 0,f=void 0,d=void 0,g=!1;switch(t){case"year":p=Math.max(1,Math.round(e/ih/365)),f=yh(n),d=Sh(n);break;case"half-year":case"quarter":case"month":p=am(e),f=vh(n),d=Mh(n);break;case"week":case"half-week":case"day":p=om(e),f=mh(n),d=Th(n),g=!0;break;case"half-day":case"quarter-day":case"hour":p=sm(e),f=_h(n),d=Ch(n);break;case"minute":p=lm(e,!0),f=xh(n),d=Dh(n);break;case"second":p=lm(e,!1),f=bh(n),d=Ih(n);break;case"millisecond":p=um(e),f=wh(n),d=kh(n)}s(p,h,c,f,d,g,a),"year"===t&&o.length>1&&0===u&&o.unshift({value:o[0].value-p})}}for(u=0;u<a.length;u++)o.push(a[u]);return a}}for(var u=[],h=[],c=0,p=0,f=0;f<o.length&&a++<r;++f){var d=ch(o[f]);if(ph(o[f]))if(l(o[f],u[u.length-1]||[],h),d!==(o[f+1]?ch(o[f+1]):null)){if(h.length){p=c,h.sort((function(t,e){return t.value-e.value}));for(var g=[],y=0;y<h.length;++y){var v=h[y].value;0!==y&&h[y-1].value===v||(g.push(h[y]),v>=i[0]&&v<=i[1]&&c++)}var m=(i[1]-i[0])/e;if(c>1.5*m&&p>m/1.5)break;if(u.push(g),c>m||t===o[f])break}h=[]}}0;var _=N(O(u,(function(t){return N(t,(function(t){return t.value>=i[0]&&t.value<=i[1]&&!t.notAdd}))})),(function(t){return t.length>0})),x=[],b=_.length-1;for(f=0;f<_.length;++f)for(var w=_[f],S=0;S<w.length;++S)x.push({value:w[S].value,level:b-f});x.sort((function(t,e){return t.value-e.value}));var M=[];for(f=0;f<x.length;++f)0!==f&&x[f].value===x[f-1].value||M.push(x[f]);return M}(this._minLevelUnit,this._approxInterval,r,n);return(i=i.concat(o)).push({value:n[1],level:0}),i},e.prototype.niceExtent=function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=ih,e[1]+=ih),e[1]===-1/0&&e[0]===1/0){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-ih}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval)},e.prototype.niceTicks=function(t,e,n){t=t||10;var i=this._extent,r=i[1]-i[0];this._approxInterval=r/t,null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n);var o=rm.length,a=Math.min(function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=r+1:i=r}return n}(rm,this._approxInterval,0,o),o-1);this._interval=rm[a][1],this._minLevelUnit=rm[Math.max(a-1,0)][0]},e.prototype.parse=function(t){return"number"==typeof t?t:+qi(t)},e.prototype.contain=function(t){return Vv(this.parse(t),this._extent)},e.prototype.normalize=function(t){return Hv(this.parse(t),this._extent)},e.prototype.scale=function(t){return Gv(t,this._extent)},e.type="time",e}(Xv),rm=[["second",th],["minute",eh],["hour",nh],["quarter-day",216e5],["half-day",432e5],["day",10368e4],["half-week",3024e5],["week",6048e5],["month",26784e5],["quarter",8208e6],["half-year",rh/2],["year",rh]];function om(t,e){return(t/=ih)>16?16:t>7.5?7:t>3.5?4:t>1.5?2:1}function am(t){return(t/=2592e6)>6?6:t>3?3:t>2?2:1}function sm(t){return(t/=nh)>12?12:t>6?6:t>3.5?4:t>2?2:1}function lm(t,e){return(t/=e?eh:th)>30?30:t>20?20:t>15?15:t>10?10:t>5?5:t>2?2:1}function um(t){return Ki(t,!0)}function hm(t,e,n){var i=new Date(t);switch(ch(e)){case"year":case"month":i[Mh(n)](0);case"day":i[Th(n)](1);case"hour":i[Ch(n)](0);case"minute":i[Dh(n)](0);case"second":i[Ih(n)](0),i[kh(n)](0)}return i.getTime()}Lv.registerClass(im);var cm=Lv.prototype,pm=Xv.prototype,fm=Bi,dm=Math.floor,gm=Math.ceil,ym=Math.pow,vm=Math.log,mm=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new Xv,e._interval=0,e}return n(e,t),e.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,i=e.getExtent();return O(pm.getTicks.call(this,t),(function(t){var e=t.value,r=Bi(ym(this.base,e));return r=e===n[0]&&this._fixMin?xm(r,i[0]):r,{value:r=e===n[1]&&this._fixMax?xm(r,i[1]):r}}),this)},e.prototype.setExtent=function(t,e){var n=this.base;t=vm(t)/vm(n),e=vm(e)/vm(n),pm.setExtent.call(this,t,e)},e.prototype.getExtent=function(){var t=this.base,e=cm.getExtent.call(this);e[0]=ym(t,e[0]),e[1]=ym(t,e[1]);var n=this._originalScale.getExtent();return this._fixMin&&(e[0]=xm(e[0],n[0])),this._fixMax&&(e[1]=xm(e[1],n[1])),e},e.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=vm(t[0])/vm(e),t[1]=vm(t[1])/vm(e),cm.unionExtent.call(this,t)},e.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},e.prototype.niceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n===1/0||n<=0)){var i=ji(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&Math.abs(i)>0;)i*=10;var r=[Bi(gm(e[0]/i)*i),Bi(dm(e[1]/i)*i)];this._interval=i,this._niceExtent=r}},e.prototype.niceExtent=function(t){pm.niceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return Vv(t=vm(t)/vm(this.base),this._extent)},e.prototype.normalize=function(t){return Hv(t=vm(t)/vm(this.base),this._extent)},e.prototype.scale=function(t){return t=Gv(t,this._extent),ym(this.base,t)},e.type="log",e}(Lv),_m=mm.prototype;function xm(t,e){return fm(t,Fi(e))}_m.getMinorTicks=pm.getMinorTicks,_m.getLabel=pm.getLabel,Lv.registerClass(mm);var bm=function(){function t(t,e,n){this._prepareParams(t,e,n)}return t.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var i=this._isOrdinal="ordinal"===t.type;this._needCrossZero=e.getNeedCrossZero&&e.getNeedCrossZero();var r=this._modelMinRaw=e.get("min",!0);V(r)?this._modelMinNum=Mm(t,r({min:n[0],max:n[1]})):"dataMin"!==r&&(this._modelMinNum=Mm(t,r));var o=this._modelMaxRaw=e.get("max",!0);if(V(o)?this._modelMaxNum=Mm(t,o({min:n[0],max:n[1]})):"dataMax"!==o&&(this._modelMaxNum=Mm(t,o)),i)this._axisDataLen=e.getCategories().length;else{var a=e.get("boundaryGap"),s=F(a)?a:[a||0,a||0];"boolean"==typeof s[0]||"boolean"==typeof s[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[gi(s[0],1),gi(s[1],1)]}},t.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,r=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),a="dataMin"===this._modelMinRaw?e:this._modelMinNum,s="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=a,u=null!=s;null==a&&(a=t?i?0:NaN:e-r[0]*o),null==s&&(s=t?i?i-1:NaN:n+r[1]*o),(null==a||!isFinite(a))&&(a=NaN),(null==s||!isFinite(s))&&(s=NaN);var h=$(a)||$(s)||t&&!i;this._needCrossZero&&(a>0&&s>0&&!l&&(a=0),a<0&&s<0&&!u&&(s=0));var c=this._determinedMin,p=this._determinedMax;return null!=c&&(a=c,l=!0),null!=p&&(s=p,u=!0),{min:a,max:s,minFixed:l,maxFixed:u,isBlank:h}},t.prototype.modifyDataMinMax=function(t,e){this[Sm[t]]=e},t.prototype.setDeterminedMinMax=function(t,e){var n=wm[t];this[n]=e},t.prototype.freeze=function(){this.frozen=!0},t}(),wm={min:"_determinedMin",max:"_determinedMax"},Sm={min:"_dataMin",max:"_dataMax"};function Mm(t,e){return null==e?null:$(e)?NaN:t.parse(e)}function Tm(t,e){var n=t.type,i=function(t,e,n){var i=t.rawExtentInfo;return i||(i=new bm(t,e,n),t.rawExtentInfo=i,i)}(t,e,t.getExtent()).calculate();t.setBlank(i.isBlank);var r=i.min,o=i.max,a=e.ecModel;if(a&&"time"===n){var s=Zv("bar",a),l=!1;if(L(s,(function(t){l=l||t.getBaseAxis()===e.axis})),l){var u=Kv(s),h=function(t,e,n,i){var r=n.axis.getExtent(),o=r[1]-r[0],a=$v(i,n.axis);if(void 0===a)return{min:t,max:e};var s=1/0;L(a,(function(t){s=Math.min(t.offset,s)}));var l=-1/0;L(a,(function(t){l=Math.max(t.offset+t.width,l)})),s=Math.abs(s),l=Math.abs(l);var u=s+l,h=e-t,c=h/(1-(s+l)/o)-h;return{min:t-=c*(s/u),max:e+=c*(l/u)}}(r,o,e,u);r=h.min,o=h.max}}return{extent:[r,o],fixMin:i.minFixed,fixMax:i.maxFixed}}function Cm(t,e){var n=e,i=Tm(t,n),r=i.extent,o=n.get("splitNumber");t instanceof mm&&(t.base=n.get("logBase"));var a=t.type;t.setExtent(r[0],r[1]),t.niceExtent({splitNumber:o,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:"interval"===a||"time"===a?n.get("minInterval"):null,maxInterval:"interval"===a||"time"===a?n.get("maxInterval"):null});var s=n.get("interval");null!=s&&t.setInterval&&t.setInterval(s)}function Dm(t,e){if(e=e||t.get("type"))switch(e){case"category":return new Wv({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new im({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(Lv.getClass(e)||Xv)}}function Im(t){var e,n,i=t.getLabelModel().get("formatter"),r="category"===t.type?t.scale.getExtent()[0]:null;return"time"===t.scale.type?(n=i,function(e,i){return t.scale.getFormattedLabel(e,i,n)}):"string"==typeof i?function(e){return function(n){var i=t.scale.getLabel(n);return e.replace("{value}",null!=i?i:"")}}(i):"function"==typeof i?(e=i,function(n,i){return null!=r&&(i=n.value-r),e(function(t,e){return"category"===t.type?t.scale.getLabel(e):e.value}(t,n),i,null!=n.level?{level:n.level}:null)}):function(e){return t.scale.getLabel(e)}}function km(t,e){var n=e*Math.PI/180,i=t.width,r=t.height,o=i*Math.abs(Math.cos(n))+Math.abs(r*Math.sin(n)),a=i*Math.abs(Math.sin(n))+Math.abs(r*Math.cos(n));return new oi(t.x,t.y,o,a)}function Am(t){var e=t.get("interval");return null==e?"auto":e}function Pm(t){return"category"===t.type&&0===Am(t.getLabelModel())}var Lm=function(){function t(){}return t.prototype.getNeedCrossZero=function(){return!this.option.scale},t.prototype.getCoordSysModel=function(){},t}();var Om={isDimensionStacked:kv,enableDataStack:Iv,getStackedDimension:Av};var Rm=Object.freeze({__proto__:null,createList:function(t){return Pv(null,t)},getLayoutRect:Wh,dataStack:Om,createScale:function(t,e){var n=e;e instanceof Uu||(n=new Uu(e));var i=Dm(n);return i.setExtent(t[0],t[1]),Cm(i,n),i},mixinAxisModelCommonMethods:function(t){A(t,Lm)},getECData:Ja,createTextStyle:function(t,e){return Cu(t,null,null,"normal"!==(e=e||{}).state)},createDimensions:function(t,e){return Sv(t,e).dimensions},createSymbol:_d,enableHoverEmphasis:Bs}),Nm=Object.freeze({__proto__:null,linearMap:Ei,round:Bi,asc:function(t){return t.sort((function(t,e){return t-e})),t},getPrecision:Fi,getPrecisionSafe:Vi,getPixelPrecision:Hi,getPercentWithPrecision:Gi,MAX_SAFE_INTEGER:9007199254740991,remRadian:Ui,isRadianAroundZero:Xi,parseDate:qi,quantity:ji,quantityExponent:Zi,nice:Ki,quantile:function(t,e){var n=(t.length-1)*e+1,i=Math.floor(n),r=+t[i-1],o=n-i;return o?r+o*(t[i]-r):r},reformIntervals:function(t){t.sort((function(t,e){return s(t,e,0)?-1:1}));for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t;function s(t,e,n){return t.interval[n]<e.interval[n]||t.interval[n]===e.interval[n]&&(t.close[n]-e.close[n]==(n?-1:1)||!n&&s(t,e,1))}},isNumeric:Ji,numericToNumber:$i}),Em=Object.freeze({__proto__:null,parse:qi,format:fh}),zm=Object.freeze({__proto__:null,extendShape:function(t){return Aa.extend(t)},extendPath:function(t,e){return du(t,e)},makePath:yu,makeImage:vu,mergePath:_u,resizePath:xu,createIcon:function(t,e,n){var i=T({rectHover:!0},e),r=i.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(r.image=t.slice(8),C(r,n),new Na(i)):yu(t.replace("path://",""),i,n,"center")},updateProps:ru,initProps:ou,getTransform:function(t,e){for(var n=Pn([]);t&&t!==e;)On(n,t.getLocalTransform(),n),t=t.parent;return n},clipPointsByRect:function(t,e){return O(t,(function(t){var n=t[0];n=cu(n,e.x),n=pu(n,e.x+e.width);var i=t[1];return i=cu(i,e.y),[n,i=pu(i,e.y+e.height)]}))},clipRectByRect:function(t,e){var n=cu(t.x,e.x),i=pu(t.x+t.width,e.x+e.width),r=cu(t.y,e.y),o=pu(t.y+t.height,e.y+e.height);if(i>=n&&o>=r)return{x:n,y:r,width:i-n,height:o-r}},registerShape:gu,getShapeClass:function(t){if(fu.hasOwnProperty(t))return fu[t]},Group:Di,Image:Na,Text:Wa,Circle:hl,Ellipse:pl,Sector:Dl,Ring:kl,Polygon:Ol,Polyline:Nl,Rect:Va,Line:Bl,BezierCurve:Gl,Arc:Ul,IncrementalDisplayable:eu,CompoundPath:Xl,LinearGradient:ql,RadialGradient:jl,BoundingRect:oi}),Bm=Object.freeze({__proto__:null,addCommas:Ah,toCamelCase:function(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,(function(t,e){return e.toUpperCase()})),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t},normalizeCssArray:Ph,encodeHTML:Rh,formatTpl:zh,getTooltipMarker:function(t,e){var n=H(t)?{color:t,extraCssText:e}:t||{},i=n.color,r=n.type;e=n.extraCssText;var o=n.renderMode||"html";return i?"html"===o?"subItem"===r?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Rh(i)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+Rh(i)+";"+(e||"")+'"></span>':{renderMode:o,content:"{"+(n.markerId||"markerX")+"|}  ",style:"subItem"===r?{width:4,height:4,borderRadius:2,backgroundColor:i}:{width:10,height:10,borderRadius:5,backgroundColor:i}}:""},formatTime:function(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=qi(e),r=n?"UTC":"",o=i["get"+r+"FullYear"](),a=i["get"+r+"Month"]()+1,s=i["get"+r+"Date"](),l=i["get"+r+"Hours"](),u=i["get"+r+"Minutes"](),h=i["get"+r+"Seconds"](),c=i["get"+r+"Milliseconds"]();return t=t.replace("MM",hh(a,2)).replace("M",a).replace("yyyy",o).replace("yy",o%100+"").replace("dd",hh(s,2)).replace("d",s).replace("hh",hh(l,2)).replace("h",l).replace("mm",hh(u,2)).replace("m",u).replace("ss",hh(h,2)).replace("s",h).replace("SSS",hh(c,3))},capitalFirst:function(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t},truncateText:Gr,getTextRect:function(t,e,n,i,r,o,a,s){return new Wa({style:{text:t,font:e,align:n,verticalAlign:i,padding:r,rich:o,overflow:a?"truncate":null,lineHeight:s}}).getBoundingRect()}}),Fm=Object.freeze({__proto__:null,map:O,each:L,indexOf:I,inherits:k,reduce:R,filter:N,bind:z,curry:B,isArray:F,isString:H,isObject:U,isFunction:V,extend:T,defaults:C,clone:S,merge:M}),Vm=yr();function Hm(t){return"category"===t.type?function(t){var e=t.getLabelModel(),n=Wm(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:n.labelCategoryInterval}:n}(t):function(t){var e=t.scale.getTicks(),n=Im(t);return{labels:O(e,(function(e,i){return{level:e.level,formattedLabel:n(e,i),rawLabel:t.scale.getLabel(e),tickValue:e.value}}))}}(t)}function Gm(t,e){return"category"===t.type?function(t,e){var n,i,r=Um(t,"ticks"),o=Am(e),a=Xm(r,o);if(a)return a;e.get("show")&&!t.scale.isBlank()||(n=[]);if(V(o))n=jm(t,o,!0);else if("auto"===o){var s=Wm(t,t.getLabelModel());i=s.labelCategoryInterval,n=O(s.labels,(function(t){return t.tickValue}))}else n=qm(t,i=o,!0);return Ym(r,o,{ticks:n,tickCategoryInterval:i})}(t,e):{ticks:O(t.scale.getTicks(),(function(t){return t.value}))}}function Wm(t,e){var n,i,r=Um(t,"labels"),o=Am(e),a=Xm(r,o);return a||(V(o)?n=jm(t,o):(i="auto"===o?function(t){var e=Vm(t).autoInterval;return null!=e?e:Vm(t).autoInterval=t.calculateCategoryInterval()}(t):o,n=qm(t,i)),Ym(r,o,{labels:n,labelCategoryInterval:i}))}function Um(t,e){return Vm(t)[e]||(Vm(t)[e]=[])}function Xm(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function Ym(t,e,n){return t.push({key:e,value:n}),n}function qm(t,e,n){var i=Im(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),u=o[0],h=r.count();0!==u&&l>1&&h/l>2&&(u=Math.round(Math.ceil(u/l)*l));var c=Pm(t),p=a.get("showMinLabel")||c,f=a.get("showMaxLabel")||c;p&&u!==o[0]&&g(o[0]);for(var d=u;d<=o[1];d+=l)g(d);function g(t){var e={value:t};s.push(n?t:{formattedLabel:i(e),rawLabel:r.getLabel(e),tickValue:t})}return f&&d-l!==o[1]&&g(o[1]),s}function jm(t,e,n){var i=t.scale,r=Im(t),o=[];return L(i.getTicks(),(function(t){var a=i.getLabel(t),s=t.value;e(t.value,a)&&o.push(n?s:{formattedLabel:r(t),rawLabel:a,tickValue:s})})),o}var Zm=[0,1],Km=function(){function t(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}return t.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return t>=n&&t<=i},t.prototype.containData=function(t){return this.scale.contain(t)},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.getPixelPrecision=function(t){return Hi(t||this.scale.getExtent(),this._extent)},t.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},t.prototype.dataToCoord=function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&$m(n=n.slice(),i.count()),Ei(t,Zm,n,e)},t.prototype.coordToData=function(t,e){var n=this._extent,i=this.scale;this.onBand&&"ordinal"===i.type&&$m(n=n.slice(),i.count());var r=Ei(t,n,Zm,e);return this.scale.scale(r)},t.prototype.pointToData=function(t,e){},t.prototype.getTicksCoords=function(t){var e=(t=t||{}).tickModel||this.getTickModel(),n=O(Gm(this,e).ticks,(function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}}),this);return function(t,e,n,i){var r=e.length;if(!t.onBand||n||!r)return;var o,a,s=t.getExtent();if(1===r)e[0].coord=s[0],o=e[1]={coord:s[0]};else{var l=e[r-1].tickValue-e[0].tickValue,u=(e[r-1].coord-e[0].coord)/l;L(e,(function(t){t.coord-=u/2})),a=1+t.scale.getExtent()[1]-e[r-1].tickValue,o={coord:e[r-1].coord+u*a},e.push(o)}var h=s[0]>s[1];c(e[0].coord,s[0])&&(i?e[0].coord=s[0]:e.shift());i&&c(s[0],e[0].coord)&&e.unshift({coord:s[0]});c(s[1],o.coord)&&(i?o.coord=s[1]:e.pop());i&&c(o.coord,s[1])&&e.push({coord:s[1]});function c(t,e){return t=Bi(t),e=Bi(e),h?t>e:t<e}}(this,n,e.get("alignWithLabel"),t.clamp),n},t.prototype.getMinorTicksCoords=function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick").get("splitNumber");return t>0&&t<100||(t=5),O(this.scale.getMinorTicks(t),(function(t){return O(t,(function(t){return{coord:this.dataToCoord(t),tickValue:t}}),this)}),this)},t.prototype.getViewLabels=function(){return Hm(this).labels},t.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},t.prototype.getTickModel=function(){return this.model.getModel("axisTick")},t.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);0===n&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},t.prototype.calculateCategoryInterval=function(){return function(t){var e=function(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}(t),n=Im(t),i=(e.axisRotate-e.labelRotate)/180*Math.PI,r=t.scale,o=r.getExtent(),a=r.count();if(o[1]-o[0]<1)return 0;var s=1;a>40&&(s=Math.max(1,Math.floor(a/40)));for(var l=o[0],u=t.dataToCoord(l+1)-t.dataToCoord(l),h=Math.abs(u*Math.cos(i)),c=Math.abs(u*Math.sin(i)),p=0,f=0;l<=o[1];l+=s){var d,g,y=ci(n({value:l}),e.font,"center","top");d=1.3*y.width,g=1.3*y.height,p=Math.max(p,d,7),f=Math.max(f,g,7)}var v=p/h,m=f/c;isNaN(v)&&(v=1/0),isNaN(m)&&(m=1/0);var _=Math.max(0,Math.floor(Math.min(v,m))),x=Vm(t.model),b=t.getExtent(),w=x.lastAutoInterval,S=x.lastTickCount;return null!=w&&null!=S&&Math.abs(w-_)<=1&&Math.abs(S-a)<=1&&w>_&&x.axisExtent0===b[0]&&x.axisExtent1===b[1]?_=w:(x.lastTickCount=a,x.lastAutoInterval=_,x.axisExtent0=b[0],x.axisExtent1=b[1]),_}(this)},t}();function $m(t,e){var n=(t[1]-t[0])/e/2;t[0]+=n,t[1]-=n}function Jm(t,e,n,i,r,o,a,s){var l=r-t,u=o-e,h=n-t,c=i-e,p=Math.sqrt(h*h+c*c),f=(l*(h/=p)+u*(c/=p))/p;s&&(f=Math.min(Math.max(f,0),1)),f*=p;var d=a[0]=t+f*h,g=a[1]=e+f*c;return Math.sqrt((d-r)*(d-r)+(g-o)*(g-o))}var Qm=new Kn,t_=new Kn,e_=new Kn,n_=new Kn,i_=new Kn,r_=[],o_=new Kn;function a_(t,e){if(e<=180&&e>0){e=e/180*Math.PI,Qm.fromArray(t[0]),t_.fromArray(t[1]),e_.fromArray(t[2]),Kn.sub(n_,Qm,t_),Kn.sub(i_,e_,t_);var n=n_.len(),i=i_.len();if(!(n<.001||i<.001)){n_.scale(1/n),i_.scale(1/i);var r=n_.dot(i_);if(Math.cos(e)<r){var o=Jm(t_.x,t_.y,e_.x,e_.y,Qm.x,Qm.y,r_,!1);o_.fromArray(r_),o_.scaleAndAdd(i_,o/Math.tan(Math.PI-e));var a=e_.x!==t_.x?(o_.x-t_.x)/(e_.x-t_.x):(o_.y-t_.y)/(e_.y-t_.y);if(isNaN(a))return;a<0?Kn.copy(o_,t_):a>1&&Kn.copy(o_,e_),o_.toArray(t[1])}}}}function s_(t,e,n){if(n<=180&&n>0){n=n/180*Math.PI,Qm.fromArray(t[0]),t_.fromArray(t[1]),e_.fromArray(t[2]),Kn.sub(n_,t_,Qm),Kn.sub(i_,e_,t_);var i=n_.len(),r=i_.len();if(!(i<.001||r<.001))if(n_.scale(1/i),i_.scale(1/r),n_.dot(e)<Math.cos(n)){var o=Jm(t_.x,t_.y,e_.x,e_.y,Qm.x,Qm.y,r_,!1);o_.fromArray(r_);var a=Math.PI/2,s=a+Math.acos(i_.dot(e))-n;if(s>=a)Kn.copy(o_,e_);else{o_.scaleAndAdd(i_,o/Math.tan(Math.PI/2-s));var l=e_.x!==t_.x?(o_.x-t_.x)/(e_.x-t_.x):(o_.y-t_.y)/(e_.y-t_.y);if(isNaN(l))return;l<0?Kn.copy(o_,t_):l>1&&Kn.copy(o_,e_)}o_.toArray(t[1])}}}function l_(t,e,n,i){var r="normal"===n,o=r?t:t.ensureState(n);o.ignore=e;var a=i.get("smooth");a&&!0===a&&(a=.3),o.shape=o.shape||{},a>0&&(o.shape.smooth=a);var s=i.getModel("lineStyle").getLineStyle();r?t.useStyle(s):o.style=s}function u_(t,e){var n=e.smooth,i=e.points;if(i)if(t.moveTo(i[0][0],i[0][1]),n>0&&i.length>=3){var r=Ct(i[0],i[1]),o=Ct(i[1],i[2]);if(!r||!o)return t.lineTo(i[1][0],i[1][1]),void t.lineTo(i[2][0],i[2][1]);var a=Math.min(r,o)*n,s=kt([],i[1],i[0],a/r),l=kt([],i[1],i[2],a/o),u=kt([],s,l,.5);t.bezierCurveTo(s[0],s[1],s[0],s[1],u[0],u[1]),t.bezierCurveTo(l[0],l[1],l[0],l[1],i[2][0],i[2][1])}else for(var h=1;h<i.length;h++)t.lineTo(i[h][0],i[h][1])}function h_(t,e,n,i){return function(t,e,n,i,r,o){var a=t.length;if(!(a<2)){t.sort((function(t,n){return t.rect[e]-n.rect[e]}));for(var s,l=0,u=!1,h=0,c=0;c<a;c++){var p=t[c],f=p.rect;(s=f[e]-l)<0&&(f[e]-=s,p.label[e]-=s,u=!0),h+=Math.max(-s,0),l=f[e]+f[n]}h>0&&o&&x(-h/a,0,a);var d,g,y=t[0],v=t[a-1];return m(),d<0&&b(-d,.8),g<0&&b(g,.8),m(),_(d,g,1),_(g,d,-1),m(),d<0&&w(-d),g<0&&w(g),u}function m(){d=y.rect[e]-i,g=r-v.rect[e]-v.rect[n]}function _(t,e,n){if(t<0){var i=Math.min(e,-t);if(i>0){x(i*n,0,a);var r=i+t;r<0&&b(-r*n,1)}else b(-t*n,1)}}function x(n,i,r){0!==n&&(u=!0);for(var o=i;o<r;o++){var a=t[o];a.rect[e]+=n,a.label[e]+=n}}function b(i,r){for(var o=[],s=0,l=1;l<a;l++){var u=t[l-1].rect,h=Math.max(t[l].rect[e]-u[e]-u[n],0);o.push(h),s+=h}if(s){var c=Math.min(Math.abs(i)/s,r);if(i>0)for(l=0;l<a-1;l++)x(o[l]*c,0,l+1);else for(l=a-1;l>0;l--)x(-o[l-1]*c,l,a)}}function w(t){var e=t<0?-1:1;t=Math.abs(t);for(var n=Math.ceil(t/(a-1)),i=0;i<a-1;i++)if(e>0?x(n,0,i+1):x(-n,a-i-1,a),(t-=n)<=0)return}}(t,"y","height",e,n,i)}function c_(){return!1}function p_(t,e,n){var i=D(),r=e.getWidth(),o=e.getHeight(),a=i.style;return a&&(a.position="absolute",a.left="0",a.top="0",a.width=r+"px",a.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=o*n,i}var f_=function(t){function e(e,n,i){var r,o=t.call(this)||this;o.motionBlur=!1,o.lastFrameAlpha=.7,o.dpr=1,o.virtual=!1,o.config={},o.incremental=!1,o.zlevel=0,o.maxRepaintRectCount=5,o.__dirty=!0,o.__firstTimePaint=!0,o.__used=!1,o.__drawIndex=0,o.__startIndex=0,o.__endIndex=0,o.__prevStartIndex=null,o.__prevEndIndex=null,i=i||Dn,"string"==typeof e?r=p_(e,n,i):U(e)&&(e=(r=e).id),o.id=e,o.dom=r;var a=r.style;return a&&(r.onselectstart=c_,a.webkitUserSelect="none",a.userSelect="none",a.webkitTapHighlightColor="rgba(0,0,0,0)",a["-webkit-touch-callout"]="none",a.padding="0",a.margin="0",a.borderWidth="0"),o.domBack=null,o.ctxBack=null,o.painter=n,o.config=null,o.dpr=i,o}return n(e,t),e.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},e.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},e.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},e.prototype.setUnpainted=function(){this.__firstTimePaint=!0},e.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=p_("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},e.prototype.createRepaintRects=function(t,e,n,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var r,o=[],a=this.maxRepaintRectCount,s=!1,l=new oi(0,0,0,0);function u(t){if(t.isFinite()&&!t.isZero())if(0===o.length){(e=new oi(0,0,0,0)).copy(t),o.push(e)}else{for(var e,n=!1,i=1/0,r=0,u=0;u<o.length;++u){var h=o[u];if(h.intersect(t)){var c=new oi(0,0,0,0);c.copy(h),c.union(t),o[u]=c,n=!0;break}if(s){l.copy(t),l.union(h);var p=t.width*t.height,f=h.width*h.height,d=l.width*l.height-p-f;d<i&&(i=d,r=u)}}if(s&&(o[r].union(t),n=!0),!n)(e=new oi(0,0,0,0)).copy(t),o.push(e);s||(s=o.length>=a)}}for(var h=this.__startIndex;h<this.__endIndex;++h){if(f=t[h]){var c=f.shouldBePainted(n,i,!0,!0);(d=f.__isRendered&&(1&f.__dirty||!c)?f.getPrevPaintRect():null)&&u(d);var p=c&&(1&f.__dirty||!f.__isRendered)?f.getPaintRect():null;p&&u(p)}}for(h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var f,d;c=(f=e[h]).shouldBePainted(n,i,!0,!0);if(f&&(!c||!f.__zr)&&f.__isRendered)(d=f.getPrevPaintRect())&&u(d)}do{r=!1;for(h=0;h<o.length;)if(o[h].isZero())o.splice(h,1);else{for(var g=h+1;g<o.length;)o[h].intersect(o[g])?(r=!0,o[h].union(o[g]),o.splice(g,1)):g++;h++}}while(r);return this._paintRects=o,o},e.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},e.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n&&this.ctxBack.scale(n,n))},e.prototype.clear=function(t,e,n){var i=this.dom,r=this.ctx,o=i.width,a=i.height;e=e||this.clearColor;var s=this.motionBlur&&!t,l=this.lastFrameAlpha,u=this.dpr,h=this;s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,o/u,a/u));var c=this.domBack;function p(t,n,i,o){if(r.clearRect(t,n,i,o),e&&"transparent"!==e){var a=void 0;j(e)?(a=e.__canvasGradient||bd(r,e,{x:0,y:0,width:i,height:o}),e.__canvasGradient=a):Z(e)&&(a=kd(r,e,{dirty:function(){h.setUnpainted(),h.__painter.refresh()}})),r.save(),r.fillStyle=a||e,r.fillRect(t,n,i,o),r.restore()}s&&(r.save(),r.globalAlpha=l,r.drawImage(c,t,n,i,o),r.restore())}!n||s?p(0,0,o,a):n.length&&L(n,(function(t){p(t.x*u,t.y*u,t.width*u,t.height*u)}))},e}(Et),d_=1e5,g_=314159,y_=.01;function v_(t){return parseInt(t,10)}var m_=function(){function t(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=T({},n||{}),this.dpr=n.devicePixelRatio||Dn,this._singleCanvas=r,this.root=t;var o=t.style;o&&(o.webkitTapHighlightColor="transparent",o.webkitUserSelect="none",o.userSelect="none",o["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var a=this._zlevelList;this._prevDisplayList=[];var s=this._layers;if(r){var l=t,u=l.width,h=l.height;null!=n.width&&(u=n.width),null!=n.height&&(h=n.height),this.dpr=n.devicePixelRatio||1,l.width=u*this.dpr,l.height=h*this.dpr,this._width=u,this._height=h;var c=new f_(l,this,this.dpr);c.__builtin__=!0,c.initContext(),s[314159]=c,c.zlevel=g_,a.push(g_),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var p=this._domRoot=function(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}(this._width,this._height);t.appendChild(p)}}return t.prototype.getType=function(){return"canvas"},t.prototype.isSingleCanvas=function(){return this._singleCanvas},t.prototype.getViewportRoot=function(){return this._domRoot},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var r=0;r<i.length;r++){var o=i[r],a=this._layers[o];if(!a.__builtin__&&a.refresh){var s=0===r?this._backgroundColor:null;a.refresh(s)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},t.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},t.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(n||(n=this._hoverlayer=this.getLayer(d_)),i||(i=n.ctx).save(),Bd(i,a,r,o===e-1))}i&&i.restore()}},t.prototype.getHoverLayer=function(){return this.getLayer(d_)},t.prototype.paintOne=function(t,e){zd(t,e)},t.prototype._paintList=function(t,e,n,i){if(this._redrawId===i){n=n||!1,this._updateLayerStatus(t);var r=this._doPaintList(t,e,n),o=r.finished,a=r.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),a&&this._paintHoverList(t),o)this.eachLayer((function(t){t.afterBrush&&t.afterBrush()}));else{var s=this;ve((function(){s._paintList(t,e,n,i)}))}}},t.prototype._compositeManually=function(){var t=this.getLayer(g_).ctx,e=this._domRoot.width,n=this._domRoot.height;t.clearRect(0,0,e,n),this.eachBuiltinLayer((function(i){i.virtual&&t.drawImage(i.dom,0,0,e,n)}))},t.prototype._doPaintList=function(t,e,n){for(var i=this,r=[],o=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var l=this._zlevelList[s],u=this._layers[l];u.__builtin__&&u!==this._hoverlayer&&(u.__dirty||n)&&r.push(u)}for(var h=!0,c=!1,p=function(a){var s,l=r[a],u=l.ctx,p=o&&l.createRepaintRects(t,e,f._width,f._height),d=n?l.__startIndex:l.__drawIndex,g=!n&&l.incremental&&Date.now,y=g&&Date.now(),v=l.zlevel===f._zlevelList[0]?f._backgroundColor:null;if(l.__startIndex===l.__endIndex)l.clear(!1,v,p);else if(d===l.__startIndex){var m=t[d];m.incremental&&m.notClear&&!n||l.clear(!1,v,p)}-1===d&&(console.error("For some unknown reason. drawIndex is -1"),d=l.__startIndex);var _=function(e){var n={inHover:!1,allClipped:!1,prevEl:null,viewWidth:i._width,viewHeight:i._height};for(s=d;s<l.__endIndex;s++){var r=t[s];if(r.__inHover&&(c=!0),i._doPaintEl(r,l,o,e,n,s===l.__endIndex-1),g)if(Date.now()-y>15)break}n.prevElClipPaths&&u.restore()};if(p)if(0===p.length)s=l.__endIndex;else for(var x=f.dpr,b=0;b<p.length;++b){var w=p[b];u.save(),u.beginPath(),u.rect(w.x*x,w.y*x,w.width*x,w.height*x),u.clip(),_(w),u.restore()}else u.save(),_(),u.restore();l.__drawIndex=s,l.__drawIndex<l.__endIndex&&(h=!1)},f=this,d=0;d<r.length;d++)p(d);return a.wxa&&L(this._layers,(function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()})),{finished:h,needsRefreshHover:c}},t.prototype._doPaintEl=function(t,e,n,i,r,o){var a=e.ctx;if(n){var s=t.getPaintRect();(!i||s&&s.intersect(i))&&(Bd(a,t,r,o),t.setPrevPaintRect(s))}else Bd(a,t,r,o)},t.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=g_);var n=this._layers[t];return n||((n=new f_("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?M(n,this._layerConfig[t],!0):this._layerConfig[t-y_]&&M(n,this._layerConfig[t-y_],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},t.prototype.insertLayer=function(t,e){var n=this._layers,i=this._zlevelList,r=i.length,o=this._domRoot,a=null,s=-1;if(n[t])w("ZLevel "+t+" has been used already");else if(function(t){return!!t&&(!!t.__builtin__||"function"==typeof t.resize&&"function"==typeof t.refresh)}(e)){if(r>0&&t>i[0]){for(s=0;s<r-1&&!(i[s]<t&&i[s+1]>t);s++);a=n[i[s]]}if(i.splice(s+1,0,t),n[t]=e,!e.virtual)if(a){var l=a.dom;l.nextSibling?o.insertBefore(e.dom,l.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.__painter=this}else w("Layer of zlevel "+t+" is not valid")},t.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i];t.call(e,this._layers[r],r)}},t.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},t.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__||t.call(e,o,r)}},t.prototype.getLayers=function(){return this._layers},t.prototype._updateLayerStatus=function(t){function e(t){o&&(o.__endIndex!==t&&(o.__dirty=!0),o.__endIndex=t)}if(this.eachBuiltinLayer((function(t,e){t.__dirty=t.__used=!1})),this._singleCanvas)for(var n=1;n<t.length;n++){if((s=t[n]).zlevel!==t[n-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}}var i,r,o=null,a=0;for(r=0;r<t.length;r++){var s,l=(s=t[r]).zlevel,u=void 0;i!==l&&(i=l,a=0),s.incremental?((u=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,a=1):u=this.getLayer(l+(a>0?y_:0),this._needsManuallyCompositing),u.__builtin__||w("ZLevel "+l+" has been used by unkown layer "+u.id),u!==o&&(u.__used=!0,u.__startIndex!==r&&(u.__dirty=!0),u.__startIndex=r,u.incremental?u.__drawIndex=-1:u.__drawIndex=r,e(r),o=u),1&s.__dirty&&!s.__inHover&&(u.__dirty=!0,u.incremental&&u.__drawIndex<0&&(u.__drawIndex=r))}e(r),this.eachBuiltinLayer((function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)}))},t.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},t.prototype._clearLayer=function(t){t.clear()},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t,L(this._layers,(function(t){t.setUnpainted()}))},t.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?M(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];if(r===t||r===t+y_)M(this._layers[r],n[t],!0)}}},t.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(I(n,t),1))},t.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot;n.style.display="none";var i=this._opts;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=this._getSize(0),e=this._getSize(1),n.style.display="",this._width!==t||e!==this._height){for(var r in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(g_).resize(t,e)}return this},t.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},t.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},t.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[314159].dom;var e=new f_("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var n=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height;this.eachLayer((function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())}))}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,l=a.length;s<l;s++){var u=a[s];Bd(n,u,o,s===l-1)}return e.dom},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t.prototype._getSize=function(t){var e=this._opts,n=["width","height"][t],i=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(null!=e[n]&&"auto"!==e[n])return parseFloat(e[n]);var a=this.root,s=document.defaultView.getComputedStyle(a);return(a[i]||v_(s[n])||v_(a.style[n]))-(v_(s[r])||0)-(v_(s[o])||0)|0},t.prototype.pathToImage=function(t,e){e=e||this.dpr;var n=document.createElement("canvas"),i=n.getContext("2d"),r=t.getBoundingRect(),o=t.style,a=o.shadowBlur*e,s=o.shadowOffsetX*e,l=o.shadowOffsetY*e,u=t.hasStroke()?o.lineWidth:0,h=Math.max(u/2,-s+a),c=Math.max(u/2,s+a),p=Math.max(u/2,-l+a),f=Math.max(u/2,l+a),d=r.width+h+c,g=r.height+p+f;n.width=d*e,n.height=g*e,i.scale(e,e),i.clearRect(0,0,d,g),i.dpr=e;var y={x:t.x,y:t.y,scaleX:t.scaleX,scaleY:t.scaleY,rotation:t.rotation,originX:t.originX,originY:t.originY};t.x=h-r.x,t.y=p-r.y,t.rotation=0,t.scaleX=1,t.scaleY=1,t.updateTransform(),t&&Bd(i,t,{inHover:!1,viewWidth:this._width,viewHeight:this._height},!0);var v=new Na({style:{x:0,y:0,image:n}});return T(t,y),v},t}();var __=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.hasSymbolVisual=!0,n}return n(e,t),e.prototype.getInitialData=function(t){return Pv(null,this,{useEncodeDefaulter:!0})},e.prototype.getLegendIcon=function(t){var e=new Di,n=_d("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1);e.add(n),n.setStyle(t.lineStyle);var i=this.getData().getVisual("symbol"),r=this.getData().getVisual("symbolRotate"),o="none"===i?"circle":i,a=.8*t.itemHeight,s=_d(o,(t.itemWidth-a)/2,(t.itemHeight-a)/2,a,a,t.itemStyle.fill);e.add(s),s.setStyle(t.itemStyle);var l="inherit"===t.iconRotate?r:t.iconRotate||0;return s.rotation=l*Math.PI/180,s.setOrigin([t.itemWidth/2,t.itemHeight/2]),o.indexOf("empty")>-1&&(s.style.stroke=s.style.fill,s.style.fill="#fff",s.style.lineWidth=2),e},e.type="series.line",e.dependencies=["grid","polar"],e.defaultOption={zlevel:0,z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0,lineStyle:{width:"bolder"}},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},e}(sf);function x_(t,e){var n=t.mapDimensionsAll("defaultedLabel"),i=n.length;if(1===i){var r=Dp(t,e,n[0]);return null!=r?r+"":null}if(i){for(var o=[],a=0;a<n.length;a++)o.push(Dp(t,e,n[a]));return o.join(" ")}}function b_(t,e){var n=t.mapDimensionsAll("defaultedLabel");if(!F(e))return e+"";for(var i=[],r=0;r<n.length;r++){var o=t.getDimensionIndex(n[r]);o>=0&&i.push(e[o])}return i.join(" ")}var w_=function(t){function e(e,n,i,r){var o=t.call(this)||this;return o.updateData(e,n,i,r),o}return n(e,t),e.prototype._createSymbol=function(t,e,n,i,r){this.removeAll();var o=_d(t,-1,-1,2,2,null,r);o.attr({z2:100,culling:!0,scaleX:i[0]/2,scaleY:i[1]/2}),o.drift=S_,this._symbolType=t,this.add(o)},e.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},e.prototype.getSymbolType=function(){return this._symbolType},e.prototype.getSymbolPath=function(){return this.childAt(0)},e.prototype.highlight=function(){Cs(this.childAt(0))},e.prototype.downplay=function(){Ds(this.childAt(0))},e.prototype.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},e.prototype.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":e.cursor},e.prototype.updateData=function(t,n,i,r){this.silent=!1;var o=t.getItemVisual(n,"symbol")||"circle",a=t.hostModel,s=e.getSymbolSize(t,n),l=o!==this._symbolType,u=r&&r.disableAnimation;if(l){var h=t.getItemVisual(n,"symbolKeepAspect");this._createSymbol(o,t,n,s,h)}else{(p=this.childAt(0)).silent=!1;var c={scaleX:s[0]/2,scaleY:s[1]/2};u?p.attr(c):ru(p,c,a,n),hu(p)}if(this._updateCommon(t,n,s,i,r),l){var p=this.childAt(0);if(!u){c={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:p.style.opacity}};p.scaleX=p.scaleY=0,p.style.opacity=0,ou(p,c,a,n)}}u&&this.childAt(0).stopAnimation("remove"),this._seriesModel=a},e.prototype._updateCommon=function(t,e,n,i,r){var o,a,s,l,u,h,c,p,f=this.childAt(0),d=t.hostModel;if(i&&(o=i.emphasisItemStyle,a=i.blurItemStyle,s=i.selectItemStyle,l=i.focus,u=i.blurScope,h=i.labelStatesModels,c=i.hoverScale,p=i.cursorStyle),!i||t.hasItemOption){var g=i&&i.itemModel?i.itemModel:t.getItemModel(e),y=g.getModel("emphasis");o=y.getModel("itemStyle").getItemStyle(),s=g.getModel(["select","itemStyle"]).getItemStyle(),a=g.getModel(["blur","itemStyle"]).getItemStyle(),l=y.get("focus"),u=y.get("blurScope"),h=Tu(g),c=y.getShallow("scale"),p=g.getShallow("cursor")}var v=t.getItemVisual(e,"symbolRotate");f.attr("rotation",(v||0)*Math.PI/180||0);var m=xd(t.getItemVisual(e,"symbolOffset"),n);m&&(f.x=m[0],f.y=m[1]),p&&f.attr("cursor",p);var _=t.getItemVisual(e,"style"),x=_.fill;if(f instanceof Na){var b=f.style;f.useStyle(T({image:b.image,x:b.x,y:b.y,width:b.width,height:b.height},_))}else f.__isEmptyBrush?f.useStyle(T({},_)):f.useStyle(_),f.style.decal=null,f.setColor(x,r&&r.symbolInnerColor),f.style.strokeNoScale=!0;var w=t.getItemVisual(e,"liftZ"),S=this._z2;null!=w?null==S&&(this._z2=f.z2,f.z2+=w):null!=S&&(f.z2=S,this._z2=null);var M=r&&r.useNameLabel;Mu(f,h,{labelFetcher:d,labelDataIndex:e,defaultText:function(e){return M?t.getName(e):x_(t,e)},inheritColor:x,defaultOpacity:_.opacity}),this._sizeX=n[0]/2,this._sizeY=n[1]/2;var C=f.ensureState("emphasis");if(C.style=o,f.ensureState("select").style=s,f.ensureState("blur").style=a,c){var D=Math.max(1.1,3/this._sizeY);C.scaleX=this._sizeX*D,C.scaleY=this._sizeY*D}this.setSymbolScale(1),Bs(this,l,u)},e.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},e.prototype.fadeOut=function(t,e){var n=this.childAt(0),i=this._seriesModel,r=Ja(this).dataIndex,o=e&&e.animation;if(this.silent=n.silent=!0,e&&e.fadeLabel){var a=n.getTextContent();a&&su(a,{style:{opacity:0}},i,{dataIndex:r,removeOpt:o,cb:function(){n.removeTextContent()}})}else n.removeTextContent();su(n,{style:{opacity:0},scaleX:0,scaleY:0},i,{dataIndex:r,cb:t,removeOpt:o})},e.getSymbolSize=function(t,e){return F(n=t.getItemVisual(e,"symbolSize"))||(n=[+n,+n]),[n[0]||0,n[1]||0];var n},e}(Di);function S_(t,e){this.parent.drift(t,e)}function M_(t,e,n,i){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(i.isIgnore&&i.isIgnore(n))&&!(i.clipShape&&!i.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(n,"symbol")}function T_(t){return null==t||U(t)||(t={isIgnore:t}),t||{}}function C_(t){var e=t.hostModel,n=e.getModel("emphasis");return{emphasisItemStyle:n.getModel("itemStyle").getItemStyle(),blurItemStyle:e.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:e.getModel(["select","itemStyle"]).getItemStyle(),focus:n.get("focus"),blurScope:n.get("blurScope"),hoverScale:n.get("scale"),labelStatesModels:Tu(e),cursorStyle:e.get("cursor")}}var D_=function(){function t(t){this.group=new Di,this._SymbolCtor=t||w_}return t.prototype.updateData=function(t,e){e=T_(e);var n=this.group,i=t.hostModel,r=this._data,o=this._SymbolCtor,a=e.disableAnimation,s=C_(t),l={disableAnimation:a},u=e.getSymbolPoint||function(e){return t.getItemLayout(e)};r||n.removeAll(),t.diff(r).add((function(i){var r=u(i);if(M_(t,r,i,e)){var a=new o(t,i,s,l);a.setPosition(r),t.setItemGraphicEl(i,a),n.add(a)}})).update((function(h,c){var p=r.getItemGraphicEl(c),f=u(h);if(M_(t,f,h,e)){var d=t.getItemVisual(h,"symbol")||"circle",g=p&&p.getSymbolType&&p.getSymbolType();if(!p||g&&g!==d)n.remove(p),(p=new o(t,h,s,l)).setPosition(f);else{p.updateData(t,h,s,l);var y={x:f[0],y:f[1]};a?p.attr(y):ru(p,y,i)}n.add(p),t.setItemGraphicEl(h,p)}else n.remove(p)})).remove((function(t){var e=r.getItemGraphicEl(t);e&&e.fadeOut((function(){n.remove(e)}))})).execute(),this._getSymbolPoint=u,this._data=t},t.prototype.isPersistent=function(){return!0},t.prototype.updateLayout=function(){var t=this,e=this._data;e&&e.eachItemGraphicEl((function(e,n){var i=t._getSymbolPoint(n);e.setPosition(i),e.markRedraw()}))},t.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=C_(t),this._data=null,this.group.removeAll()},t.prototype.incrementalUpdate=function(t,e,n){function i(t){t.isGroup||(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}n=T_(n);for(var r=t.start;r<t.end;r++){var o=e.getItemLayout(r);if(M_(e,o,r,n)){var a=new this._SymbolCtor(e,r,this._seriesScope);a.traverse(i),a.setPosition(o),this.group.add(a),e.setItemGraphicEl(r,a)}}},t.prototype.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl((function(t){t.fadeOut((function(){e.remove(t)}))})):e.removeAll()},t}();function I_(t,e,n){var i=t.getBaseAxis(),r=t.getOtherAxis(i),o=function(t,e){var n=0,i=t.scale.getExtent();"start"===e?n=i[0]:"end"===e?n=i[1]:i[0]>0?n=i[0]:i[1]<0&&(n=i[1]);return n}(r,n),a=i.dim,s=r.dim,l=e.mapDimension(s),u=e.mapDimension(a),h="x"===s||"radius"===s?1:0,c=O(t.dimensions,(function(t){return e.mapDimension(t)})),p=!1,f=e.getCalculationInfo("stackResultDimension");return kv(e,c[0])&&(p=!0,c[0]=f),kv(e,c[1])&&(p=!0,c[1]=f),{dataDimsForPoint:c,valueStart:o,valueAxisDim:s,baseAxisDim:a,stacked:!!p,valueDim:l,baseDim:u,baseDataOffset:h,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function k_(t,e,n,i){var r=NaN;t.stacked&&(r=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(r)&&(r=t.valueStart);var o=t.baseDataOffset,a=[];return a[o]=n.get(t.baseDim,i),a[1-o]=r,e.dataToPoint(a)}var A_="undefined"!=typeof Float32Array,P_=A_?Float32Array:Array;function L_(t){return F(t)?A_?new Float32Array(t):t:new P_(t)}var O_=Math.min,R_=Math.max;function N_(t,e){return isNaN(t)||isNaN(e)}function E_(t,e,n,i,r,o,a,s,l){for(var u,h,c,p,f,d,g=n,y=0;y<i;y++){var v=e[2*g],m=e[2*g+1];if(g>=r||g<0)break;if(N_(v,m)){if(l){g+=o;continue}break}if(g===n)t[o>0?"moveTo":"lineTo"](v,m),c=v,p=m;else{var _=v-u,x=m-h;if(_*_+x*x<.5){g+=o;continue}if(a>0){for(var b=g+o,w=e[2*b],S=e[2*b+1];w===v&&S===m&&y<i;)y++,g+=o,w=e[2*(b+=o)],S=e[2*b+1],_=(v=e[2*g])-u,x=(m=e[2*g+1])-h;var M=y+1;if(l)for(;N_(w,S)&&M<i;)M++,w=e[2*(b+=o)],S=e[2*b+1];var T=.5,C=0,D=0,I=void 0,k=void 0;if(M>=i||N_(w,S))f=v,d=m;else{C=w-u,D=S-h;var A=v-u,P=w-v,L=m-h,O=S-m,R=void 0,N=void 0;"x"===s?(R=Math.abs(A),N=Math.abs(P),f=v-R*a,d=m,I=v+R*a,k=m):"y"===s?(R=Math.abs(L),N=Math.abs(O),f=v,d=m-R*a,I=v,k=m+R*a):(R=Math.sqrt(A*A+L*L),f=v-C*a*(1-(T=(N=Math.sqrt(P*P+O*O))/(N+R))),d=m-D*a*(1-T),k=m+D*a*T,I=O_(I=v+C*a*T,R_(w,v)),k=O_(k,R_(S,m)),I=R_(I,O_(w,v)),d=m-(D=(k=R_(k,O_(S,m)))-m)*R/N,f=O_(f=v-(C=I-v)*R/N,R_(u,v)),d=O_(d,R_(h,m)),I=v+(C=v-(f=R_(f,O_(u,v))))*N/R,k=m+(D=m-(d=R_(d,O_(h,m))))*N/R)}t.bezierCurveTo(c,p,f,d,v,m),c=I,p=k}else t.lineTo(v,m)}u=v,h=m,g+=o}return y}var z_=function(){this.smooth=0,this.smoothConstraint=!0},B_=function(t){function e(e){var n=t.call(this,e)||this;return n.type="ec-polyline",n}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new z_},e.prototype.buildPath=function(t,e){var n=e.points,i=0,r=n.length/2;if(e.connectNulls){for(;r>0&&N_(n[2*r-2],n[2*r-1]);r--);for(;i<r&&N_(n[2*i],n[2*i+1]);i++);}for(;i<r;)i+=E_(t,n,i,r,r,1,e.smooth,e.smoothMonotone,e.connectNulls)+1},e.prototype.getPointOn=function(t,e){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var n,i,r=this.path.data,o=ha.CMD,a="x"===e,s=[],l=0;l<r.length;){var u=void 0,h=void 0,c=void 0,p=void 0,f=void 0,d=void 0,g=void 0;switch(r[l++]){case o.M:n=r[l++],i=r[l++];break;case o.L:if(u=r[l++],h=r[l++],(g=a?(t-n)/(u-n):(t-i)/(h-i))<=1&&g>=0){var y=a?(h-i)*g+i:(u-n)*g+n;return a?[t,y]:[y,t]}n=u,i=h;break;case o.C:u=r[l++],h=r[l++],c=r[l++],p=r[l++],f=r[l++],d=r[l++];var v=a?bo(n,u,c,f,t,s):bo(i,h,p,d,t,s);if(v>0)for(var m=0;m<v;m++){var _=s[m];if(_<=1&&_>=0){y=a?_o(i,h,p,d,_):_o(n,u,c,f,_);return a?[t,y]:[y,t]}}n=f,i=d}}},e}(Aa),F_=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(z_),V_=function(t){function e(e){var n=t.call(this,e)||this;return n.type="ec-polygon",n}return n(e,t),e.prototype.getDefaultShape=function(){return new F_},e.prototype.buildPath=function(t,e){var n=e.points,i=e.stackedOnPoints,r=0,o=n.length/2,a=e.smoothMonotone;if(e.connectNulls){for(;o>0&&N_(n[2*o-2],n[2*o-1]);o--);for(;r<o&&N_(n[2*r],n[2*r+1]);r++);}for(;r<o;){var s=E_(t,n,r,o,o,1,e.smooth,a,e.connectNulls);E_(t,i,r+s-1,s,o,-1,e.stackedOnSmooth,a,e.connectNulls),r+=s+1,t.closePath()}},e}(Aa);function H_(t,e,n,i,r){var o=t.getArea(),a=o.x,s=o.y,l=o.width,u=o.height,h=n.get(["lineStyle","width"])||2;a-=h/2,s-=h/2,l+=h,u+=h,a=Math.floor(a),l=Math.round(l);var c=new Va({shape:{x:a,y:s,width:l,height:u}});if(e){var p=t.getBaseAxis(),f=p.isHorizontal(),d=p.inverse;f?(d&&(c.shape.x+=l),c.shape.width=0):(d||(c.shape.y+=u),c.shape.height=0),ou(c,{shape:{width:l,height:u,x:a,y:s}},n,null,i,"function"==typeof r?function(t){r(t,c)}:null)}return c}function G_(t,e,n){var i=t.getArea(),r=Bi(i.r0,1),o=Bi(i.r,1),a=new Dl({shape:{cx:Bi(t.cx,1),cy:Bi(t.cy,1),r0:r,r:o,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});e&&("angle"===t.getBaseAxis().dim?a.shape.endAngle=i.startAngle:a.shape.r=r,ou(a,{shape:{endAngle:i.endAngle,r:o}},n));return a}function W_(t,e){return t.type===e}function U_(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++)if(t[n]!==e[n])return;return!0}}function X_(t){for(var e=1/0,n=1/0,i=-1/0,r=-1/0,o=0;o<t.length;){var a=t[o++],s=t[o++];isNaN(a)||(e=Math.min(a,e),i=Math.max(a,i)),isNaN(s)||(n=Math.min(s,n),r=Math.max(s,r))}return[[e,n],[i,r]]}function Y_(t,e){var n=X_(t),i=n[0],r=n[1],o=X_(e),a=o[0],s=o[1];return Math.max(Math.abs(i[0]-a[0]),Math.abs(i[1]-a[1]),Math.abs(r[0]-s[0]),Math.abs(r[1]-s[1]))}function q_(t){return"number"==typeof t?t:t?.5:0}function j_(t,e,n){for(var i=e.getBaseAxis(),r="x"===i.dim||"radius"===i.dim?0:1,o=[],a=0,s=[],l=[],u=[];a<t.length-2;a+=2)switch(u[0]=t[a+2],u[1]=t[a+3],l[0]=t[a],l[1]=t[a+1],o.push(l[0],l[1]),n){case"end":s[r]=u[r],s[1-r]=l[1-r],o.push(s[0],s[1]);break;case"middle":var h=(l[r]+u[r])/2,c=[];s[r]=c[r]=h,s[1-r]=l[1-r],c[1-r]=u[1-r],o.push(s[0],s[1]),o.push(c[0],c[1]);break;default:s[r]=l[r],s[1-r]=u[1-r],o.push(s[0],s[1])}return o.push(t[a++],t[a++]),o}function Z_(t,e,n){var i=t.getVisual("visualMeta");if(i&&i.length&&t.count()&&"cartesian2d"===e.type){for(var r,o,a=i.length-1;a>=0;a--){var s=t.getDimensionInfo(i[a].dimension);if("x"===(r=s&&s.coordDim)||"y"===r){o=i[a];break}}if(o){var l=e.getAxis(r),u=O(o.stops,(function(t){return{coord:l.toGlobalCoord(l.dataToCoord(t.value)),color:t.color}})),h=u.length,c=o.outerColors.slice();h&&u[0].coord>u[h-1].coord&&(u.reverse(),c.reverse());var p=function(t,e){var n,i,r=[],o=t.length;function a(t,e,n){var i=t.coord;return{coord:n,color:Ve((n-i)/(e.coord-i),[t.color,e.color])}}for(var s=0;s<o;s++){var l=t[s],u=l.coord;if(u<0)n=l;else{if(u>e){i?r.push(a(i,l,e)):n&&r.push(a(n,l,0),a(n,l,e));break}n&&(r.push(a(n,l,0)),n=null),r.push(l),i=l}}return r}(u,"x"===r?n.getWidth():n.getHeight()),f=p.length;if(!f&&h)return u[0].coord<0?c[1]?c[1]:u[h-1].color:c[0]?c[0]:u[0].color;var d=p[0].coord-10,g=p[f-1].coord+10,y=g-d;if(y<.001)return"transparent";L(p,(function(t){t.offset=(t.coord-d)/y})),p.push({offset:f?p[f-1].offset:.5,color:c[1]||"transparent"}),p.unshift({offset:f?p[0].offset:.5,color:c[0]||"transparent"});var v=new ql(0,0,0,0,p,!0);return v[r]=d,v[r+"2"]=g,v}}}function K_(t,e,n){var i=t.get("showAllSymbol"),r="auto"===i;if(!i||r){var o=n.getAxesByScale("ordinal")[0];if(o&&(!r||!function(t,e){var n=t.getExtent(),i=Math.abs(n[1]-n[0])/t.scale.count();isNaN(i)&&(i=0);for(var r=e.count(),o=Math.max(1,Math.round(r/5)),a=0;a<r;a+=o)if(1.5*w_.getSymbolSize(e,a)[t.isHorizontal()?1:0]>i)return!1;return!0}(o,e))){var a=e.mapDimension(o.dim),s={};return L(o.getViewLabels(),(function(t){var e=o.scale.getRawOrdinalNumber(t.tickValue);s[e]=1})),function(t){return!s.hasOwnProperty(e.get(a,t))}}}}function $_(t,e){return[t[2*e],t[2*e+1]]}function J_(t){if(t.get(["endLabel","show"]))return!0;for(var e=0;e<ns.length;e++)if(t.get([ns[e],"endLabel","show"]))return!0;return!1}function Q_(t,e,n,i){if(W_(e,"cartesian2d")){var r=i.getModel("endLabel"),o=r.get("valueAnimation"),a=i.getData(),s={lastFrameIndex:0},l=J_(i)?function(n,i){t._endLabelOnDuring(n,i,a,s,o,r,e)}:null,u=e.getBaseAxis().isHorizontal(),h=H_(e,n,i,(function(){var e=t._endLabel;e&&n&&null!=s.originalX&&e.attr({x:s.originalX,y:s.originalY})}),l);if(!i.get("clip",!0)){var c=h.shape,p=Math.max(c.width,c.height);u?(c.y-=p,c.height+=2*p):(c.x-=p,c.width+=2*p)}return l&&l(1,h),h}return G_(e,n,i)}var tx=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.init=function(){var t=new Di,e=new D_;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},e.prototype.render=function(t,e,n){var i=this,r=t.coordinateSystem,o=this.group,a=t.getData(),s=t.getModel("lineStyle"),l=t.getModel("areaStyle"),u=a.getLayout("points")||[],h="polar"===r.type,c=this._coordSys,p=this._symbolDraw,f=this._polyline,d=this._polygon,g=this._lineGroup,y=t.get("animation"),v=!l.isEmpty(),m=l.get("origin"),_=I_(r,a,m),x=v&&function(t,e,n){if(!n.valueDim)return[];for(var i=e.count(),r=L_(2*i),o=0;o<i;o++){var a=k_(n,t,e,o);r[2*o]=a[0],r[2*o+1]=a[1]}return r}(r,a,_),b=t.get("showSymbol"),w=b&&!h&&K_(t,a,r),S=this._data;S&&S.eachItemGraphicEl((function(t,e){t.__temp&&(o.remove(t),S.setItemGraphicEl(e,null))})),b||p.remove(),o.add(g);var M,T=!h&&t.get("step");r&&r.getArea&&t.get("clip",!0)&&(null!=(M=r.getArea()).width?(M.x-=.1,M.y-=.1,M.width+=.2,M.height+=.2):M.r0&&(M.r0-=.5,M.r+=.5)),this._clipShapeForSymbol=M;var D=Z_(a,r,n)||a.getVisual("style")[a.getVisual("drawType")];if(f&&c.type===r.type&&T===this._step){v&&!d?d=this._newPolygon(u,x):d&&!v&&(g.remove(d),d=this._polygon=null),h||this._initOrUpdateEndLabel(t,r,Bh(D));var I=g.getClipPath();if(I)ou(I,{shape:Q_(this,r,!1,t).shape},t);else g.setClipPath(Q_(this,r,!0,t));b&&p.updateData(a,{isIgnore:w,clipShape:M,disableAnimation:!0,getSymbolPoint:function(t){return[u[2*t],u[2*t+1]]}}),U_(this._stackedOnPoints,x)&&U_(this._points,u)||(y?this._doUpdateAnimation(a,x,r,n,T,m):(T&&(u=j_(u,r,T),x&&(x=j_(x,r,T))),f.setShape({points:u}),d&&d.setShape({points:u,stackedOnPoints:x})))}else b&&p.updateData(a,{isIgnore:w,clipShape:M,disableAnimation:!0,getSymbolPoint:function(t){return[u[2*t],u[2*t+1]]}}),y&&this._initSymbolLabelAnimation(a,r,M),T&&(u=j_(u,r,T),x&&(x=j_(x,r,T))),f=this._newPolyline(u),v&&(d=this._newPolygon(u,x)),h||this._initOrUpdateEndLabel(t,r,Bh(D)),g.setClipPath(Q_(this,r,!0,t));var k=t.get(["emphasis","focus"]),A=t.get(["emphasis","blurScope"]);(f.useStyle(C(s.getLineStyle(),{fill:"none",stroke:D,lineJoin:"bevel"})),Hs(f,t,"lineStyle"),f.style.lineWidth>0&&"bolder"===t.get(["emphasis","lineStyle","width"]))&&(f.getState("emphasis").style.lineWidth=+f.style.lineWidth+1);Ja(f).seriesIndex=t.seriesIndex,Bs(f,k,A);var P=q_(t.get("smooth")),L=t.get("smoothMonotone"),O=t.get("connectNulls");if(f.setShape({smooth:P,smoothMonotone:L,connectNulls:O}),d){var R=a.getCalculationInfo("stackedOnSeries"),N=0;d.useStyle(C(l.getAreaStyle(),{fill:D,opacity:.7,lineJoin:"bevel",decal:a.getVisual("style").decal})),R&&(N=q_(R.get("smooth"))),d.setShape({smooth:P,stackedOnSmooth:N,smoothMonotone:L,connectNulls:O}),Hs(d,t,"areaStyle"),Ja(d).seriesIndex=t.seriesIndex,Bs(d,k,A)}var E=function(t){i._changePolyState(t)};a.eachItemGraphicEl((function(t){t&&(t.onHoverStateChange=E)})),this._polyline.onHoverStateChange=E,this._data=a,this._coordSys=r,this._stackedOnPoints=x,this._points=u,this._step=T,this._valueOrigin=m,t.get("triggerLineEvent")&&(this.packEventData(t,f),d&&this.packEventData(t,d))},e.prototype.packEventData=function(t,e){Ja(e).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},e.prototype.highlight=function(t,e,n,i){var r=t.getData(),o=gr(r,i);if(this._changePolyState("emphasis"),!(o instanceof Array)&&null!=o&&o>=0){var a=r.getLayout("points"),s=r.getItemGraphicEl(o);if(!s){var l=a[2*o],u=a[2*o+1];if(isNaN(l)||isNaN(u))return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(l,u))return;var h=t.get("zlevel"),c=t.get("z");(s=new w_(r,o)).x=l,s.y=u,s.setZ(h,c);var p=s.getSymbolPath().getTextContent();p&&(p.zlevel=h,p.z=c,p.z2=this._polyline.z2+1),s.__temp=!0,r.setItemGraphicEl(o,s),s.stopSymbolAnimation(!0),this.group.add(s)}s.highlight()}else _f.prototype.highlight.call(this,t,e,n,i)},e.prototype.downplay=function(t,e,n,i){var r=t.getData(),o=gr(r,i);if(this._changePolyState("normal"),null!=o&&o>=0){var a=r.getItemGraphicEl(o);a&&(a.__temp?(r.setItemGraphicEl(o,null),this.group.remove(a)):a.downplay())}else _f.prototype.downplay.call(this,t,e,n,i)},e.prototype._changePolyState=function(t){var e=this._polygon;bs(this._polyline,t),e&&bs(e,t)},e.prototype._newPolyline=function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new B_({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(e),this._polyline=e,e},e.prototype._newPolygon=function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new V_({shape:{points:t,stackedOnPoints:e},segmentIgnoreThreshold:2}),this._lineGroup.add(n),this._polygon=n,n},e.prototype._initSymbolLabelAnimation=function(t,e,n){var i,r,o=e.getBaseAxis(),a=o.inverse;"cartesian2d"===e.type?(i=o.isHorizontal(),r=!1):"polar"===e.type&&(i="angle"===o.dim,r=!0);var s=t.hostModel,l=s.get("animationDuration");"function"==typeof l&&(l=l(null));var u=s.get("animationDelay")||0,h="function"==typeof u?u(null):u;t.eachItemGraphicEl((function(t,o){var s=t;if(s){var c=[t.x,t.y],p=void 0,f=void 0,d=void 0;if(n)if(r){var g=n,y=e.pointToCoord(c);i?(p=g.startAngle,f=g.endAngle,d=-y[1]/180*Math.PI):(p=g.r0,f=g.r,d=y[0])}else{var v=n;i?(p=v.x,f=v.x+v.width,d=t.x):(p=v.y+v.height,f=v.y,d=t.y)}var m=f===p?0:(d-p)/(f-p);a&&(m=1-m);var _="function"==typeof u?u(o):l*m+h,x=s.getSymbolPath(),b=x.getTextContent();s.attr({scaleX:0,scaleY:0}),s.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:_}),b&&b.animateFrom({style:{opacity:0}},{duration:300,delay:_}),x.disableLabelAnimation=!0}}))},e.prototype._initOrUpdateEndLabel=function(t,e,n){var i=t.getModel("endLabel");if(J_(t)){var r=t.getData(),o=this._polyline,a=this._endLabel;a||((a=this._endLabel=new Wa({z2:200})).ignoreClip=!0,o.setTextContent(this._endLabel),o.disableLabelAnimation=!0);var s=function(t){for(var e,n,i=t.length/2;i>0&&(e=t[2*i-2],n=t[2*i-1],isNaN(e)||isNaN(n));i--);return i-1}(r.getLayout("points"));s>=0&&(Mu(o,Tu(t,"endLabel"),{inheritColor:n,labelFetcher:t,labelDataIndex:s,defaultText:function(t,e,n){return null!=n?b_(r,n):x_(r,t)},enableTextSetter:!0},function(t,e){var n=e.getBaseAxis(),i=n.isHorizontal(),r=n.inverse,o=i?r?"right":"left":"center",a=i?"middle":r?"top":"bottom";return{normal:{align:t.get("align")||o,verticalAlign:t.get("verticalAlign")||a}}}(i,e)),o.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},e.prototype._endLabelOnDuring=function(t,e,n,i,r,o,a){var s=this._endLabel,l=this._polyline;if(s){t<1&&null==i.originalX&&(i.originalX=s.x,i.originalY=s.y);var u=n.getLayout("points"),h=n.hostModel,c=h.get("connectNulls"),p=o.get("precision"),f=o.get("distance")||0,d=a.getBaseAxis(),g=d.isHorizontal(),y=d.inverse,v=e.shape,m=y?g?v.x:v.y+v.height:g?v.x+v.width:v.y,_=(g?f:0)*(y?-1:1),x=(g?0:-f)*(y?-1:1),b=g?"x":"y",w=function(t,e,n){for(var i,r,o=t.length/2,a="x"===n?0:1,s=0,l=-1,u=0;u<o;u++)if(r=t[2*u+a],!isNaN(r)&&!isNaN(t[2*u+1-a]))if(0!==u){if(i<=e&&r>=e||i>=e&&r<=e){l=u;break}s=u,i=r}else i=r;return{range:[s,l],t:(e-i)/(r-i)}}(u,m,b),S=w.range,M=S[1]-S[0],T=void 0;if(M>=1){if(M>1&&!c){var C=$_(u,S[0]);s.attr({x:C[0]+_,y:C[1]+x}),r&&(T=h.getRawValue(S[0]))}else{(C=l.getPointOn(m,b))&&s.attr({x:C[0]+_,y:C[1]+x});var D=h.getRawValue(S[0]),I=h.getRawValue(S[1]);r&&(T=function(t,e,n,i,r){var o=null==e||"auto"===e;if(null==i)return i;if("number"==typeof i)return Bi(d=Ye(n||0,i,r),o?Math.max(Fi(n||0),Fi(i)):e);if("string"==typeof i)return r<1?n:i;for(var a=[],s=n,l=i,u=Math.max(s?s.length:0,l.length),h=0;h<u;++h){var c=t.getDimensionInfo(h);if(c&&"ordinal"===c.type)a[h]=(r<1&&s?s:l)[h];else{var p=s&&s[h]?s[h]:0,f=l[h],d=Ye(p,f,r);a[h]=Bi(d,o?Math.max(Fi(p),Fi(f)):e)}}return a}(n,p,D,I,w.t))}i.lastFrameIndex=S[0]}else{var k=1===t||i.lastFrameIndex>0?S[0]:0;C=$_(u,k);r&&(T=h.getRawValue(k)),s.attr({x:C[0]+_,y:C[1]+x})}r&&Lu(s).setLabelText(T)}},e.prototype._doUpdateAnimation=function(t,e,n,i,r,o){var a=this._polyline,s=this._polygon,l=t.hostModel,u=function(t,e,n,i,r,o,a,s){for(var l=function(t,e){var n=[];return e.diff(t).add((function(t){n.push({cmd:"+",idx:t})})).update((function(t,e){n.push({cmd:"=",idx:e,idx1:t})})).remove((function(t){n.push({cmd:"-",idx:t})})).execute(),n}(t,e),u=[],h=[],c=[],p=[],f=[],d=[],g=[],y=I_(r,e,a),v=t.getLayout("points")||[],m=e.getLayout("points")||[],_=0;_<l.length;_++){var x=l[_],b=!0,w=void 0,S=void 0;switch(x.cmd){case"=":w=2*x.idx,S=2*x.idx1;var M=v[w],T=v[w+1],C=m[S],D=m[S+1];(isNaN(M)||isNaN(T))&&(M=C,T=D),u.push(M,T),h.push(C,D),c.push(n[w],n[w+1]),p.push(i[S],i[S+1]),g.push(e.getRawIndex(x.idx1));break;case"+":var I=x.idx,k=y.dataDimsForPoint,A=r.dataToPoint([e.get(k[0],I),e.get(k[1],I)]);S=2*I,u.push(A[0],A[1]),h.push(m[S],m[S+1]);var P=k_(y,r,e,I);c.push(P[0],P[1]),p.push(i[S],i[S+1]),g.push(e.getRawIndex(I));break;case"-":b=!1}b&&(f.push(x),d.push(d.length))}d.sort((function(t,e){return g[t]-g[e]}));var L=u.length,O=L_(L),R=L_(L),N=L_(L),E=L_(L),z=[];for(_=0;_<d.length;_++){var B=d[_],F=2*_,V=2*B;O[F]=u[V],O[F+1]=u[V+1],R[F]=h[V],R[F+1]=h[V+1],N[F]=c[V],N[F+1]=c[V+1],E[F]=p[V],E[F+1]=p[V+1],z[_]=f[B]}return{current:O,next:R,stackedOnCurrent:N,stackedOnNext:E,status:z}}(this._data,t,this._stackedOnPoints,e,this._coordSys,0,this._valueOrigin),h=u.current,c=u.stackedOnCurrent,p=u.next,f=u.stackedOnNext;if(r&&(h=j_(u.current,n,r),c=j_(u.stackedOnCurrent,n,r),p=j_(u.next,n,r),f=j_(u.stackedOnNext,n,r)),Y_(h,p)>3e3||s&&Y_(c,f)>3e3)return a.stopAnimation(),a.setShape({points:p}),void(s&&(s.stopAnimation(),s.setShape({points:p,stackedOnPoints:f})));a.shape.__points=u.current,a.shape.points=h;var d={shape:{points:p}};u.current!==h&&(d.shape.__points=u.next),a.stopAnimation(),ru(a,d,l),s&&(s.setShape({points:h,stackedOnPoints:c}),s.stopAnimation(),ru(s,{shape:{stackedOnPoints:f}},l),a.shape.points!==s.shape.points&&(s.shape.points=a.shape.points));for(var g=[],y=u.status,v=0;v<y.length;v++){if("="===y[v].cmd){var m=t.getItemGraphicEl(y[v].idx1);m&&g.push({el:m,ptIdx:v})}}a.animators&&a.animators.length&&a.animators[0].during((function(){s&&s.dirtyShape();for(var t=a.shape.__points,e=0;e<g.length;e++){var n=g[e].el,i=2*g[e].ptIdx;n.x=t[i],n.y=t[i+1],n.markRedraw()}}))},e.prototype.remove=function(t){var e=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl((function(t,i){t.__temp&&(e.remove(t),n.setItemGraphicEl(i,null))})),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},e.type="line",e}(_f);var ex={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},nearest:function(t){return t[0]}},nx=function(t){return Math.round(t.length/2)};function ix(t){return{seriesType:t,reset:function(t,e,n){var i=t.getData(),r=t.get("sampling"),o=t.coordinateSystem,a=i.count();if(a>10&&"cartesian2d"===o.type&&r){var s=o.getBaseAxis(),l=o.getOtherAxis(s),u=s.getExtent(),h=n.getDevicePixelRatio(),c=Math.abs(u[1]-u[0])*(h||1),p=Math.round(a/c);if(p>1){"lttb"===r&&t.setData(i.lttbDownSample(i.mapDimension(l.dim),1/p));var f=void 0;"string"==typeof r?f=ex[r]:"function"==typeof r&&(f=r),f&&t.setData(i.downSample(i.mapDimension(l.dim),1/p,f,nx))}}}}}var rx=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return n(e,t),e.prototype.getInitialData=function(t,e){return Pv(null,this,{useEncodeDefaulter:!0})},e.prototype.getMarkerPosition=function(t){var e=this.coordinateSystem;if(e&&e.clampData){var n=e.dataToPoint(e.clampData(t)),i=this.getData(),r=i.getLayout("offset"),o=i.getLayout("size");return n[e.getBaseAxis().isHorizontal()?0:1]+=r+o/2,n}return[NaN,NaN]},e.type="series.__base_bar__",e.defaultOption={zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"},e}(sf);sf.registerClass(rx);var ox=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}var i,r;return n(e,t),e.prototype.getInitialData=function(){return Pv(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},e.prototype.getProgressive=function(){return!!this.get("large")&&this.get("progressive")},e.prototype.getProgressiveThreshold=function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return e>t&&(t=e),t},e.prototype.brushSelector=function(t,e,n){return n.rect(e.getItemLayout(t))},e.type="series.bar",e.dependencies=["grid","polar"],e.defaultOption=(i=rx.defaultOption,r={clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1},M(M({},i,!0),r,!0)),e}(rx),ax=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},sx=function(t){function e(e){var n=t.call(this,e)||this;return n.type="sausage",n}return n(e,t),e.prototype.getDefaultShape=function(){return new ax},e.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=.5*(o-r),s=r+a,l=e.startAngle,u=e.endAngle,h=e.clockwise,c=Math.cos(l),p=Math.sin(l),f=Math.cos(u),d=Math.sin(u);(h?u-l<2*Math.PI:l-u<2*Math.PI)&&(t.moveTo(c*r+n,p*r+i),t.arc(c*s+n,p*s+i,a,-Math.PI+l,l,!h)),t.arc(n,i,o,l,u,!h),t.moveTo(f*o+n,d*o+i),t.arc(f*s+n,d*s+i,a,u-2*Math.PI,u-Math.PI,!h),0!==r&&(t.arc(n,i,r,u,l,h),t.moveTo(c*r+n,d*r+i)),t.closePath()},e}(Aa);function lx(t,e,n){return e*Math.sin(t)*(n?-1:1)}function ux(t,e,n){return e*Math.cos(t)*(n?1:-1)}var hx=[0,0],cx=Math.max,px=Math.min;var fx=function(t){function e(){var n=t.call(this)||this;return n.type=e.type,n._isFirstFrame=!0,n}return n(e,t),e.prototype.render=function(t,e,n,i){this._model=t,this._removeOnRenderedListener(n),this._updateDrawMode(t);var r=t.get("coordinateSystem");("cartesian2d"===r||"polar"===r)&&(this._isLargeDraw?this._renderLarge(t,e,n):this._renderNormal(t,e,n,i))},e.prototype.incrementalPrepareRender=function(t){this._clear(),this._updateDrawMode(t),this._updateLargeClip(t)},e.prototype.incrementalRender=function(t,e){this._incrementalRenderLarge(t,e)},e.prototype._updateDrawMode=function(t){var e=t.pipelineContext.large;null!=this._isLargeDraw&&e===this._isLargeDraw||(this._isLargeDraw=e,this._clear())},e.prototype._renderNormal=function(t,e,n,i){var r,o=this.group,a=t.getData(),s=this._data,l=t.coordinateSystem,u=l.getBaseAxis();"cartesian2d"===l.type?r=u.isHorizontal():"polar"===l.type&&(r="angle"===u.dim);var h=t.isAnimationEnabled()?t:null,c=function(t,e){var n=t.get("realtimeSort",!0),i=e.getBaseAxis();0;if(n&&"category"===i.type&&"cartesian2d"===e.type)return{baseAxis:i,otherAxis:e.getOtherAxis(i)}}(t,l);c&&this._enableRealtimeSort(c,a,n);var p=t.get("clip",!0)||c,f=function(t,e){var n=t.getArea&&t.getArea();if(W_(t,"cartesian2d")){var i=t.getBaseAxis();if("category"!==i.type||!i.onBand){var r=e.getLayout("bandWidth");i.isHorizontal()?(n.x-=r,n.width+=2*r):(n.y-=r,n.height+=2*r)}}return n}(l,a);o.removeClipPath();var d=t.get("roundCap",!0),g=t.get("showBackground",!0),y=t.getModel("backgroundStyle"),v=y.get("borderRadius")||0,m=[],_=this._backgroundEls,x=i&&i.isInitSort,b=i&&"changeAxisOrder"===i.type;function w(t){var e=bx[l.type](a,t),n=function(t,e,n){return new("polar"===t.type?Dl:Va)({shape:kx(e,n,t),silent:!0,z2:0})}(l,r,e);return n.useStyle(y.getItemStyle()),"cartesian2d"===l.type&&n.setShape("r",v),m[t]=n,n}a.diff(s).add((function(e){var n=a.getItemModel(e),i=bx[l.type](a,e,n);if(g&&w(e),a.hasValue(e)&&xx[l.type](i)){var s=!1;p&&(s=dx[l.type](f,i));var y=gx[l.type](t,a,e,i,r,h,u.model,!1,d);c&&(y.forceLabelAnimation=!0),Mx(y,a,e,n,i,t,r,"polar"===l.type),x?y.attr({shape:i}):c?yx(c,h,y,i,e,r,!1,!1):ou(y,{shape:i},t,e),a.setItemGraphicEl(e,y),o.add(y),y.ignore=s}})).update((function(e,n){var i=a.getItemModel(e),S=bx[l.type](a,e,i);if(g){var M=void 0;0===_.length?M=w(n):((M=_[n]).useStyle(y.getItemStyle()),"cartesian2d"===l.type&&M.setShape("r",v),m[e]=M);var T=bx[l.type](a,e);ru(M,{shape:kx(r,T,l)},h,e)}var C=s.getItemGraphicEl(n);if(a.hasValue(e)&&xx[l.type](S)){var D=!1;if(p&&(D=dx[l.type](f,S))&&o.remove(C),C?hu(C):C=gx[l.type](t,a,e,S,r,h,u.model,!!C,d),c&&(C.forceLabelAnimation=!0),b){var I=C.getTextContent();if(I){var k=Lu(I);null!=k.prevValue&&(k.prevValue=k.value)}}b||Mx(C,a,e,i,S,t,r,"polar"===l.type),x?C.attr({shape:S}):c?yx(c,h,C,S,e,r,!0,b):ru(C,{shape:S},t,e,null),a.setItemGraphicEl(e,C),C.ignore=D,o.add(C)}else o.remove(C)})).remove((function(e){var n=s.getItemGraphicEl(e);n&&uu(n,t,e)})).execute();var S=this._backgroundGroup||(this._backgroundGroup=new Di);S.removeAll();for(var M=0;M<m.length;++M)S.add(m[M]);o.add(S),this._backgroundEls=m,this._data=a},e.prototype._renderLarge=function(t,e,n){this._clear(),Dx(t,this.group),this._updateLargeClip(t)},e.prototype._incrementalRenderLarge=function(t,e){this._removeBackground(),Dx(e,this.group,!0)},e.prototype._updateLargeClip=function(t){var e=t.get("clip",!0)?function(t,e,n,i,r){return t?"polar"===t.type?G_(t,e,n):"cartesian2d"===t.type?H_(t,e,n,i,r):null:null}(t.coordinateSystem,!1,t):null;e?this.group.setClipPath(e):this.group.removeClipPath()},e.prototype._enableRealtimeSort=function(t,e,n){var i=this;if(e.count()){var r=t.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(e,t,n),this._isFirstFrame=!1;else{var o=function(t){var n=e.getItemGraphicEl(t);if(n){var i=n.shape;return(r.isHorizontal()?Math.abs(i.height):Math.abs(i.width))||0}return 0};this._onRendered=function(){i._updateSortWithinSameData(e,o,r,n)},n.getZr().on("rendered",this._onRendered)}}},e.prototype._dataSort=function(t,e,n){var i=[];return t.each(t.mapDimension(e.dim),(function(t,e){var r=n(e);r=null==r?NaN:r,i.push({dataIndex:e,mappedValue:r,ordinalNumber:t})})),i.sort((function(t,e){return e.mappedValue-t.mappedValue})),{ordinalNumbers:O(i,(function(t){return t.ordinalNumber}))}},e.prototype._isOrderChangedWithinSameData=function(t,e,n){for(var i=n.scale,r=t.mapDimension(n.dim),o=Number.MAX_VALUE,a=0,s=i.getOrdinalMeta().categories.length;a<s;++a){var l=t.rawIndexOf(r,i.getRawOrdinalNumber(a)),u=l<0?Number.MIN_VALUE:e(t.indexOfRawIndex(l));if(u>o)return!0;o=u}return!1},e.prototype._isOrderDifferentInView=function(t,e){for(var n=e.scale,i=n.getExtent(),r=Math.max(0,i[0]),o=Math.min(i[1],n.getOrdinalMeta().categories.length-1);r<=o;++r)if(t.ordinalNumbers[r]!==n.getRawOrdinalNumber(r))return!0},e.prototype._updateSortWithinSameData=function(t,e,n,i){if(this._isOrderChangedWithinSameData(t,e,n)){var r=this._dataSort(t,n,e);this._isOrderDifferentInView(r,n)&&(this._removeOnRenderedListener(i),i.dispatchAction({type:"changeAxisOrder",componentType:n.dim+"Axis",axisId:n.index,sortInfo:r}))}},e.prototype._dispatchInitSort=function(t,e,n){var i=e.baseAxis,r=this._dataSort(t,i,(function(n){return t.get(t.mapDimension(e.otherAxis.dim),n)}));n.dispatchAction({type:"changeAxisOrder",componentType:i.dim+"Axis",isInitSort:!0,axisId:i.index,sortInfo:r})},e.prototype.remove=function(t,e){this._clear(this._model),this._removeOnRenderedListener(e)},e.prototype.dispose=function(t,e){this._removeOnRenderedListener(e)},e.prototype._removeOnRenderedListener=function(t){this._onRendered&&(t.getZr().off("rendered",this._onRendered),this._onRendered=null)},e.prototype._clear=function(t){var e=this.group,n=this._data;t&&t.isAnimationEnabled()&&n&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],n.eachItemGraphicEl((function(e){uu(e,t,Ja(e).dataIndex)}))):e.removeAll(),this._data=null,this._isFirstFrame=!0},e.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},e.type="bar",e}(_f),dx={cartesian2d:function(t,e){var n=e.width<0?-1:1,i=e.height<0?-1:1;n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height);var r=t.x+t.width,o=t.y+t.height,a=cx(e.x,t.x),s=px(e.x+e.width,r),l=cx(e.y,t.y),u=px(e.y+e.height,o),h=s<a,c=u<l;return e.x=h&&a>r?s:a,e.y=c&&l>o?u:l,e.width=h?0:s-a,e.height=c?0:u-l,n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height),h||c},polar:function(t,e){var n=e.r0<=e.r?1:-1;if(n<0){var i=e.r;e.r=e.r0,e.r0=i}var r=px(e.r,t.r),o=cx(e.r0,t.r0);e.r=r,e.r0=o;var a=r-o<0;if(n<0){i=e.r;e.r=e.r0,e.r0=i}return a}},gx={cartesian2d:function(t,e,n,i,r,o,a,s,l){var u=new Va({shape:T({},i),z2:1});(u.__dataIndex=n,u.name="item",o)&&(u.shape[r?"height":"width"]=0);return u},polar:function(t,e,n,i,r,o,a,s,l){var u=!r&&l?sx:Dl,h=new u({shape:i,z2:1});h.name="item";var c,p,f=Sx(r);if(h.calculateTextPosition=(c=f,p=({isRoundCap:u===sx}||{}).isRoundCap,function(t,e,n){var i=e.position;if(!i||i instanceof Array)return yi(t,e,n);var r=c(i),o=null!=e.distance?e.distance:5,a=this.shape,s=a.cx,l=a.cy,u=a.r,h=a.r0,f=(u+h)/2,d=a.startAngle,g=a.endAngle,y=(d+g)/2,v=p?Math.abs(u-h)/2:0,m=Math.cos,_=Math.sin,x=s+u*m(d),b=l+u*_(d),w="left",S="top";switch(r){case"startArc":x=s+(h-o)*m(y),b=l+(h-o)*_(y),w="center",S="top";break;case"insideStartArc":x=s+(h+o)*m(y),b=l+(h+o)*_(y),w="center",S="bottom";break;case"startAngle":x=s+f*m(d)+lx(d,o+v,!1),b=l+f*_(d)+ux(d,o+v,!1),w="right",S="middle";break;case"insideStartAngle":x=s+f*m(d)+lx(d,-o+v,!1),b=l+f*_(d)+ux(d,-o+v,!1),w="left",S="middle";break;case"middle":x=s+f*m(y),b=l+f*_(y),w="center",S="middle";break;case"endArc":x=s+(u+o)*m(y),b=l+(u+o)*_(y),w="center",S="bottom";break;case"insideEndArc":x=s+(u-o)*m(y),b=l+(u-o)*_(y),w="center",S="top";break;case"endAngle":x=s+f*m(g)+lx(g,o+v,!0),b=l+f*_(g)+ux(g,o+v,!0),w="left",S="middle";break;case"insideEndAngle":x=s+f*m(g)+lx(g,-o+v,!0),b=l+f*_(g)+ux(g,-o+v,!0),w="right",S="middle";break;default:return yi(t,e,n)}return(t=t||{}).x=x,t.y=b,t.align=w,t.verticalAlign=S,t}),o){var d=r?"r":"endAngle",g={};h.shape[d]=r?0:i.startAngle,g[d]=i[d],(s?ru:ou)(h,{shape:g},o)}return h}};function yx(t,e,n,i,r,o,a,s){var l,u;o?(u={x:i.x,width:i.width},l={y:i.y,height:i.height}):(u={y:i.y,height:i.height},l={x:i.x,width:i.width}),s||(a?ru:ou)(n,{shape:l},e,r,null),(a?ru:ou)(n,{shape:u},e?t.baseAxis.model:null,r)}function vx(t,e){for(var n=0;n<e.length;n++)if(!isFinite(t[e[n]]))return!0;return!1}var mx=["x","y","width","height"],_x=["cx","cy","r","startAngle","endAngle"],xx={cartesian2d:function(t){return!vx(t,mx)},polar:function(t){return!vx(t,_x)}},bx={cartesian2d:function(t,e,n){var i=t.getItemLayout(e),r=n?function(t,e){var n=t.get(["itemStyle","borderColor"]);if(!n||"none"===n)return 0;var i=t.get(["itemStyle","borderWidth"])||0,r=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),o=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(i,r,o)}(n,i):0,o=i.width>0?1:-1,a=i.height>0?1:-1;return{x:i.x+o*r/2,y:i.y+a*r/2,width:i.width-o*r,height:i.height-a*r}},polar:function(t,e,n){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}}};function Sx(t){return function(t){var e=t?"Arc":"Angle";return function(t){switch(t){case"start":case"insideStart":case"end":case"insideEnd":return t+e;default:return t}}}(t)}function Mx(t,e,n,i,r,o,a,s){var l=e.getItemVisual(n,"style");s||t.setShape("r",i.get(["itemStyle","borderRadius"])||0),t.useStyle(l);var u=i.getShallow("cursor");u&&t.attr("cursor",u);var h=s?a?r.r>=r.r0?"endArc":"startArc":r.endAngle>=r.startAngle?"endAngle":"startAngle":a?r.height>=0?"bottom":"top":r.width>=0?"right":"left",c=Tu(i);Mu(t,c,{labelFetcher:o,labelDataIndex:n,defaultText:x_(o.getData(),n),inheritColor:l.fill,defaultOpacity:l.opacity,defaultOutsidePosition:h});var p=t.getTextContent();if(s&&p){var f=i.get(["label","position"]);t.textConfig.inside="middle"===f||null,function(t,e,n,i){if("number"!=typeof i)if(F(e))t.setTextConfig({rotation:0});else{var r,o=t.shape,a=o.clockwise?o.startAngle:o.endAngle,s=o.clockwise?o.endAngle:o.startAngle,l=(a+s)/2,u=n(e);switch(u){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":r=l;break;case"startAngle":case"insideStartAngle":r=a;break;case"endAngle":case"insideEndAngle":r=s;break;default:return void t.setTextConfig({rotation:0})}var h=1.5*Math.PI-r;"middle"===u&&h>Math.PI/2&&h<1.5*Math.PI&&(h-=Math.PI),t.setTextConfig({rotation:h})}else t.setTextConfig({rotation:i})}(t,"outside"===f?h:f,Sx(a),i.get(["label","rotate"]))}!function(t,e,n,i){if(t){var r=Lu(t);r.prevValue=r.value,r.value=n;var o=e.normal;r.valueAnimation=o.get("valueAnimation"),r.valueAnimation&&(r.precision=o.get("precision"),r.defaultInterpolatedText=i,r.statesModels=e)}}(p,c,o.getRawValue(n),(function(t){return b_(e,t)}));var d=i.getModel(["emphasis"]);Bs(t,d.get("focus"),d.get("blurScope")),Hs(t,i),function(t){return null!=t.startAngle&&null!=t.endAngle&&t.startAngle===t.endAngle}(r)&&(t.style.fill="none",t.style.stroke="none",L(t.states,(function(t){t.style&&(t.style.fill=t.style.stroke="none")})))}var Tx=function(){},Cx=function(t){function e(e){var n=t.call(this,e)||this;return n.type="largeBar",n}return n(e,t),e.prototype.getDefaultShape=function(){return new Tx},e.prototype.buildPath=function(t,e){for(var n=e.points,i=this.__startPoint,r=this.__baseDimIdx,o=0;o<n.length;o+=2)i[r]=n[o+r],t.moveTo(i[0],i[1]),t.lineTo(n[o],n[o+1])},e}(Aa);function Dx(t,e,n){var i=t.getData(),r=[],o=i.getLayout("valueAxisHorizontal")?1:0;r[1-o]=i.getLayout("valueAxisStart");var a=i.getLayout("largeDataIndices"),s=i.getLayout("barWidth"),l=t.getModel("backgroundStyle");if(t.get("showBackground",!0)){var u=i.getLayout("largeBackgroundPoints"),h=[];h[1-o]=i.getLayout("backgroundStart");var c=new Cx({shape:{points:u},incremental:!!n,silent:!0,z2:0});c.__startPoint=h,c.__baseDimIdx=o,c.__largeDataIndices=a,c.__barWidth=s,function(t,e,n){var i=e.get("borderColor")||e.get("color"),r=e.getItemStyle();t.useStyle(r),t.style.fill=null,t.style.stroke=i,t.style.lineWidth=n.getLayout("barWidth")}(c,l,i),e.add(c)}var p=new Cx({shape:{points:i.getLayout("largePoints")},incremental:!!n});p.__startPoint=r,p.__baseDimIdx=o,p.__largeDataIndices=a,p.__barWidth=s,e.add(p),function(t,e,n){var i=n.getVisual("style");t.useStyle(T({},i)),t.style.fill=null,t.style.stroke=i.fill,t.style.lineWidth=n.getLayout("barWidth")}(p,0,i),Ja(p).seriesIndex=t.seriesIndex,t.get("silent")||(p.on("mousedown",Ix),p.on("mousemove",Ix))}var Ix=Tf((function(t){var e=function(t,e,n){var i=t.__baseDimIdx,r=1-i,o=t.shape.points,a=t.__largeDataIndices,s=Math.abs(t.__barWidth/2),l=t.__startPoint[r];hx[0]=e,hx[1]=n;for(var u=hx[i],h=hx[1-i],c=u-s,p=u+s,f=0,d=o.length/2;f<d;f++){var g=2*f,y=o[g+i],v=o[g+r];if(y>=c&&y<=p&&(l<=v?h>=l&&h<=v:h>=v&&h<=l))return a[f]}return-1}(this,t.offsetX,t.offsetY);Ja(this).dataIndex=e>=0?e:null}),30,!1);function kx(t,e,n){if(W_(n,"cartesian2d")){var i=e,r=n.getArea();return{x:t?i.x:r.x,y:t?r.y:i.y,width:t?i.width:r.width,height:t?r.height:i.height}}var o=e;return{cx:(r=n.getArea()).cx,cy:r.cy,r0:t?r.r0:o.r0,r:t?r.r:o.r,startAngle:t?o.startAngle:0,endAngle:t?o.endAngle:2*Math.PI}}var Ax=2*Math.PI,Px=Math.PI/180;function Lx(t,e){return Wh(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function Ox(t,e){var n=Lx(t,e),i=t.get("center"),r=t.get("radius");F(r)||(r=[0,r]),F(i)||(i=[i,i]);var o=zi(n.width,e.getWidth()),a=zi(n.height,e.getHeight()),s=Math.min(o,a);return{cx:zi(i[0],o)+n.x,cy:zi(i[1],a)+n.y,r0:zi(r[0],s/2),r:zi(r[1],s/2)}}function Rx(t,e,n){e.eachSeriesByType(t,(function(t){var e=t.getData(),i=e.mapDimension("value"),r=Lx(t,n),o=Ox(t,n),a=o.cx,s=o.cy,l=o.r,u=o.r0,h=-t.get("startAngle")*Px,c=t.get("minAngle")*Px,p=0;e.each(i,(function(t){!isNaN(t)&&p++}));var f=e.getSum(i),d=Math.PI/(f||p)*2,g=t.get("clockwise"),y=t.get("roseType"),v=t.get("stillShowZeroSum"),m=e.getDataExtent(i);m[0]=0;var _=Ax,x=0,b=h,w=g?1:-1;if(e.setLayout({viewRect:r,r:l}),e.each(i,(function(t,n){var i;if(isNaN(t))e.setItemLayout(n,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:g,cx:a,cy:s,r0:u,r:y?NaN:l});else{(i="area"!==y?0===f&&v?d:t*d:Ax/p)<c?(i=c,_-=c):x+=t;var r=b+w*i;e.setItemLayout(n,{angle:i,startAngle:b,endAngle:r,clockwise:g,cx:a,cy:s,r0:u,r:y?Ei(t,m,[u,l]):l}),b=r}})),_<Ax&&p)if(_<=.001){var S=Ax/p;e.each(i,(function(t,n){if(!isNaN(t)){var i=e.getItemLayout(n);i.angle=S,i.startAngle=h+w*n*S,i.endAngle=h+w*(n+1)*S}}))}else d=_/x,b=h,e.each(i,(function(t,n){if(!isNaN(t)){var i=e.getItemLayout(n),r=i.angle===c?c:t*d;i.startAngle=b,i.endAngle=b+w*r,b+=w*r}}))}))}var Nx=Math.PI/180;function Ex(t,e,n,i,r,o,a,s,l,u){if(!(t.length<2)){for(var h=t.length,c=0;c<h;c++)if("outer"===t[c].position&&"labelLine"===t[c].labelAlignTo){var p=t[c].label.x-u;t[c].linePoints[1][0]+=p,t[c].label.x=u}h_(t,l,l+a)&&function(t){for(var o={list:[],maxY:0},a={list:[],maxY:0},s=0;s<t.length;s++)if("none"===t[s].labelAlignTo){var l=t[s],u=l.label.y>n?a:o,h=Math.abs(l.label.y-n);if(h>u.maxY){var c=l.label.x-e-l.len2*r,p=i+l.len,d=Math.abs(c)<p?Math.sqrt(h*h/(1-c*c/p/p)):p;u.rB=d,u.maxY=h}u.list.push(l)}f(o),f(a)}(t)}function f(t){for(var o=t.rB,a=o*o,s=0;s<t.list.length;s++){var l=t.list[s],u=Math.abs(l.label.y-n),h=i+l.len,c=h*h,p=Math.sqrt((1-Math.abs(u*u/a))*c);l.label.x=e+(p+l.len2)*r}}}function zx(t){return"center"===t.position}function Bx(t){var e,n,i=t.getData(),r=[],o=!1,a=(t.get("minShowLabelAngle")||0)*Nx,s=i.getLayout("viewRect"),l=i.getLayout("r"),u=s.width,h=s.x,c=s.y,p=s.height;function f(t){t.ignore=!0}i.each((function(t){var s=i.getItemGraphicEl(t),c=s.shape,p=s.getTextContent(),d=s.getTextGuideLine(),g=i.getItemModel(t),y=g.getModel("label"),v=y.get("position")||g.get(["emphasis","label","position"]),m=y.get("distanceToLabelLine"),_=y.get("alignTo"),x=zi(y.get("edgeDistance"),u),b=y.get("bleedMargin"),w=g.getModel("labelLine"),S=w.get("length");S=zi(S,u);var M=w.get("length2");if(M=zi(M,u),Math.abs(c.endAngle-c.startAngle)<a)return L(p.states,f),void(p.ignore=!0);if(function(t){if(!t.ignore)return!0;for(var e in t.states)if(!1===t.states[e].ignore)return!0;return!1}(p)){var T,C,D,I,k=(c.startAngle+c.endAngle)/2,A=Math.cos(k),P=Math.sin(k);e=c.cx,n=c.cy;var O,R="inside"===v||"inner"===v;if("center"===v)T=c.cx,C=c.cy,I="center";else{var N=(R?(c.r+c.r0)/2*A:c.r*A)+e,E=(R?(c.r+c.r0)/2*P:c.r*P)+n;if(T=N+3*A,C=E+3*P,!R){var z=N+A*(S+l-c.r),B=E+P*(S+l-c.r),F=z+(A<0?-1:1)*M;T="edge"===_?A<0?h+x:h+u-x:F+(A<0?-m:m),C=B,D=[[N,E],[z,B],[F,B]]}I=R?"center":"edge"===_?A>0?"right":"left":A>0?"left":"right"}var V=y.get("rotate");if("number"==typeof V)O=V*(Math.PI/180);else if("center"===v)O=0;else{var H=A<0?-k+Math.PI:-k;"radial"===V||!0===V?O=H:"tangential"===V&&"outside"!==v&&"outer"!==v?(O=H+Math.PI/2)>Math.PI/2&&(O-=Math.PI):O=0}if(o=!!O,p.x=T,p.y=C,p.rotation=O,p.setStyle({verticalAlign:"middle"}),R){p.setStyle({align:I});var G=p.states.select;G&&(G.x+=p.x,G.y+=p.y)}else{var W=p.getBoundingRect().clone();W.applyTransform(p.getComputedTransform());var U=(p.style.margin||0)+2.1;W.y-=U/2,W.height+=U,r.push({label:p,labelLine:d,position:v,len:S,len2:M,minTurnAngle:w.get("minTurnAngle"),maxSurfaceAngle:w.get("maxSurfaceAngle"),surfaceNormal:new Kn(A,P),linePoints:D,textAlign:I,labelDistance:m,labelAlignTo:_,edgeDistance:x,bleedMargin:b,rect:W})}s.setTextConfig({inside:R})}})),!o&&t.get("avoidLabelOverlap")&&function(t,e,n,i,r,o,a,s){for(var l=[],u=[],h=Number.MAX_VALUE,c=-Number.MAX_VALUE,p=0;p<t.length;p++){var f=t[p].label;zx(t[p])||(f.x<e?(h=Math.min(h,f.x),l.push(t[p])):(c=Math.max(c,f.x),u.push(t[p])))}for(Ex(u,e,n,i,1,0,o,0,s,c),Ex(l,e,n,i,-1,0,o,0,s,h),p=0;p<t.length;p++){var d=t[p];if(f=d.label,!zx(d)){var g=d.linePoints;if(g){var y="edge"===d.labelAlignTo,v=d.rect.width,m=void 0;(m=y?f.x<e?g[2][0]-d.labelDistance-a-d.edgeDistance:a+r-d.edgeDistance-g[2][0]-d.labelDistance:f.x<e?f.x-a-d.bleedMargin:a+r-f.x-d.bleedMargin)<d.rect.width&&(d.label.style.width=m,"edge"===d.labelAlignTo&&(v=m));var _=g[1][0]-g[2][0];y?f.x<e?g[2][0]=a+d.edgeDistance+v+d.labelDistance:g[2][0]=a+r-d.edgeDistance-v-d.labelDistance:(f.x<e?g[2][0]=f.x+d.labelDistance:g[2][0]=f.x-d.labelDistance,g[1][0]=g[2][0]+_),g[1][1]=g[2][1]=f.y}}}}(r,e,n,l,u,p,h,c);for(var d=0;d<r.length;d++){var g=r[d],y=g.label,v=g.labelLine,m=isNaN(y.x)||isNaN(y.y);if(y){y.setStyle({align:g.textAlign}),m&&(L(y.states,f),y.ignore=!0);var _=y.states.select;_&&(_.x+=y.x,_.y+=y.y)}if(v){var x=g.linePoints;m||!x?(L(v.states,f),v.ignore=!0):(a_(x,g.minTurnAngle),s_(x,g.surfaceNormal,g.maxSurfaceAngle),v.setShape({points:x}),y.__hostTarget.textGuideLineConfig={anchor:new Kn(x[0][0],x[0][1])})}}}function Fx(t,e,n){var i=t.get("borderRadius");return null==i?n?{innerCornerRadius:0,cornerRadius:0}:null:(F(i)||(i=[i,i]),{innerCornerRadius:gi(i[0],e.r0),cornerRadius:gi(i[1],e.r)})}var Vx=function(t){function e(e,n,i){var r=t.call(this)||this;r.z2=2;var o=new Wa;return r.setTextContent(o),r.updateData(e,n,i,!0),r}return n(e,t),e.prototype.updateData=function(t,e,n,r){var o=this,a=t.hostModel,s=t.getItemModel(e),l=s.getModel("emphasis"),u=t.getItemLayout(e),h=T(Fx(s.getModel("itemStyle"),u,!0),u);if(isNaN(h.startAngle))o.setShape(h);else{if(r)o.setShape(h),"scale"===a.getShallow("animationType")?(o.shape.r=u.r0,ou(o,{shape:{r:u.r}},a,e)):null!=n?(o.setShape({startAngle:n,endAngle:n}),ou(o,{shape:{startAngle:u.startAngle,endAngle:u.endAngle}},a,e)):(o.shape.endAngle=u.startAngle,ru(o,{shape:{endAngle:u.endAngle}},a,e));else hu(o),ru(o,{shape:h},a,e);o.useStyle(t.getItemVisual(e,"style")),Hs(o,s);var c=(u.startAngle+u.endAngle)/2,p=a.get("selectedOffset"),f=Math.cos(c)*p,d=Math.sin(c)*p,g=s.getShallow("cursor");g&&o.attr("cursor",g),this._updateLabel(a,t,e),o.ensureState("emphasis").shape=i({r:u.r+(l.get("scale")&&l.get("scaleSize")||0)},Fx(l.getModel("itemStyle"),u)),T(o.ensureState("select"),{x:f,y:d,shape:Fx(s.getModel(["select","itemStyle"]),u)}),T(o.ensureState("blur"),{shape:Fx(s.getModel(["blur","itemStyle"]),u)});var y=o.getTextGuideLine(),v=o.getTextContent();y&&T(y.ensureState("select"),{x:f,y:d}),T(v.ensureState("select"),{x:f,y:d}),Bs(this,l.get("focus"),l.get("blurScope"))}},e.prototype._updateLabel=function(t,e,n){var i=this,r=e.getItemModel(n),o=r.getModel("labelLine"),a=e.getItemVisual(n,"style"),s=a&&a.fill,l=a&&a.opacity;Mu(i,Tu(r),{labelFetcher:e.hostModel,labelDataIndex:n,inheritColor:s,defaultOpacity:l,defaultText:t.getFormattedLabel(n,"normal")||e.getName(n)});var u=i.getTextContent();i.setTextConfig({position:null,rotation:null}),u.attr({z2:10});var h=t.get(["label","position"]);if("outside"!==h&&"outer"!==h)i.removeTextGuideLine();else{var c=this.getTextGuideLine();c||(c=new Nl,this.setTextGuideLine(c)),function(t,e,n){var i=t.getTextGuideLine(),r=t.getTextContent();if(r){for(var o=e.normal,a=o.get("show"),s=r.ignore,l=0;l<is.length;l++){var u=is[l],h=e[u],c="normal"===u;if(h){var p=h.get("show");if((c?s:Q(r.states[u]&&r.states[u].ignore,s))||!Q(p,a)){var f=c?i:i&&i.states.normal;f&&(f.ignore=!0);continue}i||(i=new Nl,t.setTextGuideLine(i),c||!s&&a||l_(i,!0,"normal",e.normal),t.stateProxy&&(i.stateProxy=t.stateProxy)),l_(i,!1,u,h)}}if(i){C(i.style,n),i.style.fill=null;var d=o.get("showAbove");(t.textGuideLineConfig=t.textGuideLineConfig||{}).showAbove=d||!1,i.buildPath=u_}}else i&&t.removeTextGuideLine()}(this,function(t,e){e=e||"labelLine";for(var n={normal:t.getModel(e)},i=0;i<ns.length;i++){var r=ns[i];n[r]=t.getModel([r,e])}return n}(r),{stroke:s,opacity:tt(o.get(["lineStyle","opacity"]),l,1)})}},e}(Dl),Hx=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.ignoreLabelLineUpdate=!0,e}return n(e,t),e.prototype.init=function(){var t=new Di;this._sectorGroup=t},e.prototype.render=function(t,e,n,i){var r,o=t.getData(),a=this._data,s=this.group;if(!a&&o.count()>0){for(var l=o.getItemLayout(0),u=1;isNaN(l&&l.startAngle)&&u<o.count();++u)l=o.getItemLayout(u);l&&(r=l.startAngle)}if(this._emptyCircleSector&&s.remove(this._emptyCircleSector),0===o.count()&&t.get("showEmptyCircle")){var h=new Dl({shape:Ox(t,n)});h.useStyle(t.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=h,s.add(h)}o.diff(a).add((function(t){var e=new Vx(o,t,r);o.setItemGraphicEl(t,e),s.add(e)})).update((function(t,e){var n=a.getItemGraphicEl(e);n.updateData(o,t,r),n.off("click"),s.add(n),o.setItemGraphicEl(t,n)})).remove((function(e){uu(a.getItemGraphicEl(e),t,e)})).execute(),Bx(t),"expansion"!==t.get("animationTypeUpdate")&&(this._data=o)},e.prototype.dispose=function(){},e.prototype.containPoint=function(t,e){var n=e.getData().getItemLayout(0);if(n){var i=t[0]-n.cx,r=t[1]-n.cy,o=Math.sqrt(i*i+r*r);return o<=n.r&&o>=n.r0}},e.type="pie",e}(_f);var Gx=function(){function t(t,e){this._getDataWithEncodedVisual=t,this._getRawData=e}return t.prototype.getAllNames=function(){var t=this._getRawData();return t.mapArray(t.getName)},t.prototype.containName=function(t){return this._getRawData().indexOfName(t)>=0},t.prototype.indexOfName=function(t){return this._getDataWithEncodedVisual().indexOfName(t)},t.prototype.getItemVisual=function(t,e){return this._getDataWithEncodedVisual().getItemVisual(t,e)},t}(),Wx=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.init=function(e){t.prototype.init.apply(this,arguments),this.legendVisualProvider=new Gx(z(this.getData,this),z(this.getRawData,this)),this._defaultLabelLine(e)},e.prototype.mergeOption=function(){t.prototype.mergeOption.apply(this,arguments)},e.prototype.getInitialData=function(){return function(t,e,n){e=F(e)&&{coordDimensions:e}||T({encodeDefine:t.getEncode()},e);var i=t.getSource(),r=Sv(i,e).dimensions,o=new wv(r,t);return o.initData(i,n),o}(this,{coordDimensions:["value"],encodeDefaulter:B(pc,this)})},e.prototype.getDataParams=function(e){var n=this.getData(),i=t.prototype.getDataParams.call(this,e),r=[];return n.each(n.mapDimension("value"),(function(t){r.push(t)})),i.percent=Gi(r,e,n.hostModel.get("percentPrecision")),i.$vars.push("percent"),i},e.prototype._defaultLabelLine=function(t){or(t,"labelLine",["show"]);var e=t.labelLine,n=t.emphasis.labelLine;e.show=e.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},e.type="series.pie",e.defaultOption={zlevel:0,z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},e}(sf);var Ux=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.type="grid",e.dependencies=["xAxis","yAxis"],e.layoutMode="box",e.defaultOption={show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},e}(jh),Xx=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",xr).models[0]},e.type="cartesian2dAxis",e}(jh);A(Xx,Lm);var Yx={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},qx=M({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Yx),jx=M({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},Yx),Zx={category:qx,value:jx,time:M({scale:!0,splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},jx),log:C({scale:!0,logBase:10},jx)},Kx={value:1,category:1,time:1,log:1};function $x(t,e,i,r){L(Kx,(function(o,a){var s=M(M({},Zx[a],!0),r,!0),l=function(t){function i(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e+"Axis."+a,n}return n(i,t),i.prototype.mergeDefaultAndTheme=function(t,e){var n=Uh(this),i=n?Yh(t):{};M(t,e.getTheme().get(a+"Axis")),M(t,this.getDefaultOption()),t.type=Jx(t),n&&Xh(t,i,n)},i.prototype.optionUpdated=function(){"category"===this.option.type&&(this.__ordinalMeta=Rv.createByAxisModel(this))},i.prototype.getCategories=function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},i.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},i.type=e+"Axis."+a,i.defaultOption=s,i}(i);t.registerComponentModel(l)})),t.registerSubTypeDefaulter(e+"Axis",Jx)}function Jx(t){return t.type||(t.data?"category":"value")}var Qx=function(){function t(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}return t.prototype.getAxis=function(t){return this._axes[t]},t.prototype.getAxes=function(){return O(this._dimList,(function(t){return this._axes[t]}),this)},t.prototype.getAxesByScale=function(t){return t=t.toLowerCase(),N(this.getAxes(),(function(e){return e.scale.type===t}))},t.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},t}(),tb=["x","y"];function eb(t){return"interval"===t.type||"time"===t.type}var nb=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="cartesian2d",e.dimensions=tb,e}return n(e,t),e.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t=this.getAxis("x").scale,e=this.getAxis("y").scale;if(eb(t)&&eb(e)){var n=t.getExtent(),i=e.getExtent(),r=this.dataToPoint([n[0],i[0]]),o=this.dataToPoint([n[1],i[1]]),a=n[1]-n[0],s=i[1]-i[0];if(a&&s){var l=(o[0]-r[0])/a,u=(o[1]-r[1])/s,h=r[0]-n[0]*l,c=r[1]-i[0]*u,p=this._transform=[l,0,0,u,h,c];this._invTransform=zn([],p)}}},e.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},e.prototype.containPoint=function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},e.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},e.prototype.dataToPoint=function(t,e,n){n=n||[];var i=t[0],r=t[1];if(this._transform&&null!=i&&isFinite(i)&&null!=r&&isFinite(r))return At(n,t,this._transform);var o=this.getAxis("x"),a=this.getAxis("y");return n[0]=o.toGlobalCoord(o.dataToCoord(i,e)),n[1]=a.toGlobalCoord(a.dataToCoord(r,e)),n},e.prototype.clampData=function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),o=i.getExtent(),a=n.parse(t[0]),s=i.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(r[0],r[1]),a),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),s),Math.max(o[0],o[1])),e},e.prototype.pointToData=function(t,e){var n=[];if(this._invTransform)return At(n,t,this._invTransform);var i=this.getAxis("x"),r=this.getAxis("y");return n[0]=i.coordToData(i.toLocalCoord(t[0]),e),n[1]=r.coordToData(r.toLocalCoord(t[1]),e),n},e.prototype.getOtherAxis=function(t){return this.getAxis("x"===t.dim?"y":"x")},e.prototype.getArea=function(){var t=this.getAxis("x").getGlobalExtent(),e=this.getAxis("y").getGlobalExtent(),n=Math.min(t[0],t[1]),i=Math.min(e[0],e[1]),r=Math.max(t[0],t[1])-n,o=Math.max(e[0],e[1])-i;return new oi(n,i,r,o)},e}(Qx),ib=function(t){function e(e,n,i,r,o){var a=t.call(this,e,n,i)||this;return a.index=0,a.type=r||"value",a.position=o||"bottom",a}return n(e,t),e.prototype.isHorizontal=function(){var t=this.position;return"top"===t||"bottom"===t},e.prototype.getGlobalExtent=function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},e.prototype.pointToData=function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},e.prototype.setCategorySortInfo=function(t){if("category"!==this.type)return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)},e}(Km);function rb(t){return"cartesian2d"===t.get("coordinateSystem")}function ob(t){var e={xAxisModel:null,yAxisModel:null};return L(e,(function(n,i){var r=i.replace(/Model$/,""),o=t.getReferringComponents(r,xr).models[0];e[i]=o})),e}var ab=function(){function t(t,e,n){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=tb,this._initCartesian(t,e,n),this.model=t}return t.prototype.getRect=function(){return this._rect},t.prototype.update=function(t,e){var n=this._axesMap;this._updateScale(t,this.model),L(n.x,(function(t){Cm(t.scale,t.model)})),L(n.y,(function(t){Cm(t.scale,t.model)}));var i={};L(n.x,(function(t){lb(n,"y",t,i)})),L(n.y,(function(t){lb(n,"x",t,i)})),this.resize(this.model,e)},t.prototype.resize=function(t,e,n){var i=t.getBoxLayoutParams(),r=!n&&t.get("containLabel"),o=Wh(i,{width:e.getWidth(),height:e.getHeight()});this._rect=o;var a=this._axesList;function s(){L(a,(function(t){var e=t.isHorizontal(),n=e?[0,o.width]:[0,o.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),function(t,e){var n=t.getExtent(),i=n[0]+n[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return i-t+e}}(t,e?o.x:o.y)}))}s(),r&&(L(a,(function(t){if(!t.model.get(["axisLabel","inside"])){var e=function(t){var e=t.model,n=t.scale;if(e.get(["axisLabel","show"])&&!n.isBlank()){var i,r,o=n.getExtent();r=n instanceof Wv?n.count():(i=n.getTicks()).length;var a,s=t.getLabelModel(),l=Im(t),u=1;r>40&&(u=Math.ceil(r/40));for(var h=0;h<r;h+=u){var c=l(i?i[h]:{value:o[0]+h},h),p=km(s.getTextRect(c),s.get("rotate")||0);a?a.union(p):a=p}return a}}(t);if(e){var n=t.isHorizontal()?"height":"width",i=t.model.get(["axisLabel","margin"]);o[n]-=e[n]+i,"top"===t.position?o.y+=e.height+i:"left"===t.position&&(o.x+=e.width+i)}}})),s()),L(this._coordsList,(function(t){t.calcAffineTransform()}))},t.prototype.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n)return n[e||0]},t.prototype.getAxes=function(){return this._axesList.slice()},t.prototype.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}U(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,r=this._coordsList;i<r.length;i++)if(r[i].getAxis("x").index===t||r[i].getAxis("y").index===e)return r[i]},t.prototype.getCartesians=function(){return this._coordsList.slice()},t.prototype.convertToPixel=function(t,e,n){var i=this._findConvertTarget(e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},t.prototype.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},t.prototype._findConvertTarget=function(t){var e,n,i=t.seriesModel,r=t.xAxisModel||i&&i.getReferringComponents("xAxis",xr).models[0],o=t.yAxisModel||i&&i.getReferringComponents("yAxis",xr).models[0],a=t.gridModel,s=this._coordsList;if(i)I(s,e=i.coordinateSystem)<0&&(e=null);else if(r&&o)e=this.getCartesian(r.componentIndex,o.componentIndex);else if(r)n=this.getAxis("x",r.componentIndex);else if(o)n=this.getAxis("y",o.componentIndex);else if(a){a.coordinateSystem===this&&(e=this._coordsList[0])}return{cartesian:e,axis:n}},t.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},t.prototype._initCartesian=function(t,e,n){var i=this,r=this,o={left:!1,right:!1,top:!1,bottom:!1},a={x:{},y:{}},s={x:0,y:0};if(e.eachComponent("xAxis",l("x"),this),e.eachComponent("yAxis",l("y"),this),!s.x||!s.y)return this._axesMap={},void(this._axesList=[]);function l(e){return function(n,i){if(sb(n,t)){var l=n.get("position");"x"===e?"top"!==l&&"bottom"!==l&&(l=o.bottom?"top":"bottom"):"left"!==l&&"right"!==l&&(l=o.left?"right":"left"),o[l]=!0;var u=new ib(e,Dm(n),[0,0],n.get("type"),l),h="category"===u.type;u.onBand=h&&n.get("boundaryGap"),u.inverse=n.get("inverse"),n.axis=u,u.model=n,u.grid=r,u.index=i,r._axesList.push(u),a[e][i]=u,s[e]++}}}this._axesMap=a,L(a.x,(function(e,n){L(a.y,(function(r,o){var a="x"+n+"y"+o,s=new nb(a);s.master=i,s.model=t,i._coordsMap[a]=s,i._coordsList.push(s),s.addAxis(e),s.addAxis(r)}))}))},t.prototype._updateScale=function(t,e){function n(t,e){L(function(t,e){var n={};return L(t.mapDimensionsAll(e),(function(e){n[Av(t,e)]=!0})),E(n)}(t,e.dim),(function(n){e.scale.unionExtentFromData(t,n)}))}L(this._axesList,(function(t){if(t.scale.setExtent(1/0,-1/0),"category"===t.type){var e=t.model.get("categorySortInfo");t.scale.setSortInfo(e)}})),t.eachSeries((function(t){if(rb(t)){var i=ob(t),r=i.xAxisModel,o=i.yAxisModel;if(!sb(r,e)||!sb(o,e))return;var a=this.getCartesian(r.componentIndex,o.componentIndex),s=t.getData(),l=a.getAxis("x"),u=a.getAxis("y");"list"===s.type&&(n(s,l),n(s,u))}}),this)},t.prototype.getTooltipAxes=function(t){var e=[],n=[];return L(this.getCartesians(),(function(i){var r=null!=t&&"auto"!==t?i.getAxis(t):i.getBaseAxis(),o=i.getOtherAxis(r);I(e,r)<0&&e.push(r),I(n,o)<0&&n.push(o)})),{baseAxes:e,otherAxes:n}},t.create=function(e,n){var i=[];return e.eachComponent("grid",(function(r,o){var a=new t(r,e,n);a.name="grid_"+o,a.resize(r,n,!0),r.coordinateSystem=a,i.push(a)})),e.eachSeries((function(t){if(rb(t)){var e=ob(t),n=e.xAxisModel,i=e.yAxisModel,r=n.getCoordSysModel();0;var o=r.coordinateSystem;t.coordinateSystem=o.getCartesian(n.componentIndex,i.componentIndex)}})),i},t.dimensions=tb,t}();function sb(t,e){return t.getCoordSysModel()===e}function lb(t,e,n,i){n.getAxesOnZeroOf=function(){return r?[r]:[]};var r,o=t[e],a=n.model,s=a.get(["axisLine","onZero"]),l=a.get(["axisLine","onZeroAxisIndex"]);if(s){if(null!=l)ub(o[l])&&(r=o[l]);else for(var u in o)if(o.hasOwnProperty(u)&&ub(o[u])&&!i[h(o[u])]){r=o[u];break}r&&(i[h(r)]=!0)}function h(t){return t.dim+"_"+t.index}}function ub(t){return t&&"category"!==t.type&&"time"!==t.type&&function(t){var e=t.scale.getExtent(),n=e[0],i=e[1];return!(n>0&&i>0||n<0&&i<0)}(t)}var hb=Math.PI,cb=function(){function t(t,e){this.group=new Di,this.opt=e,this.axisModel=t,C(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var n=new Di({x:e.position[0],y:e.position[1],rotation:e.rotation});n.updateTransform(),this._transformGroup=n}return t.prototype.hasBuilder=function(t){return!!pb[t]},t.prototype.add=function(t){pb[t](this.opt,this.axisModel,this.group,this._transformGroup)},t.prototype.getGroup=function(){return this.group},t.innerTextLayout=function(t,e,n){var i,r,o=Ui(e-t);return Xi(o)?(r=n>0?"top":"bottom",i="center"):Xi(o-hb)?(r=n>0?"bottom":"top",i="center"):(r="middle",i=o>0&&o<hb?n>0?"right":"left":n>0?"left":"right"),{rotation:o,textAlign:i,textVerticalAlign:r}},t.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},t.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},t}(),pb={axisLine:function(t,e,n,i){var r=e.get(["axisLine","show"]);if("auto"===r&&t.handleAutoShown&&(r=t.handleAutoShown("axisLine")),r){var o=e.axis.getExtent(),a=i.transform,s=[o[0],0],l=[o[1],0];a&&(At(s,s,a),At(l,l,a));var u=T({lineCap:"round"},e.getModel(["axisLine","lineStyle"]).getLineStyle()),h=new Bl({subPixelOptimize:!0,shape:{x1:s[0],y1:s[1],x2:l[0],y2:l[1]},style:u,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1});h.anid="line",n.add(h);var c=e.get(["axisLine","symbol"]);if(null!=c){var p=e.get(["axisLine","symbolSize"]);"string"==typeof c&&(c=[c,c]),"string"!=typeof p&&"number"!=typeof p||(p=[p,p]);var f=xd(e.get(["axisLine","symbolOffset"])||0,p),d=p[0],g=p[1];L([{rotate:t.rotation+Math.PI/2,offset:f[0],r:0},{rotate:t.rotation-Math.PI/2,offset:f[1],r:Math.sqrt((s[0]-l[0])*(s[0]-l[0])+(s[1]-l[1])*(s[1]-l[1]))}],(function(e,i){if("none"!==c[i]&&null!=c[i]){var r=_d(c[i],-d/2,-g/2,d,g,u.stroke,!0),o=e.r+e.offset;r.attr({rotation:e.rotate,x:s[0]+o*Math.cos(t.rotation),y:s[1]-o*Math.sin(t.rotation),silent:!0,z2:11}),n.add(r)}}))}}},axisTickLabel:function(t,e,n,i){var r=function(t,e,n,i){var r=n.axis,o=n.getModel("axisTick"),a=o.get("show");"auto"===a&&i.handleAutoShown&&(a=i.handleAutoShown("axisTick"));if(!a||r.scale.isBlank())return;for(var s=o.getModel("lineStyle"),l=i.tickDirection*o.get("length"),u=yb(r.getTicksCoords(),e.transform,l,C(s.getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])}),"ticks"),h=0;h<u.length;h++)t.add(u[h]);return u}(n,i,e,t),o=function(t,e,n,i){var r=n.axis;if(!J(i.axisLabelShow,n.get(["axisLabel","show"]))||r.scale.isBlank())return;var o=n.getModel("axisLabel"),a=o.get("margin"),s=r.getViewLabels(),l=(J(i.labelRotate,o.get("rotate"))||0)*hb/180,u=cb.innerTextLayout(i.rotation,l,i.labelDirection),h=n.getCategories&&n.getCategories(!0),c=[],p=cb.isLabelSilent(n),f=n.get("triggerEvent");return L(s,(function(s,l){var d="ordinal"===r.scale.type?r.scale.getRawOrdinalNumber(s.tickValue):s.tickValue,g=s.formattedLabel,y=s.rawLabel,v=o;if(h&&h[d]){var m=h[d];U(m)&&m.textStyle&&(v=new Uu(m.textStyle,o,n.ecModel))}var _=v.getTextColor()||n.get(["axisLine","lineStyle","color"]),x=r.dataToCoord(d),b=new Wa({x:x,y:i.labelOffset+i.labelDirection*a,rotation:u.rotation,silent:p,z2:10+(s.level||0),style:Cu(v,{text:g,align:v.getShallow("align",!0)||u.textAlign,verticalAlign:v.getShallow("verticalAlign",!0)||v.getShallow("baseline",!0)||u.textVerticalAlign,fill:"function"==typeof _?_("category"===r.type?y:"value"===r.type?d+"":d,l):_})});if(b.anid="label_"+d,f){var w=cb.makeAxisEventDataBase(n);w.targetType="axisLabel",w.value=y,Ja(b).eventData=w}e.add(b),b.updateTransform(),c.push(b),t.add(b),b.decomposeTransform()})),c}(n,i,e,t);(function(t,e,n){if(Pm(t.axis))return;var i=t.get(["axisLabel","showMinLabel"]),r=t.get(["axisLabel","showMaxLabel"]);n=n||[];var o=(e=e||[])[0],a=e[1],s=e[e.length-1],l=e[e.length-2],u=n[0],h=n[1],c=n[n.length-1],p=n[n.length-2];!1===i?(fb(o),fb(u)):db(o,a)&&(i?(fb(a),fb(h)):(fb(o),fb(u)));!1===r?(fb(s),fb(c)):db(l,s)&&(r?(fb(l),fb(p)):(fb(s),fb(c)))}(e,o,r),function(t,e,n,i){var r=n.axis,o=n.getModel("minorTick");if(!o.get("show")||r.scale.isBlank())return;var a=r.getMinorTicksCoords();if(!a.length)return;for(var s=o.getModel("lineStyle"),l=i*o.get("length"),u=C(s.getLineStyle(),C(n.getModel("axisTick").getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])})),h=0;h<a.length;h++)for(var c=yb(a[h],e.transform,l,u,"minorticks_"+h),p=0;p<c.length;p++)t.add(c[p])}(n,i,e,t.tickDirection),e.get(["axisLabel","hideOverlap"]))&&function(t){var e=[];t.sort((function(t,e){return e.priority-t.priority}));var n=new oi(0,0,0,0);function i(t){if(!t.ignore){var e=t.ensureState("emphasis");null==e.ignore&&(e.ignore=!1)}t.ignore=!0}for(var r=0;r<t.length;r++){var o=t[r],a=o.axisAligned,s=o.localRect,l=o.transform,u=o.label,h=o.labelLine;n.copy(o.rect),n.width-=.1,n.height-=.1,n.x+=.05,n.y+=.05;for(var c=o.obb,p=!1,f=0;f<e.length;f++){var d=e[f];if(n.intersect(d.rect)){if(a&&d.axisAligned){p=!0;break}if(d.obb||(d.obb=new Ql(d.localRect,d.transform)),c||(c=new Ql(s,l)),c.intersect(d.obb)){p=!0;break}}}p?(i(u),h&&i(h)):(u.attr("ignore",o.defaultAttr.ignore),h&&h.attr("ignore",o.defaultAttr.labelGuideIgnore),e.push(o))}}(function(t){for(var e=[],n=0;n<t.length;n++){var i=t[n];if(!i.defaultAttr.ignore){var r=i.label,o=r.getComputedTransform(),a=r.getBoundingRect(),s=!o||o[1]<1e-5&&o[2]<1e-5,l=r.style.margin||0,u=a.clone();u.applyTransform(o),u.x-=l/2,u.y-=l/2,u.width+=l,u.height+=l;var h=s?new Ql(a,o):null;e.push({label:r,labelLine:i.labelLine,rect:u,localRect:a,obb:h,priority:i.priority,defaultAttr:i.defaultAttr,layoutOption:i.computedLayoutOption,axisAligned:s,transform:o})}}return e}(O(o,(function(t){return{label:t,priority:t.z2,defaultAttr:{ignore:t.ignore}}}))))},axisName:function(t,e,n,i){var r=J(t.axisName,e.get("name"));if(r){var o,a,s=e.get("nameLocation"),l=t.nameDirection,u=e.getModel("nameTextStyle"),h=e.get("nameGap")||0,c=e.axis.getExtent(),p=c[0]>c[1]?-1:1,f=["start"===s?c[0]-p*h:"end"===s?c[1]+p*h:(c[0]+c[1])/2,gb(s)?t.labelOffset+l*h:0],d=e.get("nameRotate");null!=d&&(d=d*hb/180),gb(s)?o=cb.innerTextLayout(t.rotation,null!=d?d:t.rotation,l):(o=function(t,e,n,i){var r,o,a=Ui(n-t),s=i[0]>i[1],l="start"===e&&!s||"start"!==e&&s;Xi(a-hb/2)?(o=l?"bottom":"top",r="center"):Xi(a-1.5*hb)?(o=l?"top":"bottom",r="center"):(o="middle",r=a<1.5*hb&&a>hb/2?l?"left":"right":l?"right":"left");return{rotation:a,textAlign:r,textVerticalAlign:o}}(t.rotation,s,d||0,c),null!=(a=t.axisNameAvailableWidth)&&(a=Math.abs(a/Math.sin(o.rotation)),!isFinite(a)&&(a=null)));var g=u.getFont(),y=e.get("nameTruncate",!0)||{},v=y.ellipsis,m=J(t.nameTruncateMaxWidth,y.maxWidth,a),_=new Wa({x:f[0],y:f[1],rotation:o.rotation,silent:cb.isLabelSilent(e),style:Cu(u,{text:r,font:g,overflow:"truncate",width:m,ellipsis:v,fill:u.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:u.get("align")||o.textAlign,verticalAlign:u.get("verticalAlign")||o.textVerticalAlign}),z2:1});if(function(t){var e=t.itemTooltipOption,n=t.componentModel,i=t.itemName,r=H(e)?{formatter:e}:e,o=n.mainType,a=n.componentIndex,s={componentType:o,name:i,$vars:["name"]};s[o+"Index"]=a;var l=t.formatterParamsExtra;l&&L(E(l),(function(t){pt(s,t)||(s[t]=l[t],s.$vars.push(t))}));var u=Ja(t.el);u.componentMainType=o,u.componentIndex=a,u.tooltipConfig={name:i,option:C({content:i,formatterParams:s},r)}}({el:_,componentModel:e,itemName:r}),_.__fullText=r,_.anid="name",e.get("triggerEvent")){var x=cb.makeAxisEventDataBase(e);x.targetType="axisName",x.name=r,Ja(_).eventData=x}i.add(_),_.updateTransform(),n.add(_),_.decomposeTransform()}}};function fb(t){t&&(t.ignore=!0)}function db(t,e){var n=t&&t.getBoundingRect().clone(),i=e&&e.getBoundingRect().clone();if(n&&i){var r=Pn([]);return Nn(r,r,-t.rotation),n.applyTransform(On([],r,t.getLocalTransform())),i.applyTransform(On([],r,e.getLocalTransform())),n.intersect(i)}}function gb(t){return"middle"===t||"center"===t}function yb(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord;a[0]=u,a[1]=0,s[0]=u,s[1]=n,e&&(At(a,a,e),At(s,s,e));var h=new Bl({subPixelOptimize:!0,shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0});h.anid=r+"_"+t[l].tickValue,o.push(h)}return o}function vb(t){var e=mb(t);if(e){var n=e.axisPointerModel,i=e.axis.scale,r=n.option,o=n.get("status"),a=n.get("value");null!=a&&(a=i.parse(a));var s=function(t){return!!t.get(["handle","show"])}(n);null==o&&(r.status=s?"show":"hide");var l=i.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==a||a>l[1])&&(a=l[1]),a<l[0]&&(a=l[0]),r.value=a,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function mb(t){var e,n=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return n&&n.axesInfo[(e=t,e.type+"||"+e.id)]}var _b={},xb=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return n(e,t),e.prototype.render=function(e,n,i,r){this.axisPointerClass&&vb(e),t.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,i,!0)},e.prototype.updateAxisPointer=function(t,e,n,i){this._doUpdateAxisPointerClass(t,n,!1)},e.prototype.remove=function(t,e){var n=this._axisPointer;n&&n.remove(e)},e.prototype.dispose=function(e,n){this._disposeAxisPointer(n),t.prototype.dispose.apply(this,arguments)},e.prototype._doUpdateAxisPointerClass=function(t,n,i){var r=e.getAxisPointerClass(this.axisPointerClass);if(r){var o=function(t){var e=mb(t);return e&&e.axisPointerModel}(t);o?(this._axisPointer||(this._axisPointer=new r)).render(t,o,n,i):this._disposeAxisPointer(n)}},e.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},e.registerAxisPointerClass=function(t,e){_b[t]=e},e.getAxisPointerClass=function(t){return t&&_b[t]},e.type="axis",e}(gf),bb=yr();var wb=["axisLine","axisTickLabel","axisName"],Sb=["splitArea","splitLine","minorSplitLine"],Mb=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.axisPointerClass="CartesianAxisPointer",n}return n(e,t),e.prototype.render=function(e,n,i,r){this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new Di,this.group.add(this._axisGroup),e.get("show")){var a=e.getCoordSysModel(),s=function(t,e,n){n=n||{};var i=t.coordinateSystem,r=e.axis,o={},a=r.getAxesOnZeroOf()[0],s=r.position,l=a?"onZero":s,u=r.dim,h=i.getRect(),c=[h.x,h.x+h.width,h.y,h.y+h.height],p={left:0,right:1,top:0,bottom:1,onZero:2},f=e.get("offset")||0,d="x"===u?[c[2]-f,c[3]+f]:[c[0]-f,c[1]+f];if(a){var g=a.toGlobalCoord(a.dataToCoord(0));d[p.onZero]=Math.max(Math.min(g,d[1]),d[0])}o.position=["y"===u?d[p[l]]:c[0],"x"===u?d[p[l]]:c[3]],o.rotation=Math.PI/2*("x"===u?0:1),o.labelDirection=o.tickDirection=o.nameDirection={top:-1,bottom:1,left:-1,right:1}[s],o.labelOffset=a?d[p[s]]-d[p.onZero]:0,e.get(["axisTick","inside"])&&(o.tickDirection=-o.tickDirection),J(n.labelInside,e.get(["axisLabel","inside"]))&&(o.labelDirection=-o.labelDirection);var y=e.get(["axisLabel","rotate"]);return o.labelRotate="top"===l?-y:y,o.z2=1,o}(a,e),l=new cb(e,T({handleAutoShown:function(t){for(var n=a.coordinateSystem.getCartesians(),i=0;i<n.length;i++){var r=n[i].getOtherAxis(e.axis).type;if("value"===r||"log"===r)return!0}return!1}},s));L(wb,l.add,l),this._axisGroup.add(l.getGroup()),L(Sb,(function(t){e.get([t,"show"])&&Tb[t](this,this._axisGroup,e,a)}),this),r&&"changeAxisOrder"===r.type&&r.isInitSort||function(t,e,n){if(t&&e){var i,r=(i={},t.traverse((function(t){bu(t)&&t.anid&&(i[t.anid]=t)})),i);e.traverse((function(t){if(bu(t)&&t.anid){var e=r[t.anid];if(e){var i=o(t);t.attr(o(e)),ru(t,i,n,Ja(t).dataIndex)}}}))}function o(t){var e={x:t.x,y:t.y,rotation:t.rotation};return function(t){return null!=t.shape}(t)&&(e.shape=T({},t.shape)),e}}(o,this._axisGroup,e),t.prototype.render.call(this,e,n,i,r)}},e.prototype.remove=function(){bb(this).splitAreaColors=null},e.type="cartesianAxis",e}(xb),Tb={splitLine:function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank()){var o=n.getModel("splitLine"),a=o.getModel("lineStyle"),s=a.get("color");s=F(s)?s:[s];for(var l=i.coordinateSystem.getRect(),u=r.isHorizontal(),h=0,c=r.getTicksCoords({tickModel:o}),p=[],f=[],d=a.getLineStyle(),g=0;g<c.length;g++){var y=r.toGlobalCoord(c[g].coord);u?(p[0]=y,p[1]=l.y,f[0]=y,f[1]=l.y+l.height):(p[0]=l.x,p[1]=y,f[0]=l.x+l.width,f[1]=y);var v=h++%s.length,m=c[g].tickValue;e.add(new Bl({anid:null!=m?"line_"+c[g].tickValue:null,subPixelOptimize:!0,autoBatch:!0,shape:{x1:p[0],y1:p[1],x2:f[0],y2:f[1]},style:C({stroke:s[v]},d),silent:!0}))}}},minorSplitLine:function(t,e,n,i){var r=n.axis,o=n.getModel("minorSplitLine").getModel("lineStyle"),a=i.coordinateSystem.getRect(),s=r.isHorizontal(),l=r.getMinorTicksCoords();if(l.length)for(var u=[],h=[],c=o.getLineStyle(),p=0;p<l.length;p++)for(var f=0;f<l[p].length;f++){var d=r.toGlobalCoord(l[p][f].coord);s?(u[0]=d,u[1]=a.y,h[0]=d,h[1]=a.y+a.height):(u[0]=a.x,u[1]=d,h[0]=a.x+a.width,h[1]=d),e.add(new Bl({anid:"minor_line_"+l[p][f].tickValue,subPixelOptimize:!0,autoBatch:!0,shape:{x1:u[0],y1:u[1],x2:h[0],y2:h[1]},style:c,silent:!0}))}},splitArea:function(t,e,n,i){!function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank()){var o=n.getModel("splitArea"),a=o.getModel("areaStyle"),s=a.get("color"),l=i.coordinateSystem.getRect(),u=r.getTicksCoords({tickModel:o,clamp:!0});if(u.length){var h=s.length,c=bb(t).splitAreaColors,p=ut(),f=0;if(c)for(var d=0;d<u.length;d++){var g=c.get(u[d].tickValue);if(null!=g){f=(g+(h-1)*d)%h;break}}var y=r.toGlobalCoord(u[0].coord),v=a.getAreaStyle();for(s=F(s)?s:[s],d=1;d<u.length;d++){var m=r.toGlobalCoord(u[d].coord),_=void 0,x=void 0,b=void 0,w=void 0;r.isHorizontal()?(_=y,x=l.y,b=m-_,w=l.height,y=_+b):(_=l.x,x=y,b=l.width,y=x+(w=m-x));var S=u[d-1].tickValue;null!=S&&p.set(S,f),e.add(new Va({anid:null!=S?"area_"+S:null,shape:{x:_,y:x,width:b,height:w},style:C({fill:s[f]},v),autoBatch:!0,silent:!0})),f=(f+1)%h}bb(t).splitAreaColors=p}}}(t,e,n,i)}},Cb=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return n(e,t),e.type="xAxis",e}(Mb),Db=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=Cb.type,e}return n(e,t),e.type="yAxis",e}(Mb),Ib=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="grid",e}return n(e,t),e.prototype.render=function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new Va({shape:t.coordinateSystem.getRect(),style:C({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},e.type="grid",e}(gf),kb={offset:0};var Ab={label:{enabled:!0},decal:{show:!1}},Pb=yr(),Lb={};function Ob(t,e){var n=t.getModel("aria");if(n.get("enabled")){var i=S(Ab);M(i.label,t.getLocaleModel().get("aria"),!1),M(n.option,i,!1),function(){if(n.getModel("decal").get("show")){var e=ut();t.eachSeries((function(t){if(!t.isColorBySeries()){var n=e.get(t.type);n||(n={},e.set(t.type,n)),Pb(t).scope=n}})),t.eachRawSeries((function(e){if(!t.isSeriesFiltered(e))if("function"!=typeof e.enableAriaDecal){var n=e.getData();if(e.isColorBySeries()){var i=Sc(e.ecModel,e.name,Lb,t.getSeriesCount()),r=n.getVisual("decal");n.setVisual("decal",u(r,i))}else{var o=e.getRawData(),a={},s=Pb(e).scope;n.each((function(t){var e=n.getRawIndex(t);a[e]=t}));var l=o.count();o.each((function(t){var i=a[t],r=o.getName(t)||t+"",h=Sc(e.ecModel,r,s,l),c=n.getItemVisual(i,"decal");n.setItemVisual(i,"decal",u(c,h))}))}}else e.enableAriaDecal();function u(t,e){var n=t?T(T({},e),t):e;return n.dirty=!0,n}}))}}(),function(){var i=t.getLocaleModel().get("aria"),o=n.getModel("label");if(o.option=C(o.option,i),!o.get("enabled"))return;var a=e.getZr().dom;if(o.get("description"))return void a.setAttribute("aria-label",o.get("description"));var s,l=t.getSeriesCount(),u=o.get(["data","maxCount"])||10,h=o.get(["series","maxCount"])||10,c=Math.min(l,h);if(l<1)return;var p=function(){var e=t.get("title");e&&e.length&&(e=e[0]);return e&&e.text}();if(p){var f=o.get(["general","withTitle"]);s=r(f,{title:p})}else s=o.get(["general","withoutTitle"]);var d=[],g=l>1?o.get(["series","multiple","prefix"]):o.get(["series","single","prefix"]);s+=r(g,{seriesCount:l}),t.eachSeries((function(e,n){if(n<c){var i=void 0,a=e.get("name")?"withName":"withoutName";i=r(i=l>1?o.get(["series","multiple",a]):o.get(["series","single",a]),{seriesId:e.seriesIndex,seriesName:e.get("name"),seriesType:(_=e.subType,t.getLocaleModel().get(["series","typeNames"])[_]||"自定义图")});var s=e.getData();if(s.count()>u)i+=r(o.get(["data","partialData"]),{displayCnt:u});else i+=o.get(["data","allData"]);for(var h=o.get(["data","separator","middle"]),p=o.get(["data","separator","end"]),f=[],g=0;g<s.count();g++)if(g<u){var y=s.getName(g),v=s.getValues(g),m=o.get(["data",y?"withName":"withoutName"]);f.push(r(m,{name:y,value:v.join(h)}))}i+=f.join(h)+p,d.push(i)}var _}));var y=o.getModel(["series","multiple","separator"]),v=y.get("middle"),m=y.get("end");s+=d.join(v)+m,a.setAttribute("aria-label",s)}()}function r(t,e){if("string"!=typeof t)return t;var n=t;return L(e,(function(t,e){n=n.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)})),n}}function Rb(t){if(t&&t.aria){var e=t.aria;null!=e.show&&(e.enabled=e.show),e.label=e.label||{},L(["description","general","series","data"],(function(t){null!=e[t]&&(e.label[t]=e[t])}))}}var Nb=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dataset",e}return n(e,t),e.prototype.init=function(e,n,i){t.prototype.init.call(this,e,n,i),this._sourceManager=new Jp(this),Qp(this)},e.prototype.mergeOption=function(e,n){t.prototype.mergeOption.call(this,e,n),Qp(this)},e.prototype.optionUpdated=function(){this._sourceManager.dirty()},e.prototype.getSourceManager=function(){return this._sourceManager},e.type="dataset",e.defaultOption={seriesLayoutBy:oc},e}(jh),Eb=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dataset",e}return n(e,t),e.type="dataset",e}(gf);Ky([function(t){t.registerPainter("canvas",m_)}]),Ky([function(t){t.registerChartView(tx),t.registerSeriesModel(__),t.registerLayout(function(t,e){return{seriesType:t,plan:yf(),reset:function(t){var n=t.getData(),i=t.coordinateSystem,r=t.pipelineContext,o=e||r.large;if(i){var a=O(i.dimensions,(function(t){return n.mapDimension(t)})).slice(0,2),s=a.length,l=n.getCalculationInfo("stackResultDimension");kv(n,a[0])&&(a[0]=l),kv(n,a[1])&&(a[1]=l);var u=n.getStore(),h=n.getDimensionIndex(a[0]),c=n.getDimensionIndex(a[1]);return s&&{progress:function(t,e){for(var n=t.end-t.start,r=o&&L_(n*s),a=[],l=[],p=t.start,f=0;p<t.end;p++){var d=void 0;if(1===s){var g=u.get(h,p);d=i.dataToPoint(g,null,l)}else a[0]=u.get(h,p),a[1]=u.get(c,p),d=i.dataToPoint(a,null,l);o?(r[f++]=d[0],r[f++]=d[1]):e.setItemLayout(p,d.slice())}o&&e.setLayout("points",r)}}}}}}("line",!0)),t.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),n=t.getModel("lineStyle").getLineStyle();n&&!n.stroke&&(n.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",n)}}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,ix("line"))},function(t){t.registerChartView(fx),t.registerSeriesModel(ox),t.registerLayout(t.PRIORITY.VISUAL.LAYOUT,B(Jv,"bar")),t.registerLayout(t.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,Qv),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,ix("bar")),t.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},(function(t,e){var n=t.componentType||"series";e.eachComponent({mainType:n,query:t},(function(e){t.sortInfo&&e.axis.setCategorySortInfo(t.sortInfo)}))}))},function(t){t.registerChartView(Hx),t.registerSeriesModel(Wx),function(t,e){function n(e,n){var i=[];return e.eachComponent({mainType:"series",subType:t,query:n},(function(t){i.push(t.seriesIndex)})),i}L([[t+"ToggleSelect","toggleSelect"],[t+"Select","select"],[t+"UnSelect","unselect"]],(function(t){e(t[0],(function(e,i,r){e=T({},e),r.dispatchAction(T(e,{type:t[1],seriesIndex:n(i,e)}))}))}))}("pie",t.registerAction),t.registerLayout(B(Rx,"pie")),t.registerProcessor(function(t){return{seriesType:t,reset:function(t,e){var n=e.findComponents({mainType:"legend"});if(n&&n.length){var i=t.getData();i.filterSelf((function(t){for(var e=i.getName(t),r=0;r<n.length;r++)if(!n[r].isSelected(e))return!1;return!0}))}}}}("pie")),t.registerProcessor(function(t){return{seriesType:t,reset:function(t,e){var n=t.getData();n.filterSelf((function(t){var e=n.mapDimension("value"),i=n.get(e,t);return!("number"==typeof i&&!isNaN(i)&&i<0)}))}}}("pie"))}]),Ky([function(t){t.registerComponentView(Ib),t.registerComponentModel(Ux),t.registerCoordinateSystem("cartesian2d",ab),$x(t,"x",Xx,kb),$x(t,"y",Xx,kb),t.registerComponentView(Cb),t.registerComponentView(Db),t.registerPreprocessor((function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}))},function(t){t.registerPreprocessor(Rb),t.registerVisual(t.PRIORITY.VISUAL.ARIA,Ob)},function(t){t.registerComponentModel(Nb),t.registerComponentView(Eb)}]),t.Axis=Km,t.ChartView=_f,t.ComponentModel=jh,t.ComponentView=gf,t.List=wv,t.Model=Uu,t.PRIORITY=Hg,t.SeriesModel=sf,t.color=Ue,t.connect=function(t){if(F(t)){var e=t;t=null,L(e,(function(e){null!=e.group&&(t=e.group)})),t=t||"g_"+Iy++,L(e,(function(e){e.group=t}))}return Cy[t]=!0,t},t.dataTool={},t.dependencies={zrender:"5.2.1"},t.disConnect=Ay,t.disconnect=Py,t.dispose=function(t){"string"==typeof t?t=Ty[t]:t instanceof dy||(t=Ly(t)),t instanceof dy&&!t.isDisposed()&&t.dispose()},t.env=a,t.extendChartView=function(t){var e=_f.extend(t);return _f.registerClass(e),e},t.extendComponentModel=function(t){var e=jh.extend(t);return jh.registerClass(e),e},t.extendComponentView=function(t){var e=gf.extend(t);return gf.registerClass(e),e},t.extendSeriesModel=function(t){var e=sf.extend(t);return sf.registerClass(e),e},t.format=Bm,t.getCoordinateSystemDimensions=function(t){var e=Lc.get(t);if(e)return e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice()},t.getInstanceByDom=Ly,t.getInstanceById=function(t){return Ty[t]},t.getMap=function(t){return Eg(t)},t.graphic=zm,t.helper=Rm,t.init=function(t,e,n){var i=Ly(t);if(i)return i;var r=new dy(t,e,n);return r.id="ec_"+Dy++,Ty[r.id]=r,wr(t,ky,r.id),uy(r),zg.trigger("afterinit",r),r},t.innerDrawElementOnCanvas=zd,t.matrix=Bn,t.number=Nm,t.parseGeoJSON=Sg,t.parseGeoJson=Sg,t.registerAction=Fy,t.registerCoordinateSystem=Vy,t.registerLayout=Hy,t.registerLoading=Xy,t.registerLocale=Qu,t.registerMap=Yy,t.registerPostInit=Ey,t.registerPostUpdate=zy,t.registerPreprocessor=Ry,t.registerProcessor=Ny,t.registerTheme=Oy,t.registerTransform=qy,t.registerUpdateLifecycle=By,t.registerVisual=Gy,t.setCanvasCreator=function(t){_("createCanvas",t)},t.throttle=Tf,t.time=Em,t.use=Ky,t.util=Fm,t.vector=Ot,t.version="5.2.2",t.zrUtil=dt,t.zrender=Ri,Object.defineProperty(t,"__esModule",{value:!0})}));
