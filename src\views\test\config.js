export const CONFIG = {
  tableConfig: {
    isSelection: false, // 显示复选框
    isRowClick: true, // 使用行点击
    isStripe: true, // 隐藏斑马纹，与背景色不可同时用
    // tableHeight: 500, //表格高度
    isShowSummary: false, // 显示共计
    treeProps: { children: 'children', hasChildren: 'hasChildren' },
    rowKey: 'id'
  },
  tableHeader: [
    {
      prop: 'lieMing',
      title: '列名',
      type: 'link'
    },
    {
      prop: 'oldVal',
      title: '旧值'
    },
    {
      prop: 'newVal',
      title: '新值'
    },
    {
      prop: 'xiuGaiSJ',
      title: '修改时间',
      align: 'center'
    },
    {
      prop: 'ip',
      title: 'IP',
      align: 'center'
    },
    {
      prop: 'caoZuoZheXM',
      title: '操作者姓名'
    }
    // {
    //   prop: 'action',
    //   title: '操作',
    //   type: 'slot'
    // }
  ]
}
