$--font-path: '~element-ui/lib/theme-chalk/fonts';

@import "~element-ui/packages/theme-chalk/src/index";

body {
  // 页面整体缩放比例
  --content-scale: 1;

  &.size-large {
    --content-scale: 1.1
  }

  &.size-huge {
    --content-scale: 1.2
  }

  &.size-verysmall {
    // 页面整体缩放比例
    --content-scale: 0.8;
    --common-margin: #{$--common-margin / 2};
  }

  zoom: var(--content-scale);
  // 元素之间公共间距
  --common-margin: #{$--common-margin};
  // 表单名称与值之间的间距
  --label-value-margin: #{$--label-value-margin};
  // 元素公共内边距
  --common-padding: #{$--common-padding};

  /* 默认颜色
-------------------------- */
  /// color|1|Brand Color|0
  --color-primary: #{$--color-primary};
  /// color|1|Functional Color|1
  --color-success: #{$--color-success};
  /// color|1|Functional Color|1
  --color-warning: #{$--color-warning};
  /// color|1|Functional Color|1
  --color-danger: #{$--color-danger};
  /// color|1|Functional Color|1
  --color-info: #{$--color-info};
  // 紫色按钮
  --color-purple: #{$color-primary-purple};
  // 更加透明的主题色
  --color-primary-opacify: #{transparentize($--color-primary, 0.4)};

  // 基础字体颜色
  --font-color-base: #{$--font-color-base};
  --disabled-color: #{$--disabled-color};
  /// placeholder 字体颜色
  --color-text-placeholder: #{$--color-text-placeholder};

  /* 正文字体
-------------------------- */
  // 控件内部字体颜色
  --font-color-inner: #{$--font-color-inner};
  // 正文字体颜色
  --font-color-regular: #{$--color-text-regular};
  // 正文字体大小
  --font-size-regular: #{$--font-size-regular};
  // 正文字体
  --font-family-regular: #{$--font-family-regular};
  // 正文字体粗细
  --font-weight-regular: #{$--font-weight-regular};
  // 正文字体
  --font-regular: #{$--font-weight-regular $--font-size-regular $--font-family-regular};
  // 加粗字体颜色
  --font-color-medium: #{$--font-color-medium};
  // 加粗字体大小
  --font-size-medium: #{$--font-size-medium};
  // 加粗字体
  --font-family-medium: #{$--font-family-medium};
  // 加粗字体粗细
  --font-weight-medium: #{$--font-weight-medium};
  // 加粗字体
  --font-medium: #{$--font-weight-medium $--font-size-medium $--font-family-medium};


  // 默认边框宽度
  --border-width-base: #{$--border-width-base};
  // 默认边框颜色
  --border-color-base: #{$--border-color-base};
  // 默认边框
  --border-base: #{$--border-base};
  // 公共圆角大小
  --border-radius-base: #{$--border-radius-base};

  /* 链接颜色
-------------------------- */
  --link-color: #{$--link-color};


  // 表头背景色
  --table-header-background-color: #{$--table-header-background-color};
  // 表格当前行颜色
  --table-current-row-background-color: #{$--table-current-row-background-color};
  // 表格单元格上下内边距
  --table-cell-padding-vertical: #{$--table-cell-padding-vertical};
  // 表格单元格左右内边距
  --table-cell-padding-horizontal: #{$--table-cell-padding-horizontal};
  // 表格斑马纹颜色
  --table-stripe-color: #{$--table-stripe-color};

  /* 表单
-------------------------- */
  /// fontSize||Font|1
  --form-label-text-color: #{$--form-label-text-color};
  // 表单输入元素宽度
  --form-item-content-width: #{$--form-item-content-width};
  // 表单输入框高度
  --form-item-content-height: #{$--form-item-content-height};
  // 日期宽度
  --date-item-width: calc(6 * var(--font-size-regular));

  // 小宽度弹窗宽度
  --dialog-width-mini: #{$--dialog-width-mini};
  // 小宽度弹窗宽度
  --dialog-width-small: #{$--dialog-width-small};
  // 中等宽度弹窗宽度
  --dialog-width-medium: #{$--dialog-width-medium};
  // 大宽度弹窗宽度
  --dialog-width-large: #{$--dialog-width-large};

  // 内容区域固定高度
  --content-fixed-height: calc(100vh / var(--content-scale) - #{$--content-header-height});
}


.el-alert {
  &--primary {
    color: $--color-primary;
    background-color: $--color-primary-light-9;
  }

  &[plain] {
    .el-alert__description {
      color: $--font-color-regular;
    }
  }

  &[bold] {
    .el-alert__description {
      font: var(--font-medium);
      color: $--font-color-medium;
    }
  }

  & &__description {
    margin: 0;
  }

  & &__closebtn {
    top: 50%;
    transform: translateY(-50%);
  }

  &__content {
    width: 100%;
    padding: 0;
  }

  &__icon {
    margin-right: calc(var(--common-margin) / 2);
  }
}

.el-input__validateIcon {
  display: none;
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  margin-top: 0 !important;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 4px 12px 0 rgb(0 0 0 / 15%);
  opacity: 1;
  transform: translate(-50%, -50%);

  &__header {
    .el-dialog__headerbtn {
      display: flex;
      align-items: center;
      font-size: 20px
    }
  }

  &__body {
    padding: calc(var(--common-margin) + 6px) calc(var(--common-margin));
  }

  &__footer {
    padding: var(--common-margin);
    border-top: $--border-base;

    .el-button {
      padding: $--common-padding ($--common-padding * 2 + 6) !important;
    }
  }


}


// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}


// 按钮下的span
.el-text-color span {
  color: var(--color-primary);
}

// 删除按钮下的span
.el-text-color-del span {
  color: #F3222F;
}

// 按钮
.el-button-color.el-button.el-button--default.el-button--small {
  color: var(--font-color-inner);
  background: var(--color-primary);
  border: 1px solid var(--color-primary);
}


.el-message {
  top: 5vh !important;
  z-index: 9999 !important;
}


.el-message-box .el-button.el-button--default.el-button--small.el-button--primary {
  color: var(--font-color-inner);
  background: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  color: var(--font-color-inner);
  background: var(--color-primary);
  border: 1px solid var(--color-primary);

  &::after {
    border-color: var(--font-color-inner);
  }
}

// .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
//   background: var(--color-primary-opacify);
//   border: 1px solid var(--color-primary-opacify);
//   color: var(--font-color-inner);

//   &::after {
//     border-color: var(--font-color-inner);
//   }
// }

.el-checkbox__input.is-checked + .el-checkbox__label {
  color: var(--color-primary);
}

.el-radio {
  display: inline-flex;
  align-items: center;
  margin-right: calc(var(--common-margin) * 3 / 2);
  line-height: var(--form-item-content-height);
}

.el-radio__input.is-checked .el-radio__inner {
  background: var(--color-primary);
  border-color: var(--color-primary);

  &::after {
    border: 2px solid var(--font-color-inner);
    border-radius: 50%;
  }
}

.el-radio__input.is-checked + .el-radio__label {
  color: var(--color-primary);
}

.el-button:not(.el-button--text) {
  &:not(:first-child) {
    margin-left: var(--common-margin);
  }
}

.el-button--primary {
  color: var(--font-color-inner);
  background: var(--color-primary);
  border: 1px solid var(--color-primary);

  &:hover,
  &:active,
  &:visited {
    color: var(--font-color-inner);
    background: var(--color-primary);
    border: 1px solid var(--color-primary);
    opacity: 1;
  }
}

.el-table {
  border: $--border-base;

  th,
  td {
    border: none;


    &.el-table__cell {
      //padding: var(--table-cell-padding-vertical) 0;
      border-top: $--border-base;
      border-top-color: transparent;
      border-bottom: $--border-base;

      &.is-leaf {
        border-bottom: $--border-base;
      }

      & > .cell {
        padding: 0 6px;
        line-height: 16px;
      }
    }
  }

  &::before {
    display: none;
  }

  th.el-table__cell {
    font-weight: 500;
    font-size: var(--font-size-regular);
    color: var(--font-color-regular);
    background: $--table-header-background-color;
  }

  td {
    font-weight: 400;
    font-size: var(--font-size-regular);
    color: var(--font-color-regular);
  }


  &--striped &__body {
    tr.el-table__row--striped td.el-table__cell {
      background: $--table-stripe-color;
    }
  }

  &__empty-block {
    position: sticky;
    left: 0;
    width: 100% !important;
    border-top: none;
  }

  &__fixed-right-patch {
    background: $--table-header-background-color;
  }
}

.el-input {
  .el-input__inner {
    box-sizing: border-box;
    font: var(--font-regular);
    font-size: var(--font-size-regular);
    background: #fff;
    border-radius: 4px;
    opacity: 1;
  }

  &__icon {
    line-height: 30px;
  }

  &-number &__inner {
    text-align: left;
  }
}

.el-select {
  .el-input__suffix {
    top: 50%;
    height: auto;
    transform: translateY(-50%);
  }

  &-dropdown {
    &.is-multiple {
      .el-select-dropdown__item {
        padding-left: 68px;
        $select-size: $--font-size-regular;

        &::after {
          position: absolute;
          top: 50%;
          left: 20px;
          display: inline-block;
          width: $select-size;
          height: $select-size;
          content: "";
          background: #FFF;
          border: var(--border-base);
          border-radius: 2px;
          transform: translateY(-50%);
        }

        &.selected {
          &::after {
            font-size: $select-size * 10 / 14;
            line-height: $select-size;
            color: #FFF;
            text-align: center;
            background-color: $--color-primary;
          }

        }
      }
    }
  }
}


.el-form {
  &-item {
    margin-bottom: calc(1.5 * var(--font-size-regular));

    &__label {
      padding-right: var(--label-value-margin);
      font: var(--font-regular);
      line-height: $--form-item-content-height;
      color: var(--form-label-text-color);

      .label {
        &-row-2 {
          line-height: $--form-item-content-height / 2;
        }
      }
    }

    &.expand {
      .el-form-item {
        &__label {
          width: var(--label-width) !important;
        }

        &__content {
          width: calc(100% - var(--label-width));
          max-width: unset;
          margin-left: var(--label-width) !important;
        }
      }
    }

    &__content {
      display: flex;
      align-items: center;
      max-width: var(--form-item-content-width);
      line-height: var(--form-item-content-height);

      .el-input {
        width: var(--form-item-content-width);
      }

      .el-select {
        width: var(--form-item-content-width);

        .el-input {
          width: 100%;
        }
      }

      .el-textarea {
        width: var(--form-item-content-width);
        margin-top: 5px;
      }

      .el-input-number {
        width: var(--form-item-content-width);

        .el-input {
          width: 100%;
        }
      }

      .form-item-tip {
        margin-left: 8px;
        font: var(--font-regular);
        color: var(--color-danger);
      }
    }

    &__error {
      margin-left: 12px;
      font: var(--font-regular);
      font-size: 12px;
      white-space: nowrap;
    }

  }


  &--label-top {

    .el-input,
    .el-select,
    .el-textarea,
    .el-input-number {
      width: 100%;
    }

    .el-form-item {
      &__label {
        line-height: 100%;
      }
    }
  }
}

.el-form--inline {
  .el-form-item {
    margin-bottom: 0;
  }
}

.el-pagination {
  display: flex;
  justify-content: flex-end;
  padding: var(--common-margin) 5px;
  padding-bottom: 0;

  &.is-background .el-pager li:not(.disabled).active {
    background-color: var(--color-primary);
  }
}

// .el-checkbox__inner {
//   width: 16px;
//   height: 16px;
// }
.el-tabs {
  &__header {
    margin: 0;
  }

  &__item.is-active {
    color: var(--color-primary);
  }

  .el-tabs__active-bar {
    background-color: var(--color-primary);
  }

  .el-tabs__item {
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-size: var(--font-size-regular);
    font-weight: var(--font-weight-medium);
  }

  .el-tabs__item:hover {
    color: var(--color-primary);
  }
}

.el-tag {
  height: auto;
  line-height: 100%;

  &--small {
    line-height: 1.7;
  }

  &.el-tag--info .el-tag__close {
    color: $--font-color-inner;
  }
}

.el-scrollbar {
  &__wrap {

    // 隐藏下拉框自带的滚动条
    &::-webkit-scrollbar-thumb {
      display: none;
    }
  }
}

.el-card {
  &__header {
    padding: 14px 20px;
    border-bottom: $--border-base;
  }

  &__body {
    padding: 16px 20px;
  }

  border: $--border-base;
}

.el-pagination {
  .el-input .el-input__inner {
    height: 28px;
    margin-top: -4px;
    line-height: 28px;
  }
}

.el-tree {
  &-node {
    width: fit-content;
    min-width: 100%;

    &__expand-icon {
      &::before {
        display: inline-block;
        width: $--font-size-regular;
        height: $--font-size-regular;
        content: "";
        background-image: url("~@/assets/custom-theme/icons/tree-expand.png");
        background-position: center;
        background-size: contain;
      }

      &.is-leaf {
        &::before {
          background-image: url("~@/assets/custom-theme/icons/tree-leaf.png");
        }
      }
    }

    &__content > &__expand-icon {
      padding: 0;
      margin-right: var(--common-margin);
    }
  }
}

.el-date-editor {
  &--date {
    input {
      width: calc(var(--date-item-width) + 50px);
    }
  }

  &.el-date-editor--daterange {
    width: auto;

    .el-range-input {
      width: var(--date-item-width);
    }
  }

  .el-range__icon {
    line-height: 24px;
  }

  .el-range-separator {
    line-height: 24px;
  }

  .el-range__close-icon {
    line-height: 24px;
  }
}

.el-tooltip__popper {
  max-width: 50%;
}

.el-drawer {
  &__header {
    justify-content: center;
    padding: var(--common-margin) 0;
    margin: 0;
    font: var(--font-medium);
    color: var(--font-color-medium);
    background: var(--border-color-base);

    > span:first-child {
      flex: unset;
    }
  }
}


.el-collapse {
  border: none;

  &-item {
    &__header {
      font: var(--font-medium);
      background-color: transparent;
      border-bottom: none;

      &::before {
        display: inline-block;
        width: 2px;
        height: var(--font-size-regular);
        margin-right: calc(var(--common-margin) / 2);
        content: '';
        background-color: var(--color-primary);

      }
    }

    &__wrap {
      background-color: transparent;
    }
  }
}

.el-descriptions {
  &__title {
    font: var(--font-medium);
    color: $--font-color-base;

    &::before {
      display: inline-block;
      width: 2px;
      height: var(--font-size-regular);
      margin-right: calc(var(--common-margin) / 2);
      content: "";
      background-color: var(--color-primary);
    }
  }

  &-item {
    &__container {
      align-items: center;

    }

    &__label {
      flex-shrink: 0;
      line-height: 100%;
      color: var(--form-label-text-color);
    }

  }
}

.el-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
}
