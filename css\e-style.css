/* 登录页 */
.login_container {
  background: url('../images/login_bg.png') left top/cover;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.login_box {
  max-width: 1200px;
  width: 95%;
  min-width: 800px;
  height: 600px;
  background: #fff;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  display: flex;
}

.login_box .left {
  width: 43%;
}

.login_box .right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login_box .logo {
  background: url('../images/login_left.png') center/cover;
  position: relative;
  z-index: 1;
  display: flex;
  flex-flow: column;
  justify-content: center;
  text-align: center;
}

.login_box .logo .mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  background: linear-gradient(90deg, rgba(51, 103, 204, 0.8), rgba(134, 175, 224, 0.8));
}

.login_box .logo .logo_icon {
  margin-bottom: 80px;
}

.login_box .logo .logo_describe {
  font-size: 24px;
  color: #fff;
}

.login_box .logo .logo_describe .en {
  font-size: 12px;
  transform: scale(.85, .98);
}

.login_box .form {
  max-width: 420px;
  width: 80%;
}

.login_box .form_title {
  font-size: 36px;
  color: rgba(51, 51, 51, 0.8);
  text-align: center;
  margin-bottom: 30px;
}

.login_box .form .nav {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40px;
}

.login_box .form .nav>li {
  width: 120px;
  text-align: center;
  position: relative;
}

.login_box .form .nav>li::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  height: 2px;
  width: 0;
  background: #3D6CC8;
  transition: width 0.3s cubic-bezier(.66, 1.95, .7, .7);
}

.login_box .form .nav>li>a {
  color: rgba(0, 0, 0, 0.2);
  line-height: 62px;
  padding: 0;
  font-size: 18px;
  font-weight: 700;
  transition: color 0.2s;
}

.login_box .form .nav>li>a:hover {
  color: rgba(0, 0, 0, 0.5);
}

.login_box .form .nav>li.active>a {
  color: #3D6CC8;
}

.login_box .form .nav>li.active::before {
  width: 100%;
}


.login_box .form .tab-content {
  height: 185px;
}

.login_box .btn.form_btn {
  width: 160px;
  font-size: 16px;
  line-height: 24px;
  background: rgba(61, 108, 200, 1);
  border-radius: 8px;
  color: #fff;
  border: none;
  outline: none;
  transition: all 0.3s;
}

.login_box .btn.form_btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 3px 6px 2px rgba(61, 108, 200, 0.7);
}

.e_form_item {
  position: relative;
  margin-bottom: 20px;
}

.e_form_item_label {
  position: absolute;
  left: 36px;
  line-height: 14px;
  padding: 0 3px;
  top: calc(50% - 7px);
  color: #3D6CC8;
  transition: all 0.2s;

}

.e_form_item.focus .e_form_item_label {
  top: calc(0% - 7px);
  font-size: 12px;
  background: linear-gradient(transparent calc(50% - 1px), #fff calc(50% - 1px), #fff calc(50% + 2px), transparent calc(50% + 2px));
}

.e_input {
  width: 100%;
  height: 48px;
  background: rgba(24, 144, 255, 0.05);
  border-radius: 4px;
  border: 1px solid transparent;
  color: rgba(0, 0, 0, 0.65);
  transition: all .2s;
  font-size: 14px;
  padding: 15px 36px;
}

.e_form_item.focus .e_input {
  border-color: #3D6CC8;
}


.e_form_item.code .btn {
  position: absolute;
  right: 10px;
  top: calc(50% - 16px);
  color: rgba(255, 255, 255, 0.85);
  border: none;
  border-radius: 100px;
  font-size: 12px;
  line-height: 20px;
  padding: 6px 15px;
  background: linear-gradient(90deg, rgba(61, 108, 200, 1), rgba(61, 108, 200, 0.7));
  outline: none;
  transition: all 0.3s;
}

.e_form_item.code .btn:hover {
  box-shadow: 0 0 4px 2px rgba(61, 108, 200, 0.7);
}

.e_checkbox {
  position: relative;
  display: inline-flex;
  align-items: center;
}

.e_checkbox_input {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  cursor: pointer;
  opacity: 0;
}

.e_checkbox_inner {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  width: 16px;
  height: 16px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 100px;
  transition: all .3s;
}

.e_checkbox_inner:after {
  position: absolute;
  top: 3px;
  left: 3px;
  display: table;
  width: 8px;
  height: 8px;
  background-color: #3D6CC8;
  border-top: 0;
  border-left: 0;
  border-radius: 8px;
  transform: scale(0);
  opacity: 0;
  transition: all .3s cubic-bezier(.78, .14, .15, .86);
  content: " ";
}

.e_checkbox_input:checked+.e_checkbox_inner:after {
  transform: scale(1);
  opacity: 1;
  transition: all .3s cubic-bezier(.78, .14, .15, .86);
}



.e_checkbox_input:checked+.e_checkbox_inner::before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid #3D6CC8;
  border-radius: 50%;
  -webkit-animation: antRadioEffect .36s ease-in-out;
  animation: antRadioEffect .36s ease-in-out;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  content: "";
}

@-webkit-keyframes antRadioEffect {
  0% {
    transform: scale(1);
    opacity: .5
  }

  to {
    transform: scale(1.6);
    opacity: 0
  }
}

@keyframes antRadioEffect {
  0% {
    transform: scale(1);
    opacity: .5
  }

  to {
    transform: scale(1.6);
    opacity: 0
  }
}

.e_checkbox_label {
  padding: 0 8px;
  color: rgba(0, 0, 0, 0.55);
}

.forget_password {
  position: absolute;
  right: 0;
  top: 0;
}

/* 公共头部*/
.main {
  height: 100%;
  display: flex;
  flex-flow: column;
  overflow: hidden;
}

.header {
  flex: 0 0 68px;
  height: 68px;
}

.header .logo {
  background: #3D6CC8;
  color: #fff;
  width: 240px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 68px;
  margin-left: -15px;
}

.header .logo>img {
  width: 60px;
}

.header .logo>span {
  margin-left: 15px;
  font-size: 18px;
}

.header .notice {
  overflow: hidden;
  margin-left: 15px;
}

.header .notice_inner {
  line-height: 52px;
  height: 52px;
  background: #E6ECF8;
  display: flex;
  font-size: 18px;
  padding: 0 15px;
  border-radius: 8px;
}

.header .notice_inner>.text {
  width: calc(100% - 110px);
}

.header .notice_inner>.more {
  flex: 1;
}

.e_menu {
  font-size: 16px;
  padding: 4px 45px;
  height: 68px;
}

.menu_top {
  display: flex;
  flex-flow: nowrap;
  justify-content: flex-end;
  align-items: center;
  margin: 6px 0;
}

.menu_top .text {
  font-weight: 700;
}

.menu_top .status {
  display: flex;
  font-size: 12px;
  line-height: 24px;
  color: #fff;
  text-align: center;
  margin: 0 12px;
}

.menu_top .status>li {
  width: 56px;
}

.menu_top .status>li:first-child {
  border-radius: 5px 0 0 5px;
}

.menu_top .status>li:last-child {
  border-radius: 0 5px 5px 0;
}

.menu_bottom .dropdown+.dropdown {
  margin-left: 20px;
}

.menu_bottom .dropdown-menu>li>a {
  padding: 1px 20px;
  cursor: pointer;
}

.menu_bottom .dropdown.multiCol .dropdown-menu {
  height: 584px;
}
.menu_bottom .dropdown.multiCol.open>.dropdown-menu {
  display: flex;
  flex-flow: wrap;
  writing-mode: vertical-lr;
}
.menu_bottom .dropdown.multiCol.open>.dropdown-menu>li {
  writing-mode: horizontal-tb;
  border-right: 1px solid #e9e5e5;
}

#login_info {
  width: 280px;
}

.login_info {
  display: flex;
  align-items: center;
}

.login_info>.info {
  flex: 1;
  display: flex;
  align-items: center;
  overflow: hidden;
  cursor: pointer;
}

.login_info>.info>img {
  width: 42px;
  padding-right: 12px;
}

.login_info>.info>span {
  flex: 1;
  font-size: 14px;
  color: #555555;
  white-space: nowrap;
}

.login_info>.icon {
  color: #3D6CC8;
  width: 50px;
  text-align: center;
  position: relative;
}

.login_info>.icon .iconfont {
  font-size: 24px;
  line-height: 66px;
}

.login_info .message_count {
  min-width: 18px;
  height: 18px;
  line-height: 18px;
  padding: 0 6px;
  color: #fff;
  font-size: 12px;
  white-space: nowrap;
  text-align: center;
  background: #f5222d;
  border-radius: 10px;
  box-shadow: 0 0 0 1px #fff;
  position: absolute;
  left: calc(50% + 5px);
  bottom: calc(50% + 5px);
}

.login_info>.icon .dropdown-menu {
  text-align: right;
  min-width: 0;
}

/* 页面选项卡 */
.inner {
  flex: 1;
  background: #F4F4F4;
  overflow: hidden;
  padding: 5px;
}

.page {
  height: 100%;
}

.page>.nav {
  display: flex;
}

.page>.nav>li {
  padding-right: 5px;
}

.page>.nav>li+li a {
  margin-left: 5px;
  padding: 0 34px 0 14px;
}

.page>.nav>li>a {
  line-height: 35px;
  text-align: center;
  padding: 0 24px;
  background: #D9E0ED;
  font-size: 15px;
  color: rgba(61, 108, 200, 0.6);
  white-space: nowrap;
}

.page>.nav>li.active>a {
  background: #fff;
  color: rgba(61, 108, 200, 1);
  font-weight: 700;
}

.page>.nav>li>.close {
  opacity: 0.5;
}

.page>.nav>li>.iconfont {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 16px;
  line-height: 16px;
  height: 16px;
  cursor: pointer;
  color: #000;
}

.page>.nav>li>.iconfont:hover {
  background: rgba(0, 0, 0, 0.1);
}

.page>.tab-content {
  position: relative;
  background: #fff;
  height: calc(100% - 30px);
  box-shadow: 0px 5px 5px rgb(0 0 0 / 10%);
}

.page>.tab-content>.tab-pane,
.page>.tab-content>.tab-pane,
.page>.tab-content>.tab-pane,
.page>.tab-content>.tab-pane>.flex-row {
  height: 100%;
}
/* .page>.tab-content>.tab-pane{
  visibility:hidden;
}
.page>.tab-content>.active{
  visibility: inherit;
} */
.page>.tab-content>.tab-pane.box {
  padding: 15px;
}

/* 公共按钮单选 */
.e_btn_group {
  display: flex;
  align-items: center;
}

.e_btn_group>li>a {
  color: #999;
  border: 1px solid #999;
  border-radius: 4px;
  padding: 4px 20px;
  cursor: pointer;
}

.e_btn_group>li+li {
  margin-left: 20px;
}

.e_btn_group>li.active>a {
  border-color: #3D6CC8;
  color: #3D6CC8;
  box-shadow: none;
  cursor: pointer;
}

/* 病人一览表 */

#patient_list {
  position: relative;
  height: calc(100% - 50px);
  overflow-y: scroll;
  overflow-x: hidden;

}


.patient_list {
  display: flex;
  flex-flow: wrap;
  margin: 0 -10px;
}

.patient_list .item {
  padding: 8px 10px 10px 10px;
  flex: 0 0 16.5%;
  overflow: hidden;
}




.patient_list .item_box {
  padding: 8px;
  /* background: #F6FBFF; */
  border: 1px solid rgb(61 108 200 / 60%);
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
}

.patient_list .item_box .user_text {
  font-size: 14px;
  margin: 6px 0 6px 8px;
}

.patient_list .item_box .user_text b {
  font-size: 14px;
}

.patient_list .item_box .status {
  height: 4px;
  background: #3D6CC8;
  margin-bottom: 4px;
}

.patient_list .item_box .message {
  font-size: 12px;
  height: 32px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 265px;
}

.patient_list .item_box .del {
  height: 20px;
  display: block;
}

.patient_list .item_box .del>span {
  text-align: center;
  font-size: 16px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: auto;
  line-height: 30px;
  height: 26px;
  background: #C7D3DF;
  /* background: #888888; */
  color: rgba(0, 0, 0, 0.4);
}

.patient_list .item_box .del>span:hover {
  color: rgba(0, 0, 0, 1);
  background: #AAAAAA;
}

#patient_info,
.patient_info {
  width: 300px;
  /* width: 360px; */

}


.patient_info .panel-group {
  margin-bottom: 0;
  border-left: 1px solid #cccccc;
  border-right: 1px solid #cccccc;
  overflow: hidden;
  position: absolute;
  width: 300px;
  top: 0;
  bottom: 0;
}

.panel-group .panel {
  border: none;
}

.patient_info .panel-group .panel+.panel {
  margin-top: 2px;
}

.patient_info .panel-heading {
  padding: 0;
}

.patient_info .panel-title {
  background: #3D6CC8;
  color: #fff;
  line-height: 42px;
  font-weight: 700;
  font-size: 14px;
  padding: 0 27px;
  text-decoration: none;
}

.patient_info .panel-title-text {
  margin-left: 10px;
}

.patient_info .panel-title-text>b {
  color: #FFA654;
}

.patient_info .panel-body {
  font-size: 14px;
}

/* 一览表表单 */
#patient_filter {
  /* flex: 0 0 50px; */
  padding-top: 15px;
  overflow: hidden;
}

/* 专科日志 */
.patient_info_one {
  text-align: center;
  font-size: 14px;
}

.patient_info_one .box_label {
  margin: 5px 0;
}

.patient_info_one .box_value {
  width: 60px;
  line-height: 40px;
  background: #F4F4F4;
  border-radius: 4px;
  margin: auto;
  font-size: 18px;
  color: #3D6CC8;
}

/*病人详细菜单栏*/

.e_inner .right-menu {
  height: 100%;
  padding-left: 5px;
}

.e_inner .panel {
  margin-bottom: 0px;
}

.e_inner .menu-left {
  height: 100%;
  min-width: 285px;
}

.e_inner .panel .menu-head {
  background-color: #3D6CC8;
  padding: 10px 15px;
  font-size: 14px;
  color: #fff;
}

.e_inner .menu-left #panel_group {
  overflow: hidden;
  height: 100%;
  overflow-y: auto;
}

.e_inner .menu-left .panel a {
  font-size: 14px;
}

.e_inner .menu-left .panel a:hover {
  cursor: pointer;
}

.e_inner .panel .panel-body-flew-tag {
  bottom: 0;
  padding: 10px 0 0 0;
  position: relative;
}

.e_inner .panel .panel-body-flew-tag .tag-name {
  font-family: Microsoft YaHei;
  font-style: normal;
  font-weight: bold;
  font-size: 18px;
  line-height: 24px;
}

.e_inner .panel .panel-body-flew-tag .tag-no {
  font-family: Microsoft YaHei;
  font-style: normal;
  font-weight: bold;
  font-size: 14px;
  line-height: 16px;
  /* identical to box height */
  color: #555555;
  padding: 0 0px 0 5px;
}

.e_inner .panel .panel-body-flew-tag .tag-icon {
  float: right;
  color: red;
  line-height: 30px;
  margin-right: 10px;
}

.e_inner .panel .panel-body-flew-tag .tag-xx {
  padding: 0 0px 0 5px;
}

.e_inner .panel .panel-body ul {
  padding: 0 0 0 15px;
}

.e_inner .panel .panel-body .list-item {
  position: relative;
  display: block;
  padding: 3px 0;
  /* background-color: #fff; */
}

.e_inner .panel .panel-body .list-item1 {
  position: relative;
  display: block;
  padding: 3px 0;
  /* background-color: #fff; */
}

.e_inner .panel .panel-body .list-item1 a {
  color: #000;
  font-size: 14px;
}

.e_inner .panel .panel-body .list-item a {
  color: #000;
  font-size: 14px;
}

.e_inner .panel .panel-body .list-item h4 {
  color: #000;
}

.e_inner .right-menu .inner-tab {
  height: 100%;
  padding: 3px 3px;
  flex: 1;
  background: #F4F4F4;
  overflow: hidden;
}

.e_inner .right-menu .tab-pane {
  height: 100%;
}

.e_inner .right-menu .tab-pane .nav-tabs li a {
  color: #333333;
}

.e_inner .right-menu .nav-tabs>li.active>a,
.nav-tabs>li.active>a:focus,
.nav-tabs>li.active>a:hover {
  border: none;
  border-bottom: 3px solid #3D6CC8;
  cursor: default;
  background-color: none;
  color: #3D6CC8;
}

/* .e_inner .panel .panel-collapse {
  background-color: #f5f5f5;
} */

.e_inner .panel .blackbody {
  font-family: Microsoft YaHei;
  font-style: normal;
  font-weight: bold;
  color: #333;
}

.e_inner .panel .panel-body .table-title {
  width: 45%;
  text-align: left;
  color: #000;
  font-size: 14px;
  min-width: 100px;
}

.e_inner .panel .panel-body .none {
  display: none;
}

.e_inner .panel .panel-body .datebox .combo-arrow {
  background-color: #ddd;
}

.e_inner .panel .panel-body .table-value {
  width: 60%;
  text-align: left;
  font-size: 14px;
  min-width: 135px;
  color: #000;
}

.e_inner .panel .panel-body .table-filter {
  width: 100%;
  font-size: 14px;
}

.e_inner .panel .panel-body .table-value .spacing {
  margin: 0 0 0 2px;
}

#page .tab-content .advice {
  width: 100%;
  height: calc(100% - 42px);
}
.xgpg_text{
  background: #3D6CC8;
  color: #fff;
  padding: 1px 3px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 3px;
}
.xgpg_text:hover{
  background: rgb(61, 108, 200,0.5);
}
/*长期，临时医嘱*/
.container-yz_sz .table-list {
  padding: 5px 0 0 0;
}


.container-yz_sz .table-list .checkbox-list-left {
  padding: 0 5px 0 15px;
  min-width: 100px;
}

.container-yz_sz .table-list .checkbox-list-right {
  padding: 0 15px 0 5px;
  min-width: 125px;
}

.container-yz_sz .table-list .spance {
  padding: 0 5px;
}

.container-yz_sz .table-list .spance_btn {
  padding: 0 10px 0 0;
}

.container-yz_sz .table-list .red {
  color: red;
}

.container-yz_sz .downmenu {
  color: rgba(51, 51, 51);
  cursor: pointer;
}

.container-yz_sz .downmenu:hover {
  text-decoration: none;
}

.container-yz_sz #th_list>tr>td,
.container-yz_sz #data_list>tr>td,
.container-yz_sz #yz_list>tr>td {
  text-align: center;
  margin: 0px;
  overflow: hidden;
  white-space: nowrap;
  font-size: 14px;
  height: 40px;
}

.container-yz_sz #yz_list>tr>td {
  border: 1px solid #fff;
}

/* .container-yz_sz #th_list>tr>td{
  border: 1px solid #fff;
} */
/* .container-yz_sz #data_list>tr>td{
  border: 1px solid #000;
} */
.container-yz_sz #th_list select {
  color: #000;
}

.container-yz_sz .container-person .border-blue {
  border: 1px solid #3D6CC8;
  padding: 3px;
  border-radius: 3px;
}

.container-yz_sz .container-person .border-red {
  color: #C82222;
  padding: 3px;
}
.container-yz_sz .container-person .dqmrkcl-red {
  color: #C82222;
  padding: 3px;
  border-radius: 3px;
}

.container-yz_sz .container-person .border-black {
  /* border: 1px solid #000; */
  font-family: Microsoft YaHei;
  font-style: normal;
  font-weight: bold;
  color: #000;
  font-size: 14px;
  padding: 3px;
}

.container-yz_sz .container-person .border-title {
  font-family: Microsoft YaHei;
  font-style: normal;
  font-weight: bold;
  color: #000;
  font-size: 16px;
}

.container-yz_sz .container-person .border-space {
  padding-right: 5px;
}

.container-yz_sz .container-person .border-space a:hover {
  text-decoration: none;
}

.container-yz_sz #yzdata_list input {
  border: 1px solid #ddd;
  color: #000;
}

.container-yz_sz #yzdata_list input:focus {
  border: 1px solid #000;
}

/* 自适应 */
@media (max-width: 1600px) {
  .patient_list .item {
    flex: 0 0 25%;
  }

  .e_btn_group>li>a {
    padding: 4px 10px;
  }
}

@media (max-width: 1400px) {
  .patient_list .item {
    flex: 0 0 33.33%;
  }

  .patient_type .form-inline label {
    display: none;
  }
}

@media (max-width: 1170px) {
  .patient_list .item {
    flex: 0 0 50%;
  }

  .header .logo {
    width: 120px;
  }

  .header .logo>span {
    display: none;
  }
}

#menu-right .tab-content>.tab-pane{
  display: block;
  height: 0px;
  overflow: hidden;
  z-index: -1;
  position: relative;
}

#menu-right .tab-content>.active{
  display: block;
  height: 100%;
  z-index: 100;
  /* overflow: auto; */
}



/******* 自定义  ***/
.e_custom_red{
  color: #f5222d;
  background: none;
}
.e_custom_black{
  color: #000;
  background: none;
}
.e_custom_orange{
  color: #fa8c16;
  background: none;
}
.e_custom_circle_red{
  background: rgb(245 34 45 / 30%);
  border-color: rgb(245 34 45 / 20%);
  padding: 0 3px;
  color: #f5222d;
}