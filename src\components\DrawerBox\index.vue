<script setup>
import { computed, reactive, useAttrs, watch } from 'vue'
import DetailTitle from '@/components/DetailTitle/index.vue'
import { useFormChangeConfirm } from '@/utils/hooks/formchangeconfirm'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  modal: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: '编辑'
  },
  enableFormChangeConfirm: {
    type: Boolean,
    default: true
  },
  // 监听数据对象
  targetData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  // 数据对象发生变动后要保存时的保存方法
  targetDataCallback: {
    type: Function,
    default: () => {}
  },
  cancelButtonProps: {
    type: Object,
    default: () => {
      return {
        show: true,
        type: '',
        text: '关闭'
      }
    }
  },
  confirmButtonProps: {
    type: Object,
    default: () => {
      return {
        show: true,
        type: 'primary',
        text: '保存'
      }
    }
  }
})

const _cancelButtonProps = computed(() => {
  return {
    show: true,
    type: '',
    text: '关闭',
    ...props.cancelButtonProps
  }
})
const _confirmButtonProps = computed(() => {
  return {
    show: true,
    type: 'primary',
    text: '保存',
    ...props.confirmButtonProps
  }
})

const attrs = useAttrs()
const defaultAttrs = reactive({
  // wrapperClosable: false,
  size: '45%'
})
const mergeAttrs = { ...defaultAttrs, ...attrs }

const emit = defineEmits(['update:visible', 'cancel'])

watch(
  () => props.visible,
  (newValue, oldValue) => {
    if (newValue) {
      clearFormWatcher()
    }
  }
)

const drawerClose = async () => {
  await cancel()
}

const cancel = async () => {
  console.log('drawer-box:cancel')
  if (props.enableFormChangeConfirm)
    try {
      await beforeLeave()
      emit('update:visible', false)
    } catch (err) {
      console.log(err)
    }
  else emit('update:visible', false)
}

const submit = () => {
  console.log('drawer-box:submit')
  emit('submit')
}
const { beforeLeave, clearFormWatcher } = useFormChangeConfirm(
  props.targetData,
  props.targetDataCallback
)

defineExpose({
  cancel
})
</script>

<template>
  <el-drawer
    :before-close="drawerClose"
    :modal="modal"
    :visible="visible"
    :with-header="false"
    direction="rtl"
    title="我是标题"
    v-bind="mergeAttrs"
  >
    <div class="drawer_box">
      <div class="drawer-header">
        <detail-title :title="title">
          <template #extra>
            <slot name="extra"></slot>
          </template>
        </detail-title>
      </div>
      <div class="drawer-content">
        <slot name="content"></slot>
      </div>
      <div class="drawer-footer">
        <slot name="footer">
          <el-button
            v-show="_cancelButtonProps.show"
            :type="_cancelButtonProps.type"
            size="small"
            @click="cancel"
          >
            {{ _cancelButtonProps.text }}
          </el-button>
          <el-button
            v-show="_confirmButtonProps.show"
            :type="_confirmButtonProps.type"
            size="small"
            @click="submit"
          >
            {{ _confirmButtonProps.text }}
          </el-button>
        </slot>
      </div>
    </div>
  </el-drawer>
</template>

<style lang="scss" scoped>
.el-drawer__body {
  height: 100%;

  .drawer_box {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .drawer-header {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      padding: 10px;
      height: 48px;
      border-bottom: 1px solid #dcdfe6;

      .detail-title {
        width: 100%;
      }
    }

    .drawer-content {
      padding: 10px;
      flex: 1;
      width: 100%;
      overflow: auto;
    }

    .drawer-footer {
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      border-top: 1px solid #dcdfe6;
      height: 64px;
    }
  }
}
</style>
