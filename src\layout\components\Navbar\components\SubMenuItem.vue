<template>
  <el-menu-item
    v-if="!item.children || !item.children.length"
    v-show="!item.hidden"
    :index="resolvePath(item.path)"
  >
    {{ item.meta.title }}
  </el-menu-item>
  <el-submenu v-else ref="subMenu" :index="resolvePath(item.path)">
    <template #title>
      {{ item.meta.title }}
    </template>
    <sub-menu-item
      v-for="(child, index) in item.children"
      :key="index"
      :item="child"
      :base-path="resolvePath(item.path)"
    />
  </el-submenu>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import { mapState } from 'vuex'
export default {
  name: 'SubMenuItem',
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    },
    // 层级
    lowChildNum: {
      type: Number,
      default: 0
    },
    itemClickNativeVal: {
      type: String,
      default: ''
    }
  },
  data() {
    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
    // TODO: refactor with render function
    return {
      currentLowChildNum: 0
    }
  },
  computed: {
    ...mapState({
      nightMode: ({ theme }) => theme?.nightMode
    })
  },
  mounted() {},
  methods: {
    resolvePath(_path) {
      if (isExternal(_path)) {
        return _path
      }
      if (isExternal(this.basePath)) {
        return this._path
      }
      return path.resolve(this.basePath, _path)
    },
    itemClickNative(to) {
      this.$emit('itemClickNative', to)
    }
  }
}
</script>
<style lang="scss" scoped>
.single-item {
  display: inline-block;
  .el-menu-item {
    height: 50px;
    line-height: 50px;
  }
}
</style>
