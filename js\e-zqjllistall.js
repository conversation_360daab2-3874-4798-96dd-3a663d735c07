let url = window.location.href.split("?") || []
let text = url[1].split("&")
let params = {}
for (let i of text) {
  let fd = i.split("=")
  params[fd[0]] = fd[1]
}
// console.log(params)
var obj1 = []
// 统一页面启动
$(document).ready(() => {
  // 1.专科知情记录列表，新增记录列表e_init
  WRT_e.api.bzqjllistall.getZqjlByList({
    params: {
      "as_zkid": params["as_zkid"],
      "as_blid": params["as_blid"]
    },
    success(data) {
      if (data.Code == 1) {
        // console.log("专科知情记录列表，新增记录列表",data)
        WRT_config.ZqjlByList = data.Result
        console.log("1.专科知情记录列表，新增记录列表e_init", WRT_config.ZqjlByList)
        // 142行由于不知道返回值样子所以随便写了个进去<所以如果有值就会报错>
        // console.log("e_init-Reslut",WRT_config.ZqjlByList.Result)
        init()
      }
    }
  })
})
/******************** 公共方法 ********************/
//父界面关闭调用时调用该接口
// function delTab(){
//   CheckWsChanged(val=>{
//     if(val){
//       return false;
//     }
//     else{
//       parent.delBlws()
//     }
//   })
// }
//父界面关闭调用时调用该接口
function delTab(){
  CheckWsChanged(val=>{
    if(val){
      return false;
    }
    else{
      parent.delBlws()
    }
  })
}
// 刷新
function bgmcNow(arr,i,cb){
  
  var obj_lst2 = document.querySelectorAll("#type_title"+arr[0].name);
  // console.log(33,obj_lst2[0].innerText ,arr,i,cb)
  
  // var bgmc=arr[i].contentWindow.name
  var bgmc1=obj_lst2[0].innerText
  let res=confirm(`【${bgmc1}】有正在编辑的页面数据发生变化，留在本页请点确定，关闭请点取消`)
  if(res){
    cb(true)
  }else{
    if(i<arr.length-1)bgmcNow(arr,i+1,cb);
      else
        cb(false)
  }
}
function CheckWsChanged(cb){
  var obj_lst = document.querySelectorAll(".panel iframe");
  var obj_lst1
  var obj_Lst=[]
  // 是否为ifream状态

  // console.log(obj_lst )
  // var popNew = $("#simpleModeModalnew").attr("style")
  // var popEdit=  $("#simpleModeModal").attr("style")
  //
  var addBoolean=$("#simpleModeModalnew").length!=0&&$("#simpleModeModalnew").css("display")=="block"
  //
  var editBoolean=$("#simpleModeModal").length!=0&&$("#simpleModeModal").css("display")=="block"
  
  if ((!addBoolean && !editBoolean) &&(obj_lst==null||obj_lst.length == 0)) {
    cb(false);
  }else {
    // console.log(44,obj_lst,addBoolean,editBoolean)
    if (addBoolean) {
      obj_lst1= document.querySelectorAll("#simpleModeModalnew iframe")
      obj_Lst=$.merge(Array.prototype.slice.call(obj_lst),Array.prototype.slice.call(obj_lst1));
      bgmcNow(obj_Lst,0,cb);
    } else if(editBoolean){
      obj_lst1= document.querySelectorAll("#simpleModeModal iframe")
      obj_Lst=$.merge(Array.prototype.slice.call(obj_lst),Array.prototype.slice.call(obj_lst1));
      bgmcNow(obj_Lst,0,cb);
    }else{
      bgmcNow(obj_lst,0,cb);
    }
  }
//   return true;
  return false;
}

/********************初始化********************/
function init() {
  //  2.根据文书类型获取病人已书写文书列表e_GetBrWssByLx(左侧汇总列表)
  WRT_e.api.bzqjllistall.getZqjlBrWssByLx({
    params: {
      "as_blid": params["as_blid"],
      "as_wslx": "^24^25^26^27^28^29^55^2A^2B^2C^2D^2E^2F^30"
    },
    success(data) {
      if (data.Code == 1) {
        WRT_config.ZqjlBrWssByLx = data.Result
        console.log("2.根据文书类型获取病人已书写文书列表e_GetBrWssByLx(左侧汇总列表)", WRT_config.ZqjlBrWssByLx)
        let json = WRT_config.ZqjlBrWssByLx.map(function (item) {
          item.content_Control = 0
          item.right_nrUrl = 0
          return item
        })
        // let timeArr =WRT_config.ZqjlBrWssByLx.sort(function (a, b) {
        //   var a_ = parseInt(a.JLSJ.substr(6,13))
        //   var b_ = parseInt(b.JLSJ.substr(6,13))
        //   return a_ - b_;
        //   // return b_ - a_;
        // }); 
        // // console.log('000111',timeArr,timeArr[0].GSMC,timeArr[0].JLSJ)
        for (var i = 0; i < json.length; i++) {
          var a = json[i]
          // console.log(a.ID)
          // 3.更新知情记录列表，新增记录列表e_UpdateZqjlHtml'
          WRT_e.api.bzqjllistall.getUpdateZqjlHtml({
            params: {
              // "as_zkid":params["as_zkid"],
              "as_wsid": a.ID,
              "as_blid": params["as_blid"]
            },
            success(data) {
              if (data.Code == 1) {
                WRT_config.UpdateZqjlHtml = data
                // console.log("3.更新知情记录列表，新增记录列表e_UpdateZqjlHtml",WRT_config.UpdateZqjlHtml)

              }
            }
          })
        }
        // 左
        $('.bzqjllistall_inner_left').append(
          new left_Menu().init({
            data: WRT_config.ZqjlBrWssByLx
          }).render().$el
        )
        // 右
        $('.panel-group').html(
          new right_title().init({
            data: {
              dataNR: WRT_config.ZqjlBrWssByLx,
              dataID: WRT_config.ZqjlBrWssByLx.ID
            }
          }).render().$el
        )
      }
    }
  })

  // 2.知情记录点击编辑e_GetWsUrl(右侧显示内返回值为url已经自行获取了)
  // WRT_e.api.bzqjllistall.getWsUrl({
  //   params: {"as_gsdm":"5327","al_wsid":-252,"as_wslx":"2A","al_blid":1492768,"al_zyid":1492768},
  //   success(data) {
  //     if (data.Code == 1) {
  //       WRT_config.WsUrl=data
  //       console.log(" 2.知情记录点击编辑e_GetWsUrl(右侧显示内容)",WRT_config.WsUrl)
  //     }
  //   }
  // })
  // ${JSON.stringify(obj.MenuList)}
}
//
function save(id) {
  if (document.getElementById("if_" + id).src.indexOf("blwsdetail") >= 0) {
    try {
      document.getElementById("if_" + id).contentWindow.ehr_save();
    }
    catch (e) {
      WRT_e.ui.message({
        type: 'error',
        title: '提示信息',
        content: `保存出错!`,
        onOk() {
        }
      })
    }
  }
  else {
    WRT_e.ui.message({
      title: '提示信息',
      content: `无保存功能!`,
      type_icon: `glyphicon-remove-sign`,
      onOk() {
      }
    })
  }
}
function printWs(id) {
  if (document.getElementById("if_" + id).src.indexOf("blwsdetail") >= 0) {
    try {
      document.getElementById("if_" + id).contentWindow.ehr_print();
    }
    catch (e) {
      WRT_e.ui.message({
        type: 'error',
        title: '提示信息',
        content: `打印出错!`,
        onOk() {
        }
      })

    }
  }
  else {
    document.getElementById("if_" + id).contentWindow.print();
  }
}
function delete_btn(id) {
  if (document.getElementById("if_" + id).src.indexOf("blwsdetail") >= 0) {
    try {
      var childWindow = $("#if_" + id)[0].contentWindow;
      childWindow.ehr_delete()
    }
    catch (e) {
      WRT_e.ui.message({
        type: 'error',
        title: '提示信息',
        content: `删除出错!`,
        onOk() {
        }
      })
    }
  }
  else {
    WRT_e.ui.message({
      title: '提示信息',
      content: `无删除功能!`,
      type_icon: `glyphicon-remove-sign`,
      onOk() {
      }
    })
  }
}
//查找回车事件
function calAge() {
  var evt = window.event || e;
  if (evt.keyCode == 13) {
    $("#left_Search_btn").click();
  }
}
// 列表
// 左侧
var left_Menu = WRT_e.view.extend({
  render: function () {
    // <span style="color:${obj.SPBZ && obj.SPBZ=='0'?`red`:`` }">${obj.GSMC}</span>
    console.log(9999000888,WRT_config);
    this.$el.html(`
    <div class="tab-content">
      <div class="tab-pane active in fade">
        <ul id="myTabs" class="nav nav-tabs" role="tablist">
          <li role="presentation" class="active">
            <a href="#已保存记录列表" aria-controls="已保存记录列表" role="tab" id="bqjl" class="bqjlss" aria-expanded="" value="已保存记录列表" data-toggle="tab">已保存记录列表</a>
          </li>
          <li role="presentation">
            <a href="#新增记录" aria-controls="新增记录" role="tab" id="xzjl" class="bqjlss" aria-expanded="" value="新增记录(常用)" data-toggle="tab">新增记录(常用)</a>
          </li>
          <li role="presentation">
            <a href="#搜索" aria-controls="搜索" role="tab""  id="bqjlss"class="bqjlss" value="搜索" role="tab" aria-expanded="" data-toggle="tab">搜索</a>
          </li>
        </ul>
      
      </div>
      <div class="tab-content advice" id="left_list_nr">
        <div role="tabpanel" class="tab-pane active zqhz" id="已保存记录列表">
        
          <div class="list_title">知情记录汇总</div>
          <div class="tab-pane" id="zqjlnr">
            <ul class="list_inner">
              ${_.map(WRT_config.ZqjlBrWssByLx, (obj) => `
              <li class="zqjlsubmenu" id="zqjlsubmenu${obj.ID}">
                <span>
                  <a href="javascript:void(0)" class="btnlist${obj.ID}" id="type_title${obj.ID}" name="${obj.ID}"  style="color:${obj.SPBZ && obj.SPBZ=='0'?`red`:obj.GDBZ && obj.GDBZ=='0'?`red`:`` }"  >
                    <i class="glyphicon glyphicon-triangle-right" style="color:#707070"></i>
                    ${obj.GSMC}
                  </a>
                </span>
              </li>`).join('')}
            </ul>
          </div>
        </div>

        <div class="tab-pane xzjl" role="tabpanel" id="新增记录">
          <div class="list_title">新增记录(常用)</div>
          <div class="tab-pane" id="zqjlnrxz">
            <ul class="list_inner">
            ${_.map(WRT_config.ZqjlByList.MenuList, (obj) => `
              <li class="updatesubmenu" id="updatesubmenu_${obj.DM}_${obj.WSLX}" name="${obj.DM}" gsdm="${obj.DM}" wslx="${obj.WSLX}" mc="${obj.MC}" wsid="0" name="${obj.DM}">
                ${obj.length != 0 ? `
                ${obj.WC == "1" ? `
                  <span>
                  <a href="javascript:void(0)" class="btnlist" id="type_title" name ="0">
                    ${obj.MC}( √ )
                  </a>
                  </span>`: `
                  <span>
                    <a href="javascript:void(0)" class="btnlist" id="type_title"  name ="0">
                      ${obj.MC}
                    </a>
                  </span>
                  `}
                `: ``}
              </li>`).join('')}
            </ul>
          </div>
        </div>

        <div class="tab-pane xzjlss" role="tabpanel" id="搜索">
          <div class="input-group left_Search">
            <input type="secrch" class="form-control left_Search_input"  data-options="searcher:searchZqjl,prompt:'请输入拼音码或者中文检索'"  id="left_Search_input" onkeydown="calAge(event)">
            <span class="input-group-btn">
              <button class="btn btn-default glyphicon glyphicon-search" id="left_Search_btn" type="button"></button>
            </span>
          </div>
          <div class="searchsubmenu"></div>
        </div>
      </div>
    </div>
    `)
    return this
  },
  events: {
    // 已保存记录列表（标签页选择）
    "click #bqjl": function () {
      WRT_e.api.bzqjllistall.getZqjlBrWssByLx({
        params: {
          "as_blid": params["as_blid"],
          // "as_wslx": "^24^25^26^27^28^29^55^2A^2B"
          "as_wslx": "^24^25^26^27^28^29^55^2A^2B^2C^2D^2E^2F^30"
        },
        success(data) {
          if (data.Code == 1) {
            WRT_config.ZqjlBrWssByLx = data.Result
            // console.log("2.根据文书类型获取病人已书写文书列表e_GetBrWssByLx(左侧汇总列表)",WRT_config.ZqjlBrWssByLx)
            // console.log($(".nav>li>a").attr("aria-expanded"))
            // 左
            $('#zqjlnr').html(
              new left_zqhzsx().init({
                data: WRT_config.ZqjlBrWssByLx
              }).render().$el
            )
          }
        }
      })
    },
    // 新增记录(常用)（标签页选择）
    "click #xzjl": function () {
      // console.log($(".nav>li>a").attr("aria-expanded"))
      // 1.专科知情记录列表，新增记录列表e_init
      // if(WRT_config.ZqjlByList.BLZT==1||WRT_config.ZqjlByList.STU==1){
      //   if(WRT_config.ZqjlByList.STU==1){
      //     WRT_e.ui.message({
      //       title: '提示',
      //       content: `没有医生代码或签名图片的学生账号不能书写知情同意书`,
      //       onOk() {
      //       },
      //     })
      //   }else{
      //     WRT_e.ui.message({
      //       title: '提示',
      //       content: `不允许新增记录`,
      //       onOk() {
      //       },
      //     })
      //   }
      //   return
      // }
      WRT_e.api.bzqjllistall.getZqjlByList({
        params: {
          "as_zkid": params["as_zkid"],
          "as_blid": params["as_blid"]
        },
        success(data) {
          if (data.Code == 1) {
            WRT_config.ZqjlByList = data.Result
            console.log("1.专科知情记录列表，新增记录列表e_init", WRT_config.ZqjlByList)
            // 左
            $('#zqjlnrxz').html(
              new left_zqxzcysx().init({
                data: WRT_config.ZqjlByList
              }).render().$el
            )
          }
        }
      })
    },
    // 左侧列表
    // 知情记录汇总
    "click .zqjlsubmenu": function (e) {
      // console.log(e)
      // console.log(e.target.name)
      let json = WRT_config.ZqjlBrWssByLx.map(function (item) {
        item.content_Control = 1
        if (item.GDBZ == "1") {
          item.right_nrUrl = "as_wsid=" + item.ID + "&as_blid=" + item.BLID
          return item
        } else {
          item.right_nrUrl = "as_blid=" + params["as_blid"] + "&as_gsdm=" + item.GSDM + "&as_zyid=" + params["as_zyid"] + "&as_wsid=" + item.ID + "&as_wslx=" + item.WSLX
          return item
        }
      })
      // console.log("item:",json)
      var a = json.find(ev => ev.ID == e.target.name)
      console.log(a)
      if (a.ID == e.target.name) {
        $('.panel-group').html(
          new right_Menu().init({
            data: {
              dataNR: a,
              dataID: a.ID,
              dataUrl: a.right_nrUrl,
              dataMC: a.GSMC
            }
          }).render().$el
        )
      }
    },
    // 新增记录(常用)
    "click .updatesubmenu": function (e) {
      if(WRT_config.ZqjlByList.BLZT==1||WRT_config.ZqjlByList.STU==1){
        if(WRT_config.ZqjlByList.STU==1){
          WRT_e.ui.message({
            title: '提示',
            content: `没有医生代码或签名图片的学生账号不能书写知情同意书`,
            onOk() {
            },
          })
        }else{
          WRT_e.ui.message({
            title: '提示',
            content: `该病人病历已归档无法新增知情记录，请解封后再新增`,
            onOk() {
            },
          })
        }
        return
      }
      var xzarray = (e.currentTarget.id).split("_")
      // console.log(xzarray)
      let json = WRT_config.ZqjlByList.MenuList.map(function (item) {
        item.lsqk = 1
        if (item.DM == xzarray[1]) {
          item.right_nrUrl = "as_blid=" + params["as_blid"] + "&as_gsdm=" + xzarray[1] + "&as_zyid=" + params["as_zyid"] + "&as_wsid=0&as_wslx=" + xzarray[2] + "&tmpid=0.8908087397992528"
          item.ID = 0
          // item.lsqk= 1
          return item
        } else {
          return item
        }
      })
      console.log("item:", json)
      var tcurl = json.find(e => e.DM == xzarray[1])
      console.log("item111:", tcurl)
      $('.panel-group').html(
        new right_Menu_xz().init({
          data: {
            dataRes: tcurl,
            dataResID: tcurl.DM,
            dataID: tcurl.ID,
            dataUrl: tcurl.right_nrUrl,
            dataParam: WRT_config.ZqjlBrWssByLx,
            dataMC: tcurl.MC
          }
        }).render().$el
      )
    },
    // 搜索
    "click #left_Search_btn": function () {
      // console.log("搜索")
      var input_val = document.getElementById("left_Search_input").value
      // console.log(input_val)

      if (input_val != '') {
        // 1、知情记录列表，搜索e_GetZqjlByKey(补充接口)
        WRT_e.api.bzqjllistall.getZqjlByKey({
          params: {
            "as_key": $("#left_Search_input").val(),
            "as_wslx": "00"
          },
          success(data) {
            if (data.Code == 1) {
              let json = JSON.parse(data.Result)
              WRT_config.ZqjlByKey = json
              // console.log("1、知情记录列表，搜索e_GetZqjlByKey(补充接口):",WRT_config.ZqjlByKey)
              // $('#left_Search_input').bind('keypress', function (event) { 
              //   if (event.keyCode == "13") { 
              //     $("#left_Search_btn").click();
              //   }
              // })
              $('.searchsubmenu').html(
                new left_search_res().init({
                  data: WRT_config.ZqjlByKey
                }).render().$el
              )
            }
          }
        })
      } else {
        $(".search_res").empty();//find("li").remove();
      }
    }
  }
})

// 重新获取左侧1(已保存)
var left_zqhzsx = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
    <ul class="list_inner">
      ${_.map(WRT_config.ZqjlBrWssByLx, (obj) => `
      <li class="zqjlsubmenu" id="zqjlsubmenu${obj.ID}">
        <span>
          <a href="javascript:void(0)" class="btnlist${obj.ID}" id="type_title${obj.ID}" name="${obj.ID}">
            <i class="glyphicon glyphicon-triangle-right"></i>
            ${obj.GSMC}
          </a>
        </span>
      </li>`).join('')}
    </ul>
    `)
    return this
  }
})
// 重新获取左侧2（新增记录）
var left_zqxzcysx = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
    <ul class="list_inner">
    ${_.map(this.data.MenuList, (obj) => `
      <li class="updatesubmenu" id="updatesubmenu_${obj.DM}_${obj.WSLX}" gsdm="${obj.DM}" wslx="${obj.WSLX}" wsid="0">
        ${obj.length != 0 ? `
        ${obj.WC == "1" ? `
          <span>
          <a href="javascript:void(0)" class="btnlist" id="type_title" name ="0">
            ${obj.MC}( √ )
          </a>
          </span>`: `
          <span>
            <a href="javascript:void(0)" class="btnlist" id="type_title"  name ="0">
              ${obj.MC}
            </a>
          </span>
          `}
        `: ``}
      </li>`).join('')}
    </ul>
    `)
    return this
  }
})
// 列表右侧
// 默认头
var right_title = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
      <div class="panel panel-default" id="panel">
      <div class="panel-title1" href="#type" id="type_title">
        <span>文书内容</span>
        <span class=r"zqsave"><a class="zqsaveclick" id="zqsave">保存</a></span>
        <span class="zqdel"><a class="zqdelclick" id="zqdel">删除</a></span>
        <span class="zqdy" ><a class="zqdyclick" id="zqdyv">打印</a></span>
      </div>
      <div id="type" class="panel-collapse collapse in">
        <div class="blzqnr"></div>
      </div>
    </div>`)
    return this
  },
  events: {
    "click .zqsaveclick": function () {
      WRT_e.ui.message({
        title: '提示',
        content: `无保存功能`,
        onOk() {
        }
      })
    },
    "click .zqdelclick": function () {
      WRT_e.ui.message({
        title: '提示',
        content: `无删除功能`,
        onOk() {
        }
      })
    },
    "click .zqdyclick": function () {
      WRT_e.ui.message({
        title: '提示',
        content: `无打印功能`,
        onOk() {
        }
      })
    }
  }
})
// 右侧内容（显示）
var right_Menu = WRT_e.view.extend({
  render: function () {
    // console.log(this.data.dataNR)
    this.$el.html(`
      <div class="panel panel-default" id="panel${this.data.dataID}" name="${this.data.dataID}">
        <div class="panel-title" href="#type" id="type_title${this.data.dataID}">
          <span class="title${this.data.dataID}" style="float:left;padding-left: 10px;">${this.data.dataMC}</span>
          <span class="btnTitle">
            <span class="zqsave"><a class="zqsaveclick " id="zqsave${this.data.dataID}" name="${this.data.dataID}">保存</a></span>
            <span class="zqdel"><a class="zqdelclick " id="zqdel${this.data.dataID}" name="${this.data.dataID}">删除</a></span>
            <span class="zqdy" ><a class="zqdyclick " id="zqdyv${this.data.dataID}" name="${this.data.dataID}">打印</a></span>
          </span>
        </div>
        <div id="type" class="panel-collapse collapse in" data-stopPropagation="true">
          ${this.data.dataNR.GDBZ == "1" ? `
          <iframe id="if_${this.data.dataID}" name="${this.data.dataID}" src="${WRT_config.server}/zyblws/zyblwsPdf.aspx?` + this.data.dataUrl + `" rameborder="0" width="100%" height="700px"></iframe>
          `: `
          <iframe id="if_${this.data.dataID}" name="${this.data.dataID}" src="${WRT_config.server}/zyblws/blwsdetail.aspx?` + this.data.dataUrl + `" rameborder="0" width="100%" height="700px"></iframe>
          `}
        </div>
      </div>
    `)
    return this
  },
  events: {
    // 保存
    "click .zqsaveclick": function (esave) {
      // console.log("保存")
      // console.log(esave)
      // console.log(esave.target.name)
      esave.stopPropagation()
      save(esave.target.name)
    },
    // 删除
    "click .zqdelclick": function (edel) {
      // console.log("删除")
      edel.stopPropagation()
      // console.log(edel)
      // console.log("按钮名字：",edel.target.name)
      delete_btn(edel.target.name)
      // var childWindow = $("#if_"+edel.target.name)[0].contentWindow; 
      // childWindow.ehr_delete()

    },
    // 打印
    "click .zqdyclick": function (edy) {
      // console.log("打印")
      // console.log(edy)
      // console.log(edy.target.name)
      edy.stopPropagation()
      let jsondy = WRT_config.ZqjlBrWssByLx.map(function (item) {
        item.content_Control = 1
        if (item.GDBZ == "1") {
          item.right_nrUrl = "as_wsid=" + item.ID + "&as_blid=" + item.BLID
          return item
        } else {
          item.right_nrUrl = "as_blid=" + params["as_blid"] + "&as_gsdm=" + item.GSDM + "&as_zyid=" + params["as_zyid"] + "&as_wsid=" + item.ID + "&as_wslx=" + item.WSLX
          return item
        }
        // item.right_nrUrl = "as_blid="+params["as_blid"]+"&as_gsdm="+item.GSDM+"&as_zyid="+params["as_zyid"]+"&as_wsid="+item.ID+"&as_wslx="+item.WSLX+"&tmpid=0.2328870294890346"
        // return item
      })
      // console.log("item:",jsondy)
      printWs(edy.target.name)
    },
  }
})

// 新增
var right_Menu_xz = WRT_e.view.extend({
  render: function () {
    console.log(this.data.dataRes.lsqk)
    // var a = json.find(ev => ev.DM==this.data.dataResID)
    this.$el.html(`
      <div class="panel panel-default" id="panel${this.data.dataID}" name="${this.data.dataID}">
        <div class="panel-title" href="#type" id="type_title${this.data.dataID}">
          ${this.data.dataRes.lsqk == 1 ? `
          <span>
            <span class="add${this.data.dataID}">新增&nbsp;&nbsp;&nbsp;&nbsp;</span>
            <span class="title${this.data.dataID}">${this.data.dataMC}</span>
          </span>` : `
          <span class="title">${this.data.dataMC}</span>
          `}
          <span class="btnTitle">
            <span class="zqsave"><a class="zqsaveclick" id="zqsave${this.data.dataID}" name="${this.data.dataID}">保存</a></span>
            <span class="zqdel"><a class="zqdelclick" id="zqdel${this.data.dataID}" name="${this.data.dataID}">删除</a></span>
            <span class="zqdy" ><a class="zqdyclick" id="zqdyv${this.data.dataID}" name="${this.data.dataID}">打印</a></span>
          </span>
        </div>
        <div id="type" class="panel-collapse collapse in" data-stopPropagation="true">
          <iframe id="if_${this.data.dataID}" name="${this.data.dataID}" src="${WRT_config.server}/zyblws/blwsdetail.aspx?` + this.data.dataUrl + `" rameborder="0" width="100%" height="700px"></iframe>
        </div>
      </div>
    `)
    return this
  },
  events: {
    // 保存
    "click .zqsaveclick": function (esave) {
      // console.log("保存")
      // console.log(esave)
      // console.log(esave.target.name)
      esave.stopPropagation()
      save(esave.target.name)
    },
    // 删除
    "click .zqdelclick": function (edel) {
      // console.log("删除")
      edel.stopPropagation()
      // console.log(edel)
      // console.log("按钮名字：",edel.target.name)
      var childWindow = $("#if_" + edel.target.name)[0].contentWindow;
      childWindow.ehr_delete()
    },
    // 打印
    "click .zqdyclick": function (edy) {
      // console.log("打印")
      // console.log(edy)
      // console.log(edy.target.name)
      edy.stopPropagation()
      let jsondy = WRT_config.ZqjlBrWssByLx.map(function (item) {
        item.content_Control = 1
        item.right_nrUrl = "as_blid=" + params["as_blid"] + "&as_gsdm=" + item.GSDM + "&as_zyid=" + params["as_zyid"] + "&as_wsid=" + item.ID + "&as_wslx=" + item.WSLX + "&tmpid=0.2328870294890346"
        return item
      })
      if (edy.target.name == 0) {
        WRT_e.ui.message({
          title: '提示信息',
          content: `请先保存再打印!`,
          onOk() {
          }
        })
      } else {
        printWs(edy.target.name)
      }
    },
  }
})


// 搜索(左)
var left_search_res = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
    <ul class="search_res">
      ${_.map(WRT_config.ZqjlByKey, (obj) => `
        <li class="search_res_nr" id="search_res_nr${obj.DM}" name="${obj.DM}">
        <span>
          <a href="javascript:void(0)" class="btnlist${obj.DM}" id="type_title${obj.DM}" name="${obj.DM}">
            <i class="glyphicon glyphicon-triangle-right"></i>
            ${obj.MC}
          </a>
        </span>
        </li>
      `).join(``)}
    </ul>`)
    return this
  },
  events: {
    "click .search_res_nr": function (e1) {
      // console.log("搜索内容点击")
      // console.log(e1, e1.target.name)
      // 搜索新增弹窗控制
      if(WRT_config.ZqjlByList.BLZT==1||WRT_config.ZqjlByList.STU==1){
        if(WRT_config.ZqjlByList.STU==1){
          WRT_e.ui.message({
            title: '提示',
            content: `没有医生代码或签名图片的学生账号不能书写知情同意书`,
            onOk() {
            },
          })
        }else{
          WRT_e.ui.message({
            title: '提示',
            content: `该病人病历已归档无法新增知情记录，请解封后再新增`,
            onOk() {
            },
          })
        }
        return
      }
      let json = WRT_config.ZqjlByKey.map(function (item) {
        item.lsqk = 1
        item.right_sreach_nrUrl = "as_blid=" + params["as_blid"] + "&as_gsdm=" + item.DM + "&as_zyid=" + params["as_zyid"] + "&as_wsid=0&as_wslx=" + item.WSLX + "&tmpid=0.4132779541427096"
        item.ID = 0
        return item
      })
      // console.log("item:",json)
      var a = json.find(ev => ev.DM == e1.target.name)
      // var b1 = json.find(ev1 => ev1.right_nrUrl)
      var url = json.find(evurl => evurl.right_sreach_nrUrl)
      // console.log(url.right_sreach_nrUrl)
      $('.panel-group').html(
        new right_Menu_Search().init({
          data: {
            dataRes: WRT_config.ZqjlByKey,
            dataResID: a.DM,
            dataID: a.ID,
            dataParam: WRT_config.ZqjlBrWssByLx,
            dataMC: a.GSMC
          }
        }).render().$el
      )
    }
  }
})
// 搜索显示(右侧)
var right_Menu_Search = WRT_e.view.extend({
  render: function () {
    let json = WRT_config.ZqjlByKey.map(function (item) {
      return item
    })
    // console.log("item:",json)
    var a = json.find(ev => ev.DM == this.data.dataResID)
    this.$el.html(`
      <div class=" panel panel-default" id="panel${a.ID}" name="${a.ID}">
        <div class="panel-title" href="#type" id="type_title${a.ID}">
          ${a.lsqk == 1 ? `
          <span>
            <span class="add${a.ID}">新增&nbsp;&nbsp;&nbsp;&nbsp;</span>
            <span class="title${a.ID}">${a.MC}</span>
          </span>` : `
          <span class="title${this.data.dataID}">${this.data.dataMC}</span>
          `}
          <span class="btnTitle">
            <span class="zqsave"><a class="zqsaveclick" id="zqsave${a.ID}" name="${a.ID}">保存</a></span>
            <span class="zqdel"><a class="zqdelclick" id="zqdel${a.ID}" name="${a.ID}">删除</a></span>
            <span class="zqdy" ><a class="zqdyclick" id="zqdyv${a.ID}" name="${a.ID}">打印</a></span>
          </span>
        </div>
        <div id="type" class="panel-collapse collapse in">
          <iframe id="if_${a.ID}" name="${a.ID}" src="${WRT_config.server}/zyblws/blwsdetail.aspx?` + a.right_sreach_nrUrl + `" rameborder="0" width="100%" height="700px"></iframe>
        </div>
      </div>
    `)
    return this
  },
  events: {
    // 保存
    "click .zqsaveclick": function (esave) {
      // console.log("保存")
      // console.log(esave)
      // console.log(esave.target.name)
      esave.stopPropagation()
      save(esave.target.name)
    },
    // 删除
    "click .zqdelclick": function (edel) {
      // console.log("删除")
      edel.stopPropagation()
      // console.log(edel)
      // console.log("按钮名字：",edel.target.name)
      var childWindow = $("#if_" + edel.target.name)[0].contentWindow;
      childWindow.ehr_delete()
    },
    // 打印
    "click .zqdyclick": function (edy) {
      // console.log("打印")
      // console.log(edy)
      // console.log(edy.target.name)
      edy.stopPropagation()
      let jsondy = WRT_config.ZqjlBrWssByLx.map(function (item) {
        item.content_Control = 1
        item.right_nrUrl = "as_blid=" + params["as_blid"] + "&as_gsdm=" + item.GSDM + "&as_zyid=" + params["as_zyid"] + "&as_wsid=" + item.ID + "&as_wslx=" + item.WSLX + "&tmpid=0.2328870294890346"
        return item
      })
      if (edy.target.name == 0) {
        WRT_e.ui.message({
          title: '提示信息',
          content: `请先保存再打印!`,
          onOk() {
          }
        })
      } else {
        printWs(edy.target.name)
      }
    },
  }
})


// 回掉函数
function save_callback(wsid, jlsj, gsdm, wslx, gsmc, tmpid, sscx,type) {
  // console.log("wsid:",wsid,";", "jlsj:",jlsj,";", "gsdm:",gsdm,";", "wslx:",wslx,";", "gsmc:",gsmc,";", "tmpid:",tmpid,";", "sscx:",sscx)
  WRT_e.ui.hint({
    type: 'success',
    msg: type||'保存成功'
  })
  setTimeout(() => {
    // console.log($("#bqjlss").attr("aria-expanded"))
    if (WRT_config.ZqjlByKey != undefined && ($("#bqjlss").attr("aria-expanded") == "true")) {
      let json = WRT_config.ZqjlByKey.map(function (item) {
        item.lsqk = 0
        item.ID = wsid
        item.right_sreach_nrUrl = "as_blid=" + params["as_blid"] + "&as_gsdm=" + item.DM + "&as_zyid=" + params["as_zyid"] + "&as_wsid=" + wsid + "&as_wslx=" + item.WSLX
        return item
      })
      // console.log("1234",json)
      var a = json.find(ev => ev.DM == gsdm)
      // console.log(a)

      $('.panel-group').html(
        new right_Menu_Search().init({
          data: {
            dataRes: WRT_config.ZqjlByKey,
            dataResID: gsdm,
            dataID: wsid,
            dataParam: WRT_config.ZqjlBrWssByLx,
            dataMC: jlsj + gsmc
          }
        }).render().$el
      )
    } else if (WRT_config.UpdateZqjlHtml.Result != undefined && ($("#xzjl").attr("aria-expanded") == "true")) {
      // console.log('测试',WRT_config.UpdateZqjlHtml.Result != undefined && ($("#xzjl").attr("aria-expanded") == "true"))
      let json2 = WRT_config.UpdateZqjlHtml.Result
      json2.right_nrUrl = "as_blid=" + params["as_blid"] + "&as_gsdm=" + gsdm + "&as_zyid=" + params["as_zyid"] + "&as_wsid=" + wsid + "&as_wslx=" + $(".updatesubmenu").attr("wslx") + "&tmpid=0.8908087397992528"
      json2.ID = wsid
      json2.lsqk = 0
      // console.log("23454",json2)
      // 1.专科知情记录列表，新增记录列表e_init
      WRT_e.api.bzqjllistall.getZqjlByList({
        params: {
          "as_zkid": params["as_zkid"],
          "as_blid": params["as_blid"]
        },
        success(data) {
          if (data.Code == 1) {
            WRT_config.ZqjlByList = data.Result
            // console.log("1.专科知情记录列表，新增记录列表e_init",WRT_config.ZqjlByList)
            // 左
            $('#zqjlnrxz').html(
              new left_zqxzcysx().init({
                data: WRT_config.ZqjlByList
              }).render().$el
            )
          }
        }
      })
      $('.panel-group').html(
        new right_Menu_xz().init({
          data: {
            dataRes: WRT_config.ZqjlByList,
            dataResID: gsdm,
            dataID: json2.ID,
            dataUrl: json2.right_nrUrl,
            dataParam: WRT_config.ZqjlBrWssByLx,
            dataMC: jlsj + gsmc
          }
        }).render().$el
      )
    }
  }, 10)

}
function del_callback(wsid, tmpid, sscx, wslx) {
  // console.log(wsid, tmpid, sscx, wslx)
  WRT_e.ui.hint({
    type: 'success',
    msg: '删除成功'
  })
  WRT_e.api.bzqjllistall.getZqjlBrWssByLx({
    params: {
      "as_blid": params["as_blid"],
      // "as_wslx": "^24^25^26^27^28^29^55^2A^2B"
      "as_wslx": "^24^25^26^27^28^29^55^2A^2B^2C^2D^2E^2F^30"
    },
    success(data) {
      if (data.Code == 1) {
        WRT_config.ZqjlBrWssByLx = data.Result
        console.log("2.根据文书类型获取病人已书写文书列表e_GetBrWssByLx(左侧汇总列表)", WRT_config.ZqjlBrWssByLx)

        // 左
        $('#zqjlnr').html(
          new left_zqhzsx().init({
            data: WRT_config.ZqjlBrWssByLx
          }).render().$el
        )
        // 1.专科知情记录列表，新增记录列表e_init
        WRT_e.api.bzqjllistall.getZqjlByList({
          params: {
            "as_zkid": params["as_zkid"],
            "as_blid": params["as_blid"]
          },
          success(data) {
            if (data.Code == 1) {
              WRT_config.ZqjlByList = data.Result
              // console.log("1.专科知情记录列表，新增记录列表e_init",WRT_config.ZqjlByList)
              // 左
              $('#zqjlnrxz').html(
                new left_zqxzcysx().init({
                  data: WRT_config.ZqjlByList
                }).render().$el
              )
            }
          }
        })
      }
    }
  })
  // 右
  $('.panel-group').html(
    new right_title().init({
      data: {
        dataNR: WRT_config.ZqjlBrWssByLx,
        dataID: WRT_config.ZqjlBrWssByLx.ID
      }
    }).render().$el
  )
}


