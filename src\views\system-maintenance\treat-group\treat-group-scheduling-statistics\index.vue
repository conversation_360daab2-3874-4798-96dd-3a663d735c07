<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <div class="header-item-title">排班年月:</div>
        <div>
          <el-date-picker v-model="paiBanNY" type="month" placeholder="请选择年月"></el-date-picker>
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">选择专科:</div>
        <div>
          <el-select v-model="zhuanKeMC" placeholder="请选择专科" size="small">
            <el-option
              v-for="item in zhuanKeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="header-item">
        <div>
          <el-select v-model="paiBanType">
            <el-option
              v-for="item in paiBanOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="header-item-button"><el-button>查询</el-button></div>
        <div class="header-item-button"><el-button>查询全院</el-button></div>
        <div class="header-item-button"><el-button>导出</el-button></div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">治疗组排班统计</div>
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData">
          <el-table-column prop="zhuanKeMC" label="专科"></el-table-column>
          <el-table-column prop="zuMing" label="组名"></el-table-column>
          <el-table-column prop="guDingCWS" label="固定床位数"></el-table-column>
          <el-table-column prop="buGuDingCWS" label="不固定床位数"></el-table-column>
          <el-table-column prop="xiuGaiSJ" label="修改时间"></el-table-column>
          <el-table-column prop="shenPiSJ" label="审批时间"></el-table-column>
          <el-table-column prop="zhiDaoLS" label="指导老师"></el-table-column>
          <el-table-column prop="zhuZhenYS" label="主诊医师(组长)"></el-table-column>
          <el-table-column prop="zhuGuanYS" label="主管医师"></el-table-column>
          <el-table-column prop="jingZhiYS" label="经治医师"></el-table-column>
          <el-table-column prop="lunZhuanYS" label="轮转医师"></el-table-column>
          <el-table-column prop="qiTa" label="其他"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      paiBanNY: '',
      zhuanKeMC: '',
      zhuanKeOptions: [
        {
          value: '1',
          label: '消化内科'
        }
      ],
      paiBanType: '0',
      paiBanOption: [
        {
          value: '0',
          label: '正常排班'
        },
        {
          value: '1',
          label: '所有'
        }
      ],
      tableData: [
        {
          zhuanKeMC: '心脑血管科',
          zuMing: '心血管内科胡开宇组',
          guDingCWS: '',
          buGuDingCWS: '',
          xiuGaiSJ: '',
          shenPiSJ: '',
          zhiDaoLS: '',
          zhuZhenYS: '胡开宇',
          zhuGuanYS: '',
          jingZhiYS: '周江华,朱千里',
          lunZhuanYS: '',
          qiTa: ''
        }
      ]
    }
  },
  methods: {
    handleClick(row) {
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 720px;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
