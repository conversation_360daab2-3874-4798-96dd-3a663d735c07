<template>
  <div class="container">
    <div class="header">
      <div class="header-item" style="width: 10%">
        <el-select v-model="jiaoJieBanLX">
          <el-option
            v-for="item in jiaoJieBanLXOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <div class="header-item">
        <div style="margin-right: 10px; font-weight: bold">时间范围:</div>
        <el-date-picker
          v-model="chaXunSJ"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd 00:00:00"
        ></el-date-picker>
      </div>
      <div class="header-item">
        <div style="margin-right: 10px">搜索关键词:</div>
        <div class="header-item-input">
          <el-input v-model="search" placeholder="请输入内容"></el-input>
        </div>
        <div class="header-item-button">
          <el-button @click="handleQuery">查询</el-button>
          <el-button class="print">打印</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">本专科交接班记录</div>
        <div class="button"><el-button type="primary" @click="handleAdd">新增</el-button></div>
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData">
          <el-table-column align="center" type="index" width="70"></el-table-column>
          <el-table-column align="center" type="selection" width="70"></el-table-column>
          <el-table-column prop="jiaoBanSJ" width="180" label="交班时间"></el-table-column>
          <el-table-column prop="jieBanSJ" width="180" label="接班时间"></el-table-column>
          <el-table-column prop="zhuanKeMC" width="119" label="科室"></el-table-column>
          <el-table-column prop="jiLuRYMC" width="160" label="交班医生"></el-table-column>
          <el-table-column prop="jieBanRYMC" width="88" label="接班医生"></el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">查看</el-button>
              <el-divider direction="vertical"></el-divider>
              <el-button type="text" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible" width="40%" :before-close="handleClose">
      <div slot="title" class="dialog-title">
        无纸化病人-{{ handleMode == 'update' ? '查看' : '新增' }}
      </div>
      <table>
        <tbody>
          <tr>
            <td class="info-label">病案号:</td>
            <td class="info-value">
              <el-input v-model="bingAnHao"></el-input>
            </td>
            <td class="info-label">病人名称:</td>
            <td class="info-value">
              <el-input v-model="bingRenMC"></el-input>
            </td>
          </tr>
          <tr>
            <td class="info-label">科室:</td>
            <td class="info-value">
              <el-input v-model="zhuanKe"></el-input>
            </td>
            <td class="info-label">入院时间:</td>
            <td class="info-value">
              <el-input v-model="ruYuanSJ"></el-input>
            </td>
          </tr>
          <tr>
            <td class="info-label">出院时间:</td>
            <td class="info-value">
              <el-input v-model="chuYuanSJ"></el-input>
            </td>
            <td class="info-label">操作:</td>
            <td class="info-value">
              <!-- <div class="flex">
                <el-radio v-model="zhuangTai" label="0">启用</el-radio>
                <el-radio v-model="zhuangTai" label="1">停用</el-radio>
              </div> -->
            </td>
          </tr>
        </tbody>
      </table>
      <div class="bottom-btn-group">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { searchAllInfoOfSR } from '@/api/specialist-menu'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      jiaoJieBanLXOptions: [
        {
          label: '未接班',
          value: 2
        },
        {
          label: '已接班',
          value: 1
        },
        {
          label: '全部',
          value: ''
        }
      ], //主页交接班类型列表
      jiaoJieBanLX: '', //交接班类型选择值
      chaXunSJ: '', //主页查询时间
      search: '', //主页搜索关键词
      tableData: [],
      dialogVisible: false,
      handleMode: '',
      bingAnHao: '',
      bingRenMC: '',
      zhuanKe: '',
      bingQu: '',
      bingChuangHao: '',
      ruYuanSJ: '',
      chuYuanSJ: '',
      tiJiaoYS: ''
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    })
  },
  methods: {
    handleClick(row) {
      this.dialogVisible = true
      this.handleMode = 'update'
      this.bingAnHao = row.bingAnHao
      this.bingRenMC = row.bingRenMC
      this.zhuanKe = row.zhuanKe
      this.ruYuanSJ = row.ruYuanSJ
      this.chuYuanSJ = row.chuYuanSJ
      console.log(row)
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleAdd() {
      this.handleMode = 'add'
      this.dialogVisible = true
      this.bingAnHao = ''
      this.bingRenMC = ''
      this.zhuanKe = ''
      this.ruYuanSJ = ''
      this.chuYuanSJ = ''
    },
    async handleQuery() {
      let res = await searchAllInfoOfSR({
        zhuanKeID: this.zhuanKeID,
        kaiShiSJ: this.chaXunSJ[0],
        jieShuSJ: this.chaXunSJ[1],
        guanJianZi: this.search
        // jiaoJieBanLX: this.jiaoJieBanLX
      })
      this.tableData = res.data
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
  .table {
    min-height: 650px;
    width: 70%;
  }
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 70%;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}

::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
td {
  border: 1px solid #ddd;
  border-collapse: collapse; /* 移除表格内边框间的间隙 */
  height: 35px;
  padding: 10px;
}
.info-label {
  text-align: right;
  width: 10vw;
  background-color: #eaf0f9;
}
.info-value {
  width: 14vw;
  height: 30px;
  background-color: #f7f9fd;
}
.flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.no-padding {
  padding: 0;
  .no-padding-label {
    padding: 10px;
  }
  ::v-deep .el-button {
    flex: 1;
    height: 100%;
  }
}
.bottom-btn-group {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 30px;
  ::v-deep .el-button {
    margin-left: 10px;
  }
}
::v-deep .print {
  background-color: #356ac5 !important;
}
</style>
