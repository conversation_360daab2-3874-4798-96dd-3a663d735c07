WRT_e.api = WRT_e.api || {}

WRT_e.api.au = {
  //登录判断 params, success, error
  login: function (o = {}) {
    o.params = o.params || {}
    $.ajax({
      url: WRT_config.server + '/login.aspx/e_login',
      type: 'post',
      data: JSON.stringify(o.params),
      dataType: "json",
      contentType: "application/json; charset=utf-8",
      success: function (msg) {
        if (msg.d.Code === "1") {
          o.msg = msg.d
          if (o.success) o.success(o.msg)
        } else {
          //验证错误
          WRT_e.ui.hint({
            type: 'error',
            msg: msg.d.CodeMsg
          })
        }
      },
    })
  },
  //单点登陆
  loginToken: function (o = {}) {
    o.params = o.params || {}
    $.ajax({
      url: WRT_config.server2 + '/authentication/v1/login/getTokenUser?yingYongDM=020',
      type: 'GET',
      beforeSend: function (XMLHttpRequest) {
        XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
      },
      data: '',
      dataType: "json",
      contentType: "application/json; charset=utf-8",
      success: function (msg) {
        if (msg.hasError == "0") {
          o.msg = msg
          if (o.success) o.success(o.msg)
        } else {
          //验证错误
          WRT_e.ui.hint({
            type: 'error',
            msg: msg.d.CodeMsg
          })
          // setTimeout(function(){
          //   let url="http://wsyy.Wzhospital.cn/web/auth/#/logout?url_redirect="+encodeURIComponent(window.location.href)+'&app_key=3d6yugnzd8xyf9fqe0oh4gyjuyj3ced46yamxh3ix3cyx0ftyybdvuj4737qz475'
          //   window.location.href=url
          // },1000)
        }
      },
      error(error){
        o.error = error
        if (o.error) o.success(o.error)
      }
    })
  },
  //校验Token
  getTokenUser: function (o = {}) {
    o.params = o.params || {}
    $.ajax({
      url: WRT_config.server2 + '/authentication/v1/login/getTokenUser',
      type: 'GET',
      beforeSend: function (XMLHttpRequest) {
        XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
      },
      data: '',
      dataType: "json",
      contentType: "application/json; charset=utf-8",
      success: function (msg) {
        if (msg.hasError == "0") {
          o.msg = msg
          if (o.success) o.success(o.msg)
        } else {
          //验证错误
          WRT_e.ui.hint({
            type: 'error',
            msg: msg.d.CodeMsg
          })
          setTimeout(function(){
            let url="https://wsyy.wzhospital.cn/web/auth/#/resetToken?url_redirect="+encodeURIComponent(window.location.href)+'&app_key=3d6yugnzd8xyf9fqe0oh4gyjuyj3ced46yamxh3ix3cyx0ftyybdvuj4737qz475'
            window.location.href=url
          },1000)
        }
      },
      error:function(){
        let url="https://wsyy.wzhospital.cn/web/auth/#/resetToken?url_redirect="+encodeURIComponent(window.location.href)+'&app_key=3d6yugnzd8xyf9fqe0oh4gyjuyj3ced46yamxh3ix3cyx0ftyybdvuj4737qz475'
        window.location.href=url
      }
    })
  },
  //单点登陆获取信息
  getTokeninfo: function (o = {}) {
    o.params = o.params || {}
    $.ajax({
      url: WRT_config.server + '/login.aspx/e_singleLogin',
      type: 'post',
      data: JSON.stringify(o.params),
      dataType: "json",
      contentType: "application/json; charset=utf-8",
      success: function (msg) {
        if (msg.d.Code === "1") {
          o.msg = msg.d
          if (o.success) o.success(o.msg)
        } else {
          //验证错误
          WRT_e.ui.hint({
            type: 'error',
            msg: msg.d.CodeMsg
          })
        }
      },
    })
  },
  Getinit: function (o = {}) {
    o.params = o.params || {}
    $.ajax({
      url: WRT_config.server + '/login.aspx/e_init',
      type: 'post',
      dataType: "json",
      contentType: "application/json; charset=utf-8",
      success: function (msg) {
        o.msg = msg.d
        if (o.success) o.success(o.msg)
      },
    })
  },
  getCheckCode: function (o = {}) { //获取验证码
    o.params = o.params || {}
    $.ajax({
      url: WRT_config.server + '/login.aspx/e_vcodes',
      type: 'post',
      data: JSON.stringify(o.params),
      dataType: "json",
      contentType: "application/json; charset=utf-8",
      success: function (msg) {
        if (o.success) o.success(msg.d)
      },
      error: function (error) {
        WRT_e.ui.hint({
          type: 'error',
          msg: JSON.parse(error.responseText).errorMessage
        })
      }
    })
  },
  //获取url
  getQRinit: function (o = {}) { 
    o.params = o.params || {}
    $.ajax({
      url: WRT_config.server + '/login.aspx/e_ewminit',
      type: 'post',
      data: JSON.stringify(o.params),
      dataType: "json",
      contentType: "application/json; charset=utf-8",
      success: function (msg) {
        if (o.success) o.success(msg.d)
      },
      error: function (error) {
        WRT_e.ui.hint({
          type: 'error',
          msg: JSON.parse(error.responseText).errorMessage
        })
      }
    })
  },
  getQRCode: function (o = {}) { //获取验证码
    o.params = o.params || {}
    $.ajax({
      url: o.url,
      type: 'post',
      data: JSON.stringify(o.params),
      dataType: "json",
      contentType: "application/json; charset=utf-8",
      success: function (msg) {
        if (o.success) o.success(msg)
      },
      error: function (error) {
        WRT_e.ui.hint({
          type: 'error',
          msg: JSON.parse(error.responseText).errorMessage
        })
      }
    })
  },
  getQRCodeProgress: function (o = {}) { //获取验证码
    o.params = o.params || {}
    $.ajax({
      url: o.url,
      type: 'post',
      data: JSON.stringify(o.params),
      dataType: "json",
      contentType: "application/json; charset=utf-8",
      success: function (msg) {
        if (o.success) o.success(msg)
      },
      error: function (error) {
        WRT_e.ui.hint({
          type: 'error',
          msg: JSON.parse(error.responseText).errorMessage
        })
      }
    })
  },
  //获取url
  login_ewm: function (o = {}) { 
    o.params = o.params || {}
    $.ajax({
      url: WRT_config.server + '/login.aspx/e_login_ewm',
      type: 'post',
      data: JSON.stringify(o.params),
      dataType: "json",
      contentType: "application/json; charset=utf-8",
      success: function (msg) {
        if (o.success) o.success(msg.d)
      },
      error: function (error) {
        WRT_e.ui.hint({
          type: 'error',
          msg: JSON.parse(error.responseText).errorMessage
        })
      }
    })
  },
  //获取CDSS开关状态
  getCdssSwitch: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyz.aspx/e_getCdssSwitch',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
    }
  },
  //获取化验医嘱CDSS消息
  getHyxx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + '/ashx/cdss.ashx?'+o.params,
        type: 'get',
        dataType: "json",
        data: {},
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
    }
  },
  //获取菜单列表
  loginByAccount: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+'/authentication/v1/login/loginByAccount',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
    
  },
  //获取菜单列表
  getMeunList: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+`/sysmanage/v1/roles/getRouters?yingYongDM=${o.params.yingYongDM}&caiDanCS=${o.params.caiDanCS}`,
        type: 'get',
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        dataType: "json",
        data: "",
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
          // setTimeout(function(){
          //   let url="http://wsyy.Wzhospital.cn/web/auth/#/logout?url_redirect="+encodeURIComponent(window.location.href)+'&app_key=3d6yugnzd8xyf9fqe0oh4gyjuyj3ced46yamxh3ix3cyx0ftyybdvuj4737qz475'
          //   window.location.href=url
          // },1000)
        }
      })
    
  },
  //获取
  getAllAppForm: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+'/checktreat/v1/basic/getAllAppForm',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
    
  },
  //获取
  autoAppoint: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+'/checktreat/v1/basic/autoAppoint?shenQingDanSJID='+o.params.shenQingDanSJID,
        type: 'POST',
        dataType: "json",
        data: '',
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
    
  },
  //获取规则引擎
  getTRBSCode: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+'/app-decisionsupport/v1/rule/triggerRuleByScenarioCodeVersion2',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
    
  },
  //获取规则引擎
  getRuleEngine: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+'/app-decisionsupport/v1/rule/itemOfRuleEngine',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
    
  },
  //规则引擎特殊说明备注
  saveShuoMing: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+'/app-decisionsupport/v1/BasicData/saveShuoMing',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
    
  },
  //规则引擎化验通过规则判断获取审批项目
  getApprovalProjectByRule: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+'/app-decisionsupport/v1/socialApproval/getApprovalProjectByRule',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
    
  },
}