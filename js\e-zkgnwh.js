
let buMenID=null
let lists_arr=[]
let params = {}
let url = window.location.href.split("?") || []
if(url[1]){
  let text = url[1].split("&")
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
}
// console.log(params)
var obj1 = []
// 统一页面启动
$(document).ready(() => {
  // $(".title").html(decodeURI(params["as_zkmc"])||"")
  WRT_e.api.zkgnwh.getinit({
    params:{},
    success(msg){
      if(msg.data){
        WRT_config.allmenu=msg.data
        init()
      }else{
        WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
      }
    }
  })
  // init()
})
/********************初始化********************/
function init() {
  //左侧
  var Menuleft = new Menuleft_View();
  Menuleft.$el = $("#left_lists");
  Menuleft.init({ data: WRT_config.allmenu }).render();
}


var Menuleft_View = WRT_e.view.extend({
  render: function () {
    let html=`
    ${_.map(this.data.buMenList||[],(obj,index)=>`
    <tr>
      <td class="Ltable_td L_td${index}" onclick="select(${index})">${obj.buMenMC}</td>
    </tr>
    `
    ).join('')}`
    this.$el.html(html)
  return this;
  },
})

var Menuright_View = WRT_e.view.extend({
  render: function () {
    let html=`
    ${_.map(this.data,(obj,index)=>`
    <tr class="right_table_tr">
      <td><select id='bm_selected${index}' ${!obj.type?'disabled':''}>
      ${_.map(WRT_config.allmenu.shuXingDMList,(ev)=>`
      <option value="${ev.daiMa}" ${obj.shuXingDM==ev.daiMa?`selected`:''}>${ev.mingCheng}</option>
      `).join('')}
      </select></td>
      <td><select id='sxz_selected${index}'>
      ${_.map(WRT_config.allmenu.shuXingZhiList,(ev)=>`
      <option value="${ev.daiMa}" ${obj.shuXingZhi==ev.daiMa?`selected`:''}>${ev.mingCheng}</option>
      `).join('')}
      </select></td>
      <td><input type="text" class="text_input" id="bz_input${index}" value="${obj.shuXingBZ||''}" /></td>
      <td><select id='zt_selected${index}'>
      ${_.map(WRT_config.allmenu.zhuangTaiBZList,(ev)=>`
      <option value="${ev.daiMa}" ${obj.zhuangTaiBZ==ev.daiMa?`selected`:''}>${ev.mingCheng}</option>
      `).join('')}
      </select></td>
      <td>${obj.caoZuoZheXM||''}</td>
      <td style="width: 170px;">${obj.xiuGaiSJ||''}</td>
      <td><a class="edit_btn" onclick="save(${index})">保存</a></td>
    </tr>
    `
    ).join('')}`
    this.$el.html(html)
  return this;
  },

})


/**
 * 事件
 */
//选中
 function select(index){
  $('.Ltable_td').removeClass('Ltable_td_active')
  $(`.L_td${index}`).addClass('Ltable_td_active')
  let list=WRT_config.allmenu.buMenList
  if(list[index]){
    buMenID=list[index].buMenID
    $("#right_lists").html('')
    WRT_e.api.zkgnwh.getSearch({
      params:{buMenID:list[index].buMenID},
      success(msg){
        if(msg.data){
          WRT_config.rightLists=msg.data
          //右侧
          if(WRT_config.rightLists.length==0){
            $("#right_lists").html('暂无数据！')
            return
          }
          lists_arr=WRT_config.rightLists
          var Menuright = new Menuright_View();
          Menuright.$el = $("#right_lists");
          Menuright.init({ data: lists_arr }).render();
        }else{
          WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
        }
      }
    })
  }
}
//新增
function add(){
  let dm=''
  let sxz=''
  let zt=''
  if(WRT_config.allmenu.shuXingDMList){
    dm=WRT_config.allmenu.shuXingDMList[0].daiMa
  }
  if(WRT_config.allmenu.shuXingZhiList){
    sxz=WRT_config.allmenu.shuXingDMList[0].daiMa
  }
  if(WRT_config.allmenu.zhuangTaiBZList){
    zt=WRT_config.allmenu.shuXingDMList[0].daiMa
  }
  let list={
    buMenID: buMenID,
    shuXingDM: dm,
    shuXingZhi: sxz,
    caoZuoZheID: params['yhid'],
    zhuangTaiBZ: zt,
    type:true
  }
  WRT_config.rightLists.push(list)
  var Menuright = new Menuright_View();
  Menuright.$el = $("#right_lists");
  Menuright.init({ data: WRT_config.rightLists }).render();
}
//保存
function save(index){
  let bm=$(`#bm_selected${index} option:selected`).val()
  let szx=$(`#sxz_selected${index} option:selected`).val()
  let zt=$(`#zt_selected${index} option:selected`).val()
  let bz=$(`#bz_input${index}`).val()
  if(bz.length>25){
    WRT_e.ui.hint({msg:'属性备注长度25个中文字！'})
    return
  }
  if(zt||szx||bm){//||(val!=WRT_config.rightLists[index].zhuangTaiBZ)
    let list=WRT_config.rightLists[index]
    WRT_config.rightLists[index].shuXingDM=bm
    WRT_config.rightLists[index].shuXingZhi=szx
    WRT_config.rightLists[index].zhuangTaiBZ=zt
    WRT_config.rightLists[index].shuXingBZ=bz
    if(list.type){
      WRT_e.api.zkgnwh.addBuMenSXList({
        params:{
          buMenID: list.buMenID,
          buMenMC: list.buMenMC,
          shuXingDM: bm,
          shuXingMC: list.shuXingMC,
          shuXingZhi: szx,
          shuXingZhiMC: list.shuXingZhiMC,
          caoZuoZheID: list.caoZuoZheID,
          caoZuoZheXM: list.caoZuoZheXM,
          xiuGaiSJ: list.xiuGaiSJ,
          zhuangTaiBZ: zt,
          shuXingBZ:bz,
          zhuangTaiBZMC: list.zhuangTaiBZMC
        },
        success(msg){
          if(msg.hasError==0){
            WRT_e.ui.hint({msg:'保存成功！',type:'success'})
            if(WRT_config.rightLists[index].type){
              delete WRT_config.rightLists[index].type
            }
            var Menuright = new Menuright_View();
            Menuright.$el = $("#right_lists");
            Menuright.init({ data: WRT_config.rightLists }).render();
          }else{
            WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
          }
        }
      })
    }else{
      WRT_e.api.zkgnwh.save({
        params:{
          buMenID: list.buMenID,
          buMenMC: list.buMenMC,
          shuXingDM: bm,
          shuXingMC: list.shuXingMC,
          shuXingZhi: szx,
          shuXingZhiMC: list.shuXingZhiMC,
          caoZuoZheID: list.caoZuoZheID,
          caoZuoZheXM: list.caoZuoZheXM,
          xiuGaiSJ: list.xiuGaiSJ,
          zhuangTaiBZ: zt,
          shuXingBZ:bz,
          zhuangTaiBZMC: list.zhuangTaiBZMC
        },
        success(msg){
          if(msg.hasError==0){
            WRT_e.ui.hint({msg:'保存成功！',type:'success'})
            var Menuright = new Menuright_View();
            Menuright.$el = $("#right_lists");
            Menuright.init({ data: WRT_config.rightLists }).render();
          }else{
            WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
          }
        }
      })
    }
  }
}