<template>
  <div class="content">
    <div class="content-header">
      <div class="header-button">
        <div class="title">审方结果</div>
        <el-badge :value="shenFangBadge" :max="99" class="msg-badge" />
        <i class="el-icon-arrow-right"></i>
      </div>
      <div class="header-button">
        <div class="title">处方修改</div>
        <el-badge :value="chuFangBadge" :max="99" class="msg-badge" />
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>
    <div class="content-list">
      <template v-if="messageList && messageList.length > 0">
        <div v-for="(messageItem, index) in messageList" :key="index" class="list-item">
          <el-alert
            v-show="messageItem.neiRong.length"
            type="primary"
            show-icon
            plain
            :closable="false"
            :description="messageItem.neiRong[0].neiRong"
          ></el-alert>
          <el-button class="actions" type="primary" size="mini">查看</el-button>
        </div>
      </template>
      <div v-else style="padding: 40px 0; font-size: 18px; color: #777; text-align: center">
        - 没有新消息 -
      </div>
    </div>
  </div>
</template>

<script>
import { getUnreadMessage } from '@/api/patient'

export default {
  name: 'PopoverWindow',
  props: {
    msgNum: {
      type: Number,
      default: 0
    },
    userState: {
      type: Object,
      default: () => ({})
    },
    getToken: {
      type: Function,
      default: () => ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      messageList: [],
      shenFangBadge: 0,
      chuFangBadge: 0
    }
  },
  mounted() {
    this.getAllMsg()
  },
  methods: {
    async getAllMsg() {
      try {
        const [messageList, shenFangMsg, chuFangMsg] = await Promise.all([
          this.getMessage(),
          this.getShenFangMsg(),
          this.getChuFangMsg()
        ])

        this.messageList = messageList || []
        this.shenFangBadge = shenFangMsg && Array.isArray(shenFangMsg) ? shenFangMsg.length : 0
        this.chuFangBadge = chuFangMsg && Array.isArray(chuFangMsg) ? chuFangMsg.length : 0

        const messageCount = this.shenFangBadge + this.chuFangBadge + this.messageList.length
        this.$emit('update:msgNum', messageCount)
      } catch (err) {
        this.shenFangBadge = 0
        this.chuFangBadge = 0
        this.messageList = []
      }
    },
    // 获取未读消息
    async getMessage() {
      try {
        const res = await getUnreadMessage()
        if (res.hasError === 0 && Array.isArray(res.data)) {
          return res.data
        } else {
          return []
        }
      } catch (err) {
        return []
      }
    },
    // 获取审方结果
    getShenFangMsg() {},
    // 获取处方修改
    getChuFangMsg() {},
    // 点击查看
    handleClickMsg(messageItem) {}
  }
}
</script>

<style lang="scss" scoped>
.content {
  .content-header {
    display: flex;
    justify-content: space-between;
    .header-button {
      width: 120px;
      height: 28px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      padding-left: 8px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      cursor: pointer;
      .title {
        font-weight: 400;
        flex: 1;
      }
      ::v-deep .el-badge__content {
        top: 0;
        background-color: #356ac5;
        margin: auto;
        border-radius: 10px;
        height: 20px;
        color: #ffffff;
        font-size: 12px;
        text-align: center;
        white-space: nowrap;
      }
    }
  }
  .content-list {
    overflow-y: auto;
    max-height: 450px;
    display: flex;
    flex-direction: column;
    .list-item {
      margin-top: 10px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      line-height: normal;
      position: relative;
      &:first-child {
        margin-top: 0;
      }
      .actions {
        position: absolute;
        bottom: 10px;
        right: 10px;
        .el-button--mini {
          padding: 4px 12px;
          font-size: 14px;
          border-radius: 3px;
        }
      }
    }
  }
}
::v-deep .el-alert {
  .el-alert__icon.is-big {
    align-self: flex-start;
    font-size: 20px;
  }
  .el-alert__description {
    padding-bottom: 26px;
  }
}
</style>
