{"name": "base-wyyy-template", "version": "1.0.0", "description": "Bs-Base", "author": "Lxc <<EMAIL>>", "scripts": {"dev": "vue-cli-service serve", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --fix --ext .js,.vue src", "build:test": "vue-cli-service build --mode test", "build:pre": "vue-cli-service build --mode staging", "build:prod": "vue-cli-service build", "preview": "node build/index.js --preview", "new": "plop", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "prepare": "husky install"}, "dependencies": {"axios": "^1.9.0", "better-scroll": "^2.5.1", "clipboard": "^2.0.11", "core-js": "^3.42.0", "darkreader": "4.9.69", "date-fns": "^2.30.0", "dropzone": "5.5.1", "echarts": "^4.9.0", "element-ui": "^2.15.14", "fuse.js": "3.4.4", "js-cookie": "2.2.0", "js-md5": "^0.7.3", "lodash": "^4.17.21", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "^8.2.0", "screenfull": "4.2.0", "script-loader": "0.7.2", "sortablejs": "1.8.4", "uuid": "^9.0.1", "v-charts": "^1.19.0", "vue": "^2.7.16", "vue-demi": "^0.13.11", "vue-i18n": "7.3.2", "vue-loader": "^15.11.1", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vuedraggable": "2.20.0", "vuex": "3.6.2", "vuex-persistedstate": "4.1.0", "wyyy-component": "1.3.11-beta.121"}, "devDependencies": {"@babel/core": "^7.27.3", "@babel/parser": "^7.27.3", "@types/lodash": "^4.17.17", "@types/uuid": "^10.0.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/cli-plugin-babel": "~4.5.19", "@vue/cli-plugin-eslint": "~4.5.19", "@vue/cli-plugin-unit-jest": "~4.5.19", "@vue/cli-service": "~4.5.19", "@vue/eslint-config-prettier": "^6.0.0", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-loader": "^8.4.1", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-import": "^1.13.8", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "css-loader": "^3.6.0", "eslint": "^6.8.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^9.33.0", "html-webpack-plugin": "3.2.0", "husky": "^8.0.3", "lint-staged": "^15.5.2", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "postcss": "^8.5.4", "postcss-html": "^1.8.0", "postcss-loader": "^3.0.0", "postcss-scss": "^4.0.9", "prettier": "^2.8.8", "runjs": "4.3.2", "sass": "1.26.2", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "^2.2.0", "style-loader": "^2.0.0", "stylelint": "^16.20.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-scss": "^13.1.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-scss": "^6.12.0", "svg-baker-runtime": "^1.4.7", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "typescript": "^5.8.3", "url-loader": "2.3.0"}, "browserslist": ["cover 99.5%"], "engines": {"node": ">= 14.17.6", "npm": ">= 6.14.15"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "license": "MIT", "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"], "*.{js,vue}": "eslint --cache --fix"}}