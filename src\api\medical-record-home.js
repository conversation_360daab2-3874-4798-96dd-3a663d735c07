import request from '@/utils/request'

// 住院医生站_查询_病案首页初始化
export function InitTheFirstPageOfMedicalRecord(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/InitTheFirstPageOfMedicalRecord',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_病案首页保存或提交
export function saveTheFirstPageOfMedicalRecord(data) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/saveTheFirstPageOfMedicalRecord',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_查询一个BingLiID的病案首页
export function getBingAnSYByBingLiID(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getBingAnSYByBingLiID',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_病案首页cdss参数
export function getPatientBasic(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getShouYeCDSSNR',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 电子病历_查询_查询一个专科id的常用手术
export function getCommonSurgeriesByZhuanKe(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getCommonSurgeriesByZhuanKe',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 电子病历_查询_查询一个科室id的常用诊断
export function getCommonDiagnosesByZhuanKe(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getCommonDiagnosesByZhuanKe',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_分页查询YlBasySswy取状态
export function getYlBasySswyByPageAndZhuangTai(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getYlBasySswyByPageAndZhuangTai',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 分页搜索YlZdVo列表开启状态
export function getZhenDuanByPageAndZhuanTai(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getZhenDuanByPageAndZhuanTai',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_分页查询手术icd识别状态
export function getYlSsicdByPageAndZhuanTai(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getYlSsicdByPageAndZhuanTai',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
