<template>
  <div id="loader-wrapper">
    <div id="loader" />
    <div class="loader-section section-left" />
    <div class="loader-section section-right" />
    <div class="load_title">正在加载系统资源，请耐心等待</div>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth' // get token from cookie
import { mapGetters } from 'vuex'
export default {
  name: 'Login',
  data() {
    return {
      loading: false,
      redirect: undefined
    }
  },

  computed: {
    ...mapGetters({
      where: 'user/where',
      redirectParams: 'user/redirectParams'
    })
  },

  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },

  created() {
    console.log(this.where)
    console.log(this.app_key)
    if (!getToken()) {
      const url = `${this.where}/#/login?${this.redirectParams}`
      console.log('模板应用登录 地址->', url)
      window.location = url
    }
  },
  methods: {}
}
</script>

<style lang="scss"></style>
