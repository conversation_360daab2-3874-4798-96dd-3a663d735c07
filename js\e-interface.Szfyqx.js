WRT_e.api = WRT_e.api || {}

WRT_e.api.Szfyqx = {
  //获取
  getQuery: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":portal
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/szfyqx/szfyqx.aspx/e_Query_blid',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  //获取单个
  getQueryid: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":portal
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/szfyqx/szfyqx.aspx/e_Query_id',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  //新增
  getInsert: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":portal
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/szfyqx/szfyqx.aspx/e_Insert',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  //修改
  getUpdate: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":portal
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/szfyqx/szfyqx.aspx/e_Update',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  //删除
  getDelet: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":portal
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/szfyqx/szfyqx.aspx/e_Delet',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  
}