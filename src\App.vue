<template>
  <div id="app" :class="{ app__night: nightMode }">
    <exp-tip />
    <router-view v-if="!isLock" />
    <lock-screen />
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import AuthUser from '@/components/AuthUser/index.mixin'
import LockScreen from '@/components/LockScreen'
import { getIconfontUrlMethod } from '@/utils/serverIconfont.js'
import ExpTip from '@/components/ExpTip'

export default {
  name: 'App',
  components: {
    LockScreen,
    ExpTip
  },
  mixins: [AuthUser],
  computed: {
    ...mapGetters({
      isLock: 'lockscreen/isLock',
      lockTime: 'lockscreen/lockTime'
    }),
    ...mapState({
      nightMode: ({ theme }) => theme?.nightMode
    })
  },
  mounted() {
    getIconfontUrlMethod()
  }
}
</script>
