<!-- 手术ICD维护 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="head-search">
        <span class="search-label">输入名称或拼音:</span>
        <el-input v-model="searchValue" style="width: 170px" />
        <el-button type="primary">查询</el-button>
      </div>
      <div class="table-background">
        <div class="table-title">
          <div>
            <span />
            手术ICD维护
          </div>
          <el-button
            type="primary"
            @click="
              openEditDrawer({
                shouShuID: 0,
                shouShuMC: '',
                icddm: '',
                shiFouBM: '',
                zhuangTaiBZ: true,
                shouShuSX: '',
                suoShuXT: '',
                shiFouBT: true
              })
            "
          >
            新增
          </el-button>
        </div>
        <div class="table-component">
          <table>
            <thead>
              <tr style="text-align: left">
                <th>手术名称</th>
                <th>ICD代码</th>
                <th>是否别名</th>
                <th>状态标志</th>
                <th>手术属性</th>
                <th>所属系统</th>
                <th>是否必填</th>
                <th style="width: 70px; text-align: center">操作</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in icdList">
                <tr :key="index" :class="[index % 2 == 1 ? 'tr-two' : 'tr-one']">
                  <td>
                    {{ item.shouShuMC }}
                  </td>
                  <td>
                    {{ item.icddm }}
                  </td>
                  <td>
                    {{ item.shiFouBM }}
                  </td>
                  <td>
                    <el-tag v-if="item.zhuangTaiBZ === '1'">启用</el-tag>
                    <el-tag v-else type="danger">停用</el-tag>
                  </td>
                  <td>
                    {{ item.shouShuSX }}
                  </td>
                  <td>
                    {{ item.suoShuXT }}
                  </td>
                  <td>
                    <el-tag v-if="item.shiFouBT === '1'" type="danger">必填</el-tag>
                    <el-tag v-else>非必填</el-tag>
                  </td>
                  <td style="text-align: center">
                    <el-button type="text" @click="openEditDrawer(item)">编辑</el-button>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <icd-drawer
      :visible.sync="icdVisible"
      :icd-data.sync="icdData"
      @upload-success="initSystemIcd"
    />
  </div>
</template>

<script>
import IcdDrawer from './components/IcdDrawer.vue'
import { getYlSsicdByPage } from '@/api/system-maintenance'
export default {
  name: 'SystemOperationIcd',
  components: {
    IcdDrawer
  },
  data() {
    return {
      icdVisible: false,
      icdData: null,
      searchValue: '',
      icdList: []
    }
  },
  mounted() {
    this.initSystemIcd()
  },
  methods: {
    initSystemIcd() {
      getYlSsicdByPage({
        pageIndex: 1,
        pageSize: 10,
        wenBen: '31'
      }).then((res) => {
        if (res.hasError === 0) {
          this.icdList = res.data
          console.log('手术icd res:', res)
        }
      })
    },
    openEditDrawer(data) {
      this.icdData = data
      this.icdVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .head-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        width: 960px;
        span {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        margin-top: 10px;
        width: 960px;
        table {
          margin-top: 10px;
          width: 100%;
        }
        th,
        td {
          border: 1px solid #ddd;
          border-collapse: collapse; /* 移除表格内边框间的间隙 */
          height: 35px;
          padding: 5px 8px;
        }
        .tr-one {
          background-color: #f6f6f6;
        }
        th,
        .tr-two {
          background-color: #eff3fb;
        }
        .el-tag {
          border-radius: 2px;
        }
      }
    }
  }
}
</style>
