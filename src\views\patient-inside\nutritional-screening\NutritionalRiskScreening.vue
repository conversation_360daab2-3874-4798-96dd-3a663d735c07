<template>
  <div class="temperature-sheet-view">
    <el-timeline>
      <el-timeline-item v-for="(activity, index) in activities" :key="index" :type="activity.type">
        {{ activity.content }}
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script>
import { format, parseISO, addDays, startOfWeek, endOfWeek, differenceInDays } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      activities: [
        {
          content: '支持使用图标',
          type: 'primary'
        },
        {
          content: '支持自定义颜色',
          type: 'primary'
        },
        {
          content: '支持自定义尺寸',
          type: 'primary'
        },
        {
          content: '默认样式的节点',
          type: 'primary'
        }
      ]
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
    console.log(this.patientDetail)
  },
  methods: {
    // 页面初始化
    async init() {}
  }
}
</script>

<style lang="scss" scoped>
.temperature-sheet-view {
  background-color: #fff;
  padding: 8px;
  height: 100%;
}
</style>
