<template>
  <div ref="tagsViewContainer" class="tags-view-container">
    <el-tabs
      v-model="activeRoute"
      @tab-remove="closeSelectedTag"
      @contextmenu.prevent.native="openMenu($event)"
    >
      <el-tab-pane
        v-for="tag in visitedViews"
        :key="tag.name"
        :name="tag.name"
        :label="tag.title"
        :closable="!tag.meta?.affix"
      >
        <component :is="getComponent(tag)" :key="`${tag.name}-${refreshKey}`" />
      </el-tab-pane>
    </el-tabs>
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li @click="refreshSelectedTag">刷新</li>
      <li @click="closeOthersTags">关闭其它</li>
      <li @click="initTags">关闭所有</li>
    </ul>
  </div>
</template>

<script>
import { loadView } from '@/store/modules/permission'
import { EventBus } from '@/utils/event-bus'
import { isNumberStr } from 'wyyy-component/src/utils'

export default {
  name: 'PatientTagView',
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      refreshKey: 0
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    moduleName() {
      return `patientDetail/${this.bingLiID}`
    },
    // 从Vuex动态模块获取visitedViews
    visitedViews() {
      if (this.$store.hasModule(this.moduleName)) {
        return this.$store.state[this.moduleName].visitedViews || []
      }
      return []
    },
    // 从Vuex动态模块获取activeRoute
    activeRoute: {
      get() {
        if (this.$store.hasModule(this.moduleName)) {
          return this.$store.state[this.moduleName].activeRoute
        }
        return ''
      },
      set(val) {
        if (this.$store.hasModule(this.moduleName)) {
          this.$store.dispatch(`${this.moduleName}/setActiveRoute`, val)
          // 使用带病人ID的事件名
          EventBus.$emit(`activeRoute_${this.bingLiID}`, val)
        }
      }
    },
    activeMenuItem() {
      if (this.$store.hasModule(this.moduleName)) {
        return this.$store.state[this.moduleName].activeMenuItem
      }
      return null
    }
  },
  watch: {
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    },
    // 修改watch逻辑，避免无限循环
    activeMenuItem: {
      handler(newItem, oldItem) {
        // 只在真正变化时执行，避免无限循环
        if (newItem && (!oldItem || newItem.name !== oldItem.name)) {
          // 检查是否已经存在于visitedViews中
          const exists = this.visitedViews.some((v) => v.name === newItem.name)
          if (!exists) {
            this.addTags(newItem)
          } else {
            // 如果已存在，只切换到该标签，不重复添加
            this.activeRoute = newItem.name
          }
        }
      },
      deep: true,
      immediate: false // 防止初始化时触发
    }
  },
  mounted() {
    // 初始化标签
    this.$store.dispatch(`${this.moduleName}/initTags`)

    // 监听sidebarClick事件
    EventBus.$on('sidebarClick', this.handleSidebarClick)
  },
  beforeDestroy() {
    // 清理事件监听器
    EventBus.$off('sidebarClick', this.handleSidebarClick)
  },
  methods: {
    // 动态获取组件
    getComponent(tag) {
      if (tag.component) {
        return tag.component
      }
      // 如果tag中没有组件，则动态加载
      if (tag.name === 'InPatientOrders') {
        return loadView('patient-inside/inpatient-orders-menu/InPatientOrders')
      }
      return null
    },
    initTags() {
      this.$store.dispatch(`${this.moduleName}/initTags`)
    },
    addTags(menu) {
      this.$store.dispatch(`${this.moduleName}/addVisitedView`, menu)
      this.activeRoute = menu.name
    },
    refreshSelectedTag() {
      this.refreshKey += 1
    },
    closeSelectedTag(name) {
      this.$store.dispatch(`${this.moduleName}/delVisitedView`, name)
    },
    closeOthersTags() {
      this.$store.dispatch(`${this.moduleName}/closeOthersTags`, this.selectedTag.name)
    },
    // 处理sidebarClick事件
    handleSidebarClick(menuItem) {
      if (menuItem && menuItem.name) {
        // 防止重复添加相同的标签
        const exists = this.visitedViews.some((v) => v.name === menuItem.name)
        if (!exists) {
          this.addTags(menuItem)
        } else {
          // 如果已存在，只切换到该标签
          this.activeRoute = menuItem.name
        }
      }
    },
    openMenu(e) {
      if (
        e.srcElement.id &&
        e.srcElement.id.split('tab-')[1] &&
        !isNumberStr(e.srcElement.id.split('tab-')[1])
      ) {
        this.left = e.clientX
        this.top = e.clientY
        this.visible = true
        console.log(e.srcElement.id)
        let id = e.srcElement.id.split('tab-')[1]
        this.selectedTag = this.visitedViews.find((f) => f.name === id)
      }
    },
    closeMenu() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: calc(100% - 93px);
  ::v-deep .el-tabs {
    height: 100%;
    .el-tabs__header {
      background: #fff;
      .el-tabs__nav {
        .el-tabs__item {
          height: 32px;
          line-height: 32px;
          padding: 0 10px;
        }
      }
    }
    .el-tabs__content {
      height: calc(100% - 32px);
      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .contextmenu {
    position: fixed;
    z-index: 3000;
    padding: 5px 0;
    margin: 0;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    list-style-type: none;
    background: #fff;
    border-radius: 4px;
    box-shadow: 2px 2px 3px 0 rgb(0 0 0 / 30%);

    li {
      padding: 7px 16px;
      margin: 0;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }

  // }
}
</style>
