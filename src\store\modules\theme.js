/**
 * @<NAME_EMAIL>
 * @Date 2023-08-16
 * @LastEditors hybtalented
 * @LastEditTime 2024-02-19
 * @FilePath /base-wyyy-template/src/store/modules/theme.js
 * @Description 主题配置
 */
// import { type Module } from 'vuex'

/**
 * @typedef {'default' | 'normal'} Theme
 * @typedef {'standard' | 'large' | 'huge' | 'verysmall'} Size
 */
// export type Theme = 'default' | 'normal'
// export type Size = 'standard' | 'large' | 'huge' | 'verysmall'
// export interface IThemeState {
//   theme: Theme
//   size: Size
// }
const state = {
  /**
   * @type {Theme} 主题
   */
  theme: 'normal',
  /**
   * @type {Size} 页面缩放比例
   */
  size: 'standard',
  /**
   * 是否夜间模式
   */
  nightMode: false
}
/**
 * 获取样式 style 的 ID
 * @param {Theme} theme
 * @returns
 */
function getThemeStyleSheetId(theme) {
  return `theme-${theme}`
}
/**
 * 获取缩放比例配置ID
 * @param {Size} size
 */
function getSizeId(size) {
  return `size-${size}`
}
const module = {
  namespaced: true,
  state,
  actions: {
    /**
     * 设置并初始化缩放比例
     * @param {*} param0
     * @param {Size} size
     */
    async setSize({ dispatch, commit }, size) {
      await dispatch('initSize', size)
      commit('setSize', size)
    },
    /**
     * 设置并初始化主题
     * @param {*} param0
     * @param {Theme} theme
     */
    async setTheme({ dispatch, commit }, theme) {
      await dispatch('initTheme', theme)
      commit('setTheme', theme)
    },
    /**
     * 切换夜间模式/日间模式
     * @param {*} param0
     * @param {boolean} nightMode
     */
    async toggleNightMode({ dispatch, commit, state }) {
      const nightMode = !state.nightMode
      await dispatch('initNightMode', nightMode)
      commit('setNightMode', nightMode)
    },
    /**
     * 主题初始化，在启动页面时调用
     * @param {*} param0
     */
    async init({ dispatch, state }) {
      await dispatch('initTheme', state.theme)
      await dispatch('initSize', state.size)
      await dispatch('initNightMode', state.nightMode)
    },
    /**
     * 初始化夜间模式
     * @param {*} context
     * @param {boolean} nightMode
     */
    initNightMode(context, nightMode) {
      const DarkReader = require('darkreader')
      if (nightMode) {
        DarkReader.enable({
          brightness: 70,
          contrast: 100,
          sepia: 10
        })
      } else {
        DarkReader.disable()
      }
    },
    /**
     * 初始化缩放比例
     * @param {*} context
     * @param {Size} size
     */
    async initSize(context, size) {
      const classList = document.body.classList
      classList.remove(
        getSizeId('huge'),
        getSizeId('large'),
        getSizeId('standard'),
        getSizeId('verysmall')
      )
      classList.add(getSizeId(size))
    },
    /**
     * 初始化主题
     * @param {*} param0
     * @param {Theme} theme 页面主题
     */
    async initTheme({ state }, theme) {
      const themeName = theme
      const currentThemeId = getThemeStyleSheetId(state.theme)
      const newThemeId = getThemeStyleSheetId(themeName)
      const currentThemeStyleSheet = document.querySelector(`#${currentThemeId}`)
      const newThemeStyleSheet = document.querySelector(`#${newThemeId}`)
      if (currentThemeStyleSheet) {
        currentThemeStyleSheet.disabled = true
      }
      if (newThemeStyleSheet) {
        newThemeStyleSheet.disabled = false
      } else {
        switch (themeName) {
          case 'default':
            console.log('theme-init-default进入')
            await require(`!style-loader?{"attributes":{id:"theme-default"}}!css-loader!postcss-loader!sass-loader!@/styles/theme/default.scss`)
            break

          case 'normal':
            console.log('theme-init-normal进入')
            await require(`!style-loader?{"attributes":{id:"theme-normal"}}!css-loader!postcss-loader!sass-loader!@/styles/theme/normal.scss`)
            break
        }
      }
    }
  },
  mutations: {
    /**
     * 保存主题色
     * @param {*} state
     * @param {Theme} value
     */
    setTheme(state, value) {
      state.theme = value
    },
    /**
     * 保存缩放比例
     * @param {*} state
     * @param {Size} size
     */
    setSize(state, size) {
      state.size = size
    },
    /**
     * 保存夜间模式状态
     * @param {*} state
     * @param {boolean} nightMode
     */
    setNightMode(state, nightMode) {
      state.nightMode = nightMode
    }
  }
}

export default module
