
let params = {}
let url = window.location.href.split("?") || []
if(url[1]){
  let text = url[1].split("&")
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
}

WRT_config.url = params || {}
var keyid = 0

// 统一页面启动
$(document).ready(() => {
  WRT_e.api.CrrtYzjld.getSMTZ({
    params:{ 
      bingLiID: parseInt(params["blid"])// (自己填)
    },
    success(msg){
      if (msg.hasError == 0) {
        WRT_config.SMTZ = msg.data
        WRT_e.api.CrrtYzjld.getbrSMTZ({
          params:{ 
            zhuYuanID: parseInt(params["blid"])// (自己填)
          },
          success(msg){
            if(msg.hasError ==0){
              WRT_config.brInfo =msg.data || {}
              // 处方单表单
              var CRRTCFDForm = new MenuCFDForm_View()
              CRRTCFDForm.$el = $(".CFDForm");
              CRRTCFDForm.init().render();
            }else{
              WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
            }
          }
        })
      }
    }
  })
})

/******************** 公共方法 ********************/
// 保存（表单保存） 已完成
function saveCFD (){
  // 适应症参数获取？
  // 适应症
  var syz = document.querySelectorAll('input[name=syz_radio]:checked');
  var SYZvalues = [];
  syz.forEach(function(checkbox) {
    if (checkbox.value.indexOf('其他：')!=-1) {
      SYZvalues.push(checkbox.value + $('.showForm input[name=SYZQT]').val());
    } else {
      SYZvalues.push(checkbox.value);
    }
  });
  // 禁忌症
  var jjz = document.querySelectorAll('input[name=jjz_radio]:checked');
  var JJZvalues = [];
  jjz.forEach(function(checkbox) {
    JJZvalues.push(checkbox.value);
  });
  // 管路预冲（药品）
  var gdycYP = document.querySelectorAll('input[name=guanLuYCYP]:checked');
  var gdycYPvalues = [];
  gdycYP.forEach(function(checkbox) {
    gdycYPvalues.push(checkbox.value);
  });
  // 置换液
  var ZHY = document.querySelectorAll('input[name=zhiHuanYe]:checked');
  var ZHYvalues = [];
  ZHY.forEach(function(checkbox) {
    ZHYvalues.push(checkbox.value);
  });
  // 稀释方式 TXAXSQHVal
  var XSFS = document.querySelectorAll('input[name=txxsq_check]:checked');
  var TXAXSQHVal = [];
  XSFS.forEach(function(checkbox) {
    TXAXSQHVal.push(checkbox.value);
  });
  // 血浆置换
  var XJCS = $('.showForm input[name=xjzhXJ]').val()!=''?'血浆：'+$('.showForm input[name=xjzhXJ]').val():''
  var XJZHCheckbox = document.querySelectorAll('input[name=ZJZH]:checked');
  var XJZHvalue = XJCS==''?[]:[XJCS];
  XJZHCheckbox.forEach(function(checkbox) {
    if (checkbox.value == 'NS1000ml：') {
      XJZHvalue.push(checkbox.value + $('.showForm input[name=xjzhNSVal]').val());
    } else if (checkbox.value == '20%白蛋白：') {
      XJZHvalue.push(checkbox.value + $('.showForm input[name=xjzhBDBVal]').val());
    } else if (checkbox.value == '10%葡萄糖酸钙针：') {
      XJZHvalue.push(checkbox.value + $('.showForm input[name=xjzhPPTSGVal]').val());
    } else if (checkbox.value == '人工胶体：') {
      XJZHvalue.push(checkbox.value + $('.showForm input[name=zjzhRGJTVal]').val());
    } else if (checkbox.value == '其他：') {
      XJZHvalue.push(checkbox.value + $('.showForm input[name=xjzhQTVal]').val());
    }
  });

  let param = {
    "aki": $('.showForm input[name=AKIfq]:checked').val(), // AKI分期：
    "aptt": $('.showForm input[name=KNFAAPPT]').val(), // APTT目标值：
    "id":"", // 新增保存 id传空 这里没序号
    "bingLiID": WRT_config.url["blid"], // 病例ID
    // "bingQuID":"",
    "shouCiLRSJ": new Date().Format("yyyy-MM-dd HH:mm:ss"), // 首次录入时间
    // "xiuGaiSJ":"", // 修改时间
    // "yiZhuYHID":"", // 医嘱用户ID
    // "yiZhuYHXM":"", // 医嘱用户姓名
    // "xiuGaiYHID":"", // 修改用户ID
    // "xiuGaiYHXM":"", // 修改用户姓名
    "yiShengYHID": WRT_config.url["yhid"], // 医生用户ID（自己对数据）
    // "yiShengQM":"", // 医生签名
    "huShiYHID": WRT_config.url["yhid"], // 护士用户ID（自己对数据）
    // "huShiQM":"", // 护士签名
    // "jiGouDM":"", // 机构代码
    "zhenDuanXX": $('.showForm input[name=zhenDuanXX]').val(), //诊断信息
    "shenGao": $('.showForm input[name=shenGao]').val(), // 身高（cm）？
    "tiZhong": $('.showForm input[name=tiZhong]').val(),  // 体重（kg）？
    "tiWen": $('.showForm input[name=tiWen]').val(), // T 体温
    "xueYa": $('.showForm input[name=xueYaSY]').val() + '/' + $('.showForm input[name=xueYaXY]').val(), // 血压 上压/下压
    "xinTiao": $('.showForm input[name=xinTiao]').val(), // P 心跳
    "huXi": $('.showForm input[name=huXi]').val(), // R 呼吸
    "xueGuanTLLX": $('.showForm input[name=xueGuanTLLX]:checked').val() && $('.showForm input[name=xueGuanTLLX]:checked').val().indexOf('其他')!=-1?$('.showForm input[name=xueGuanTLLX]:checked').val() + $('.showForm input[name=xueGuanTLLXVal').val():$('.showForm input[name=xueGuanTLLX]:checked').val(), // 血管通路(类型)
    "xueGuanTLBW": $('.showForm input[name=xueGuanTLBWLR]:checked').val()+','+$('.showForm input[name=xueGuanTLBW]:checked').val(), // 血管通路(部位)
    // $('.showForm input[name=xueGuanTLBW]:checked').val(), // 血管通路(部位)
    "shiYingZheng": SYZvalues.join(','), // 适应症
    "jinJiZheng": JJZvalues.join(','), // 禁忌症
    "moShi": $('.showForm input[name=ms_radio]:checked').val() && $('.showForm input[name=ms_radio]:checked').val().indexOf('其他')!=-1?$('.showForm input[name=ms_radio]:checked').val() + $('.showForm input[name=MSQTVal').val():$('.showForm input[name=ms_radio]:checked').val(), // 模式
    "lvQi": $('.showForm input[name=nq_radio]:checked').val() && $('.showForm input[name=nq_radio]:checked').val().indexOf('其他')!=-1?$('.showForm input[name=nq_radio]:checked').val() + $('.showForm input[name=LQVal').val():$('.showForm input[name=nq_radio]:checked').val(), // 滤器
    "guanLuYC": $('.showForm input[name=guanLuYC]').val(), // 管路预冲
    "guanLuYCYP": gdycYPvalues.join(','), // 管路预冲（药品）中内容
    "yuChongYeJRTN": $('.showForm input[name=yuChongYeJRTN]:checked').val(), // 预充液是否进入体内
    "zhiHuanYe": ZHYvalues.join(','), // 置换液
    "xiShiFS": $('.showForm input[name=xsfs_check]:checked').val() && $('.showForm input[name=xsfs_check]:checked').val().indexOf('透析+稀释：')!=-1?$('.showForm input[name=xsfs_check]:checked').val() + TXAXSQHVal.join(','):$('.showForm input[name=xsfs_check]:checked').val(), // 稀释方式
    "kangNingFA": $('.showForm input[name=KNFA_radio]:checked').val() && $('.showForm input[name=KNFA_radio]:checked').val().indexOf('其他')!=-1?$('.showForm input[name=KNFA_radio]:checked').val() + $('.showForm input[name=KNFAVAL').val():$('.showForm input[name=KNFA_radio]:checked').val(), // 抗凝方案（对应医嘱药品）
    "fuHeJL": $('.showForm input[name=KNFAFHJL]').val(), // 负荷计量
    "weiChiJL": $('.showForm input[name=KNFAWC]').val(),  // 维持剂量
    "juYuanSuanJL": $('.showForm input[name=KNFAGJSJL]').val(),  //枸橼酸剂量
    "gaiJiLiang":  $('.showForm input[name=KNFAGJSGJL]').val(),  // 钙剂量
    "qiTaJL": $('.showForm input[name=KNFAGJSQTJL]').val(),  // 钙剂量
    "muBiaoXLL": $('.showForm input[name=mbxls]').val(),  // 目标血流速
    "qianZhiHL": $('.showForm input[name=qzh]').val(), // 前置换
    "houZhiHL": $('.showForm input[name=hzh]').val(), // 后置换
    "touXiYe": $('.showForm input[name=txy]').val(), // 透析液
    "tanSuanQN": $('.showForm input[name=tsqn]').val(), // 5%碳酸氢钠
    "lvHuaJia": $('.showForm input[name=lhj]').val(),  // 10%氯化钾
    "muBiaoCLL": $('.showForm input[name=mbcll]').val(),  // 目标超滤量
    "yuQiZLSJ": $('.showForm input[name=yqzlsj]').val(),  // 预期治疗时间
    "jingChaoLMBL": $('.showForm input[name=jclmb]').val(),  // 净超滤目标
    // "tingZhiRYYHID":"", // 停止人员用户ID
    // "tingZhiSJ":"", // 停止时间
    // "zhiXingRYYHID": "", // 执行人员用户ID
    // "zhiXingSJ": "", // 执行时间
    // "tingZhiRYYHXM": "", // 停止人员用户姓名
    // "zhiXingRYYHXM": "", // 执行人员用户姓名
    // "yiShengQMSJ": "", // 医师签名时间
    // "huShiQMSJ": "", // 护士签名时间
    // "jieShuSJ": "", // 结束时间
    // "jieShuRYYHID": "", // 结束人员用户ID
    // "jieShuRYYHXM": "", // 结束人员用户姓名
    "xueJiangZH": XJZHvalue.join(','), // 血浆置换
    "fengGuan": $('.showForm input[name=fg_radio]:checked').val() && $('.showForm input[name=fg_radio]:checked').val().indexOf('其他')!=-1?$('.showForm input[name=fg_radio]:checked').val() + $('.showForm input[name=FGQT').val():$('.showForm input[name=fg_radio]:checked').val(), // 封管
    "xueJiangFLLL": $('.showForm input[name=xjflll]').val(),  // 血浆分离流量
    "xueJiangZLZL": $('.showForm input[name=xjzlzl]').val()  // 血浆治疗总量
  }
  WRT_e.api.CrrtYzjld.addListCrrt({
    params: param,
    success(msg){
      if (msg.hasError == 0) {
        keyid = msg.data
        let list={
          CRRTID:msg.data,
          crrtMuBanMC:[param.kangNingFA]
        }
        parent.closeCRRT(list)
        // var CRRTCFDForm = new MenuCFDForm_View()
        // CRRTCFDForm.$el = $(".CFDForm");
        // CRRTCFDForm.init({ data: param }).render();
        // // 1、新增化验结果 insertHuayanjg
        // if (WRT_config.noxgjcHyjg.length>0) {
        //   let data = []
        //   WRT_config.noxgjcHyjg.forEach(item=>{
        //     data.push({
        //       "chuFangDanID": msg.data, //处方单ID
        //       "testID": item.testID, //testID
        //       "huaYanJG": item.huaYanJG, //化验结果
        //       "jianYanSJ": item.jianYanSJ?item.jianYanSJ:'' //检验时间
        //     })
        //   })
          
        
      } else {
        WRT_e.ui.hint({
          type: 'error',
          msg: '处方单保存失败'
        })
      }
    }
  })
}


// 透析+稀释单选是否选择
function changeNow() {
  var TXAXS = $('.showForm input[type="radio"][name="xsfs_check"]:checked').val()
  var checkbox = document.querySelectorAll('input[name=txxsq_check]');
  checkbox.forEach(function(item) {
    if (TXAXS == '透析+稀释：') {
      item.removeAttribute('disabled')
    } else {
      item.setAttribute('disabled', '')
      item.checked = false
      
    }
    
  })
  // 医嘱新增  
  var TXAXSAdd = $('.addtc input[type="radio"][name="xsfsAdd_check"]:checked').val()
  var checkbox = document.querySelectorAll('input[name=txxsqhAdd_check]');
  checkbox.forEach(function(item) {
    if (TXAXSAdd == '透析+稀释：') {
      item.removeAttribute('disabled')
    } else {
      item.setAttribute('disabled', '')
      item.checked = false
    }
    
  })
  // $('.showForm input[name="txxsq_check"]').removeAttr('disabled')
}
// 封管单选是否选择
function FGchangeNow() {
  var TXAXS = $('.showForm input[type="radio"][name="fg_radio"]:checked').val()
  var checkbox = document.querySelectorAll('input[name=FGQT]');
  checkbox.forEach(function(item) {
    if (TXAXS == '其他：') {
      $('.showForm input[name=FGQT]').removeAttr('disabled')
    } else {
      $('.showForm input[name=FGQT]').attr('disabled', '')
    }
  })
  // $('.showForm input[name="txxsq_check"]').removeAttr('disabled')
}
// 滤器单选是否选择
function LQchangeNow() {
  var TXAXS = $('.showForm input[type="radio"][name="nq_radio"]:checked').val()
  if (TXAXS == '其他：') {
    $('.showForm input[name=LQVal]').removeAttr('disabled')
  } else {
    $('.showForm input[name=LQVal]').attr('disabled', '')
  }
  // $('.showForm input[name="txxsq_check"]').removeAttr('disabled')
}
// 模式单选是否选择
function MSchangeNow() {
  var TXAXS = $('.showForm input[type="radio"][name="ms_radio"]:checked').val()
  if (TXAXS == '其他：') {
    $('.showForm input[name=MSQTVal]').removeAttr('disabled')
  } else {
    $('.showForm input[name=MSQTVal]').attr('disabled', '')
  }
}
// 血管通路单选是否选择
function XGTLchange() {
  var TXAXS = $('.showForm input[type="radio"][name="xueGuanTLLX"]:checked').val()
  
  if (TXAXS == '其他：') {
    $('.showForm input[name=xueGuanTLLXVal]').removeAttr('disabled')
  } else {
    $('.showForm input[name=xueGuanTLLXVal]').attr('disabled', '')
  }
  // $('.showForm input[name="txxsq_check"]').removeAttr('disabled')
}
// 血浆置换：多选是否选择
function CJZHchange() {
  var checkbox = document.querySelectorAll('input[name=ZJZH]:checked');
  checkbox.forEach(function(item) {
    if (item.value == 'NS1000ml：') {
      ($('.showForm input[name="xjzhNSVal"]')).removeAttr('disabled')
    } else {
      ($('.showForm input[name="xjzhNSVal"]')).attr('disabled', '')
    }
    if (item.value == '20%白蛋白：') {
      ($('.showForm input[name="xjzhBDBVal"]')).removeAttr('disabled')
    } else {
      ($('.showForm input[name="xjzhBDBVal"]')).attr('disabled', '')
    }
    if (item.value == '10%葡萄糖酸钙针：') {
      ($('.showForm input[name="xjzhPPTSGVal"]')).removeAttr('disabled')
    } else {
      ($('.showForm input[name="xjzhPPTSGVal"]')).attr('disabled', '')
    }
    if (item.value == '人工胶体：') {
      ($('.showForm input[name="zjzhRGJTVal"]')).removeAttr('disabled')
    } else {
      ($('.showForm input[name="zjzhRGJTVal"]')).attr('disabled', '')
    }
    if (item.value == '其他：') {
      ($('.showForm input[name="xjzhQTVal"]')).removeAttr('disabled')
    } else {
      ($('.showForm input[name="xjzhQTVal"]')).attr('disabled', '')
    }
    //   item.removeAttribute('disabled')
    // } else {
      // item.setAttribute('disabled', '')
    // }
    
  })
  // $('.showForm input[name="txxsq_check"]').removeAttr('disabled')
}
// 适应症多选是否选择
function SYZchange() {
  var checkbox = document.querySelectorAll('input[name=syz_radio]:checked');
  checkbox.forEach(function(item) {
    if (item.value == '其他：') {
      ($('.showForm input[name="SYZQT"]')).removeAttr('disabled')
    } else {
      ($('.showForm input[name="SYZQT"]')).attr('disabled', '')
    }
  })
}
// 抗凝方案
function KNFAchange() {
  var TXAXS = $('.showForm input[type="radio"][name="KNFA_radio"]:checked').val()
  if (TXAXS == '其他：') {
    $('.showForm input[name=KNFAVAL]').removeAttr('disabled')
  } else {
    $('.showForm input[name=KNFAVAL]').attr('disabled', '')
  }
}

// 数字
function validateNumber(el) {
  var value = el.value;
  var valid = /^[0-9]*\.?[0-9]*$/.test(value); // 正则表达式判断是否为数字或小数
  if (!valid) {
    // 如果输入无效，则设置为上一个有效的值
    el.value = el.value.replace(/[^0-9.]/g, ''); 
  } else {
    const numStr = value.toString();
    // 分割小数点前后的字符串
    const parts = numStr.split('.');
    // 如果小数点后面有部分，则返回小数位数，否则返回0
    const partsLong =  parts.length === 2 ? parts[1].length : 0;
    if (partsLong>2) {
      el.value=el.value.toString().match(/^\d+(?:\.\d{0,2})?/)
    } else {
      if (partsLong == 0 && (el.value[0]=='0' && el.value[1])) {
        el.value =  el.value.replace(/^0+([1-9]+)$/, '$1')
      }
    }
  } 
}

/******************** 视图 ********************/
// 表2 血液净化处方单 ———— 填写处方单
var MenuCFDForm_View = WRT_e.view.extend({
  render: function () {
    let html=`
      <div class="showForm" style="padding: 12px 0; color: rgba(0, 0, 0, .65); background-color: #fff;">
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold;width: 100px;min-width: 100px;">基础信息：</span>
            <span>
              <span class="iTabel"> 诊断 </span>
              <input class="allInput" name="zhenDuanXX" value="${this.data && this.data.zhenDuanXX?this.data.zhenDuanXX:WRT_config.brInfo.ruYuanZD?WRT_config.brInfo.ruYuanZD:''}" ${this.data?`readonly`:``} style="height:26px;width: 500px;"> 
            </span>
            <span>
              <span class="iTabel"> 身高 </span>
              <input class="allInput" name="shenGao" type="number" oninput="validateNumber(this)" value="${this.data && this.data.shenGao?this.data.shenGao:WRT_config.SMTZ.filter(e=>e.shenGao!=null).length>0?WRT_config.SMTZ.filter(e=>e.shenGao!=null)[0].shenGao : ''}" ${this.data?`readonly`:``} style="height:26px;width: 70px;"> 
            </span>
            <span>
              <span class="iTabel"> 体重 </span>
              <input class="allInput" name="tiZhong" type="number" oninput="validateNumber(this)" value="${this.data && this.data.tiZhong?this.data.tiZhong:WRT_config.SMTZ.filter(e=>e.tiZhong!=null).length>0?WRT_config.SMTZ.filter(e=>e.tiZhong!=null)[0].tiZhong : ''}" ${this.data?`readonly`:``} style="height:26px;width: 70px;"> 
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold;width: 100px;min-width: 100px;">生命体征：</span>
            <span style="padding-right: 20px">
              <span class="iTabel"> T: </span>
              <input class="allInput" name="tiWen" value="${this.data && this.data.tiWen?this.data.tiWen:WRT_config.SMTZ.filter(e=>e.tiWen!=null).length>0?WRT_config.SMTZ.filter(e=>e.tiWen!=null)[0].tiWen : ''}" ${this.data?`readonly`:``} style="height:26px;width: 56px;"> ℃
            </span>
            <span style="padding-right: 20px">
              <span class="iTabel"> PB: </span>
              <input class="allInput" name="xueYaSY" value="${this.data && this.data.xueYa && String(this.data.xueYa).indexOf('/')!==-1?((this.data.xueYa).split('/'))[0]:WRT_config.SMTZ.filter(e=>e.shangYa!=null).length>0?WRT_config.SMTZ.filter(e=>e.shangYa!=null)[0].shangYa : ''}" ${this.data?`readonly`:``} style="height:26px;width:60px;"> / 
              <input class="allInput" name="xueYaXY" value="${this.data && this.data.xueYa && String(this.data.xueYa).indexOf('/')!==-1?((this.data.xueYa).split('/'))[1]:WRT_config.SMTZ.filter(e=>e.xiaYa!=null).length>0?WRT_config.SMTZ.filter(e=>e.xiaYa!=null)[0].xiaYa :''}" ${this.data?`readonly`:``} style="height:26px;width:60px;"> mmHg
            </span>
            <span style="padding-right: 20px">
              <span class="iTabel"> P: </span>
              <input class="allInput" name="xinTiao" value="${this.data && this.data.xinTiao?this.data.xinTiao:WRT_config.SMTZ.filter(e=>e.xinLv!=null).length>0?WRT_config.SMTZ.filter(e=>e.xinLv!=null)[0].xinLv : ''}" ${this.data?`readonly`:``} style="height:26px;width: 70px;"> 次/分
            </span>
            <span style="padding-right: 20px">
              <span class="iTabel"> R: </span>
              <input class="allInput" name="huXi" value="${this.data && this.data.huXi?this.data.huXi:WRT_config.SMTZ.filter(e=>e.huXi!=null).length>0?WRT_config.SMTZ.filter(e=>e.huXi!=null)[0].huXi : ''}" ${this.data?`readonly`:``} style="height:26px;width: 70px;"> 次/分
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold;width: 100px;min-width: 100px;">血管通路：</span>
            <span  class="changecheck" style="flex-direction: column;">
              <div style="display: flex;justify-content: flex-start;">
                <span class="iTabel"> 类型 </span>
                <label class="radio-inline">
                  <input name="xueGuanTLLX" type="radio" value="临时导管" onclick="XGTLchange()" ${this.data && this.data.xueGuanTLLX && this.data.xueGuanTLLX=='临时导管'?`checked`:this.data?`disabled`:``}/>
                  <span class="iTabel"> 临时导管 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLLX" type="radio" value="长期导管" onclick="XGTLchange()"  ${this.data && this.data.xueGuanTLLX && this.data.xueGuanTLLX=='长期导管'?`checked`:this.data?`disabled`:``}/>  
                  <span class="iTabel"> 长期导管 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLLX" type="radio" value="动静脉瘘" onclick="XGTLchange()"  ${this.data && this.data.xueGuanTLLX && this.data.xueGuanTLLX=='动静脉瘘'?`checked`:this.data?`disabled`:``}/>  
                  <span class="iTabel"> 动静脉瘘 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLLX" type="radio" value="其他：" onclick="XGTLchange()"  ${this.data && this.data.xueGuanTLLX && this.data.xueGuanTLLX.indexOf('其他：')!=-1?`checked`:this.data?`disabled`:``}/>  
                  <span class="iTabel"> 其他 </span>
                  &nbsp;&nbsp;<input class="allInput" name="xueGuanTLLXVal" value="${this.data && this.data.xueGuanTLLX && ((this.data.xueGuanTLLX).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?((this.data.xueGuanTLLX).split(',')).filter(item=>item.indexOf('其他：')!=-1)[0].split('：')[1]:''}" style="height:26px" ${this.data?`readonly`:`disabled`}>
                </label>
              </div>
              <div style="display: flex;justify-content: flex-start;">
                <span class="iTabel"> 部位 </span>
                <label class="radio-inline">
                  <input name="xueGuanTLBWLR" type="radio" value="左侧" ${this.data && this.data.xueGuanTLBW && this.data.xueGuanTLBW.indexOf('左侧')==-1?`checked`:this.data?`disabled`:``}/>
                  <span class="iTabel"> 左侧 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLBWLR" type="radio" value="右侧" ${this.data && this.data.xueGuanTLBW && this.data.xueGuanTLBW.indexOf('右侧')==-1?`checked`:this.data?`disabled`:``} />  
                  <span class="iTabel"> 右侧 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLBW" type="radio" value="股静脉" ${this.data && this.data.xueGuanTLBW && this.data.xueGuanTLBW.indexOf('股静脉')==-1?`checked`:this.data?`disabled`:``} />  
                  <span class="iTabel"> 股静脉 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLBW" type="radio" value="颈内静脉" ${this.data && this.data.xueGuanTLBW && this.data.xueGuanTLBW.indexOf('颈内静脉')==-1?`checked`:this.data?`disabled`:``} />
                  <span class="iTabel"> 颈内静脉 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLBW" type="radio" value="锁骨下静脉" ${this.data && this.data.xueGuanTLBW && this.data.xueGuanTLBW.indexOf('锁骨下静脉')==-1?`checked`:this.data?`disabled`:``} />  
                  <span class="iTabel"> 锁骨下静脉 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLBW" type="radio" value="动静脉瘘" ${this.data && this.data.xueGuanTLBW && this.data.xueGuanTLBW.indexOf('动静脉瘘')==-1?`checked`:this.data?`disabled`:``} />  
                  <span class="iTabel"> 动静脉瘘 </span>
                </label>
              </div>
            </span>
            
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;"> 适应症：</span>
            <span class="changeRadio">
              <label class="radio-inline" style="padding-left: 2px;min-width: 120px;">
                <input name="syz_radio" type="checkbox" value="容量过负荷" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item == '容量过负荷')[0] == '容量过负荷'?`checked disabled`:this.data?`disabled`:``} />
                <span class="iTabel"> 容量过负荷 </span>
              </label>
              <label class="radio-inline">
                <input name="syz_radio" type="checkbox" value="严重酸碱及电解质紊乱" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item == '严重酸碱及电解质紊乱')[0] == '严重酸碱及电解质紊乱'?`checked disabled`:this.data?`disabled`:``}/>  
                <span class="iTabel" style="min-width: 196px;"> 严重酸碱及电解质紊乱 </span>
              </label>
              <label class="radio-inline">
                <input name="syz_radio" type="checkbox" value="急慢性肾衰竭" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item == '急慢性肾衰竭')[0] == '急慢性肾衰竭'?`checked disabled`:this.data?`disabled`:``}/>  
                <span class="iTabel" style="min-width: 124px;"> 急慢性肾衰竭 </span>
              </label>
              <label class="radio-inline">
                <input name="syz_radio" type="checkbox" value="多器官功能障碍综合症" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item == '多器官功能障碍综合症')[0] == '多器官功能障碍综合症'?`checked disabled`:this.data?`disabled`:``} />  
                <span class="iTabel" style="min-width: 196px;"> 多器官功能障碍综合症 </span>
              </label>
              <label class="radio-inline">
                <input name="syz_radio" type="checkbox" value="药物中毒" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item == '药物中毒')[0] == '药物中毒'?`checked disabled`:this.data?`disabled`:``} />  
                <span class="iTabel" style="min-width: 88px;"> 药物中毒 </span>
              </label>
              <label class="radio-inline">
                <input name="syz_radio" type="checkbox" value="免疫相关性疾病" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item == '免疫相关性疾病')[0] == '免疫相关性疾病'?`checked disabled`:this.data?`disabled`:``} />  
                <span class="iTabel" style="min-width: 130px;"> 免疫相关性疾病 </span>
              </label>
              <label class="radio-inline">
                <input name="syz_radio" type="checkbox" value="急性肝衰竭" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item == '急性肝衰竭')[0] == '急性肝衰竭'?`checked disabled`:this.data?`disabled`:``} />  
                <span class="iTabel"> 急性肝衰竭 </span>
              </label>
              <label class="radio-inline">
                <input name="syz_radio" type="checkbox" value="其他：" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?`checked disabled`:this.data?`disabled`:``} />  
                <span class="iTabel"> 其他&nbsp;&nbsp;<input class="allInput" name="SYZQT" value="${this.data && (this.data.shiYingZheng.split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?(this.data.shiYingZheng.split(',')).filter(item=>item.indexOf('其他：')!=-1)[0]:''}" ${this.data?`readonly`:`disabled`} style="height:26px"> </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;"> 禁忌症：</span>
            <span class="changeRadio">
              <label class="radio-inline" style="padding-left: 2px;">
                <input name="jjz_radio" type="checkbox" value="无" ${this.data && this.data.jinJiZheng && (this.data.jinJiZheng.split(',')).filter(item=>item == '无').length!=0?`checked disabled`:this.data?`disabled`:``} />
                <span class="iTabel"> 无 </span>
              </label>
              <label class="radio-inline">
                <input name="jjz_radio" type="checkbox" value="无法建立合适的血管通路" ${this.data && this.data.jinJiZheng && (this.data.jinJiZheng.split(',')).filter(item=>item == '无法建立合适的血管通路').length!=0?`checked disabled`:this.data?`disabled`:``} />  
                <span class="iTabel"> 无法建立合适的血管通路 </span>
              </label>
              <label class="radio-inline">
                <input name="jjz_radio" type="checkbox" value="严重凝血功能障碍" ${this.data && this.data.jinJiZheng && (this.data.jinJiZheng.split(',')).filter(item=>item == '严重凝血功能障碍').length!=0?`checked disabled`:this.data?`disabled`:``} />  
                <span class="iTabel"> 严重凝血功能障碍 </span>
              </label>
              <label class="radio-inline">
                <input name="jjz_radio" type="checkbox" value="严重活动性出血" ${this.data && this.data.jinJiZheng && (this.data.jinJiZheng.split(',')).filter(item=>item == '严重活动性出血').length!=0?`checked disabled`:this.data?`disabled`:``} />  
                <span class="iTabel"> 严重活动性出血 </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;">AKI分期：</span>
            <span class="changeRadio">
              <label class="radio-inline">
                <input name="AKIfq" type="radio" value="1期" ${this.data && this.data.aki && this.data.aki=='1期'?`checked`:this.data?`disabled`:``} />
                <span class="iTabel"> Ⅰ期 </span>
              </label>
              <label class="radio-inline">
                <input name="AKIfq" type="radio" value="2期" ${this.data && this.data.aki && this.data.aki=='2期'?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> Ⅱ期 </span>
              </label>
              <label class="radio-inline">
                <input name="AKIfq" type="radio" value="3期" ${this.data && this.data.aki && this.data.aki=='3期'?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> Ⅲ期 </span>
              </label>
              <label class="radio-inline">
                <input name="AKIfq" type="radio" value="无" ${this.data && this.data.aki && this.data.aki==''?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> 无 </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;">模  式：</span>
            <span class="changeRadio">
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="CVVH" onclick="MSchangeNow()" ${this.data && this.data.moShi && this.data.moShi=='CVVH'?`checked`:this.data?`disabled`:``} />
                <span class="iTabel"> CVVH </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="CVVHD" onclick="MSchangeNow()" ${this.data && this.data.moShi && this.data.moShi=='CVVHD'?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> CVVHD </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="CVVHDF" onclick="MSchangeNow()" ${this.data && this.data.moShi && this.data.moShi=='CVVHDF'?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> CVVHDF </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="血浆置换" onclick="MSchangeNow()" ${this.data && this.data.moShi && this.data.moShi=='血浆置换'?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> 血浆置换 </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="人工肝" onclick="MSchangeNow()" ${this.data && this.data.moShi && this.data.moShi=='人工肝'?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> 人工肝 </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="其他：" onclick="MSchangeNow()" ${this.data && this.data.moShi && this.data.moShi.indexOf('其他：')!=-1?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> 其他&nbsp;&nbsp;<input class="allInput" name="MSQTVal" value="${this.data && this.data.moShi && ((this.data.moShi).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?((this.data.moShi).split(',')).filter(item=>item.indexOf('其他：')!=-1)[0].split('：')[1]:''} " ${this.data?`readonly`:`disabled`} style="height:26px">  </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;">滤  器：</span>
            <span class="changeRadio">
              <label class="radio-inline">
                <input name="nq_radio" type="radio" value="Prismaflex M150" onclick="LQchangeNow()" ${this.data && this.data.lvQi && this.data.lvQi=='Prismaflex M150'?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> Prismaflex M150 </span>
              </label>
              <label class="radio-inline">
                <input name="nq_radio" type="radio" value="ACF-180W" onclick="LQchangeNow()" ${this.data && this.data.lvQi && this.data.lvQi=='ACF-180W'?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> ACF-180W </span>
              </label>
              <label class="radio-inline">
                <input name="nq_radio" type="radio" value="PlasmafoOP-08" onclick="LQchangeNow()" ${this.data && this.data.lvQi && this.data.lvQi=='PlasmafoOP-08'?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> PlasmafoOP-08 </span>
              </label>
              <label class="radio-inline">
                <input name="nq_radio" type="radio" value="其他：" onclick="LQchangeNow()" ${this.data && this.data.lvQi && this.data.lvQi.indexOf('其他：')!=-1?`checked`:this.data?`disabled`:``} />
                <span class="iTabel"> 其他&nbsp;&nbsp;<input class="allInput" name="LQVal" value="${this.data && this.data.lvQi && ((this.data.lvQi).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?((this.data.lvQi).split(',')).filter(item=>item.indexOf('其他：')!=-1)[0].split('：')[1]:''}" ${this.data?`readonly`:`disabled`} style="height:26px"> </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name" style="background: #00B0F0;">
            <span style="font-weight: bold;width: 100px;min-width: 100px;">抗凝方案：</span>
            <span  class="changecheck" style="flex-direction: column;">
              <div style="display: flex;justify-content: flex-start;">
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" onclick="KNFAchange()" value="无" ${this.data && this.data.kangNingFA && (this.data.kangNingFA=='' || this.data.kangNingFA=='无')?`checked`:this.data?`disabled`:``} /> 
                  <span class="iTabel"> 无 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" onclick="KNFAchange()" value="肝素" ${this.data && this.data.kangNingFA && this.data.kangNingFA=='肝素'?`checked`:this.data?`disabled`:``} />
                  <span class="iTabel"> 肝素 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" onclick="KNFAchange()" value="枸橼酸" ${this.data && this.data.kangNingFA && this.data.kangNingFA=='枸橼酸'?`checked`:this.data?`disabled`:``} />
                  <span class="iTabel"> 枸橼酸 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" onclick="KNFAchange()" value="比伐卢定" ${this.data && this.data.kangNingFA && this.data.kangNingFA=='比伐卢定'?`checked`:this.data?`disabled`:``} />
                  <span class="iTabel"> 比伐卢定 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" onclick="KNFAchange()" value="萘莫司他" ${this.data && this.data.kangNingFA && this.data.kangNingFA=='萘莫司他'?`checked`:this.data?`disabled`:``} />
                  <span class="iTabel"> 萘莫司他 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" onclick="KNFAchange()" value="其他：" ${this.data && this.data.kangNingFA &&  this.data.kangNingFA.indexOf()?`checked`:this.data?`disabled`:``} />
                  <span class="iTabel"> 其他&nbsp;&nbsp;<input class="allInput" name="KNFAVAL" value="${this.data && this.data.kangNingFA && ((this.data.kangNingFA).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?((this.data.kangNingFA).split(',')).filter(item=>item.indexOf('其他：')!=-1)[0].split('：')[1]:''}" ${this.data?`readonly`:`disabled`} style="height:26px"> </span>
                </label>
              </div>
              <div style="display: flex;justify-content: flex-start;align-items: center;padding-bottom: 5px;">
                <span class="iTabel" style="width:150px;border-right: 2px solid #fff;min-width:150px;">肝素/萘莫司他/比伐卢定</span>
                <span  class="changecheck" style="flex-direction: row;flex-wrap: wrap;">
                  <label class="radio-inline">
                    APTT目标值：
                    <input class="allInput" name="KNFAAPPT" value="${this.data && this.data.aptt?this.data.aptt:''}" ${this.data?`readonly`:``} style="height:26px"></input> 
                    &nbsp;s
                  </label>
                  <label class="radio-inline">
                    负荷计量：
                    <input class="allInput" name="KNFAFHJL" value="${this.data && this.data.fuHeJL?this.data.fuHeJL:''}" ${this.data?`readonly`:``} style="height:26px"></input> 
                    &nbsp;mg
                  </label>
                  <label class="radio-inline">
                    维持剂量：
                    <input class="allInput" name="KNFAWC" value="${this.data && this.data.weiChiJL?this.data.weiChiJL:''}" ${this.data?`readonly`:``} style="height:26px"></input> 
                    &nbsp;mg/h
                  </label>
                </span>
              </div>
              <div style="display: flex;justify-content: flex-start;padding-bottom: 5px;">
                <span class="iTabel" style="width:150px;border-right: 2px solid #fff;">枸橼酸</span>
                <label class="radio-inline" >
                  枸橼酸剂量：
                  <input class="allInput" name="KNFAGJSJL" value="${this.data && this.data.juYuanSuanJL?this.data.juYuanSuanJL:''}" ${this.data?`readonly`:``} style="height:26px"></input> 
                  &nbsp;mmol/l
                </label>
                <br/>
                <label class="radio-inline" >
                  钙剂量：
                  <input class="allInput" name="KNFAGJSGJL" value="${this.data && this.data.gaiJiLiang?this.data.gaiJiLiang:''}" ${this.data?`readonly`:``} style="height:26px"></input> 
                  &nbsp;
                </label>
              </div>
              <div style="display: flex;justify-content: flex-start;padding-bottom: 5px;">
                <span class="iTabel" style="width:150px;border-right: 2px solid #fff;">其他</span>
                <label class="radio-inline" >
                  剂量：
                  <input class="allInput" name="KNFAGJSQTJL" value="${this.data && this.data.qiTaJL?this.data.qiTaJL:''}" ${this.data?`readonly`:``} style="height:26px"></input> 
                </label>
              </div>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name" >
            <span style="font-weight: bold; width: 100px;min-width: 100px;">封管：</span>
            <span class="changeRadio">
              <label class="radio-inline">
                <input name="fg_radio" type="radio" value="肝素（1:1000U）" onclick="FGchangeNow()" ${this.data && this.data.fengGuan && this.data.fengGuan =='肝素（1:1000U）'?`checked`:this.data?`disabled`:``} />
                <span class="iTabel"> 肝素（1:1000U） </span>
              </label>
              <label class="radio-inline">
                <input name="fg_radio" type="radio" value="4%枸橼酸" onclick="FGchangeNow()" ${this.data && this.data.fengGuan && this.data.fengGuan =='4%枸橼酸'?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> 4%枸橼酸 </span>
              </label>
              <label class="radio-inline">
                <input name="fg_radio" type="radio" value="其他：" onclick="FGchangeNow()" ${this.data && this.data.fengGuan && (this.data.fengGuan).indexOf('其他') != -1?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> 其他&nbsp;&nbsp;<input class="allInput" name="FGQT" value="${this.data && this.data.fengGuan && ((this.data.fengGuan).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?((this.data.fengGuan).split(',')).filter(item=>item.indexOf('其他：')!=-1)[0].split('：')[1]:''}" ${this.data?`readonly`:`disabled`} style="height:26px">  </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;">管路预冲：</span>
            <span class="changeRadio">
              <label class="radio-inline">
                NS500ml+ &nbsp;&nbsp;
                <input class="allInput" name="guanLuYC" value="${this.data && this.data.guanLuYC?this.data.guanLuYC:''}" ${this.data?`readonly`:``} style="height:26px;width:100px"></input> 
                &nbsp;mg（
                <span  class="changecheck">
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="guanLuYCYP" type="checkbox" value="肝素" ${this.data && this.data.guanLuYCYP && (this.data.guanLuYCYP.split(',')).filter(item=>item == '肝素')[0]=='肝素'?`checked disabled`:this.data?`disabled`:``} /> 
                    <span class="iTabel"> 肝素 </span>
                  </label>
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="guanLuYCYP" type="checkbox" value="萘莫司他" ${this.data  && this.data.guanLuYCYP && (this.data.guanLuYCYP.split(',')).filter(item=>item == '萘莫司他')[0]=='萘莫司他'?`checked disabled`:this.data?`disabled`:``} /> 
                    <span class="iTabel"> 萘莫司他 </span>
                  </label>
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="guanLuYCYP" type="checkbox" value="无" ${this.data && this.data.guanLuYCYP && (this.data.guanLuYCYP.split(',')).filter(item=>item == '无')[0]=='无'?`checked disabled`:this.data?`disabled`:``} /> 
                    <span class="iTabel"> 无 </span>
                  </label>
                </span>
                ）
              </label>
            </span>
            <span>预充液是否进入体内：</span>
            <span class="changeRadio">
              <label class="radio-inline">
                <input name="yuChongYeJRTN" type="radio" value="是" ${this.data && this.data.yuChongYeJRTN=='是'?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> 是 </span>
              </label>
              <label class="radio-inline">
                <input name="yuChongYeJRTN" type="radio" value="否" ${this.data && this.data.yuChongYeJRTN=='否'?`checked`:this.data?`disabled`:``} /> 
                <span class="iTabel"> 否 </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;">置换液：</span>
              <span class="changecheck" style="flex-direction: column;">
                <div style="display: flex;flex-direction: row;align-items: center;justify-content: flex-start;">
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="zhiHuanYe" type="checkbox" value="血液滤过置换基础液" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '血液滤过置换基础液')[0]=='血液滤过置换基础液'?`checked disabled`:this.data?`disabled`:``} /> 
                    <span class="iTabel"> 血液滤过置换基础液 </span>
                  </label>
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="zhiHuanYe" type="checkbox" value="氯化钾" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '氯化钾')[0]=='氯化钾'?`checked disabled`:this.data?`disabled`:``} /> 
                    <span class="iTabel"> 氯化钾 </span>
                  </label>
                </div>
                <div style="display: flex;flex-direction: row;align-items: center;justify-content: flex-start;">
                  <label class="radio-inline" style="border-right: 2px solid #fff;left:-18px;">
                    <input name="zhiHuanYe" type="checkbox" value="PORT配方" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == 'PORT配方')[0] =='PORT配方'?`checked disabled`:this.data?`disabled`:``} /> 
                    <span class="iTabel"> PORT配方 </span>
                  </label>
                  <label class="radio-inline">
                    <input name="zhiHuanYe" type="checkbox" value="NS3000ml" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == 'NS3000ml')[0]=='NS3000ml'?`checked disabled`:this.data?`disabled`:``} /> 
                    <span class="iTabel"> NS3000 </span>
                  </label>
                  <label class="radio-inline">
                    <input name="zhiHuanYe" type="checkbox" value="5%GS1000" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '5%GS1000')[0]=='5%GS1000'?`checked disabled`:this.data?`disabled`:``} /> 
                    <span class="iTabel"> 5%GS1000 </span>
                  </label>
                  <label class="radio-inline">
                    <input name="zhiHuanYe" type="checkbox" value="50%硫酸镁1.6" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '50%硫酸镁1.6')[0]=='50%硫酸镁1.6'?`checked disabled`:this.data?`disabled`:``} /> 
                    <span class="iTabel"> 50%硫酸镁1.6 </span>
                  </label>
                  <label class="radio-inline">
                    <input name="zhiHuanYe" type="checkbox" value="10%氯化钙10ml" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '10%氯化钙10ml')[0]=='10%氯化钙10ml'?`checked disabled`:this.data?`disabled`:``} /> 
                    <span class="iTabel"> 10%氯化钙10ml </span>
                  </label>
                </div>
              </span>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;">稀释方式：</span>
              <span  class="changeRadio">
                <label class="radio-inline">
                  <input name="xsfs_check" type="radio" value="前稀释" onclick="changeNow()" ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('前稀释')!=-1?`checked`:this.data?`disabled`:``} /> 
                  <span class="iTabel"> 前稀释 </span>
                </label>
                <label class="radio-inline">
                  <input name="xsfs_check" type="radio" value="后稀释" onclick="changeNow()" ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('后稀释')!=-1?`checked`:this.data?`disabled`:``} /> 
                  <span class="iTabel"> 后稀释 </span>
                </label>
                <label class="radio-inline">
                  <input name="xsfs_check" type="radio" value="前稀释+后稀释" onclick="changeNow()" ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('前稀释+后稀释')!=-1?`checked`:this.data?`disabled`:``} /> 
                  <span class="iTabel"> 前稀释+后稀释 </span>
                </label>
                <label class="radio-inline">
                  <input name="xsfs_check" type="radio" value="透析+稀释：" onclick="changeNow()" ${this.data && this.data.xiShiFS && this.data.xiShiFS && (this.data.xiShiFS.split(',')).filter(item=>item.indexOf('透析+稀释') != -1).length!=0?`checked`:this.data?`disabled`:``} /> 
                  <span class="iTabel"> 透析+稀释 </span>
                  （ 
                  <span  class="changecheck">
                    <label class="radio-inline" style="padding-left: 2px;">
                      <input name="txxsq_check" type="checkbox" value="前" ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('前')!=-1?`checked disabled`:$('.showForm input[name=xsfs_check]:checked').val()=='透析+稀释：'?'':'disabled'} /> 
                      <span> 前 </span>
                    </label>
                    <label class="radio-inline">
                      <input name="txxsq_check" type="checkbox" value="后" ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('前')!=-1?`checked disabled`:$('.showForm input[name=xsfs_check]:checked').val()=='透析+稀释：'?'':'disabled'}/> 
                      <span> 后 </span>
                    </label>
                  </span>
                  ）
                </label>
              </span>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name" style="height:100px;">
            <span style="font-weight: bold; width: 100px;min-width: 100px;padding-right:10px">人工肝/血浆置换：</span>
            <span  class="changecheck" style="flex-direction: row;flex-wrap: wrap;">
              <label class="radio-inline" style="padding-left: 2px;padding-right: 18px;">
                <span> 血浆 </span>
                &nbsp;&nbsp;<input class="allInput" name="xjzhXJ" value="${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('血浆：')!=-1).length!=0?((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('血浆：')!=-1)[0].split('：')[1]:''}" ${this.data?'readonly':''} style="width:50px" /> &nbsp;ml
              </label>
              <label class="radio-inline" style="padding-left: 2px;">
                <input name="ZJZH" type="checkbox" value="NS1000ml：" onclick="CJZHchange()" ${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('NS1000ml：')!=-1).length!=0?`checked disabled`: this.data?'disabled':''} /> 
                <span class="iTabel"> NS1000ml </span>
                <input class="allInput" name="xjzhNSVal" value="${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('NS1000ml：')!=-1).length!=0?((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('NS1000ml：')!=-1)[0].split('：')[1]:''}" ${this.data?'readonly':'disabled'} style="width:50px" /> &nbsp;ml
              </label>
              <label class="radio-inline">
                <input name="ZJZH" type="checkbox" value="20%白蛋白：" onclick="CJZHchange()" ${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('20%白蛋白：')!=-1).length!=0?`checked disabled`:this.data?'disabled':''} /> 
                <span class="iTabel"> 20%白蛋白 </span>
                <input class="allInput" name="xjzhBDBVal" value="${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('20%白蛋白：')!=-1).length!=0?((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('20%白蛋白：')!=-1)[0].split('：')[1]:''}" ${this.data?'readonly':'disabled'} style="width:50px" /> &nbsp;ml
              </label>
              <label class="radio-inline">
                <input name="ZJZH" type="checkbox" value="10%葡萄糖酸钙针：" onclick="CJZHchange()"  ${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('20%葡萄糖酸钙针：')!=-1).length!=0?`checked disabled`:this.data?'disabled':''} /> 
                <span class="iTabel">  10%葡萄糖酸钙针 </span>
                <input class="allInput" name="xjzhPPTSGVal" value="${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('10%葡萄糖酸钙针：')!=-1).length!=0?((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('10%葡萄糖酸钙针：')!=-1)[0].split('：')[1]:''}" ${this.data?'readonly':'disabled'} style="width:50px" /> &nbsp;ml
              </label>
              <label class="radio-inline">
                <input name="ZJZH" type="checkbox" value="人工胶体：" onclick="CJZHchange()"  ${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('人工胶体：')!=-1).length!=0?`checked disabled`:this.data?'disabled':''} /> 
                <span class="iTabel"> 人工胶体 </span>
                <input class="allInput" name="zjzhRGJTVal" value="${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('人工胶体：')!=-1).length!=0?((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('人工胶体：')!=-1)[0].split('：')[1]:''}" ${this.data?'readonly':'disabled'} style="width:50px" /> &nbsp;ml
              </label>
              <label class="radio-inline">
                <input name="ZJZH" type="checkbox" value="其他：" onclick="CJZHchange()"  ${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?`checked disabled`:this.data?'disabled':''} /> 
                <span class="iTabel"> 其他 </span>
                &nbsp;&nbsp;<input class="allInput" name="xjzhQTVal" value="${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('其他：')!=-1)[0].split('：')[1]:''}" ${this.data?'readonly':'disabled'} style="width:300px" />
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name" style="background: #7dbc6ecc;">
            <span style="font-weight: bold; width: 100px;min-width: 100px;">初始参数：</span>
            <span style="flex-direction: column;">
              <div style="display: flex;justify-content: flex-start;">
                <label class="radio-inline" style="padding-left: 2px;">
                  <span> 目标血流速 </span>
                  &nbsp;&nbsp;<input class="allInput" name="mbxls" value="${this.data && this.data.muBiaoXLL?this.data.muBiaoXLL:''}" ${this.data?'readonly':''} style="width:50px" /> &nbsp;ml/min
                </label>
                <label class="radio-inline">
                  <span> 前置换 </span>
                  &nbsp;&nbsp;<input class="allInput" name="qzh" value="${this.data && this.data.qianZhiHL?this.data.qianZhiHL:''}" ${this.data?'readonly':''} style="width:50px" /> &nbsp;ml/h
                </label>
                <label class="radio-inline">
                  <span> 后置换 </span>
                  &nbsp;&nbsp;<input class="allInput" name="hzh" value="${this.data && this.data.houZhiHL?this.data.houZhiHL:''}" ${this.data?'readonly':''} style="width:50px" /> &nbsp;ml/h
                </label>
                <label class="radio-inline">
                  <span> 透析液 </span>
                  &nbsp;&nbsp;<input class="allInput" name="txy" value="${this.data && this.data.touXiYe?this.data.touXiYe:''}" ${this.data?'readonly':''} style="width:50px" /> &nbsp;ml/h
                </label>
                <label class="radio-inline">
                  <span> 5%碳酸氢钠 </span>
                  &nbsp;&nbsp;<input class="allInput" name="tsqn" value="${this.data && this.data.tanSuanQN?this.data.tanSuanQN:''}" ${this.data?'readonly':''} style="width:50px" /> &nbsp;ml/h
                </label>
              </div>
              <div style="display: flex;justify-content: flex-start;">
                <label class="radio-inline" style="padding-left: 2px;">
                  <span> 10%氯化钾 </span>
                  &nbsp;&nbsp;<input class="allInput" name="lhj" value="${this.data && this.data.lvHuaJia?this.data.lvHuaJia:''}" ${this.data?'readonly':''} style="width:50px" /> &nbsp;ml/(4L置换液)
                </label>
                <label class="radio-inline">
                  <span> 目标超滤量 </span>
                  &nbsp;&nbsp;<input class="allInput" name="mbcll" value="${this.data && this.data.muBiaoCLL?this.data.muBiaoCLL:''}" ${this.data?'readonly':''} style="width:50px" /> &nbsp;ml/h
                </label>
                <label class="radio-inline">
                  <span> 预期治疗时间 </span>
                  &nbsp;&nbsp;<input class="allInput" name="yqzlsj" value="${this.data && this.data.yuQiZLSJ?this.data.yuQiZLSJ:''}" ${this.data?'readonly':''} style="width:50px" /> &nbsp;h
                </label>
                <label class="radio-inline">
                  <span> 净超滤目标 </span>
                  &nbsp;&nbsp;<input class="allInput" name="jclmb" value="${this.data && this.data.jingChaoLMBL?this.data.jingChaoLMBL:''}" ${this.data?'readonly':''} style="width:50px" /> &nbsp;ml/d
                </label>
              </div>
              <div style="display: flex;justify-content: flex-start;position: relative;left: -16px;">
                <label class="radio-inline">
                  <span> 血浆分离流量 </span>
                  &nbsp;&nbsp;<input class="allInput" name="xjflll" value="${this.data && this.data.xueJiangFLLL?this.data.xueJiangFLLL:''}" ${this.data?'readonly':''} style="width:50px" /> &nbsp;ml/min
                </label>
                <label class="radio-inline">
                  <span> 血浆治疗总量 </span>
                  &nbsp;&nbsp;<input class="allInput" name="xjzlzl" value="${this.data && this.data.xueJiangZLZL?this.data.xueJiangZLZL:''}" ${this.data?'readonly':''} style="width:50px" /> &nbsp;ml
                </label>
              </div>
            </span>
          </span>
        </div>
      </div>
      <div class="formSort">
        <div class="LFormTitle label-name" style="height:130px;">
          <span style="font-weight: bold; width: 100px;min-width: 100px;">抗凝检测：</span>
          <span  class="changecheck" style="flex-direction: column;">
            <div style="display: flex;justify-content: flex-start;align-items: center;padding-bottom: 5px;">
              <span class="iTabel" style="width:150px;">APTT/ACT</span>
              <label class="radio-inline">
                常规q4h，根据病情调整为q1h-qd/q1h-g6h监测
              </label>
            </div>
            <div style="display: flex;justify-content: flex-start;padding-bottom: 5px;    align-items: center;">
              <span class="iTabel" style="width:150px;">枸橼酸抗凝</span>
              <span  class="changecheck" style="flex-direction: column;">
                <label class="radio-inline">
                  动态监测体内和滤器离子钙、PH、Na+、HC03-水平
                </label>
                <label class="radio-inline">
                  血气监测频率:治疗开始后5min，i调整后1h，稳定后q6h
                </label>
                <label class="radio-inline">
                  测总钙gd，目标:总钙<3mmol儿，总钙/离子钙<2.5。
                </label>
              <span>
            </div>
          </span>
        </div>
      </div>
      <div class="formSort" style="height: 32px; bottom: 15px; position: fixed; right: 0;">
        <button class="e_btn backBtn" onclick="saveCFD()">保存</button>
        <button class="e_btn backBtn" onclick="parent.closeCRRT()">关闭</button>
      </div>
    </div>
    `
    this.$el.html(html)
    return this;
  }
})
