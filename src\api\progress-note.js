import request from '@/utils/request'

/**
 * 获取病程记录初始化数据
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */
export function getProgressNoteInit(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/getWenShuBCLInit',
    method: 'get',
    params: params
  })
}

/**
 * 获取文书格式信息（包含是否可以多份）
 * @param {Object} params 参数对象
 * @param {string} params.daiMa 格式代码
 * @returns {Promise} 返回包含是否可以多份的信息，1-可以多份，0-不允许多份
 */
export function getGeShiDMFromRedis(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/v1/wenShuSupport/getGeShiDMFromRedis',
    method: 'get',
    params: params
  })
}

/**
 * 新增检查VTE状态
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.yongHuID 用户ID
 * @returns {Promise}
 */
export function checkVTEStatus(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/checkVTE',
    method: 'get',
    params: params
  })
}

/**
 * 判断病人是否需要书写改良Rankin(mRS)量表
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export function checkRankinStatus(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/checkRankin',
    method: 'get',
    params: params
  })
}

/**
 * 判断病人是否需要书写PHQ量表
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @returns {Promise}
 */
export function checkPHQStatus(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/checkPHQ',
    method: 'get',
    params: params
  })
}

/**
 * 判断病人是否需要书写NIHSS量表
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export function checkNIHSSStatus(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/checkNIHSS',
    method: 'get',
    params: params
  })
}

/**
 * ICU特殊检查
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export function checkICUStatus(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/checkICU',
    method: 'get',
    params: params
  })
}

/**
 * 检查辅检状态,归档后辅检记录(1Z)和危急值记录(1I)调用
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export function checkFJZT(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/checkFJZT',
    method: 'get',
    params: params
  })
}

/**
 * 检查产科VTE状态
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.yongHuID 用户ID
 * @returns {Promise}
 */
export function checkCKVteCnt(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/checkCKVteCnt',
    method: 'get',
    params: params
  })
}

/**
 * 根据病历ID获取病人住院危急值列表
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @returns {Promise}
 */
export function getCriticalValueByBingLiID(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/getCriticalValueByBingLiID',
    method: 'post',
    params: params
  })
}

/**
 * 根据用户id获取医生信息
 * @param {Object} params 参数对象
 * @param {string} params.yongHuID 用户ID
 * @returns {Promise}
 */
export function getDoctorInfoEhr(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getDoctorInfoEhr',
    method: 'get',
    params: params
  })
}

/**
 * 判断是否可归档
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历id
 * @param {string} params.renYuanLB 人员类别
 * @returns {Promise}
 */
export function canGuiDang(params) {
  return request({
    url: '/app-emrservice/v1/xiuGaiBingLiZT/canGuiDang',
    method: 'post',
    params: params
  })
}

/**
 * 住院医生站_系统维护_病人病历状态修改_医生归档
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历id
 * @param {string} params.yongHuID 用户id
 * @returns {Promise}
 */
export function yiShiGD(params) {
  return request({
    url: '/app-emrservice/v1/xiuGaiBingLiZT/yiShiGD',
    method: 'get',
    params: params
  })
}

/**
 * 住院医生站_系统维护_病人病历状态修改_护士归档
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历id
 * @param {string} params.yongHuID 用户id
 * @returns {Promise}
 */
export function huShiGD(params) {
  return request({
    url: '/app-emrservice/v1/xiuGaiBingLiZT/huShiGD',
    method: 'get',
    params: params
  })
}

/**
 * 住院医生站_系统维护_病人病历状态修改_医师撤回
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历id
 * @param {string} params.yongHuID 用户id
 * @returns {Promise}
 */
export function yiShiCH(params) {
  return request({
    url: '/app-emrservice/v1/xiuGaiBingLiZT/yiShiCH',
    method: 'get',
    params: params
  })
}

/**
 * 住院医生站_集中打印_初始化接口
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @returns {Promise}
 */
export function getJiZhongDYInit(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/getJiZhongDYInit',
    method: 'post',
    params: params
  })
}

/**
 * 集中打印接口
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.yongHuID 用户ID
 * @param {string} params.lieBiao  列表ID
 * @returns {Promise}
 */
export function GetPrintContent(params) {
  return request({
    url: '/app-emrservice/v1/xiuGaiBingLiZT/GetPrintContent',
    method: 'post',
    data: params
  })
}
