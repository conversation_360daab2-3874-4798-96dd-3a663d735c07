/** 授权模块 对user的补充*/
const state = {
  'logout-msg': '',
  orgKey: '',
  guid: '',
  yydmList: []
}
const mutations = {
  'SET_LOGOUT-MSG'(state, data) {
    state['logout-msg'] = data
  },
  SET_ORGKEY(state, data) {
    state.orgKey = data
  },
  SET_GUID(state, data) {
    state.guid = data
  },
  SET_YYDMLIST(state, data) {
    state.yydmList = data
  },
  SET_AUTH(state, data) {
    state[data.name] = data.value
  }
}

const actions = {
  setLogoutMsg({ commit }, data) {
    commit('SET_LOGOUT-MSG', data)
  },
  setOrgKey({ commit }, data) {
    commit('SET_ORGKEY', data)
  },
  setGuId({ commit }, data) {
    commit('SET_GUID', data)
  },
  setAuthrizedYYDMList({ commit }, data) {
    const _YYDMList = state.yydmList || []
    _YYDMList.push(data)
    const noRepeatArray = [...new Set(_YYDMList)]
    commit('SET_YYDMLIST', noRepeatArray)
  },
  removeLogoutMsg(param) {
    actions.setLogoutMsg(param, '')
  },
  removeOrgKey(param) {
    actions.setOrgKey(param, '')
  },
  removeGuId(param) {
    actions.setGuId(param, '')
  },
  removeAuthrizedYYDMList({ commit }) {
    commit('SET_YYDMLIST', [])
  }
}

const getters = {
  'logout-msg'(state) {
    return state['logout-msg']
  },
  orgKey(state) {
    return state.orgKey
  },
  guid(state) {
    return state.guid
  },
  yydmList(state) {
    return state.yydmList
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
