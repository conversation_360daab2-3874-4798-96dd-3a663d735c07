<template>
  <div class="container">
    <div class="header">
      <div class="header-item" style="width: 10%">
        <el-select v-model="jiaoJieBanLX">
          <el-option
            v-for="item in jiaoJieBanLXOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <div class="header-item">
        <div style="margin-right: 10px; width: 80px">时间范围:</div>
        <el-date-picker
          v-model="chaXunSJ"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd 00:00:00"
        ></el-date-picker>
      </div>
      <div class="header-item">
        <div style="margin-right: 10px">搜索关键词:</div>
        <div class="header-item-input">
          <el-input v-model="search" placeholder="请输入内容"></el-input>
        </div>
        <div class="header-item-button">
          <el-button @click="handleQuery">查询</el-button>
          <el-button class="print">打印</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">本专科交接班记录</div>
        <div class="button"><el-button type="primary" @click="handleAdd">新增</el-button></div>
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData">
          <el-table-column align="center" type="index" width="70"></el-table-column>
          <el-table-column align="center" type="selection" width="70"></el-table-column>
          <el-table-column prop="jiaoBanSJ" width="180" label="交班时间"></el-table-column>
          <el-table-column prop="jieBanSJ" width="180" label="接班时间"></el-table-column>
          <el-table-column prop="zhuanKeMC" width="119" label="科室"></el-table-column>
          <el-table-column prop="jiLuRYMC" width="160" label="交班医生"></el-table-column>
          <el-table-column prop="jieBanRYMC" width="88" label="接班医生"></el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">查看</el-button>
              <el-divider direction="vertical"></el-divider>
              <el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible" width="66%" :before-close="handleClose">
      <div slot="title" class="dialog-title">
        <i class="el-icon-coin"></i>
        <span style="margin-left: 5px">本专科交接班记录表</span>
      </div>
      <div style="border: 1px solid #dcdfe6; padding: 10px;background:color:#FAFBFC">
        <div style="text-align: center; font-size: 18px,font-weight:600">本专科交接班记录表</div>
        <table style="width: 100%; margin-top: 10px">
          <tr>
            <td class="info-label">交接时间：</td>
            <td class="info-value">
              <el-date-picker
                v-model="jiaoBanSJ"
                type="date"
                placeholder="选择日期"
              ></el-date-picker>
            </td>
            <td class="info-label">接班时间：</td>
            <td class="info-value flex" style="width: 240px">
              <el-date-picker
                v-model="jieBanSJ"
                type="date"
                placeholder="选择日期"
              ></el-date-picker>
              <el-button type="primary">确认接班</el-button>
            </td>
            <td class="info-label">科室：</td>
            <td class="info-value">{{ zhuanKeMC }}</td>
          </tr>
          <tr>
            <td class="info-label">本专科当天在院人数：</td>
            <td class="info-value">{{ zhuYuanBRS == null ? 0 : zhuYuanBRS }}人</td>
            <td class="info-label">本专科当天在院一级护理病人数：</td>
            <td class="info-value">{{ yiJiHLBRS == null ? 0 : yiJiHLBRS }}人</td>
            <td class="info-label">本专科当天在院四级手术病人数：</td>
            <td class="info-value">{{ siJiSSBRS == null ? 0 : siJiSSBRS }}人</td>
          </tr>
          <tr>
            <td class="info-label">
              <div>交接班记录：</div>
              <div style="color: #a66dd4; margin-right: 12px; text-decoration: underline">
                导入数据
              </div>
              <div style="color: #155bd4; margin-right: 12px; text-decoration: underline">模板</div>
            </td>
            <td class="info-value" colspan="5">
              <el-input v-model="jiaoJieBanNR" class="dialog-textarea" type="textarea"></el-input>
            </td>
          </tr>
          <tr>
            <td class="info-label">交接班医生签名：</td>
            <td class="info-value" colspan="5"><el-input></el-input></td>
          </tr>
        </table>
        <div style="margin-top: 10px">
          <i class="el-icon-info" style="color: #356ac5"></i>
          <span>注意：接班时，选择完接班时间再确认</span>
        </div>
      </div>
      <div class="bottom-btn-group">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="deleteDialogVisible" width="30%" :before-close="onCancel">
      <div slot="title" class="delete-dialog-title">
        <i class="el-icon-warning"></i>
        <span>删除提示</span>
      </div>
      <div class="delete-dialog-content">
        确认删除
        <span>【交班时间：{{ selectRow.jiaoBanSJ }}】</span>
        记录吗？
      </div>

      <span slot="footer" class="delete-dialog-footer">
        <el-button type="primary" @click="onSubmit">确 认</el-button>
        <el-button @click="onCancel">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { searchAllInfoOfSR, addList } from '@/api/specialist-menu'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      jiaoJieBanLXOptions: [
        {
          label: '未接班',
          value: 2
        },
        {
          label: '已接班',
          value: 1
        },
        {
          label: '全部',
          value: ''
        }
      ], //主页交接班类型列表
      jiaoJieBanLX: '', //交接班类型选择值
      chaXunSJ: '', //主页查询时间
      search: '', //主页搜索关键词
      tableData: [],
      dialogVisible: false,
      deleteDialogVisible: false,
      jiaoBanSJ: '',
      jieBanSJ: '',
      zhuanKeMC: '',
      zhuYuanBRS: 0,
      yiJiHLBRS: 0,
      siJiSSBRS: 0,
      jiaoJieBanNR: '',
      handleMode: '',
      selectRow: {}
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      yongHuZKMC: ({ patient }) => patient.initInfo.yongHuZKMC,
      yongHuXM: ({ patient }) => patient.initInfo.yongHuXM
    })
  },
  mounted() {
    console.log(this.$store.state)
  },
  methods: {
    handleClick(row) {
      this.dialogVisible = true
      this.handleMode = 'update'
      this.jiaoBanSJ = row.jiaoBanSJ
      this.jieBanSJ = row.jieBanSJ
      this.zhuanKeMC = row.zhuanKeMC
      this.zhuYuanBRS = row.zhuYuanBRS
      this.yiJiHLBRS = row.yiJiHLBRS
      this.siJiSSBRS = row.siJiSSBRS
      console.log(row)
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleAdd() {
      this.dialogVisible = true
      this.handleMode = 'add'
      this.jiaoBanSJ = ''
      this.jieBanSJ = ''
      this.zhuanKeMC = ''
      this.zhuYuanBRS = 0
      this.yiJiHLBRS = 0
      this.siJiSSBRS = 0
    },
    async handleQuery() {
      if (this.chaXunSJ == '' || this.chaXunSJ[0] == '' || this.chaXunSJ[1] == '') {
        this.$message.error('请填写开始日期和结束日期')
      } else {
        let res = await searchAllInfoOfSR({
          zhuanKeID: this.zhuanKeID,
          kaiShiSJ: this.chaXunSJ[0],
          jieShuSJ: this.chaXunSJ[1],
          guanJianZi: this.search
        })
        this.tableData = res.data
      }
    },
    async handleSave() {
      if (this.handleMode == 'add') {
        await addList({
          zhuanKeMC: this.yongHuZKMC, //专科名称
          // jiLuID: '', //记录id
          zhuanKeID: this.zhuanKeID, //专科id
          // jiaoJieBanLX: '', //交接班类型
          jiaoBanSJ: this.jiaoBanSJ, //交班时间
          jieBanSJ: this.jieBanSJ, //接班时间
          // zhuYuanBRS: '', //住院病人数
          // yiJiHLBRS: '', //一级护理病人数
          jiaoJieBanNR: this.jiaoJieBanNR, //交接班内容
          // caoZuoZheID: '', //操作者id
          // xiuGaiSJ: '', //修改时间
          // shouCiJLSJ: '', //首次记录时间
          // jiLuRYID: '', //记录人员id
          // jiLuRYMC: '', //交班人员名称
          // jieBanRYID: '', //接班人员id
          jieBanRYMC: this.yongHuXM //接班人员名称
          // zhuangTaiBZ: '', //状态标志
          // siJiSSBRS: '' //四级手术病人数
        })
      }
    },
    handleDelete(row) {
      this.selectRow = row
      this.deleteDialogVisible = true
    },
    onSubmit() {
      this.deleteDialogVisible = false
    },
    onCancel() {
      this.deleteDialogVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.el-date-editor {
  width: 100%;
  ::v-deep .el-input__inner {
    width: 100%;
  }
}
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
  .table {
    min-height: 650px;
    width: 70%;
  }
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 70%;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}

::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
td {
  border: 1px solid #ddd;
  border-collapse: collapse; /* 移除表格内边框间的间隙 */
  // height: 35px;
  padding: 3px;
}
.info-label {
  text-align: right;
  width: 190px;
  background-color: #eaf0f9;
  padding: 0px 5px;
}
.info-value {
  // background-color: #f7f9fd;
  width: 180px;
  padding: 5px 8px;
}
.flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.bottom-btn-group {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 30px;
  ::v-deep .el-button {
    margin-left: 10px;
  }
}
::v-deep .print {
  background-color: #356ac5 !important;
}
::v-deep .dialog-textarea {
  .el-textarea__inner {
    min-height: 200px !important;
  }
}
.delete-dialog-title {
  display: flex;
  align-items: center;
  i {
    color: #ed6a0c;
    margin-right: 6px;
    font-size: 20px;
  }
  span {
    font-weight: 600;
    font-size: 16px;
  }
}
.delete-dialog-content {
  margin-left: 38px;
  font-size: 15px;
  span {
    color: #356ac5;
  }
}
::v-deep .el-dialog__footer {
  margin-top: 10px;
  border: none;
}
</style>
