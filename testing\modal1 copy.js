// 统一页面启动
$(document).ready(() => {
  // let url =window.location.href.split("?")||[]
  // let text=url[1].split("&")
  // let params={}
  // for(let i of text){
  //     let fd=i.split("=")
  //     params[fd[0]]=fd[1]
  // }
  // console.log(params) // 后续放到init里面
  init()
})
/********************初始化********************/
function init() {

  // 知情记录列表，搜索
  WRT_e.api.bzqjllistall.getZqjlByKey({
    params: {"as_key":"患者","as_wslx":"2A"},
    success(data) {
      if (data.Code == 1) {
        let json=JSON.parse(data.Result)
        // console.log("初始化:",data)
        WRT_config.ZqjlByKey=json
        console.log("知情记录列表，搜索:",WRT_config.ZqjlByKey)
       
      }
    }
  })
  // 知情记录点击编辑(右侧显示内容)
  WRT_e.api.bzqjllistall.getWsUrl({
    params: {"as_gsdm":"5327","al_wsid":-252,"as_wslx":"2A","al_blid":1492768,"al_zyid":1492768},
    success(data) {
      if (data.Code == 1) {
        // console.log("病人已书写文书列表:",data)
        WRT_config.WsUrl=data
        console.log("知情记录点击编辑(右侧显示内容)",WRT_config.WsUrl)
      }
    }
  })
  
  // 专科知情记录列表，新增记录列表(e_init)
  WRT_e.api.bzqjllistall.getZqjlByList({
    params: {"as_zkid":88,"as_blid":1492768},
    success(data) {
      if (data.Code == 1) {
        // console.log("专科知情记录列表，新增记录列表",data)
        WRT_config.ZqjlByList=data
        console.log("专科知情记录列表，新增记录列表",WRT_config.ZqjlByList)
      }
    }
  })
  // 根据文书类型获取病人已书写文书列表(左侧汇总列表)
  WRT_e.api.bzqjllistall.getZqjlBrWssByLx({
    params:  {"as_blid":"1492768","as_wslx":"2A"},
    success(data) {
      if (data.Code == 1) {
        WRT_config.ZqjlBrWssByLx=data.Result
        console.log("根据文书类型获取病人已书写文书列表(左侧汇总列表)",WRT_config.ZqjlBrWssByLx)
        let json=WRT_config.ZqjlBrWssByLx.map(function(item){
          item.content_Control=0
          item.right_nrUrl = 0
          return item
        })
        console.log(json)
        // var a = json.find(ev => ev.ID)
        // console.log(a)
        // 左
        $('.bzqjllistall_inner_left').append(
          new left_Menu().init({
            data:WRT_config.ZqjlBrWssByLx
          }).render().$el
        )
        // 右
        $('.panel-group').html(
          new right_title().init({
            data:{
              dataNR:WRT_config.ZqjlBrWssByLx,
              dataID:WRT_config.ZqjlBrWssByLx.ID
            }
          }).render().$el
        )
      }
    }
  })
  // 更新知情记录列表，新增记录列表
  WRT_e.api.bzqjllistall.getUpdateZqjlHtml({
    params: {"as_wsid":"-249","as_blid":"1492768"},
    success(data) {
      if (data.Code == 1) {
        // console.log("专科知情记录列表，新增记录列表",data)
        WRT_config.UpdateZqjlHtml=data
        console.log("更新知情记录列表，新增记录列表",WRT_config.UpdateZqjlHtml)
      }
    }
  })

  //病程记录/入院记录初始化
  WRT_e.api.blwslist.getBlwslist({
    params: {"as_blid":"1492768","as_zyid":"1492768","as_lx":"0","as_zkid":"88","as_zkdm":"80","as_baseurl":"http://localhost:8080/"},
    success(data) {
      if (data.Code == 1) {
        // console.log("初始化:",data)
        WRT_config.BrwsCsh=data.Result
        WRT_config.BrwsCshW=data
        console.log("初始化11:",data)
        console.log("初始化:",WRT_config.BrwsCsh)
      }
    }
  })
}
// 列表
// 左侧
var left_Menu = WRT_e.view.extend({
  render:function(){
    this.$el.html(`
    <div class="tab-content" id="left_list_nr">
      <div class="tab-pane active" id="sidebar1">
        <div class="list_title">病程记录列表</div>
        <div class="tab-pane">
          <ul class="list_inner">
            ${_.map(WRT_config.ZqjlBrWssByLx, (obj)=> `
            <li class="zcjlsubmenu" id="zcjlsubmenu${obj.ID}">
              <span>
                <a href="#panel${obj.ID}" class="btnlist${obj.ID}" id="type_title${obj.ID}" name="${obj.ID}">
                  <i class="glyphicon glyphicon-triangle-right"></i>
                  ${obj.GSMC}
                </a>
              </span>
            </li>`).join('')}
          </ul>
        </div>
      </div>
      <div class="tab-pane" id="sidebar2">
        <div class="list_title">新增记录(常用)</div>
        <div class="tab-pane">
          <ul class="list_inner">
            <li class="updatesubmenu" id="updatesubmenu">
            <span>
              <a href="#panel" class="btnlist" id="type_title">

              </a>
            </span>
            </li>
          </ul>
        </div>
      </div>
      <div class="tab-pane" id="sidebar3">
        <div class="input-group left_Search">
          <input type="text" class="form-control left_Search_input" id="left_Search_input" placeholder="请输入拼音或者中文检索">
          <span class="input-group-btn">
            <button class="btn btn-default glyphicon glyphicon-search" id="left_Search_btn" type="button"></button>
          </span>
        </div>
        <div class="tab-pane">
          <div class="searchsubmenu"></div>
        </div>
      </div>
    </div>
    `)
    return this
  },
  events:{
    "click .zcjlsubmenu":function(e){
      console.log(e)
      console.log(e.target.name)
      let json=WRT_config.ZqjlBrWssByLx.map(function(item){
        item.content_Control=1
        item.right_nrUrl = "as_blid="+item.BLID+"&as_gsdm="+item.GSDM+"&as_zyid="+WRT_config.BrwsCsh.BRXX.ZYID+"&as_wsid="+item.ID+"&as_wslx="+item.WSLX
        return item
      })
      console.log("item:",json)
      var a = json.find(ev => ev.ID==e.target.name)
      if(a.ID == e.target.name) {
        $('.panel-group').html(
          new right_Menu().init({
            data:{
              dataNR:WRT_config.ZqjlBrWssByLx,
              dataID:a.ID,
              dataUrl:a.right_nrUrl
            }
          }).render().$el
        )
      }
    },
    "click #left_Search_btn":function(){
      console.log("搜索")
      var input_val=  document.getElementById("left_Search_input").value
      console.log(input_val)
      if(input_val != ''){
        $('.searchsubmenu').html(
          new right_search_res().init({
            data:WRT_config.ZqjlByKey
          }).render().$el
        )
      } else {
        $('.searchsubmenu').html($(this).html(`
        <div class="tab-pane" id="sidebar3">
          <div class="input-group left_Search">
            <input type="text" class="form-control left_Search_input" id="left_Search_input" placeholder="请输入拼音或者中文检索">
            <span class="input-group-btn">
              <button class="btn btn-default glyphicon glyphicon-search" id="left_Search_btn" type="button"></button>
            </span>
          </div>
          <div class="searchsubmenu"></div>
        </div>`))
      }
    }
  }
})
// 列表
// 右侧内容（显示）
var right_Menu= WRT_e.view.extend({
  render:function(){
    let json=WRT_config.ZqjlBrWssByLx.map(function(item){
      // item.right_nrUrl = "as_blid="+item.BLID+"&as_gsdm="+item.GSDM+"&as_zyid="+item.BLID+"&as_wsid="+item.ID+"&as_wslx="+item.WSLX
      return item
    })
    console.log(json)
    console.log("aa",this.data.dataID)
    var a = json.find(ev => ev.ID == this.data.dataID)
    var b = json.find(ev => ev.right_nrUrl == this.data.dataUrl)
    console.log("11",b)
    this.$el.html(`
      <div class="panel" id="panel${this.data.dataID}" name="${this.data.dataID}">
        <div class="panel-title" data-toggle="collapse" data-parent="#accordion" href="#type" id="type_title${a.ID}">
          <span class="title${a.ID}">${a.GSMC}</span>
          <span><a class="zqsave" id="zqsave${a.ID}" name="${a.ID}">保存</a></span>
          <span><a class="zqdel" id="zqdel${a.ID}" name="${a.ID}">删除</a></span>
          <span><a class="zqdy" id="zqdyv" name="${a.ID}">打印</a></span>
        </div>
        <div id="type" class="panel-collapse collapse in">
          <iframe id="if_${a.ID}" name="${a.ID}" src="${WRT_config.server}/zyblws/blwsdetail.aspx?`+b.right_nrUrl+`" rameborder="0" width="100%" height="700px"></iframe>
          
        </div>
      </div>
    `)
    return this
  },
  events:{
    "click .zqsave":function(esave){
      console.log("保存")
      console.log(esave)
      console.log(esave.target.name)
      var childWindow = $("#if_"+esave.target.name)[0].contentWindow; 
      childWindow.ehr_save();
    },
    "click .zqdy":function(edy){
      console.log("dayin")
      console.log(edy)
      console.log(edy.target.name)
      let jsondy=WRT_config.ZqjlBrWssByLx.map(function(item){
        item.content_Control=1
        item.right_nrUrl = "as_blid="+item.BLID+"&as_gsdm="+item.GSDM+"&as_zyid="+item.BLID+"&as_wsid="+item.ID+"&as_wslx="+item.WSLX+"&tmpid=0.2328870294890346"
        return item
      })
      console.log("item:",jsondy)
      var a = jsondy.find(e => e.ID ==edy.target.name)
      console.log(a)
      // console.log(a.ID)
      if(a.ID == edy.target.name) {
        console.log("打印显示文件")
        $('.panel-group').html(
          new right_Menu_dy().init({
            data:{
              dataNR:WRT_config.ZqjlBrWssByLx,
              dataID:a.ID,
              dataUrl:a.right_nrUrl
            }
          }).render().$el
        )
      }
    },
    "click .zqdel":function(){
      console.log("shanchu")
    },
  }
})
// // 右侧内容（打印）
//   var right_Menu_dy= WRT_e.view.extend({
//     render:function(){
//       let json=WRT_config.ZqjlBrWssByLx.map(function(item){
//         item.right_nrUrl = "as_blid="+item.BLID+"&as_gsdm="+item.GSDM+"&as_zyid="+item.BLID+"&as_wsid="+item.ID+"&as_wslx="+item.WSLX+"&tmpid=0.497805055846396"
//         return item
//       })
//       // blwsdetail.aspx?as_blid=1492768&as_gsdm=0848&as_zyid=1492768&as_wsid=-249&as_wslx=2A&tmpid=0.6157812448515083
//       console.log(json)
//       console.log("aa",this.data.dataID)
//       var a = json.find(ev => ev.ID == this.data.dataID)
//       var b1 = json.find(ev1 => ev1.right_nrUrl)
//       console.log("bb",b1)
//       this.$el.html(`
//         <div class="panel" id="panel${this.data.dataID}" name="${this.data.dataID}">
//           <div class="panel-title" data-toggle="collapse" data-parent="#accordion" href="#type" id="type_title${a.ID}">
//             <span class="title${a.ID}">${a.GSMC}</span>
//             <span><a class="zqsave" id="zqsave${a.ID}" name="${a.ID}">保存</a></span>
//             <span><a class="zqdel" id="zqdel${a.ID}" name="${a.ID}">删除</a></span>
//             <span><a class="zqdy" id="zqdyv" name="${a.ID}">打印</a></span>
//           </div>
//           <div id="type" class="panel-collapse collapse in">
//             <iframe id="if_${a.ID}" src="${WRT_config.server}/zyblws/blwsdetail.aspx?`+b1.right_nrUrl+`" rameborder="0" width="100%" height="700px"></iframe>
//           </div>
//         </div>
//       `)
//       return this
//     },
//     events:{
//       "click .zqsave":function(){
//         // var j_datas = DATAOP_IMG.split(',')
//         // let url = WRT_config.server + `/blwsdetail.aspx?as_blid=" + j_datas[4] + "&as_gsdm=" + j_datas[2] + "&as_zyid=" + j_datas[5] + "&as_wsid=" + j_wsid + "&as_wslx=" + j_datas[3] + "&as_tmpid=" + j_datas[6] + "&tmpid=" + Math.random()`
//         // page_iframe.add("保存",url)
//       },
//       "click .zqdy":function(edy){
//         console.log("打印kuang")
//         $('#type').collapse('show') 
//         WRT_e.ui.message({
//           title: '提示',
//           content: `打印出错`,
//           onOk() {
//           }
//         })
//       },
//       "click .zqdel":function(){
//         console.log("shanchu")
//       },
//     }
//   })
// 默认头
var right_title= WRT_e.view.extend({
  render:function(){
    this.$el.html(`
    <div class="panel" id="panel">
      <div class="panel-title" data-toggle="collapse" data-parent="#accordion" href="#type" id="type_title">
        <span>文书内容</span>
        <span class="zqsave1"><a class="zqsave1" id="zqsave" name="">保存</a></span>
        <span class="zqdel1"><a class="zqdel1" id="zqdel" name="">删除</a></span>
        <span class="zqdy1" ><a class="zqdy1" id="zqdyv" name="">打印</a></span>
      </div>
      <div id="type" class="panel-collapse collapse in">
      <div class="blzqnr"></div>
      </div>
    </div>`)
    return this
  },
  event:{
  "click .zqsave":function(){
    WRT_e.ui.message({
      title: '提示',
      content: `无保存功能`,
      onOk() {
      }
    })
  },
  "click .zqdel":function(){
    WRT_e.ui.message({
      title: '提示',
      content: `无删除功能`,
      onOk() {
      }
    })
  },
  "click .zqdy":function(){
    WRT_e.ui.message({
      title: '提示',
      content: `无打印功能`,
      onOk() {
      }
    })
  }
  }
})

// // 搜索
// var right_search_res= WRT_e.view.extend({
//   render:function(){
//     this.$el.html(`
//     <ul class="search_res">
//       ${_.map(WRT_config.ZqjlByKey, (obj)=> `
//         <li class="search_res_nr" id="search_res_nr${obj.DM}" name="${obj.DM}">
//         <span>
//           <a href="#panel${obj.DM}" class="btnlist${obj.DM}" id="type_title${obj.DM}" name="${obj.DM}">
//             <i class="glyphicon glyphicon-triangle-right"></i>
//             ${obj.MC}
//           </a>
//         </span>
//         </li>
//       `).join(``)}
//     </ul>`)
//     return this
//   },
//   events:{
//     "click .search_res_nr":function(e1){
//       console.log("搜索内容点击")
//       console.log(e1)
//       console.log(e1.target.name)
//       let json=WRT_config.ZqjlByKey.map(function(item){
//         // item.content_Control=1
//         item.right_sreach_nrUrl = "as_blid="+WRT_config.BrwsCsh.BRXX.BLID+"&as_gsdm="+item.DM+"&as_zyid="+WRT_config.BrwsCsh.BRXX.ZYID+"&as_wsid=0&as_wslx="+item.WSLX+"&tmpid=0.4132779541427096"
//         return item
//       })
//       console.log("item:",json)
//       var a = json.find(ev => ev.DM==e1.target.name)
//       // var b1 = json.find(ev1 => ev1.right_nrUrl)
//       var url = json.find(evurl => evurl.right_sreach_nrUrl)
//       console.log(url.right_sreach_nrUrl)
//       $('.panel-group').html(
//         new right_Menu_Search().init({
//           data:{
//             dataRes:WRT_config.ZqjlByKey,
//             dataResID:a.DM,
//             dataParam:WRT_config.ZqjlBrWssByLx,
//           }
//         }).render().$el
//       )
//     }
//   }
// })
// // 搜索显示
// var right_Menu_Search = WRT_e.view.extend({
//   render:function(){
//     let json=WRT_config.ZqjlByKey.map(function(item){
//       // item.content_Control=1
//       item.right_sreach_nrUrl = "as_blid="+WRT_config.BrwsCsh.BRXX.BLID+"&as_gsdm="+item.DM+"&as_zyid="+WRT_config.BrwsCsh.BRXX.ZYID+"&as_wsid=0&as_wslx="+item.WSLX+"&tmpid=0.4132779541427096"
//       return item
//     })
//     console.log("item:",json)
//     console.log(this.data.dataResID)
//     var a = json.find(ev => ev.DM==this.data.dataResID)
//     var url = json.find(evurl => evurl.right_sreach_nrUrl)
//     console.log(a)
//     this.$el.html(`
//       <div class="panel" id="panel${a.DM}" name="${a.DM}">
//         <div class="panel-title" data-toggle="collapse" data-parent="#accordion" href="#type" id="type_title${a.ID}">
//         <span class="title${a.DM}">${a.MC}</span>
//         </div>
//         <div id="type" class="panel-collapse collapse in">
//           <iframe id="if_${a.DM}" name="${a.DM}" src="${WRT_config.server}/zyblws/blwsdetail.aspx?`+a.right_sreach_nrUrl+`" rameborder="0" width="100%" height="700px"></iframe>
//           <div class="right_three_btn">
//             <button class="btn_s1" id="zqsave" name="${a.DM}" type="button">保存</button>
//             <button class="btn_s1" id="zqdel" name="${a.DM}" type="button">删除</button>
//             <button class="btn_s1" id="zqdy" name="${a.DM}" type="button">打印</button>
//           </div>
//         </div>
//       </div>
//     `)
//     return this
//   },
//   events:{
//     "click #zqsave":function(){
//       // var j_datas = DATAOP_IMG.split(',')
//       // let url = WRT_config.server + `/blwsdetail.aspx?as_blid=" + j_datas[4] + "&as_gsdm=" + j_datas[2] + "&as_zyid=" + j_datas[5] + "&as_wsid=" + j_wsid + "&as_wslx=" + j_datas[3] + "&as_tmpid=" + j_datas[6] + "&tmpid=" + Math.random()`
//       // page_iframe.add("保存",url)
//     },
//     "click #zqdy":function(edy){
//       console.log("打印kuang")
//       // if(save){
//       //   WRT_e.ui.message({
//       //     title: '提示',
//       //     content: `打印出错`,
//       //     onOk() {
//       //     }
//       //   })
//       // } else {
//       //   WRT_e.ui.message({
//       //     title: '提示',
//       //     content: `请先保存`,
//       //     onOk() {
//       //     }
//       //   })
//       // }
//       console.log("ssdayin")
//       console.log(edy)
//       console.log(edy.target.name)
//       let jsondy=WRT_config.ZqjlBrWssByLx.map(function(item){
//         item.content_Control=1
//         item.right_sreach_nrUrl = "as_blid="+WRT_config.BrwsCsh.BRXX.BLID+"&as_gsdm="+item.DM+"&as_zyid="+WRT_config.BrwsCsh.BRXX.ZYID+"&as_wsid=0&as_wslx="+item.WSLX+"&tmpid=0.4132779541427096"
//         return item
//       })
//       console.log("item:",jsondy)
//       var a = jsondy.find(e => e.GSDM)
//       console.log(a.GSDM)
//       // console.log(a.ID)
//       if(a.GSDM == edy.target.name) {
//         $('#type').collapse('show')
//         console.log("打印显示文件")
//         if(save){
//           WRT_e.ui.message({
//             title: '提示',
//             content: `打印出错`,
//             onOk() {
//             }
//           })
//         } else {
//           WRT_e.ui.message({
//             title: '提示',
//             content: `请先保存`,
//             onOk() {
//             }
//           })
//         }
//         $('.panel-group').html(
//           new right_searchMenu_dy().init({
//             data:{
//               dataNR:WRT_config.ZqjlBrWssByLx,
//               dataID:a.DM,
//               dataUrl:a.right_nrUrl
//             }
//           }).render().$el
//         )
//       }
//     },
//     "click #zqdel":function(){
//       console.log("shanchu")
//     },
//   }
// })
// // 搜索打印内容
// var right_searchMenu_dy= WRT_e.view.extend({
//   render:function(){
//     this.$el.html(`
//     `)
//     return this
//   }
// })






































//统一页面启动
$(document).ready(
  function () {
    // try2
    one_case.init()
  }
)
// try2
var one_case = {
  init:function(){
    //获取我我的病人数据
    // WRT_e.api.ehrSz.getMyPatByLX({
    //   params: {
    //     as_lx: '2'
    //   },
    //   success(data) {
    //     if (data.Code == 1) {
    //       $("#patient_list").html(
    //         new brList_View().init({
    //           data: JSON.parse(data.Result)['BRXX']
    //         }).render().$el
    //       )
    //     }
    //   }
    // })
    var data1 = 111
    $("#main2").append(new brList_View().init({data:brLists[1].data}).render().$el);
    $("#main2_1").append(new brList_View1().init({data:brLists[1].data}).render().$el)
    $("#main3").append(new brPortal_View().init({data:portal}).render().$el)
    $("#main5").append(new massage_View().init({data:brLists[1].data}).render().$el)
    // $("#main5").append(new massage_View().init({data:brLists[1].data}).render().$el)
  }
}
// 1个病人
var brList_View = WRT_e.view.extend({
  render:function () {
    // console.log(brLists[1].data[0].brtitle_lm)
    let html =`
    <div class="item">
      <div class="item_box">
        <div class="tag flex-row justify-content-between">
          <div>
            <span class="e-tag e-tag-blue">
            ${brLists[1].data[0].brtitle_lm}
            </span>
            <span class="e-tag e-tag-red">
              893天
            </span>
            <span class="e-tag e-tag-black">
              6
            </span>
          </div>
          <div>
            <span class="e-tag e-tag-orange">
            ${brLists[1].data[0].brtitle_rm}
            </span>
          </div>
        </div>
        <div class="flex-row align-items-center">
          <img src="../images/man.png" width="40" alt="">
          <div class="flex-fill user_text">
            <strong>${brLists[1].data[0].brno}</strong><br />
            <b>${brLists[1].data[0].brname}</b>&nbsp;&nbsp;男&nbsp;&nbsp;63岁
          </div>
        </div>
        <div class="status"></div>
        <div class="message">${brLists[1].data[0].brzd}</div>
      </div>
    </div>`
    this.$el.html(`<div class="main2">${html}</div>`)
    return this
  }
})
// 多个
var brList_View1 = WRT_e.view.extend({
  render:function () {
    this.$el.html(`
    <div class="main2">
      ${_.map(this.data, (obj)=> 
        //循环病人列表
        `<div class="item">
          <div class="item_box">
            <div class="tag flex-row justify-content-between">
              <div>
                <span class="e-tag e-tag-blue">
                ${obj.brtitle_lm}
                </span>
                <span class="e-tag e-tag-red">
                  893天
                </span>
                <span class="e-tag e-tag-black">
                  6
                </span>
              </div>
              <div>
                <span class="e-tag e-tag-orange">
                ${obj.brtitle_rm}
                </span>
              </div>
            </div>
            <div class="flex-row align-items-center">
              <img src="../images/man.png" width="40" alt="">
              <div class="flex-fill user_text">
                <strong>${obj.brno}</strong><br/>
                <b>${obj.brname}</b>&nbsp;&nbsp;男&nbsp;&nbsp;63岁
              </div>
            </div>
            <div class="status"></div>
            <div class="message">${obj.brzd}</div>
          </div>
        </div>`)}
    </div>`)
    return this
  }
})
// main3,扩展一个新的视图类
var brPortal_View = WRT_e.view.extend({
  render:function(){
    this.$el.html(`
    <div class="subject_log">
      <div class="panel-case" id="accordion">
        <div class="panel">
          <div class="panel-heading">
            <a class="flex-row align-items-center justify-content-between panel-title" data-toggle="collapse" data-parent="#accordion" href="#panel1">
              <span>
                <i class="glyphicon glyphicon-list-alt"></i>
                <span class="panel-title-text">专科日志</span>
              </span>
              <i class="glyphicon glyphicon-chevron-down"></i>
            </a>
          </div>
          <div id="panel1" class="collapse-all collapse">
            <div class="panel-body">
              <div class="row ">
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">在院</div>
                    <div class="box_value">${this.data.ZYRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">新入院</div>
                    <div class="box_value">${this.data.XRYRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">出院</div>
                    <div class="box_value">${this.data.CYRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">转入</div>
                    <div class="box_value">${this.data.ZRRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">转出</div>
                    <div class="box_value">${this.data.ZCRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">病危病人</div>
                    <div class="box_value">${this.data.WZBRRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">特级护理</div>
                    <div class="box_value">${this.data.TJHLRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">一级护理</div>
                    <div class="box_value">${this.data.YJHLRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">二级护理</div>
                    <div class="box_value">${this.data.EJHLRS}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>`)
    return this
  }
})
// 侧边
var massage_View = WRT_e.view.extend({
  render:function () {
    this.$el.html(`
    <div class="subject_log">
      <div class="panel-case" id="accordion">
        <div class="panel">
          <div class="panel-heading">
            <a class="flex-row align-items-center justify-content-between panel-title" data-toggle="collapse" data-parent="#accordion" href="#panel3">
              <span>
                <i class="glyphicon glyphicon-list-alt"></i>
                <span class="panel-title-text">病人信息</span>
              </span>
              <i class="glyphicon glyphicon-chevron-down"></i>
            </a>
          </div>
          <div id="panel3" class="collapse-all in collapse">
            <div class="panel-body">
              <div class="row">
                <div class="tag flex-row justify-content-between line1">
                  <div>
                    <span class="e-tag e-tag-blue">${brLists[1].data[0].brtitle_lm}</span>
                    <span class="e-tag e-tag-red">893天</span>
                    <span class="e-tag e-tag-black">6</span>
                  </div>
                  <div>
                    <span class="e-tag e-tag-orange">${brLists[1].data[0].brtitle_rm}</span>
                  </div>
                </div>
                <div class="line1">
                  <span class="e-tag brname">${brLists[1].data[0].brname}</span>
                  <span class="e-tag brno">${brLists[1].data[0].brno}</span>
                  <span class="e-tag brsex">男</span>
                  <span class="e-tag brage">62岁</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>`)
    return this
  }
})