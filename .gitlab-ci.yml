variables:
  RUNNER_TAG: web-emr #cicd标签
  WEB_NAME: web-emr # 项目名称
  WEB_ROUTE: test
  IMAGE_VERSION: $CI_COMMIT_TAG # 正式版本号
  IMAGE_VERSION_TEST: v1.0.4 # 测试版本号，可以不更新
  IMAGE_VERSION_BAK: v1.1.8 # 预发布的版本号
  IMAGE_ADDRESS: ************/k8s/${WEB_NAME} # 镜像地址
  NVM_DIR: /home/<USER>/nvm/nvm-0.39.1 # NVM 目录
  NODE_VERSION: v16.17.0 # Node.js 版本
  REGISTRY: http://************:4873 # NPM registry 地址

before_script:
  - docker login -u $DOCKER_USER -p $DOCKER_PWD $DOCKER_ADDRESS
  - source $NVM_DIR/nvm.sh
  - nvm use $NODE_VERSION

stages:
  - codeCheck
  - install
  - build
  - deploy
  - notify

cache:
  paths:
    - node_modules

codeCheck:
  stage: codeCheck
  tags:
    - $RUNNER_TAG
  only:
    - pre
  script:
    - sonar-scanner -Dsonar.projectKey=${WEB_NAME} -Dsonar.sources=. -Dsonar.host.url=http://************:9000 -Dsonar.login=****************************************

install:
  stage: install
  tags:
    - $RUNNER_TAG
  only:
    - master
    - pre
  script:
    - npm config set //************:4873/:_authToken +RBZBRjAneWEZJCZCqhz4w==
    - npm set registry $REGISTRY
    - pnpm i

build-test:
  variables:
    DEPLOY_STATUS: 等待手动确认发布!
    WEB_IMAGE: $IMAGE_ADDRESS:$IMAGE_VERSION_TEST.test
  stage: build
  needs: [ install ]
  tags:
    - $RUNNER_TAG
  only:
    - master
  script:
    - echo ${WEB_IMAGE}
    - npm run build:test
    - docker build -t ${WEB_IMAGE} --build-arg WEB_ROUTE=${WEB_ROUTE} .
    - docker push ${WEB_IMAGE}
    - docker rmi --force $(docker images | grep $WEB_NAME | awk '{print $3}')

deploy-test:
  variables:
    NAMESPACE: wfw-test2
    WEB_IMAGE: $IMAGE_ADDRESS:$IMAGE_VERSION_TEST.test
    DEPLOY_STATUS: 部署成功
  stage: deploy
  needs: [ build-test ]
  tags:
    - $RUNNER_TAG
  only:
    - master
  script:
    - echo ${WEB_IMAGE}
    - kubectl config use-context k8s3-back
    - envsubst < web-template-deploy.yml | kubectl delete -f - --ignore-not-found
    - envsubst < web-template-deploy.yml | kubectl create -f -
    - kubectl config use-context context-test
    - envsubst < web-template-deploy.yml | kubectl delete -f - --ignore-not-found
    - envsubst < web-template-deploy.yml | kubectl create -f -
    - 'curl -H "Content-type: application/json" -d "{\"msgtype\":\"text\", \"text\": {\"content\":\"${NOTIC_CONTENT}\"}}" $DING_WEBHOOK'

build-bak:
  variables:
    WEB_IMAGE: $IMAGE_ADDRESS:$IMAGE_VERSION_BAK.bak
  stage: build
  needs: [ install ]
  tags:
    - $RUNNER_TAG
  only:
    - pre
  script:
    - echo ${WEB_IMAGE}
    - npm run build:pre
    - docker build -t ${WEB_IMAGE} --build-arg WEB_ROUTE=${WEB_ROUTE} .
    - docker push ${WEB_IMAGE}
    - docker rmi --force `docker images | grep $WEB_NAME | awk '{print $3}'`

deploy-bak:
  variables:
    NAMESPACE: wfw-prod2
    WEB_IMAGE: $IMAGE_ADDRESS:$IMAGE_VERSION_BAK.bak
    DEPLOY_STATUS: 部署成功
  stage: deploy
  needs: [ build-bak ]
  tags:
    - $RUNNER_TAG
  only:
    - pre
  script:
    - echo ${WEB_IMAGE}
    - kubectl config use-context context-prod-c2
    - envsubst < web-template-deploy.yml | kubectl delete -f - --ignore-not-found
    - envsubst < web-template-deploy.yml | kubectl create -f -
    - 'curl -H ''Content-type: application/json'' -d ''{"msgtype":"text", "text":
      {"content":"''${NOTIC_CONTENT}''"}}'' $DING_WEBHOOK'

build-prod:
  variables:
    DEPLOY_STATUS: 等待手动确认发布!
    WEB_IMAGE: $IMAGE_ADDRESS:$IMAGE_VERSION.prod
  stage: build
  tags:
    - $RUNNER_TAG
  only:
    - tags
  script:
    - echo ${WEB_IMAGE}
    - npm run build:prod
    - docker build -t ${WEB_IMAGE} --build-arg WEB_ROUTE=${WEB_ROUTE} .
    - docker push ${WEB_IMAGE}
    - docker rmi --force $(docker images | grep $WEB_NAME | awk '{print $3}')

deploy-prod:
  variables:
    NAMESPACE: wfw-prod2
    WEB_IMAGE: $IMAGE_ADDRESS:$IMAGE_VERSION.prod
    DEPLOY_STATUS: 部署成功
  stage: deploy
  needs: [ build-prod ]
  tags:
    - $RUNNER_TAG
  only:
    - tags
  script:
    - echo ${WEB_IMAGE}
    - kubectl config view
    - kubectl config use-context k8s-2024
    - envsubst < web-template-deploy.yml | kubectl apply -f -
    - kubectl config use-context k8s-2024
    - envsubst < web-template-deploy.yml | kubectl apply -f -
    - 'curl -H "Content-type: application/json" -d "{\"msgtype\":\"text\", \"text\": {\"content\":\"${NOTIC_CONTENT}\"}}" $DING_WEBHOOK'
  when: manual

notify-error:
  variables:
    DEPLOY_STATUS: 部署失败！！！- -。
  stage: notify
  tags:
    - $RUNNER_TAG
  script:
    - 'curl -H "Content-type: application/json" -d "{\"msgtype\":\"text\", \"text\": {\"content\":\"${NOTIC_CONTENT}\"}}" $DING_WEBHOOK'
  when: on_failure
