<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>按病历内容查询</title>
  <meta name="description" content="按病历内容查询">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <link rel="stylesheet" href="css/e-common.css">
  <!-- 页面CSS -->
  <link rel="stylesheet" href="css/e-style.css">
</head>
<body>
  <table border="2" cellpadding="100px" bordercolor="#B7C3DA" class="table_content">
    <tr class="row_headings">
      <th class="keyword">查询关键字</th>
      <th class="equalion">等式</th>
      <th class="queries">查询内容值</th>
      <th class="remmark">备注</th>
    </tr>
    <tr class="column_tag">
      <td>
        <select id="sel" onChange="chg(this.value)">
          <option selected>=选择类型=</option>
          <option value="1">input_box</option>
          <option value="2">input_field</option>
        </select>
      </td>
      <td>
        
      </td>
      <td class="threeColum">
        <textarea type="text" name="input_box" id="input_box1" style="display:none"></textarea>
        <input type="text" name="input_field" id="input_field" style="display:none">
      </td>
      <td>and</td>
    </tr>
  </table>
  <button class="tj" type="submit" @click="tj()">提交</button>
<!-- <textarea type="text" name="input_box" id="input_box1" style="display:none"></textarea>
<input type="text" name="input_field" value="input_field1" id="input_field" style="display:none"> -->
</body>
<script>
  function chg(m){
  if (m!=''){
  if(m=="1"){
  document.getElementById("input_box1").style.display="";
  document.getElementById("input_field").style.display="none";
  }else if(m=="2"){
  document.getElementById("input_box1").style.display="none";
  document.getElementById("input_field").style.display="";
  }
  }else{
  document.getElementById("input_box1").style.display="none";
  document.getElementById("input_field").style.display="none";
  }
  }
  var init=function () { 
    var t1=document.getElementById("input_box1"); 
    t1.oninput=function () { 
      console.log(t1.value);
    }; 
    // IE 
    t1.onpropertychange=function () {  
      console.log(t1.value); 
    }; 
  }; 
  // window.onload=init; 
  </script>
</html>
<!--
   <input
type="button"
value="显示"
id="b1"/>
  <!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <title>Title</title>
  </head>
  <body>
    <input type="text" id="aaa"> 
  <script> 
    var input = document.getElementById('aaa'); 
    var timer = null;
    input.onkeyup = function() { 
      clearTimeout(timer); 
      timer = setTimeout(function() {  
        console.log(input.value); 
      }, 500); 
    }
  </script>
  </body>
</html>
//当前表单
  var obj = document.getElementById("please_choose"); 
  var val = obj.options[obj.selectedIndex].value
  // textarea选框的值
  // if ((val =="YD") || (val =="TB")) {
  //   var a =document.getElementById("input_box").value;
  //   console.log(a);
  //   if ((val =="YD") || (val =="TB")) {
  //     var b =document.getElementById("input_box1").value; 
  //     console.log(b);
  //     if ((val =="YD") || (val =="TB")) {
  //       var c =document.getElementById("input_box2").value; 
  //       console.log(c);
  //       if ((val =="YD") || (val =="TB")) {
  //         var d =document.getElementById("input_box3").value; 
  //         console.log(d);
  //       } 
  //     } 
  //   } 
  // } 
  // else {
  //   if ((val =="ER") || (val =="E") || (val =="SB")) {
  //     var a1 =document.getElementById("input_field").value; 
  //     console.log(a1);
  //     if ((val =="ER") || (val =="E") || (val =="SB")) {
  //       var b1 =document.getElementById("input_field1").value; 
  //       console.log(b1);
  //       if ((val =="ER") || (val =="E") || (val =="SB")) {
  //         var c1 =document.getElementById("input_field2").value; 
  //         console.log(c1);
  //         if ((val =="ER") || (val =="E") || (val =="SB")) {
  //           var d1 =document.getElementById("input_field3").value; 
  //           console.log(d1);
  //         }
  //       }
  //     }
  //   } else {

  //   }
  // }

  <div>
          <h4>4.摸索</h4>
          <div class="attpempt" id="main5">
            <div class="subject_log">
              <div class="panel-case" id="accordion">
                <div class="panel">
                  <div class="panel-heading">
                    <a class="flex-row align-items-center justify-content-between panel-title1" data-toggle="collapse" data-parent="#accordion" href="#panel3">
                      <span>
                        <i class="glyphicon glyphicon-list-alt"></i>
                        <span class="panel-title-text">病人信息</span>
                      </span>
                      <i class="glyphicon glyphicon-chevron-down icon-right"></i>
                    </a>
                  </div>
                  <div id="panel3" class="collapse-all in collapse">
                    <div class="panel-body2">
                      xx
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>


        

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
 <head>
  <meta http-equiv="content-type" content="text/html;charset=utf-8">
  <title> 获取表格内容 </title>
 </head>
 <body>
  <table id="mytable">
   <tr>
      <td>id</td>
      <td>菜品分类：</td>
      <td>name</td>
    
    </tr>
    <tr>
       <td>1</td>
       <td><input type="text" id="box"></td>
      
    </tr>
    <tr>
     <td>2</td>
      <td><textarea type="text" id="box"></textarea></td>
    </tr>
   </table>
 
   <p><input type="button" name="btn" value="获取表格数据" onclick="showTableContent('mytable')"></p>
   <p><div id="result"></div></p>
 </body>
 <script type="text/javascript">
    * 遍历表格内容返回数组
    * @param  Int   id 表格id
    * @return Array
 
   function getTableContent(id){
       var mytable = document.getElementById(id);
       var data = [];
       for(var i=0,rows=mytable.rows.length; i<rows; i++){
           for(var j=0,cells=mytable.rows[i].cells.length; j<cells; j++){
               if(!data[i]){
                   data[i] = new Array();
               }
               data[i][j] = mytable.rows[i].cells[j].value;
           }
       }
       return data;
   }
    * 显示表格内容
    * @param  Int   id 表格id
   function showTableContent(id){
       var data = getTableContent(id);
       var tmp = '';
       for(i=0,rows=data.length; i<rows; i++){
           for(j=0,cells=data[i].length; j<cells; j++){
               tmp += data[i][j] + ',';
           }
           tmp += '<br>';
       }
       document.getElementById('result').innerHTML = tmp;
   }
 </script>
  <style type="text/css">
   table{width:300px; border:1px solid #000000; border-collapse:collapse;}
   td{border:1px solid #000000; border-collapse:collapse;}
   </style>
 </html>

 <!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <title>点击文字弹出一个DIV层窗口代码</title>
    <meta charset="urf-8"/>
    <style>
    .black_overlay{
      display: none;
      position: absolute;
      top: 0%;
      left: 0%;
      width: 100%;
      height: 100%;
      background-color: black;
      z-index:1001;
      -moz-opacity: 0.8;
      opacity:.80;
      filter: alpha(opacity=88);
    }
    .white_content {
      display: none;
      position: absolute;
      top: 25%;
      left: 25%;
      width: 55%;
      height: 55%;
      padding: 20px;
      border: 10px solid orange;
      background-color: white;
      z-index:1002;
      overflow: auto;
    }
  </style>
  </head>
  <body>
    <p>示例弹出层：
      <button  onclick = "document.getElementById('light').style.display='block';document.getElementById('fade').style.display='block'">
        请点这里
      </button>
    </p>
    <div id="light" class="white_content">这是一个层窗口示例程序. <button  onclick = "document.getElementById('light').style.display='none';document.getElementById('fade').style.display='none'">点这里关闭本窗口</button></div>
    <div id="fade" class="black_overlay"></div>
  </body>
</html>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head>
  <script src="http://apps.bdimg.com/libs/jquery/2.1.4/jquery.min.js" type="text/javascript"></script>
  <script>
    function addtab(){
     var tcount=$("#tab tr").length;
     var tpl='<tr id="s'+tcount+'"><td>'+tcount+'</td><td>姓名</td><td>年龄</td><td onclick="deltab('+tcount+')">删除</td></tr>';
     $("#tab").append(tpl);
    }
    function deltab(x){
    $("#s"+x).remove();
    }
  </script>
  </head>  
<body>
  <div>
    <button onclick="addtab()">增加</button>
    <table id="tab" border="1">
      <tr>
      <td>ID</td><td>姓名</td><td>年龄</td><td>操作</td>
      </tr>
    </table>
  </div>
</body>
</html>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head>
</head>  
<body>
  <div>
    <table border='1'cellspacing="10" cellpadding="10" > <tr> <td width="200">1</td> <td width="200">2</td> <td width="200">3</td> </tr> <tr> <td>a</td> <td>b</td> <td>c</td> </tr> <tr> <td>中国</td> <td>我</td> <td>爱你</td> </tr> </table>










    <table border="1">
      <thead>
        <tr>
          <td>开户行</td>
          <td>账号</td>
          <td>转账</td>
          <td>用户</td>
        </tr>
      </thead>
      <tbody id="myTable">
        <tr>
          <td>银行</td>
          <td>1234567989874569874</td>
          <td>100</td>
          <td>小王</td>
        </tr>
        <tr>
          <td>银行</td>
          <td>1234567989874569874</td>
          <td>700</td>
          <td>小林</td>
        </tr>
      </tbody>
   </table>
   <button onclick="search()">查看</button>
  </div>
</body>

  <script>
    function search() {
      var  banktrs=document.getElementById("myTable").rows;
      var bankinfos = [];//新建一个数组
      for(var i=0;i<banktrs.length;i++){
        var tds=banktrs[i].cells;
        var bankinfo={};
        bankinfo.bank=tds[0].innerHTML;
        bankinfo.account=tds[1].innerHTML;
        bankinfo.much=tds[2].innerHTML;
        bankinfo.people=tds[3].innerHTML;
        bankinfos.push(bankinfo);
      }
      alert(JSON.stringify(bankinfos))
      return JSON.stringify(bankinfos)
    }
    // var  banktrs=document.getElementById("myTable").rows;
    // var bankinfos = [];//新建一个数组
    // for(var i=0;i<banktrs.length;i++){
    //   var tds=banktrs[i].cells;
    //   var bankinfo={};
    //   bankinfo.bank=tds[0].innerHTML;
    //   bankinfo.account=tds[1].innerHTML;
    //     bankinfos.push(bankinfo);
    // }
    // alert(JSON.stringify(bankinfos));
  </script>
  <script src="../js/e-common.js"></script>
  <script src='./lib/jquery/jquery.easyui_ehrsz.min.js'></script>
</html>







if (obj.options[obj.selectedIndex].value != '') {
  // blnrinfo.DM=obj.options[obj.selectedIndex].value; // 查询关键字
  // blnrinfo.TJ=obj4.options[obj1.selectedIndex].value; // 等式
  // blnrinfo.JG=document.getElementById("input_box").value; // 输入内容
  if (obj1.options[obj.selectedIndex].value != '') {
    blnrinfo.DM=obj.options[obj.selectedIndex].value; // 查询关键字
    blnrinfo.TJ=obj4.options[obj4.selectedIndex].value; // 等式
    blnrinfo.JG=document.getElementById("input_box").value; // 输入内容
    blnrinfo.DM1=obj1.options[obj1.selectedIndex].value; // 查询关键字
    blnrinfo.TJ2=obj4.options[obj4.selectedIndex].value; // 等式
    blnrinfo.JG3=document.getElementById("input_box1").value; // 输入内容
    console.log(13)
  }
}else {
  // if (obj1.options[obj.selectedIndex].value != '') {
  //   blnrinfo.DM=obj.options[obj.selectedIndex].value; // 查询关键字
  //   blnrinfo.TJ=obj4.options[obj1.selectedIndex].value; // 等式
  //   blnrinfo.JG=document.getElementById("input_box").value; // 输入内容
  //   blnrinfo.DM=obj.options[obj.selectedIndex].value; // 查询关键字
  //   blnrinfo.TJ=obj4.options[obj1.selectedIndex].value; // 等式
  //   blnrinfo.JG=document.getElementById("input_box").value; // 输入内容
  // }
  console.log(123)
}
  // blnrinfo.DM=obj.options[obj.selectedIndex].value; // 查询关键字
  // blnrinfo.TJ=obj4.options[obj1.selectedIndex].value; // 等式
  // blnrinfo.JG=document.getElementById("input_box").value; // 输入内容

  // blnrinfo.DM=obj1.options[obj.selectedIndex].value; // 查询关键字
  // blnrinfo.TJ=obj5.options[obj1.selectedIndex].value; // 等式
  // blnrinfo.JG=document.getElementById("input_box1").value; // 输入内容

  // blnrinfo.DM=obj2.options[obj.selectedIndex].value; // 查询关键字
  // blnrinfo.TJ=obj6.options[obj1.selectedIndex].value; // 等式
  // blnrinfo.JG=document.getElementById("input_box2").value; // 输入内容

  // blnrinfo.DM=obj3.options[obj.selectedIndex].value; // 查询关键字
  // blnrinfo.TJ=obj7.options[obj1.selectedIndex].value; // 等式
  // blnrinfo.JG=document.getElementById("input_box3").value; // 输入内容
  // blnrinfo.ZKDM=document.getElementById("departmentList").value;

// blnrinfo.DM=obj.options[obj.selectedIndex].value; // 查询关键字
// blnrinfo.TJ=obj4.options[obj1.selectedIndex].value; // 等式
// blnrinfo.JG=document.getElementById("input_box").value; // 输入内容
// blnrinfo.ZKDM=document.getElementById("departmentList").value;
// Tablenr.push(blnrinfo);
-->