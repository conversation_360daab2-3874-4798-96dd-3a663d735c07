﻿// 全局配置
var WRT_config = {
    server: "http://localhost/ehr",
    // server: "http://************/ehr",
    server2:'http://**************:38081',
    server4:'http://**************:15015',
    nwserver:"http://localhost/ehr/login.aspx",
    serveWorker:'http://**************:38081',
    runMode: "dist", // dist 正式环境， debug 开发环境 
    mockType: "0", //  0 不模拟， 1 模拟 
    au: {},//用户信息
    init: {},//基本信息
    trialuser:[],
    // trialuser:["WHX","TWX"],//权限组
    CheckHySqd:[],// 获取所有需要弹出申请单的化验项目
    hmzkZW:true, // 判断是否为专网 用于操作惠每质控
    gzyqType:true
};

// 全局运行环境
var WRT_e = {};