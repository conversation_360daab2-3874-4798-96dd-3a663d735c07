import request from '@/utils/request'

// 删除生长发育曲线记录
export function deleteGrowthDevelopmentRecord(data) {
  return request({
    url: '/app-emrservice/v1/TemperatureList/deleteGrowthDevelopmentRecord',
    method: 'delete',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据记录ID查询生长发育曲线记录
export function getShiJiangetGrowthDevelopmentRecordByIDFWByBlid(data) {
  return request({
    url: '/app-emrservice/v1/TemperatureList/getGrowthDevelopmentRecordByID',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据病历ID查询生长发育曲线列表
export function getGrowthDevelopmentRecordListByBingLiID(data) {
  return request({
    url: '/app-emrservice/v1/TemperatureList/getGrowthDevelopmentRecordListByBingLiID',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据病人编号获取最近一次住院的最新的身高体重
export function getHeightByBingRenBH(params) {
  return request({
    url: '/app-emrservice/v1/TemperatureList/getHeightByBingRenBH',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据病例ID获取更基础信息
export function getPatientBasic(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getPatientBasic',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 查询一个BingLiID的体温单时间范围
export function getShiJianFWByBlid(params) {
  return request({
    url: '/app-emrservice/v1/TemperatureList/getShiJianFWByBlid',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 查询一个时间范围的一个BingLiID的体温单数据
export function getTemperatureListVo(data) {
  return request({
    url: '/app-emrservice/v1/TemperatureList/getTemperatureListVo',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 新增生长发育曲线记录
export function insertGrowthDevelopmentRecord(data) {
  return request({
    url: '/app-emrservice/v1/TemperatureList/insertGrowthDevelopmentRecord',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 更新生长发育曲线记录
export function updateGrowthDevelopmentRecord(data) {
  return request({
    url: '/app-emrservice/v1/TemperatureList/updateGrowthDevelopmentRecord',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
