<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>新营养医嘱</title>
  <meta name="description" content="新营养医嘱">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
  #nutriOrderList, .nutriOrderListTitle, .nutriOrderListTableAll {
    text-align: center;
    margin: auto;
  }
  .nOrderListMain {
    padding-top: 50px;
  }
  .nutriOrderListTitle {
    font-family: 'Microsoft YaHei';
    width: 98%;
    /* height: 60px;
    line-height: 60px;
    padding: 0 20px; */
    padding: 10px 20px;
    /* border: 1px solid #B7C3DA; */
    border: 1px solid #E6E6E6;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    vertical-align: middle;
  }
  /* 基础信息 */
  .nolTBinfoR {
    /* display: flex;
    flex-wrap: nowrap;
    align-items: center; */
    /* width: 55%; */
    /* display: contents; */
    width: 90%;
    display: flex;
    align-items: center;
    text-align: left;
  }
  .nolTBinfoR > span {
    /* padding-right: 15px; */
    padding-right: 10px;
  }
  .tBinfoBold {
    font-weight: bolder;
    /* max-width: 355px; */
  }
  .tBinfoBcNum {
    font-family: 'Microsoft YaHei';
    font-style: normal;
    font-weight: 400;
    /* line-height: 22px; */
    color: #215FEB;
    text-align: center;
    background: #C4D2F2;
    border-radius: 4px;

  }
  .nolTBtipL {
    color: red;
  }
  /* 新增 + 表格*/
  .nutriOrderListTableAll {
    width: 98%;
    padding: 18px 22px;  
    margin-top: 10px;
    background: #F5F8FD;
    border-radius: 8px;
  }
  /* 新增 */
  .nutriOrderListTableTit {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .nutriOrderListTableTit>span {
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
  }
  .btn_save {
    height: 32px;
    line-height: 32px;
    padding: 0 25px;
    border: 1px solid  #489CFF;
    box-sizing: border-box;
    border-radius: 4px;
    background:  #489CFF;
    color: #fff;
    /* color:  #489CFF;
    background: transparent; */
    cursor: pointer;
    transition: 0.2s;
  }
  .btn_save:hover {
    color:  #489CFF;
    background: transparent;
    /* background:  #489CFF;
    color: #fff; */
  }
  /* 表格 */
  .nutriOrderListTable {
    padding-top: 10px;
    background: transparent;
  }
  .nol_newTable {
    /* border: 1px solid #B7C3DA; */
  }
  .nol_newTable .table-title {
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    /* color: #fff; */
    padding-left: 40px;
  }
  .nol_newTable .table-nr {
    height: 40px;
    line-height: 40px;
    /* border: 1px solid #B7C3DA; */
    padding-left: 40px;
    /* border: 1px #a5a5a5 solid; */
  }
  .nol_newTable .table-nr a {
    cursor: pointer;
  }
  #table_data {
    border-bottom: 1px solid #EBEBEB;
  }
  /* 新增按钮打开————弹窗页面 */
  #tableFr_List {
    width: 100%;
    height: 600px;
  }
</style>
<body id="nutriOrderList">
  <div class="nOrderListMain">
    <!-- 基本信息 -->
    <div class="nutriOrderListTitle"></div>
    <!-- 新增 + 表格 -->
    <div class="nutriOrderListTableAll">
      <!-- 新增 -->
      <div class="nutriOrderListTableTit">
        <span>记录列表</span>
        <span>
          <button class="btn_save" onclick="addNewNutritionOrder()">新增</button>
        </span>
      </div>
      <!-- 表格 -->
      <div class="nutriOrderListTable">
        <!-- border="1"  align="center" bordercolor="#A3BEFF"cellpadding="5px" cellspacing="1px"  -->
        <table  align="start" class="tb_title">
          <tbody>
            <tr>
              <td>
                <div class="nol_newTable">
  
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面JS -->
  <!-- <script src='js/e-EhrSz.js'></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-nutriOrderList.js'><\/script>");
  </script>
</body>
</html>