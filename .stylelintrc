{"extends": ["stylelint-config-standard", "stylelint-config-recommended-scss", "stylelint-config-recommended-vue/scss", "stylelint-config-html/vue", "stylelint-config-recess-order"], "overrides": [{"files": ["**/*.{vue,html}"], "customSyntax": "postcss-html"}, {"files": ["**/*.{css,scss}"], "customSyntax": "postcss-scss"}], "rules": {"selector-class-pattern": null, "import-notation": "string", "at-rule-no-unknown": null, "scss/at-rule-no-unknown": true, "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["v-deep"]}], "scss/at-import-partial-extension": "never"}, "ignoreFiles": ["node_modules/**", ".git/**", "dist/**"]}