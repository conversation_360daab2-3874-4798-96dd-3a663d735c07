<template>
  <div class="container">
    <div class="content">
      <div class="title">日间手术维护</div>
      <div class="table">
        <el-table max-height="648" border :data="tableData" style="width: fit-content">
          <el-table-column prop="xuHao" width="80" label="序号"></el-table-column>
          <el-table-column prop="ID" width="80" label="ID"></el-table-column>
          <el-table-column prop="ICD" width="160" label="ICD"></el-table-column>
          <el-table-column prop="mingCheng" width="160" label="名称"></el-table-column>
          <el-table-column prop="guoJiaGBBM" width="120" label="国家贯标编码"></el-table-column>
          <el-table-column
            prop="guoJiaGBBMMC"
            width="500"
            label="国家贯标编码名称"
          ></el-table-column>
          <el-table-column width="80" label="状态" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.zhuangTai == 0">启用</el-tag>
              <el-tag v-else type="danger">停用</el-tag>
            </template>
          </el-table-column>
          <el-table-column width="80" label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" @click="onStopUsing(scope.row)">停用</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <div slot="title" class="dialog-title">
        <i class="el-icon-warning"></i>
        <span>停用提示</span>
      </div>
      <div class="dialog-content">
        确认停用
        <span>【{{ selectRow.mingCheng }}】</span>
        吗？
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onSubmit">确 认</el-button>
        <el-button @click="onCancel">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tableData: [
        {
          xuHao: '1',
          ID: '1',
          ICD: '21.8400x006',
          mingCheng: '歪鼻鼻成形术',
          guoJiaGBBM: 'RM95020',
          guoJiaGBBMMC:
            '鼻的后天性变形行歪鼻鼻成形术/弯鼻鼻成形术/驼峰鼻矫正术/增补性鼻成形术/局限性鼻成形术',
          zhuangTai: '0'
        },
        {
          xuHao: '2',
          ID: '1',
          ICD: '21.8400x006',
          mingCheng: 'test',
          guoJiaGBBM: 'RM95020',
          guoJiaGBBMMC:
            '鼻的后天性变形行歪鼻鼻成形术/弯鼻鼻成形术/驼峰鼻矫正术/增补性鼻成形术/局限性鼻成形术',
          zhuangTai: '1'
        }
      ],
      dialogVisible: false,
      selectRow: {}
    }
  },
  methods: {
    onStopUsing(row) {
      this.dialogVisible = true
      this.selectRow = row
    },
    handleClose() {
      this.dialogVisible = false
    },
    onSubmit() {
      this.selectRow.zhuangTai = 1
      this.dialogVisible = false
    },
    onCancel() {
      this.dialogVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 16px;
  .content {
    background-color: #eff3fb;
    padding: 10px 20px;
    min-height: 780px;
    .title {
      color: #171c28;
      font-size: 14px;
      position: relative;
      margin-left: 10px;
    }
    .title::before {
      display: block;
      content: '';
      width: 3px;
      height: 14px;
      background-color: #356ac5;
      position: absolute;
      left: -9px;
      top: 3px;
    }
    .table {
      margin-top: 30px;
    }
  }
}
.dialog-title {
  display: flex;
  align-items: center;
  i {
    color: #ed6a0c;
    margin-right: 6px;
    font-size: 20px;
  }
  span {
    font-weight: 600;
    font-size: 16px;
  }
}
.dialog-content {
  margin-left: 38px;
  font-size: 15px;
  span {
    color: #356ac5;
  }
}
::v-deep .el-dialog__footer {
  margin-top: 10px;
  border: none;
}
</style>
