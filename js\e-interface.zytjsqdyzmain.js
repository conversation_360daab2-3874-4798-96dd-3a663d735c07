WRT_e.api = WRT_e.api || {}

WRT_e.api.zytjsqdyzmain = {
  //初始化
  getInit: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: bqxz
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyyz/zytjsqdyzmain.aspx/e_init',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //删除历史申请单
  deleteSqd: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: bqxz
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyyz/zytjsqdyzmain.aspx/e_delete',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取申请单明细数据
  getZytjsqdtx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: bqxz
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyyz/zytjsqdtx.aspx/e_init',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
   //保存医嘱
   getsaveYz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: bqxz
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyyz/zytjsqdtx.aspx/e_saveYz',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取门诊末次数据
  getMzmcsj: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: bqxz
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyyz/zytjsqdtx.aspx/e_mzmcsj',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
}