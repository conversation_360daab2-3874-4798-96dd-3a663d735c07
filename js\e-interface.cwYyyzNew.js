WRT_e.api = WRT_e.api || {}

WRT_e.api.cwYyyzNew = { 
  // ehr/xypyz/nutritionOrder.aspx  
  // 备注：
  // 身高体重可修改，初始化函数如果无法获取，则由医师自行输入后，计算BEE等指数
  // 没有保存权限，则无法新增，历史查看为只读



  /** 初始化 获取患者营养医嘱单  e_Init POST
   * al_blid	Long
   * al_yzdid	Long	医嘱单id，可以为0, 不为0则身高体重，NRS之类的信息从e_GetYyyzd中获取
   * (返回结果：返回值  jsonstring )
   */ 
  getInit: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + `/xypyz/nutritionOrder.aspx/e_Init`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  /**  判断用户是否有保存权限   e_GetYhBcQx   POST
   * al_yhid	Long	当前的用户id  
   * (返回结果说明： 1 有权限 0无权限)
   */
  getYhBcQx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + `/xypyz/nutritionOrder.aspx/e_GetYhBcQx`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  /**  判断用户是否有提交权限   e_GetYhTjQx   POST
   * al_yhid	Long	当前的用户id
   * al_yzdid	Long	医嘱单id
   * (返回结果说明： 1 有权限 其他无权限_直接提示返回结果)
   */
  getYhTjQx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + `/xypyz/nutritionOrder.aspx/e_GetYhTjQx`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  /**  获取患者营养医嘱单	e_GetYyyzdList   POST
   * al_blid	Long
   * (返回结果：患者所有的营养医嘱单json<表格一数组>)
   */
   getYyyzdList: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      // ?al_blid=${o.params.al_blid}
    } else {
      $.ajax({
        url: WRT_config.server + `/xypyz/nutritionOrder.aspx/e_GetYyyzdList`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            console.log(error);
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  /**  停止营养医嘱单	e_StopYyyzd  POST
   * al_zh	Long	药品组号，医嘱单中获取
   * al_yzdid	Long	医嘱单id
   * al_yzyhid	Long	停止医嘱医师id（当前用户）
   * (返回结果： string      “1”停止成功   “0”停止失败)
   */
  stopYyyzd: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + `/xypyz/nutritionOrder.aspx/e_StopYyyzd`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          // console.log(JSON.parse(error.responseText),'123',error);
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          } else {
            WRT_e.ui.hint({
              type: 'error',
              msg: JSON.parse(error.responseText).Message
            })
          }
        }
      })
    }
  },
 /** 获取单条营养医嘱单	e_GetYyyzd   POST
  * al_yzdid	Long	医嘱单id
  * (返回结果：返回值  jsonstring    单条营养医嘱单json（表格一）)
  */ 
  getYyyzd: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + `/xypyz/nutritionOrder.aspx/e_GetYyyzd`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  /**  获取单条营养医嘱单药品	e_GetYyyzd_yp   POST
   * al_yzdid	Long	医嘱单id
   * al_blid	Long	患者病历id
   *  (返回结果：返回值  jsonstring   获取营养医嘱单药品json  （上方表格二）)
   */
  getYyyzd_yp: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + `/xypyz/nutritionOrder.aspx/e_GetYyyzd_yp`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  /** 获取检验结果	e_GetHyjg  POST
   *  al_yzdid	Long	医嘱单id    传0则获取患者最近的检验结果    传yzdid获取医嘱单已保存的检验结果
   * al_blid	Long	患者病历id
   * as_zyh	String	住院号
   * (返回结果：返回值  jsonstring   获取营养医嘱单药品json  （上方表格二）)
   */
  getHyjg: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + `/xypyz/nutritionOrder.aspx/e_GetHyjg`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  /**  计算患者BMI	e_GetBMI   POST
   * ad_sg	Double	身高
   * ad_tz	Double	体重
   * 返回值  string 
   */
  getBMI: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + `/xypyz/nutritionOrder.aspx/e_GetBMI`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  /**  函数名:获取患者营养医嘱单	e_GetBEE  POST
   * as_xb	String	性别 1男 2女
   * ad_sg	Double	身高
   * ad_tz	Double	体重
   * ad_nl	Double	年龄 
   *  返回值  string 
   */
  getBEE: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + `/xypyz/nutritionOrder.aspx/e_GetBEE`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  /**  获取所有营养药品	e_GetYyypList       POST
   * as_yfdm	string	药房代码
   * (返回值  jsonstring    所有的营养药品，形成页面的列表（表格四）)
   */
  getYyypList: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + `/xypyz/nutritionOrder.aspx/e_GetYyypList`,
        // ?as_yfdm=${o.params.as_yfdm}`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  /**   函数名:计算营养物质	e_CalcYywz   POST
   *   as_ypsllist	String	格式为ypid1-sl^ypid2-sl^ypid3-sl…………
   * 返回值  jsonstring    患者营养医嘱单json  （表格一）
   */
  CalcYywz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + `/xypyz/nutritionOrder.aspx/e_CalcYywz`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  /**  提交营养医嘱单	e_Submit  POST
   * al_yzdid	Long	医嘱单id
   * al_zyid	Long	住院id
   * 返回值  string     1 提交成功   
   * 成功提示：药品已成功提交,如该病区已启用医嘱确认,请及时审核医嘱!
   * 失败提示：直接提示返回值
   */
   yyyzSubmit: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + `/xypyz/nutritionOrder.aspx/e_Submit`,
        // ?al_yzdid=${o.params.al_yzdid}`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  /**  保存营养医嘱单	e_Save    POST
   * 参数
   * am_yyyzd	jsonobject	医嘱单（表格一）新增id传0，不要传空
   * ast_yplist	jsonobject	医嘱单中选择的药品列表（表格二）
   * ast_hyjglist	jsonobject	医嘱单中的检验结果列表（表格三）
   * as_yfdm	String	药房代码
   * 结果
   * 返回值  string     1 保存成功  0 失败
   * 成功提示：数据保存成功!
   * 失败提示：系统出现未知错误,请联系管理员
   */
  yyyzSave: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + `/xypyz/nutritionOrder.aspx/e_Save`,
        // ?am_yyyzd=${o.params.am_yyyzd}&ast_yplist=${o.params.ast_yplist}&ast_hyjglist=${o.params.ast_hyjglist}&as_yfdm=${o.params.as_yfdm}`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
}