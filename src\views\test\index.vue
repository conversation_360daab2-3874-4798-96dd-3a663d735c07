<script setup>
import { reactive, ref } from 'vue'
import TopBottomLayout from '@/layout/components/TopBottom/index.vue'
import NormalTable from '@/components/NormalTable/index.vue'
import { CONFIG } from '@/views/test/config'
import { getUserInfo } from '@/api/user'
import { getYiLiaoJGDM, getYongHuID } from '@/utils/auth'

const visible = ref(true)
const formData = reactive({
  a: 0
})

const handleCancel = () => {
  console.log('cancel')
}
const loading = ref(false)
const handleSubmit = () => {
  console.log('submit', formData)
  getUserInfo({ yongHuID: getYongHuID(), yiLiaoJGDM: getYiLiaoJGDM() })
  getUserInfo({ yongHuID: getYongHuID(), yiLiaoJGDM: getYiLiaoJGDM() })
  getUserInfo({ yongHuID: getYongHuID(), yiLiaoJGDM: getYiLiaoJGDM() })
  getUserInfo({ yongHuID: getYongHuID(), yiLiaoJGDM: getYiLiaoJGDM() })
  getUserInfo({ yongHuID: getYongHuID(), yiLiaoJGDM: getYiLiaoJGDM() })

  // return new Promise((resolve, reject) => {
  //   setTimeout(() => {
  //     console.log('resolve', true)
  //     resolve(true)
  //   }, 3000)
  // })
}

const addNumber = () => {
  formData.a += 1
}
</script>

<template>
  <!--  <div>-->
  <top-bottom-layout title="测试标题12321" fulfill>
    <template #top>
      <div style="background: white; border: 1px solid black">
        <el-form inline>
          <el-form-item label="请输入：">
            <el-input></el-input>
          </el-form-item>
          <el-form-item label="请输入：">
            <el-input></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="handleSubmit">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #extraSearch>
      <div style="background: white; border: 1px solid black; width: 100%; height: 100%">
        extraSearch
      </div>
    </template>
    <template #add>
      <div
        style="background: white; border: 1px solid black; width: 100%; height: 100%"
        @click="visible = true"
      >
        add
      </div>
    </template>
    <template #extraFilter>
      <div style="background: white; border: 1px solid black; width: 100%; height: 100%">
        extraFilter
      </div>
    </template>
    <template #bottomTitleExtra>
      <div style="background: white; border: 1px solid black; width: 100%; height: 100%">add</div>
    </template>
    <template #bottomContent>
      <normal-table
        :data="[
          { lieMing: '1', id: '1' },
          {
            lieMing: '2',
            id: '2',
            children: [
              { lieMing: '3', id: '3' },
              { lieMing: '4', id: '4' },
              { lieMing: '5', id: '5' }
            ]
          },
          { lieMing: '6', id: '6' },
          { lieMing: '7', id: '7' },
          { lieMing: '8', id: '8' },
          { lieMing: '9', id: '9' }
        ]"
        :table-header="CONFIG.tableHeader"
        :config="CONFIG.tableConfig"
        :loading="loading"
      ></normal-table>
    </template>
  </top-bottom-layout>
  <!--    <left-right-layout style="margin-top: 20px">-->
  <!--      <template #left>左边</template>-->
  <!--      <template #right>右边</template>-->
  <!--    </left-right-layout>-->
  <!--    <drawer-box-->
  <!--      :visible.sync="visible"-->
  <!--      :target-data="formData"-->
  <!--      :target-data-callback="handleSubmit"-->
  <!--      @cancel="handleCancel"-->
  <!--      @submit="handleSubmit"-->
  <!--    >-->
  <!--      <template #extra>-->
  <!--        <el-button>x</el-button>-->
  <!--      </template>-->
  <!--      <template #content>-->
  <!--        <normal-card title="测试"></normal-card>-->
  <!--        <normal-card title="测试"></normal-card>-->
  <!--        <normal-card title="测试"></normal-card>-->
  <!--      </template>-->
  <!--    </drawer-box>-->
  <!--  </div>-->
</template>
<style lang="scss" scoped></style>
