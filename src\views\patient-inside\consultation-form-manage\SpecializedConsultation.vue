<template>
  <div class="clinical-pharmacy-view">
    <el-container>
      <el-aside class="left-aside" width="420px !important">
        <div style="background-color: #eff3fb; height: 100%; padding: 10px">
          <el-descriptions
            class="information"
            title="病人会诊单列表"
            :column="3"
            size="medium"
            border
          >
            <template slot="extra">
              <el-select v-model="huiZhenMC" placeholder="请选择">
                <el-option
                  v-for="item in huiZhenList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-button type="primary" size="mini">新增</el-button>
            </template>
          </el-descriptions>
          <el-table :data="huiZhenTable" border style="width: 100%; margin-top: 20px">
            <el-table-column prop="huiZhenSJ" label="会诊时间" width="180"></el-table-column>
            <el-table-column prop="huiZhenMC" label="会诊名称"></el-table-column>
          </el-table>
        </div>
      </el-aside>
      <el-main class="right-main">
        <div style="background-color: #eff3fb; height: 100%">
          <div class="box">
            <div style="height: 6%">
              <el-descriptions class="information" title="会诊单" :column="3" size="medium" border>
                <template slot="extra">
                  <el-button type="primary" size="mini">保存</el-button>
                  <el-button type="primary" size="mini">重置</el-button>
                  <el-button type="info" plain disabled size="mini">发送短信通知</el-button>
                </template>
              </el-descriptions>
            </div>
            <div class="content-box">
              <div class="content">
                <div>
                  <el-descriptions
                    class="patient-basic-information information"
                    title="病人基本信息"
                    :column="3"
                    size="medium"
                    border
                  >
                    <el-descriptions-item>
                      <template slot="label">姓名:</template>
                      周福道
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">性别:</template>
                      男
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">出生日期:</template>
                      1990-09-22
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">病区:</template>
                      423病区
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">床位:</template>
                      001
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">病案号:</template>
                      0003498036
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
                <div>
                  <el-descriptions
                    class="Applicant-information information"
                    title="申请人信息"
                    :column="3"
                    size="medium"
                    border
                  >
                    <el-descriptions-item>
                      <template slot="label">申请医师:</template>
                      王华晓
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">联系电话:</template>
                      660504
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">申请专科:</template>
                      风湿免疫科
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
                <div>
                  <el-descriptions
                    class="Application-information information"
                    title="申请信息"
                    :column="3"
                    size="medium"
                    border
                  >
                    <el-descriptions-item>
                      <template slot="label">
                        <i class="el-icon-question" style="color: rgb(53, 106, 197)"></i>
                        会诊选择:
                      </template>
                      <el-radio v-model="linchuangYXHZ" label="1">急诊</el-radio>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">会诊专科:</template>
                      A-临床药学实验室
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">邀请会诊医生:</template>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="patient-label"
                      content-class-name="patient-content"
                      span="4"
                    >
                      <template slot="label">
                        <div>患者病情及诊疗经过:</div>
                        <div class="underline-content">获取检查数据</div>
                      </template>
                      <el-input
                        v-model="huanZheBQJZLJG"
                        type="textarea"
                        :autosize="{ minRows: 3, maxRows: 15 }"
                        placeholder="请输入内容"
                        maxlength="1000"
                        show-word-limit
                        :rows="10"
                      ></el-input>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="patient-label"
                      content-class-name="patient-content"
                      span="4"
                    >
                      <template slot="label">
                        <div>初步诊断:</div>
                      </template>
                      <el-input
                        v-model="chuBuZhenDuan"
                        type="textarea"
                        :autosize="{ minRows: 3, maxRows: 15 }"
                        placeholder="请输入内容"
                        maxlength="1000"
                        show-word-limit
                        :rows="10"
                      ></el-input>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="patient-label"
                      content-class-name="patient-content"
                      span="4"
                    >
                      <template slot="label">
                        <div>申请临床药学会诊:</div>
                        <div>使用理由</div>
                        <div class="underline-content">获取检查数据</div>
                        <div class="underline-content">历史诊疗</div>
                      </template>
                      <el-input
                        v-model="changWaiYYZJ"
                        type="textarea"
                        :autosize="{ minRows: 3, maxRows: 15 }"
                        placeholder="请输入内容"
                        maxlength="1000"
                        show-word-limit
                        :rows="10"
                      ></el-input>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="patient-label"
                      content-class-name="patient-content"
                      span="4"
                    >
                      <template slot="label">
                        <div>短信通知:</div>
                      </template>
                      <el-input
                        v-model="duanXinTZ"
                        type="textarea"
                        :autosize="{ minRows: 3, maxRows: 15 }"
                        placeholder="请输入内容"
                        maxlength="1000"
                        show-word-limit
                        :rows="10"
                      ></el-input>
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
              <div class="tips">
                <i class="el-icon-warning" style="color: rgb(53, 106, 197)"></i>
                注：从营养角度会诊饮食合理性，会诊建议仅作为饮食参考，请结合临床实际。
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import { getAddConsulationInitData } from '@/api/consultation-form-manage'
export default {
  data() {
    return {
      linchuangYXHZ: '1',
      huanZheBQJZLJG: '',
      chuBuZhenDuan: '',
      changWaiYYZJ: '',
      duanXinTZ: '',
      huiZhenList: [
        {
          label: '专科会诊',
          value: '1'
        }
      ],
      huiZhenMC: '专科会诊',
      huiZhenTable: [
        {
          huiZhenSJ: '2016-05-02',
          huiZhenMC: '王小虎'
        }
      ]
    }
  },
  async mounted() {
    console.log(this.$store.state)
    await getAddConsulationInitData({
      zhuanKeID: 0
    })
  }
}
</script>
<style lang="scss" scoped>
.clinical-pharmacy-view {
  background-color: #fff;
  height: 100%;
  ::v-deep .el-descriptions__title {
    font-size: 13px;
  }
  ::v-deep .el-descriptions__title::before {
    font-weight: 600;
    border-left: 4px solid #356ac5;
    display: inline;
  }
  .title {
    font-weight: 600;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    text-align: left;
  }
  .left-aside {
    padding: 10px;
    ::v-deep .el-input__inner {
      height: 26px !important;
      width: 180px !important;
    }
    ::v-deep .el-table .el-table__cell {
      padding: 6px 0;
      font-size: 12px;
    }
  }
  .right-main {
    padding: 10px 10px 10px 0;
    .box {
      // border: 1px solid #dcdfe6;
      padding: 10px 10px 0 10px;
      height: 94%;

      .content::-webkit-scrollbar {
        display: none;
      }
      .content-box {
        border: 1px solid #dcdfe6;
        border-radius: 6px;
        padding: 10px 10px 0 10px;
        height: 100%;
        ::v-deep .content {
          overflow: scroll;
          height: 95%;
          td,
          th {
            border-collapse: collapse;
            border: 1px solid #dcdfe6;
            padding: 8px !important;
            font-size: 12px;
          }
          th {
            text-align: right;
            color: #000;
            background-color: #eaf0f9;
            width: 12%;
          }
          td {
            background-color: #f5f8fc;
            width: 18%;
          }
          .information {
            margin-bottom: 8px;
          }
          .Application-information {
            margin-bottom: 0px;
            .patient-label {
              vertical-align: top;
              .underline-content {
                text-decoration: underline;
                text-underline-offset: 2px;
                color: #155bd4;
                cursor: pointer;
              }
            }
            .patient-content {
              padding: 5px 6px !important;
            }
          }
          .right-aside {
            width: 252px !important;
          }
        }
        .tips {
          display: flex;
          align-items: center;
          height: 5%;
          font-size: 12px;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
