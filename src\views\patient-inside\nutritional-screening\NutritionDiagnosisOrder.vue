<template>
  <div class="temperature-sheet-view">营养风险筛查</div>
</template>

<script>
import { format, parseISO, addDays, startOfWeek, endOfWeek, differenceInDays } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {}
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
    console.log(this.patientDetail)
  },
  methods: {
    // 页面初始化
    async init() {}
  }
}
</script>

<style lang="scss" scoped>
.temperature-sheet-view {
  background-color: #fff;
  padding: 8px;
  height: 100%;
}
</style>
