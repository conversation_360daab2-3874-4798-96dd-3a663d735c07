<template>
  <div class="container">
    <div class="header">
      <div class="flex">
        <div class="item-title">开单日期:</div>
        <el-date-picker
          v-model="kaiDanRQ"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
        <div class="item-title">住院号:</div>
        <el-input v-model="zhuYuanHao"></el-input>
        <el-button>查询</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">医疗证明查询</div>
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData" style="width: fit-content">
          <el-table-column prop="zhuYuanHao" label="住院号" width="119"></el-table-column>
          <el-table-column prop="xingMing" label="姓名" width="128"></el-table-column>
          <el-table-column prop="zhuanKe" label="专科" width="188"></el-table-column>
          <el-table-column prop="xingBie" label="性别" width="88"></el-table-column>
          <el-table-column prop="kaiDanYS" label="开单医生" width="128"></el-table-column>
          <el-table-column prop="kaiDanSJ" label="开单时间" width="204"></el-table-column>
          <el-table-column label="状态" align="center" width="75">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.zhuangTai == 0" type="success">正常</el-tag>
              <el-tag v-else-if="scope.row.zhuangTai == 1">锁定</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="66">
            <template slot-scope="scope">
              <el-button v-if="scope.row.caoZuo == 1">解锁</el-button>
              <el-button v-else-if="scope.row.caoZuo == 0">锁定</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { medicalCertificateInit } from '@/api/system-maintenance'
export default {
  data() {
    return {
      tableData: [
        {
          zhuYuanHao: '1575668',
          xingMing: '张龙珠',
          zhuanKe: '血液内科',
          xingBie: '男',
          kaiDanYS: '张倩莺',
          kaiDanSJ: '2024-03-02 08:23:01',
          zhuangTai: '0',
          caoZuo: '0'
        },
        {
          zhuYuanHao: '1575668',
          xingMing: '张龙珠',
          zhuanKe: '血液内科',
          xingBie: '男',
          kaiDanYS: '张倩莺',
          kaiDanSJ: '2024-03-02 08:23:01',
          zhuangTai: '1',
          caoZuo: '1'
        },
        {
          zhuYuanHao: '1575668',
          xingMing: '张龙珠',
          zhuanKe: '血液内科',
          xingBie: '男',
          kaiDanYS: '张倩莺',
          kaiDanSJ: '2024-03-02 08:23:01',
          zhuangTai: '1',
          caoZuo: '1'
        }
      ],
      kaiDanRQ: '',
      zhuYuanHao: ''
    }
  },
  async mounted() {
    await medicalCertificateInit()
  },
  methods: {
    handleClick(row) {
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-table th.gutter {
  display: none;
  width: 0;
}
.container {
  background-color: #fff;
  padding: 6px 80px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    // padding: 9px 16px;
    color: #fff;
  }
  .flex {
    display: flex;
    align-items: center;
    .item-title {
      flex-shrink: 0;
    }
  }
  .flex > * {
    margin-right: 10px;
  }
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
  min-height: 700px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
