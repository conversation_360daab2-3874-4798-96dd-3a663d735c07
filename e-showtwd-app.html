<!DOCTYPE html>
<html lang="en" style="overflow-y: hidden;">

<head>
  <meta charset="utf-8">
  <title>体温单</title>
  <meta name="description" content="体温单">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
  <style>
    .twd_data {
      /* width: 1400px; */
      /* height: 2040px; */
      /* padding: 30px 80px 60px; */
      /* zoom: 0.65; */
      /* zoom: 0.45; */
      color: #000;
    }

    .twd_data_info {
      zoom: 1.94;
    }

    .twd_data_table .e_table {
      width: 1248px;
      height: 1828px;
      border-collapse: collapse;
      border-spacing: 0;
      table-layout: fixed;
      font-size: 20px;
      line-height: 1;

    }

    .twd_data_table .e_table table {
      border-collapse: collapse;
      border-spacing: 0;
      table-layout: fixed;
      width: 100%;
    }

    .twd_data_table .e_table>tbody>tr {
      height: 46px;
      overflow: hidden;
    }

    .twd_data_table .e_table>tbody>tr>td {
      border: 2px solid #000;
      text-align: center;
      padding: 0;
      position: relative;
    }

    .twd_data_table .e_table>tbody>tr>td:first-child {
      font-size: 24px;
    }

    .twd_data_table .e_table dl.table_flex {
      display: flex;
      height: 100%;
      margin: 0;
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      left: 0;
    }

    .twd_data_table .e_table dl.table_flex.table_colnum {
      flex-flow: column;
    }

    .twd_data_table .e_table dl.table_flex>dd {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      border-left: 2px solid rgba(0, 0, 0, 0.3);
    }

    .twd_data_table .e_table dl.table_flex>dd.ssd {
      overflow: inherit;
    }

    .twd_data_table .e_table dl.table_flex.table_colnum>dd {
      border-top: 2px solid rgba(0, 0, 0, 0.3);
      border-left: none;
    }

    .twd_data_table .e_table dl.table_flex>dd:first-child,
    .twd_data_table .e_table dl.table_flex.table_colnum>dd:first-child {
      border: none;
    }

    .twd_data_table .e_table dl.table_flex.table_colnum>dd:nth-child(5n-2) {
      border-color: #000;
    }

    .twd_data_table .e_table dl.table_flex.table_colnum>dd:nth-child(28),
    .twd_data_table .e_table dl.table_flex.table_colnum>dd:nth-child(43) {
      border-color: #D81E06;
    }

    .twd_data_table .e_table .mb,
    .twd_data_table .e_table .ssd {
      display: flex;
      flex-flow: column;
      line-height: 1;
      justify-content: flex-start !important;
    }

    .twd_data_table .e_table .mb>span {
      margin-bottom: 81px;
    }

    .twd_data_table .e_table .mb>span:first-child {
      margin-top: 10px;
      margin-bottom: 81px;
    }

    .twd_data_table .e_table .mb>span:nth-last-child(2) {
      margin-bottom: 69px;
    }

    .twd_data_table .e_table .ssd>span {
      margin-bottom: 81px;
    }
    .twd_data_table .e_table .ssd>.second {
      margin-bottom: 105px;
    }
    .twd_data_table .e_table .ssd>.three {
      margin-bottom: 95px;
    }
    .twd_data_table .e_table .ssd>.four {
      margin-bottom: 90px;
    }

    .twd_data_table .e_table .ssd>span:first-child {
      margin-top: 10px;
      margin-bottom: 81px;
    }

    .twd_data_table .e_table .ssd>span:nth-last-child(2) {
      margin-bottom: 215px;
    }

    .twd_data_page {
      text-align: center;
      font-size: 24px;
    }

    .twd_data_page>span {
      display: inline-block;
      width: 70px;
    }
  </style>
</head>

<body>
  <div class="container-twd">
    <div id="twd_table" style="zoom: 1.0;"></div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 登录页js文件 -->
  <script>
    var params = {} //url带参
    //统一页面启动
    $(document).ready(() => {
      for (let item of window.location.href.split('?')[1].split('&')) {
        params[item.split('=')[0]] = item.split('=')[1]
      }
      //获取体温单表格
      getTwd()
      var u = navigator.userAgent;
      var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
      var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
      if(isAndroid){
        $('#twd_table').css('zoom',0.5)
      }else{
        $('#twd_table').css('zoom',0.9)
      }
    })
    var tableData = [] //表格数据结构
    var listDate = [] //获取住院数组

    //体温单详情
    var twdData_View = WRT_e.view.extend({
      render: function () {
        //修改数据结构
        changeTemperature(this.data.list)
        // tableData.HX=[[{"type":"up","val":34},{"type":"down","val":32},{"type":"up","val":20},{"type":"down","val":36},"",{"type":"up","val":32},null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,{"type":"down","val":30}],[{"type":"up","val":32},{"type":"down","val":26},{"type":"up","val":33},{"type":"down","val":22},{"type":"up","val":21},{"type":"down","val":22}],[{"type":"up","val":30},{"type":"down","val":29},{"type":"up","val":20},"",{"type":"down","val":27},{"type":"up","val":26}],[{"type":"down","val":25},{"type":"up","val":23},{"type":"up","val":31},"",{"type":"down","val":35},{"type":"up","val":28}],[{"type":"down","val":19},{"type":"up","val":34},"","","",""],["","","","","",""],["","","","","",""]]
        this.$el.html(`
        <div class="twd_data">
          <div class="twd_data_table">
            <table class="e_table">
              <tbody>
                <tr>
                  <td>日期</td>
                  ${_.map(tableData.CLSJ,e=>`<td>${e}</td>`).join('')}
                </tr>
                <tr>
                  <td>住院天数</td>
                  ${_.map(tableData.XYTS,e=>`<td>${e}</td>`).join('')}
                </tr>
                <tr>
                  <td>手术后日期</td>
                  ${_.map(tableData.SSTS,e=>`<td>${e}</td>`).join('')}
                </tr>
                <tr>
                  <td>时间</td>
                  ${_.map(tableData.CLSJ,(e,i,arr)=>`<td ${(i+1)==arr.length?"":"style='border-right: 2px solid #D81E06'"}><dl class="table_flex"><dd>2</dd><dd>6</dd><dd>10</dd><dd>14</dd><dd>18</dd><dd>22</dd></dl></td>`).join('')}
                </tr>
                <tr style="height: 100%;">
                  <td>
                    <dl class="table_flex">
                      <dd class="mb"><span>脉搏<br/>(次/分)</span><span>200</span><span>180</span><span>160</span><span>140</span><span>120</span><span>100</span><span>80</span><span>60</span><span>40</span></dd>
                      <dd class="ssd">
                        <span>摄氏度<br/>42</span>
                        <span class="second">41</span>
                        <span class="three">40</span>
                        <span class="four">39</span>
                        <span class="four">38</span>
                        <span class="four">37</span>
                        <span class="four">36</span>
                        <span class="four">35</span>
                        <span class="flex-row justify-content-center line-height: 0.95;"><span>疼<br/>痛<br/>评<br/>分</span><span style="font-size:20px;">10<br/>8<br/>6<br/>4<br/>2<br/>0</span></span></dd>
                    </dl>
                  </td>
                  ${_.map(['','','','','','',''],(e,i,arr)=>`<td ${(i+1)==arr.length?"":"style='border-right: 2px solid #D81E06'"}><dl class="table_flex"><dd>${i==0?'<canvas id="cavsTable" style="position:absolute;left:-0.5px;top:-0.5px;z-index: 9;"></canvas>':''}</dd><dd></dd><dd></dd><dd></dd><dd></dd><dd></dd></dl><dl class="table_flex table_colnum">${_.map(['','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','',''],e=>`<dd></dd>`).join('')}</dl></td>`).join('')}
                </tr>
                <tr style="height: 4%;">
                  <td>呼吸(次/分)</td>
                  ${_.map(tableData.HX,(e,i,arr)=>`<td ${(i+1)==arr.length?"":"style='border-right: 2px solid #D81E06'"}>
                    <dl class="table_flex" style="color:#D81E06;">
                      ${_.map(e,(e_2)=>`<dd class="
                      ${e_2&&e_2.type=='up'?'align-items-start':'align-items-end'}" style="font-size:15px;">${e_2?e_2.val||'':''}</dd>`).join('')}</dl></td>`).join('')}
                </tr>
                <tr style="height: 45px;">
                  <td>血压(mmHg)</td>
                  ${_.map(tableData.SYXY,(e,i,arr)=>`<td ${(i+1)==arr.length?"":"style='border-right: 2px solid #D81E06'"}>
                    <dl class="table_flex">
                      ${_.map(e,(e_2)=>`<dd class="flex-column" style="line-height: 1.2;"><span title="上圧：${e_2&&e_2.SY||''}(mmHg)" style="font-size:12px;height:20px;">
                        ${e_2&&e_2.SY||''}</span><span style="border-top: 2px solid rgba(0, 0, 0, 0.3);transform:skewY(-30deg);width:100%;margin: 6px 0;"></span><span title="下圧：${e_2&&e_2.XY||''}(mmHg)" style="font-size:12px;height:20px;">${e_2&&e_2.XY||''}</span><span style="height:48px;">${e_2&&e_2.XYBW||''}</span></dd>`).join('')}</dl></td>`).join('')}
                </tr>
                <tr>
                  <td>体重(Kg)/BMI</td>
                  ${_.map(tableData.TZ,e=>`<td>${e}</td>`).join('')}
                </tr>
                <tr>
                  <td>总入量</td>
                  ${_.map(tableData.RL,e=>`<td>${e}</td>`).join('')}
                </tr>
                <tr>
                  <td style="font-size:20px;">总出量/尿量(ml)</td>
                  ${_.map(tableData.ZCL,(e,i)=>`<td>${tableData.ZCL[i]||tableData.NL[i]?`${tableData.ZCL[i]} / ${tableData.NL[i]}`:''}</td>`).join('')}
                </tr>
                <tr>
                  <td style="font-size:20px;">其他排出量/引流量(ml)</td>
                  ${_.map(tableData.CL,(e,i)=>`<td>${tableData.CL[i]||tableData.YLL[i]?`${tableData.CL[i]} / ${tableData.YLL[i]}`:''}</td>`).join('')}
                </tr>
                <tr>
                  <td>大便次数</td>
                  ${_.map(tableData.DBCS,e=>`<td>${e}</td>`).join('')}
                </tr>
                <tr>
                  <td>身高/腹围</td>
                  ${_.map(tableData.SG,(e,i)=>`<td>${tableData.SG[i]||tableData.FW[i]?`${tableData.SG[i]} / ${tableData.FW[i]}`:''}</td>`).join('')}
                </tr>
                <tr>
                  <td style="font-size:20px;">经皮胆红汞值(mg/dl)</td>
                  ${_.map(tableData.QT1,e=>`<td>${e}</td>`).join('')}
                </tr>
                <tr>
                  <td>其他</td>
                  ${_.map(tableData.QT2,e=>`<td>${e}</td>`).join('')}
                </tr>
              </tbody>
            </table>  
          </div>
          <div class="twd_data_page">第<span>${params['as_week']}</span>页</div>
        </div>
        `)
        return this
      },
      events: {},
    })
    //修改体温单数据的结构
    function changeTemperature(data) {
      // console.log(JSON.parse(data.smtzData))
      tableData = {
        CLSJ: [], //日期
        XYTS: [], //天数
        SSTS: [], //手术天数
        TW: [], //体温
        XLMB: [], //脉搏&&心率
        TTCD: [], //疼痛评分
        HX: [], //呼吸
        SYXY: [], //血压
        TZ: [], //体重
        RL: [], //入量
        NL: [], //尿量
        ZCL: [], //总出量
        CL: [], //出量
        YLL: [], //引流量
        DBCS: [], //大便次数
        SG: [], //身高
        FW: [], //腹围
        QT1: [], //经皮胆红汞值
        QT2: [] //其他
      }
      //初始化列表
      for (var i = 0; i < 7; i++) {
        //添加后7天日期
        let d = new Date(new Date(params['as_kssj']).setDate(new Date(params['as_kssj']).getDate() + i))
        tableData.CLSJ.push(`${d.getFullYear()}-${d.getMonth()<9?'0':''}${d.getMonth()+ 1}-${d.getDate()<10?'0':''}${d.getDate()}`)
        //当前入院天数
        tableData.XYTS.push(parseInt(new Date(params['as_kssj']).getTime() / (24 * 60 * 60 *
          1000)) - parseInt(new Date(data.bingQuRYSJ).getTime() / (24 * 60 * 60 *  
          1000)) + i + 1)
        //手术天数
        let ssjl = ''
        for (let [index, item] of listDate.shouShuTZDSJ.reverse().entries()) {
          let day = parseInt(new Date(params['as_kssj']).getTime() / (24 * 60 * 60 *
            1000)) - parseInt(new Date(item.shouShuKSSJ) / (24 * 60 * 60 *
            1000)) + i + 1
          if (day > 0 & day <= 10) {
            ssjl += `/${day}`
          }
        }
      

        tableData.SSTS.push(ssjl.substr(1))
        //初始化tableData
        tableData.TW[i] = [] //体温
        tableData.XLMB[i] = [] //脉搏&&心率
        tableData.TTCD[i] = [] //脉搏&&心率
        tableData.HX[i] = ['', '', '', '', '', ''] //呼吸
        tableData.SYXY[i] = ['', '', '', '', '', ''] //血压
        tableData.TZ[i] = '' //体重
        tableData.RL[i] = '' //入量
        tableData.NL[i] = '' //尿量
        tableData.ZCL[i] = '' //总出量
        tableData.CL[i] = '' //出量
        tableData.YLL[i] = '' //引流量
        tableData.DBCS[i] = '' //大便次数
        tableData.SG[i] = '' //身高
        tableData.FW[i] = '' //腹围
        tableData.QT1[i] = '' //经皮胆红汞值
        tableData.QT2[i] = '' //其他
      }
      let arr = data.shengMingTZSJ.filter(item=>{
        if(item.shiJianDian!=99){
          return item
        }
      })
      // let arr = data.shengMingTZSJ
      // let arr=JSON.parse(data.smtzData)
      // console.log(JSON.parse(data.smtzData))
      arr=arr.sort(function(a,b){
        let fd=new Date(a.CLSJ).getTime()-new Date(b.CLSJ).getTime()
        if(fd<0) return -1
        else return 1
      })
      let idx = 1
      for (let obj of arr) {
        obj.shiJianDian < 99 ? idx++ : ''
        //判断这条是否同一天
        let index = tableData.CLSJ.indexOf(obj.chuLiSJ.split(' ')[0])
        let time = obj.chuLiSJ.split(' ')[1].split(':')
        let SJD = (Number(time[0]) + Number(time[1]) / 60 + Number(time[2]) / 3600)
        //体温
        obj.tiWen ? tableData.TW[index].push({
          SJD: SJD,
          TW: obj.tiWen,
          FCTW: obj.fuCeTW,
          TWCLBW: obj.tiWenCLBW
        }) : ''
        //脉搏
        obj.maiBo ? tableData.XLMB[index].push({
          SJD: SJD,
          VAL: obj.maiBo,
          type: 'heart'
        }) : ''
        //心率
        obj.xinLv ? tableData.XLMB[index].push({
          SJD: SJD,
          VAL: obj.xinLv,
          type: 'heart_o'
        }) : ''
        //疼痛程度
        obj.tengTongCD != null ? tableData.TTCD[index].push({
          SJD: SJD,
          VAL: obj.tengTongCD,
          type: 'circular_o_xs'
        }) : ''
        //呼吸(分上下)
        obj.shiJianDian < 99 ? tableData.HX[index][((Number(obj.shiJianDian < 98?obj.shiJianDian:SJD) + 2) / 4) - 1] = {
          type: idx % 2 == 0 ? `up` : `down`,
          val: obj.huXi
        } : ''
        //血压(分上下)
        obj.shiJianDian < 99 ? tableData.SYXY[index][((Number(obj.shiJianDian < 98?obj.shiJianDian:SJD) + 2) / 4) - 1] = {
          SY: obj.shuZhangYa,
          XY: obj.shouSuoYa,
          XYBW: obj.xieYaCLBW == 00 ? '上左' : obj.xieYaCLBW == 01 ? '上右' : obj.xieYaCLBW == 10 ? '下左' : obj.xieYaCLBW == 11 ? '下右' : ''
        } : ''
        obj.tiZhong ? tableData.TZ[index] = (obj.tiZhong == -1 ? '平车' : obj.tiZhong == -2 ? '轮椅' : obj.tiZhong == -3 ? '卧床' : `${obj.tiZhong} / ${obj.shenGao?(obj.tiZhong/Math.pow(obj.shenGao/100,2)).toFixed(1):''}`) :
          '' //体重
        obj.zongRuLiang ? tableData.RL[index] = obj.zongRuLiang : '' //入量
        obj.niaoLiang ? tableData.NL[index] = obj.niaoLiang : '' //尿量
        obj.zongChuLiang ? tableData.ZCL[index] = obj.zongChuLiang : '' //总出量
        obj.qiTaPCL ? tableData.CL[index] = obj.qiTaPCL : '' //出量
        obj.yinLiuLiang ? tableData.YLL[index] = obj.yinLiuLiang : '' //引流量
        obj.daBianCS ? tableData.DBCS[index] = obj.daBianCS : '' //大便次数
        obj.shenGao ? tableData.SG[index] = obj.shenGao : '' //身高
        obj.fuWei ? tableData.FW[index] = obj.fuWei : '' //腹围
        obj.jingPiDHSZ ? tableData.QT1[index] = obj.jingPiDHSZ : '' //经皮胆红汞值
        obj.qiTa2 ? tableData.QT2[index] = obj.qiTa2 : '' //其他
      }
    }
    //图表
    function cavsTable() {
      var canvas = document.querySelector('#cavsTable');
      var ctx = canvas.getContext('2d');
      let width = 1090; //表格宽
      let height = 1096; //表格高
      canvas.width = width;
      canvas.height = height + 20;
      ctx.lineWidth = 2
      var TW = [] //体温
      var FCTW = [] //复测体温
      _.each(tableData.TW, (e, i) => {
        _.each(e, (el, idx) => {
          if (el.FCTW) {
            FCTW.push({
              x: width * ((Number(el.SJD) + 24 * i) / 168),
              y: height * (42.4 - Number(el.FCTW)) / 10.4,
              oldY: height * (42.4 - Number(el.TW)) / 10.4, //原来的点位
            })
          }
          TW.push({
            x: width * ((Number(el.SJD) + 24 * i) / 168),
            y: height * (42.4 - Number(el.TW)) / 10.4,
            type: el.TWCLBW == 1 ? 'circular' : el.TWCLBW == 2 ? 'circular_o' : el.TWCLBW == 3 ?
              'fork' : el.TWCLBW == 4 ? 'triangle_o' : el.TWCLBW == 5 ? 'square' : ''
          })
        })
      })
      drawPoints(ctx, TW)
      drawPointsFCTW(ctx, FCTW)
      var XLMB = [] //心率脉搏
      _.each(tableData.XLMB, (e, i) => {
        XLMB = [...XLMB, ..._.map(e, (el, idx) => ({
          x: width * ((Number(el.SJD) + 24 * i) / 168),
          y: height * (252 - Number(el.VAL)) / 212,
          type: el.type
        }))]
      })
      drawPoints(ctx, XLMB)
      var TTCD = [] //疼痛程度
      _.each(tableData.TTCD, (e, i) => {
        TTCD = [...TTCD, ..._.map(e, (el, idx) => ({
          x: width * ((Number(el.SJD) + 24 * i) / 168),
          y: (height - 1) * (104 - Number(el.VAL)) / 104,
          type: el.type
        }))]
      })
      drawPoints(ctx, TTCD)
      //显示文字
      let time = function (e) {
        return new Date(e)
      }
      let textList = []
      //入院出院
      textList.push({
        name: '入院' + chineseDate(time(listDate.bingQuRYSJ)),
        time: time(listDate.bingQuRYSJ).Format("yyyy-MM-dd"),
        SJD: getSjd(time(listDate.bingQuRYSJ).Format("HH"))
      })
      textList.push({
        name: '出     院',
        time: time(listDate.bingQuCYSJ).Format("yyyy-MM-dd"),
        SJD: getSjd(time(listDate.bingQuCYSJ).Format("HH"))
      })
      //转科
      for (let item of listDate.zhuanChuangJLSJ) {
        textList.push({
          name: '转科'+ chineseDate(time(item.zhuanChuangSJ)),/////////////////
          time: time(item.zhuanChuangSJ).Format("yyyy-MM-dd"),
          SJD: getSjd(time(item.zhuanChuangSJ).Format("HH"))
        })
      }
      //手术
      for (let item of listDate.shouShuTZDSJ) {
        if (item.shenPiYJ !== 0 && [0, 9].indexOf(item.zhuangTaiBZ) == -1) {
          textList.push({
            name: (item.maZuiHZ == 0 && ['05', '06'].indexOf(item.shouShuShiDM) > -1) ? '介     入' : '手     术',
            time: time(item.shouShuKSSJ).Format("yyyy-MM-dd"),
            SJD: getSjd(time(item.shouShuKSSJ).Format("HH"))
          })
        }
      }
      //操作信息
      // for (let item of JSON.parse(listDate.smtzData)) {
      for (let item of listDate.shengMingTZSJ) {
        let name = ''
        if (item.caoZuoXX) {
          switch (item.caoZuoXX) {
            case "1":
              textList.push({
                name: `${chineseDate(new Date(item.chuLiSJ))}分娩`,
                time: item.chuLiSJ.split(' ')[0],
                SJD: item.shiJianDian
              })
              break;
            case "2":
              let same = textList.find(e => (e.SJD == item.shiJianDian && e.time == item.chuLiSJ.split(' ')[0]))
              if (same) {
                same.name = (same.name.replace(/\s*/g, "") + '╱机械通气')
              } else {
                textList.push({
                  name: '机 械 通 气',
                  time: item.chuLiSJ.split(' ')[0],
                  SJD: item.shiJianDian
                })
              }
              break;
            case "3":
              textList.push({
                name: `${chineseDate(new Date(item.chuLiSJ))}死亡`,
                time: item.chuLiSJ.split(' ')[0],
                SJD: item.shiJianDian
              })
              break;
          }
        }
      }
      // textList = [
      //   {name: '入院十四时四十三分', time: '2018-09-25', SJD: 14},
      //   {name: '出     院', time: '1-01-01', SJD: 10},
      //   {name: '转科十五时五十分', time: '2018-09-30', SJD: 14},
      //   {name: '转科二十二时四十二分', time: '2018-10-01', SJD: 22},
      //   {name: '转科十四时五十八分', time: '2018-10-09', SJD: 14},
      //   {name: '转科二十二时三十七分', time: '2018-10-10', SJD: 22},
      //   {name: '手     术', time: '2018-09-27', SJD: 14}
      // ]
      for (let obj of textList) {
        let i = tableData.CLSJ.indexOf(obj.time)
        if (i > -1) {
          ctx.font = "24px Georgia";
          ctx.fillStyle = "#ff0000";
          for (let [index, item] of obj.name.split('').entries()) {
            ctx.fillText(item, width * ((Number(obj.SJD) + 24 * i) / 168) - 12, 65 + index * 24)
          }
        }
      }
    }
    //点坐标
    function drawPoints(ctx, data) {
      // 设置坐标点的大小  dotSize
      var dotSize = 14;
      //上一次循环的点
      var oldPoint = {
        x: 0,
        y: 0
      };
      ctx.setLineDash([]);
      //排序
      // 遍历点的坐标,以及绘画点
      data.sort(compare('x')).forEach(function (item, i) {
        // 设置坐标点的中心圆点位置（x0，y0）
        var x = item.x;
        var y = item.y;
        // 绘画坐标点
        switch (item.type) {
          case 'circular_o': //空心圆
            ctx.beginPath();
            ctx.arc(x, y, dotSize / 2, 0, 2 * Math.PI);
            ctx.strokeStyle = '#4E6EF2'
            ctx.stroke();
            break;
          case 'circular_o_xs': //小空心圆红
            ctx.beginPath();
            ctx.arc(x, y, dotSize / 3.5, 0, 2 * Math.PI);
            ctx.strokeStyle = '#D81E06'
            ctx.stroke();
            break;
          case 'circular': //实心圆
            ctx.beginPath();
            ctx.arc(x, y, dotSize / 2, 0, 2 * Math.PI);
            ctx.fillStyle = '#4E6EF2'
            ctx.fill();
            break;
          case 'fork': //叉叉
            ctx.beginPath();
            ctx.moveTo(x - dotSize / 2, y - dotSize / 2);
            ctx.lineTo(x + dotSize / 2, y + dotSize / 2);
            ctx.moveTo(x - dotSize / 2, y + dotSize / 2);
            ctx.lineTo(x + dotSize / 2, y - dotSize / 2);
            ctx.strokeStyle = '#4E6EF2'
            ctx.stroke();
            break;
          case 'triangle_o': //空心三角形
            ctx.beginPath();
            ctx.moveTo(x, y - dotSize * 0.6);
            ctx.lineTo(x + dotSize / 2, y + dotSize * 0.4);
            ctx.lineTo(x - dotSize / 2, y + dotSize * 0.4);
            ctx.closePath(); //闭合路径
            ctx.strokeStyle = "#4E6EF2";
            ctx.stroke();
            break;
          case 'square': //实心正方形
            ctx.fillStyle = '#4E6EF2'
            ctx.fillRect(x - dotSize / 2, y - dotSize / 2, dotSize, dotSize);
            break;
          case 'heart_o': //空心爱心
            var img = new Image();
            img.src = './images/heart_o.png'
            img.onload = function () {
              ctx.drawImage(img, x - dotSize / 2, y - dotSize / 2, dotSize * 1.2, dotSize * 1.2);
            }
            break;
          case 'heart': //实心爱心
            var img = new Image();
            img.src = './images/heart.png'
            img.onload = function () {
              ctx.drawImage(img, x - dotSize / 2, y - dotSize / 2, dotSize * 1.2, dotSize * 1.2);
            }
            break;
        }
        //绘制线
        if (i > 0) {
          ctx.beginPath();
          ctx.moveTo(oldPoint.x, oldPoint.y);
          ctx.lineTo(x, y);
          ctx.strokeStyle = (["heart_o", "heart"].indexOf(item.type) > -1 ? "#D81E06" : "#4E6EF2")
          ctx.stroke();
        }
        oldPoint = {
          x,
          y
        }
      });
    }

    function drawPointsFCTW(ctx, data) {
      // 设置坐标点的大小  dotSize
      var dotSize = 14;
      // 遍历点的坐标,以及绘画点
      data.forEach(function (item, i) {
        ctx.setLineDash([]);
        // 设置坐标点的中心圆点位置（x0，y0）
        var x = item.x;
        var y = item.y;
        //空心圆红
        ctx.beginPath();
        ctx.arc(x, y, dotSize / 2, 0, 2 * Math.PI);
        ctx.strokeStyle = '#D81E06'
        ctx.stroke();
        //绘制虚线
        ctx.beginPath();
        ctx.setLineDash([4, 2]);
        ctx.moveTo(item.x, item.oldY);
        ctx.lineTo(item.x, item.y);
        ctx.strokeStyle = "#D81E06"
        ctx.stroke();
      });
    }
    //获取体温单表格
    function getTwd() {
      let obj = {
        as_blid: params['as_blid'],
        as_jssj: params['as_jssj'],
        as_kssj: params['as_kssj'],
        as_week: params['as_week']
      }
      let obj1 = {
        bingLiID: params['as_blid'],
        jieShuSJ: params['as_jssj'],
        kaishiSJ: params['as_kssj'],
      }
      //获取体温单标题
      // let data_2=json_data
      //获取体温单数据
      // WRT_e.api.brtwdlb.getTwdData_app({
      // // WRT_e.api.brtwdlb.getTwdData({
      //   params: obj1,
      //   success(data_2) {
      //     if (data_2.hasError == 0) {
        listDate={
    "shengMingTZSJ": [
      {
        "bingLiID": 2628669,
        "zhuYuanID": 2628669,
        "bingQuID": 101,
        "bingRenXM": "赵翠英",
        "chuLiSJ": "2023-07-16 06:00:00",
        "shiJianDian": "6",
        "tiWen": 36.3,
        "tiWenCLBW": "4",
        "jiangWenBXS": null,
        "maiBo": 63,
        "xinLv": null,
        "huXi": 16,
        "shuZhangYa": null,
        "shouSuoYa": null,
        "tiZhong": null,
        "fuWei": null,
        "daBianCS": null,
        "zongRuLiang": null,
        "qiTaPCL": null,
        "niaoLiang": null,
        "beiZhu": null,
        "caoZuoZheID": 37179,
        "xiuGaiSJ": "2023-07-16 10:40:37",
        "fuCeTW": null,
        "caoZuoXX": null,
        "xieYaCLBW": null,
        "caoZuoSJ": null,
        "shenGao": null,
        "jingPiDHSZ": null,
        "qiTa2": null,
        "zongChuLiang": null,
        "shuJuLY": null,
        "tengTongCD": 2,
        "tengTongPGFF": "100053",
        "xieYangBHD": null,
        "yinLiuLiang": null,
        "woLi": null,
        "linshiCLSJ": "2023-07-16 00:00:00",
        "linshiSJD": 6,
        "id": 87406972
      },
      {
        "bingLiID": 2628669,
        "zhuYuanID": 2628669,
        "bingQuID": 101,
        "bingRenXM": "赵翠英",
        "chuLiSJ": "2023-07-17 06:00:00",
        "shiJianDian": "6",
        "tiWen": 36.6,
        "tiWenCLBW": "4",
        "jiangWenBXS": null,
        "maiBo": 68,
        "xinLv": null,
        "huXi": 20,
        "shuZhangYa": null,
        "shouSuoYa": null,
        "tiZhong": null,
        "fuWei": null,
        "daBianCS": null,
        "zongRuLiang": null,
        "qiTaPCL": null,
        "niaoLiang": null,
        "beiZhu": null,
        "caoZuoZheID": 4685,
        "xiuGaiSJ": "2023-07-17 09:32:09",
        "fuCeTW": null,
        "caoZuoXX": null,
        "xieYaCLBW": null,
        "caoZuoSJ": null,
        "shenGao": null,
        "jingPiDHSZ": null,
        "qiTa2": null,
        "zongChuLiang": null,
        "shuJuLY": null,
        "tengTongCD": 2,
        "tengTongPGFF": "100053",
        "xieYangBHD": null,
        "yinLiuLiang": null,
        "woLi": null,
        "linshiCLSJ": "2023-07-17 00:00:00",
        "linshiSJD": 6,
        "id": 87441937
      },
      {
        "bingLiID": 2628669,
        "zhuYuanID": 2628669,
        "bingQuID": 101,
        "bingRenXM": "赵翠英",
        "chuLiSJ": "2023-07-18 06:00:00",
        "shiJianDian": "6",
        "tiWen": 36.5,
        "tiWenCLBW": "4",
        "jiangWenBXS": null,
        "maiBo": 72,
        "xinLv": null,
        "huXi": 20,
        "shuZhangYa": null,
        "shouSuoYa": null,
        "tiZhong": null,
        "fuWei": null,
        "daBianCS": null,
        "zongRuLiang": null,
        "qiTaPCL": null,
        "niaoLiang": null,
        "beiZhu": null,
        "caoZuoZheID": 7415,
        "xiuGaiSJ": "2023-07-18 09:41:47",
        "fuCeTW": null,
        "caoZuoXX": null,
        "xieYaCLBW": null,
        "caoZuoSJ": null,
        "shenGao": null,
        "jingPiDHSZ": null,
        "qiTa2": null,
        "zongChuLiang": null,
        "shuJuLY": null,
        "tengTongCD": 1,
        "tengTongPGFF": "100053",
        "xieYangBHD": null,
        "yinLiuLiang": null,
        "woLi": null,
        "linshiCLSJ": "2023-07-18 00:00:00",
        "linshiSJD": 6,
        "id": 87476396
      },
      {
        "bingLiID": 2628669,
        "zhuYuanID": 2628669,
        "bingQuID": 101,
        "bingRenXM": "赵翠英",
        "chuLiSJ": "2023-07-19 06:00:00",
        "shiJianDian": "6",
        "tiWen": 36.2,
        "tiWenCLBW": "4",
        "jiangWenBXS": null,
        "maiBo": 70,
        "xinLv": null,
        "huXi": 20,
        "shuZhangYa": null,
        "shouSuoYa": null,
        "tiZhong": null,
        "fuWei": null,
        "daBianCS": null,
        "zongRuLiang": null,
        "qiTaPCL": null,
        "niaoLiang": null,
        "beiZhu": null,
        "caoZuoZheID": 4685,
        "xiuGaiSJ": "2023-07-19 10:02:08",
        "fuCeTW": null,
        "caoZuoXX": null,
        "xieYaCLBW": null,
        "caoZuoSJ": null,
        "shenGao": null,
        "jingPiDHSZ": null,
        "qiTa2": null,
        "zongChuLiang": null,
        "shuJuLY": null,
        "tengTongCD": 0,
        "tengTongPGFF": "100053",
        "xieYangBHD": null,
        "yinLiuLiang": null,
        "woLi": null,
        "linshiCLSJ": "2023-07-19 00:00:00",
        "linshiSJD": 6,
        "id": 87520042
      },
      {
        "bingLiID": 2628669,
        "zhuYuanID": 2628669,
        "bingQuID": 101,
        "bingRenXM": "赵翠英",
        "chuLiSJ": "2023-07-15 10:00:00",
        "shiJianDian": "10",
        "tiWen": 36.1,
        "tiWenCLBW": "4",
        "jiangWenBXS": null,
        "maiBo": 81,
        "xinLv": null,
        "huXi": 20,
        "shuZhangYa": 128,
        "shouSuoYa": 68,
        "tiZhong": 46,
        "fuWei": null,
        "daBianCS": "0",
        "zongRuLiang": null,
        "qiTaPCL": null,
        "niaoLiang": null,
        "beiZhu": null,
        "caoZuoZheID": 11573,
        "xiuGaiSJ": "2023-07-15 09:31:06",
        "fuCeTW": null,
        "caoZuoXX": null,
        "xieYaCLBW": null,
        "caoZuoSJ": null,
        "shenGao": 145,
        "jingPiDHSZ": null,
        "qiTa2": null,
        "zongChuLiang": null,
        "shuJuLY": null,
        "tengTongCD": 2,
        "tengTongPGFF": "100053",
        "xieYangBHD": null,
        "yinLiuLiang": null,
        "woLi": null,
        "linshiCLSJ": "2023-07-15 00:00:00",
        "linshiSJD": 10,
        "id": 87374860
      },
      {
        "bingLiID": 2628669,
        "zhuYuanID": 2628669,
        "bingQuID": 101,
        "bingRenXM": "赵翠英",
        "chuLiSJ": "2023-07-15 14:00:00",
        "shiJianDian": "14",
        "tiWen": 36.8,
        "tiWenCLBW": "4",
        "jiangWenBXS": null,
        "maiBo": 68,
        "xinLv": null,
        "huXi": 20,
        "shuZhangYa": null,
        "shouSuoYa": null,
        "tiZhong": null,
        "fuWei": null,
        "daBianCS": null,
        "zongRuLiang": null,
        "qiTaPCL": null,
        "niaoLiang": null,
        "beiZhu": null,
        "caoZuoZheID": 37179,
        "xiuGaiSJ": "2023-07-15 14:29:35",
        "fuCeTW": null,
        "caoZuoXX": null,
        "xieYaCLBW": null,
        "caoZuoSJ": null,
        "shenGao": null,
        "jingPiDHSZ": null,
        "qiTa2": null,
        "zongChuLiang": null,
        "shuJuLY": null,
        "tengTongCD": null,
        "tengTongPGFF": null,
        "xieYangBHD": null,
        "yinLiuLiang": null,
        "woLi": null,
        "linshiCLSJ": "2023-07-15 00:00:00",
        "linshiSJD": 14,
        "id": 87384628
      },
      {
        "bingLiID": 2628669,
        "zhuYuanID": 2628669,
        "bingQuID": 101,
        "bingRenXM": "赵翠英",
        "chuLiSJ": "2023-07-16 14:00:00",
        "shiJianDian": "14",
        "tiWen": 36.5,
        "tiWenCLBW": "4",
        "jiangWenBXS": null,
        "maiBo": 71,
        "xinLv": null,
        "huXi": 20,
        "shuZhangYa": null,
        "shouSuoYa": null,
        "tiZhong": null,
        "fuWei": null,
        "daBianCS": "0",
        "zongRuLiang": null,
        "qiTaPCL": null,
        "niaoLiang": null,
        "beiZhu": null,
        "caoZuoZheID": 9642,
        "xiuGaiSJ": "2023-07-16 14:28:33",
        "fuCeTW": null,
        "caoZuoXX": null,
        "xieYaCLBW": null,
        "caoZuoSJ": null,
        "shenGao": null,
        "jingPiDHSZ": null,
        "qiTa2": null,
        "zongChuLiang": null,
        "shuJuLY": null,
        "tengTongCD": null,
        "tengTongPGFF": null,
        "xieYangBHD": null,
        "yinLiuLiang": null,
        "woLi": null,
        "linshiCLSJ": "2023-07-16 00:00:00",
        "linshiSJD": 14,
        "id": 87421427
      },
      {
        "bingLiID": 2628669,
        "zhuYuanID": 2628669,
        "bingQuID": 101,
        "bingRenXM": "赵翠英",
        "chuLiSJ": "2023-07-17 14:00:00",
        "shiJianDian": "14",
        "tiWen": 36.6,
        "tiWenCLBW": "4",
        "jiangWenBXS": null,
        "maiBo": 75,
        "xinLv": null,
        "huXi": 18,
        "shuZhangYa": null,
        "shouSuoYa": null,
        "tiZhong": null,
        "fuWei": null,
        "daBianCS": "0",
        "zongRuLiang": null,
        "qiTaPCL": null,
        "niaoLiang": null,
        "beiZhu": null,
        "caoZuoZheID": 26450,
        "xiuGaiSJ": "2023-07-17 14:37:02",
        "fuCeTW": null,
        "caoZuoXX": null,
        "xieYaCLBW": null,
        "caoZuoSJ": null,
        "shenGao": null,
        "jingPiDHSZ": null,
        "qiTa2": null,
        "zongChuLiang": null,
        "shuJuLY": null,
        "tengTongCD": null,
        "tengTongPGFF": null,
        "xieYangBHD": null,
        "yinLiuLiang": null,
        "woLi": null,
        "linshiCLSJ": "2023-07-17 00:00:00",
        "linshiSJD": 14,
        "id": 87455652
      },
      {
        "bingLiID": 2628669,
        "zhuYuanID": 2628669,
        "bingQuID": 101,
        "bingRenXM": "赵翠英",
        "chuLiSJ": "2023-07-18 14:00:00",
        "shiJianDian": "14",
        "tiWen": 36.2,
        "tiWenCLBW": "4",
        "jiangWenBXS": null,
        "maiBo": 64,
        "xinLv": null,
        "huXi": 19,
        "shuZhangYa": null,
        "shouSuoYa": null,
        "tiZhong": null,
        "fuWei": null,
        "daBianCS": "0",
        "zongRuLiang": null,
        "qiTaPCL": null,
        "niaoLiang": null,
        "beiZhu": null,
        "caoZuoZheID": 26450,
        "xiuGaiSJ": "2023-07-18 14:32:59",
        "fuCeTW": null,
        "caoZuoXX": null,
        "xieYaCLBW": null,
        "caoZuoSJ": null,
        "shenGao": null,
        "jingPiDHSZ": null,
        "qiTa2": null,
        "zongChuLiang": null,
        "shuJuLY": null,
        "tengTongCD": null,
        "tengTongPGFF": null,
        "xieYangBHD": null,
        "yinLiuLiang": null,
        "woLi": null,
        "linshiCLSJ": "2023-07-18 00:00:00",
        "linshiSJD": 14,
        "id": 87495362
      },
      {
        "bingLiID": 2628669,
        "zhuYuanID": 2628669,
        "bingQuID": 101,
        "bingRenXM": "赵翠英",
        "chuLiSJ": "2023-07-15 18:00:00",
        "shiJianDian": "18",
        "tiWen": 36.4,
        "tiWenCLBW": "4",
        "jiangWenBXS": null,
        "maiBo": 80,
        "xinLv": null,
        "huXi": 20,
        "shuZhangYa": null,
        "shouSuoYa": null,
        "tiZhong": null,
        "fuWei": null,
        "daBianCS": null,
        "zongRuLiang": null,
        "qiTaPCL": null,
        "niaoLiang": null,
        "beiZhu": null,
        "caoZuoZheID": 12182,
        "xiuGaiSJ": "2023-07-15 17:53:00",
        "fuCeTW": null,
        "caoZuoXX": null,
        "xieYaCLBW": null,
        "caoZuoSJ": null,
        "shenGao": null,
        "jingPiDHSZ": null,
        "qiTa2": null,
        "zongChuLiang": null,
        "shuJuLY": null,
        "tengTongCD": null,
        "tengTongPGFF": null,
        "xieYangBHD": null,
        "yinLiuLiang": null,
        "woLi": null,
        "linshiCLSJ": "2023-07-15 00:00:00",
        "linshiSJD": 18,
        "id": 87390146
      },
      {
        "bingLiID": 2628669,
        "zhuYuanID": 2628669,
        "bingQuID": 101,
        "bingRenXM": "赵翠英",
        "chuLiSJ": "2023-07-17 15:04:17",
        "shiJianDian": "99",
        "tiWen": null,
        "tiWenCLBW": null,
        "jiangWenBXS": null,
        "maiBo": null,
        "xinLv": null,
        "huXi": null,
        "shuZhangYa": null,
        "shouSuoYa": null,
        "tiZhong": null,
        "fuWei": null,
        "daBianCS": null,
        "zongRuLiang": null,
        "qiTaPCL": null,
        "niaoLiang": null,
        "beiZhu": null,
        "caoZuoZheID": 4685,
        "xiuGaiSJ": "2023-07-19 10:06:37",
        "fuCeTW": null,
        "caoZuoXX": null,
        "xieYaCLBW": null,
        "caoZuoSJ": null,
        "shenGao": null,
        "jingPiDHSZ": null,
        "qiTa2": null,
        "zongChuLiang": null,
        "shuJuLY": "1",
        "tengTongCD": null,
        "tengTongPGFF": null,
        "xieYangBHD": null,
        "yinLiuLiang": null,
        "woLi": null,
        "linshiCLSJ": "2023-07-17 00:00:00",
        "linshiSJD": 99,
        "id": 87527075
      },
      {
        "bingLiID": 2628669,
        "zhuYuanID": 2628669,
        "bingQuID": 101,
        "bingRenXM": "赵翠英",
        "chuLiSJ": "2023-07-18 17:59:58",
        "shiJianDian": "99",
        "tiWen": null,
        "tiWenCLBW": null,
        "jiangWenBXS": null,
        "maiBo": null,
        "xinLv": null,
        "huXi": null,
        "shuZhangYa": null,
        "shouSuoYa": null,
        "tiZhong": null,
        "fuWei": null,
        "daBianCS": null,
        "zongRuLiang": null,
        "qiTaPCL": null,
        "niaoLiang": null,
        "beiZhu": null,
        "caoZuoZheID": 10470,
        "xiuGaiSJ": "2023-07-18 18:03:29",
        "fuCeTW": null,
        "caoZuoXX": null,
        "xieYaCLBW": null,
        "caoZuoSJ": null,
        "shenGao": null,
        "jingPiDHSZ": null,
        "qiTa2": null,
        "zongChuLiang": null,
        "shuJuLY": "1",
        "tengTongCD": null,
        "tengTongPGFF": null,
        "xieYangBHD": 98,
        "yinLiuLiang": null,
        "woLi": null,
        "linshiCLSJ": "2023-07-18 00:00:00",
        "linshiSJD": 99,
        "id": 87501745
      },
      {
        "bingLiID": 2628669,
        "zhuYuanID": 2628669,
        "bingQuID": 101,
        "bingRenXM": "赵翠英",
        "chuLiSJ": "2023-07-18 20:00:00",
        "shiJianDian": "99",
        "tiWen": null,
        "tiWenCLBW": null,
        "jiangWenBXS": null,
        "maiBo": null,
        "xinLv": null,
        "huXi": null,
        "shuZhangYa": null,
        "shouSuoYa": null,
        "tiZhong": null,
        "fuWei": null,
        "daBianCS": null,
        "zongRuLiang": null,
        "qiTaPCL": null,
        "niaoLiang": null,
        "beiZhu": null,
        "caoZuoZheID": 11573,
        "xiuGaiSJ": "2023-07-18 21:05:36",
        "fuCeTW": null,
        "caoZuoXX": null,
        "xieYaCLBW": null,
        "caoZuoSJ": null,
        "shenGao": null,
        "jingPiDHSZ": null,
        "qiTa2": null,
        "zongChuLiang": null,
        "shuJuLY": "1",
        "tengTongCD": null,
        "tengTongPGFF": null,
        "xieYangBHD": 97,
        "yinLiuLiang": null,
        "woLi": null,
        "linshiCLSJ": "2023-07-18 00:00:00",
        "linshiSJD": 99,
        "id": 87506352
      },
      {
        "bingLiID": 2628669,
        "zhuYuanID": 2628669,
        "bingQuID": 101,
        "bingRenXM": "赵翠英",
        "chuLiSJ": "2023-07-18 22:00:00",
        "shiJianDian": "99",
        "tiWen": null,
        "tiWenCLBW": null,
        "jiangWenBXS": null,
        "maiBo": null,
        "xinLv": null,
        "huXi": null,
        "shuZhangYa": null,
        "shouSuoYa": null,
        "tiZhong": null,
        "fuWei": null,
        "daBianCS": null,
        "zongRuLiang": null,
        "qiTaPCL": null,
        "niaoLiang": null,
        "beiZhu": null,
        "caoZuoZheID": 11573,
        "xiuGaiSJ": "2023-07-18 22:53:50",
        "fuCeTW": null,
        "caoZuoXX": null,
        "xieYaCLBW": null,
        "caoZuoSJ": null,
        "shenGao": null,
        "jingPiDHSZ": null,
        "qiTa2": null,
        "zongChuLiang": null,
        "shuJuLY": "1",
        "tengTongCD": null,
        "tengTongPGFF": null,
        "xieYangBHD": 95,
        "yinLiuLiang": null,
        "woLi": null,
        "linshiCLSJ": "2023-07-18 00:00:00",
        "linshiSJD": 99,
        "id": 87509119
      }
    ],
    "shouShuTZDSJ": [],
    "zhuanChuangJLSJ": [],
    "bingQuRYSJ": "2023-07-15 09:08:15",
    "bingQuCYSJ": "2023-07-19 11:01:39"
  }
            // listDate = data_2.data
            $("#twd_table").html(
              new twdData_View().init({
                data: {
                  list: listDate,
                }
              }).render().$el
            )
            //画图
            cavsTable()
      //     }else{
      //       if(data.errorMessage){
      //         alert(data.errorMessage)
      //       }
      //     }
      //   }
      // })
    }
    //打印
    function preview() {
      window.print();
    }
    // 放大缩小
    function ZoomIt(flag) {
      var zoom = Number($('#twd_table').css('zoom'))
      if (zoom > 0) $('#twd_table').css('zoom', eval('zoom' + flag + '=0.5;'));
    }
    //中文数字
    function chineseDate(date) {
      var hours = date.getHours()
      var minutes = date.getMinutes()
      // 组装小时
      hours = formatNumber(hours) + '时'
      // // 组装分钟
      if (minutes < 10) {
        minutes = '零' + formatNumber(minutes) + '分'
      } else {
        minutes = formatNumber(minutes) + '分'
      }
      return `${hours}${minutes}`
    }

    function formatNumber(n) {
      var arr = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
      if (n <= 10) {
        // 几
        return arr[n]
      } else if (n < 20) {
        // 十几
        return '十' + arr[n - 10]
      } else if (n >= 20 && n % 10 === 0) {
        // 几十
        return arr[n / 10] + '十'
      } else {
        // 几十几
        var a = parseInt(n / 10)
        var b = n % 10

        return arr[a] + '十' + arr[b]
      }
    }
    //时间点
    function getSjd(hour) {
      let Numhour = parseInt(hour)
      if (Numhour < 4) {
        return 2
      } else if (Numhour < 8) {
        return 6
      } else if (Numhour < 12) {
        return 10
      } else if (Numhour < 16) {
        return 14
      } else if (Numhour < 20) {
        return 18
      } else if (Numhour <= 24) {
        return 22
      }
    }
    //对象数组按属性排序
    function compare(key) {
      return function (value1, value2) {
        var val1 = value1[key];
        var val2 = value2[key];
        return val1 - val2;
      }
    }
  </script>
</body>

</html>
