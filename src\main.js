/**
 * <AUTHOR>
 * @Date 2024-02-19
 * @LastEditors hybtalented
 * @LastEditTime 2024-02-29
 * @FilePath /base-wyyy-template/src/main.js
 * @Description
 * @
 * @Copyright (c) 2024 by 附一医信息处, All Rights Reserved.
 */
import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets
import ElementUI from 'element-ui'
import tools from '@/utils/tools'
import App from './App'
import store from './store'
import router from './router'

import '@/icons' // icon
import '@/permission' // permission control
import '@/formchange'
import '@/utils/broadcast'

import filters from '@/filters/timeformat'
import '@/utils/store-bridge'
import { confirmFunc, messageFunc } from './global/confirmDialog'
import { Channel } from '@/utils/broadcast'

Vue.use(filters)

Vue.prototype.confirmFunc = confirmFunc
Vue.prototype.messageFunc = messageFunc
Vue.prototype.$tools = tools

Vue.prototype.$Channel = Channel

Vue.use(ElementUI)
Vue.config.productionTip = false

store.dispatch('theme/init')

new Vue({
  el: '#app',
  router,
  store,
  render: (h) => h(App)
})
