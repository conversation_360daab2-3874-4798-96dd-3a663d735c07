import { getDefaultState } from '@/store/modules/user'
import request from '@/utils/request'

const BASEURL = '/sysmanage'

// 获取当前用户ID、姓名及专科信息
export function getLoginInfo() {
  return request({
    url: '/staff/v1/doctor/getCurDoctorInfo',
    method: 'post',
    headers: {
      verifyApp: false
    }
  })
}

// 获取电子病历用户可以操作的专科列表
export function getZhuanKeList() {
  return request({
    url: `${BASEURL}/v1/user/getClinicDeptListForEHRUser`,
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}

// 修改专科
export function changeZhuanKe(params) {
  return request({
    url: `/staff/v1/doctor/changeStaffDept`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 切换专科时记录日志
export function changeZhuanKeLog(params) {
  return request({
    url: `/app-emrservice/v1/basicInfo/insertLogsZKQH`,
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 根据id列表获取菜单参数通用属性
 *  @currUserId {query} 当前用户id
 *  @yingYongDM {query} 应用ID
 */
export const getRouters = (params) => {
  return request({
    url: `${BASEURL}/v1/roles/getRouters`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

export function getUserInfo(params) {
  return request({
    url: `${BASEURL}/v1/user/getUserById`,
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * @description: 登录用户校验
 * @param {*} data
 * @return {*}
 */
export function checkPassword(data, ms) {
  return request({
    url: `${BASEURL}/v1/user/checkPassword`,
    method: 'post',
    data,
    headers: { 'ms-sign': ms, verifyApp: false }
  })
}

/**
 * 刷新token
 * @param data
 * @param data.refresh_token :String
 * @param data.uuid :String
 * @param data.yingYongDMList :Array<string>
 * @returns {*}
 */
export const refreshToken = (data) =>
  request({
    url: `/authentication/v2/login/refreshToken`,
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })

export const getAppDetail = (data) =>
  request({
    url: `/sysmanage/v1/appsystem/getAppBycode?appcode=${getDefaultState().systemID}`,
    method: 'get',
    headers: {
      verifyApp: false
    }
  })

// 上传头像
export const uploadUserPic = (params, data) =>
  request({
    url: '/sysmanage/v1/user/uploadUserPic',
    method: 'post',
    headers: {
      'content-type': 'multipart/form-data',
      verifyApp: false
    },
    params,
    data
  })

export const getUserImgById = (params) =>
  request({
    url: '/sysmanage/v1/user-basic/getUserImgById',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })

/**
 * 登录时获取医生信息
 * @param {Object} params
 * @param {String} params.yongHuID 用户ID
 * @returns {Promise}
 */
export function getDoctorInfoByUserID(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/v1/BasicData/getDoctorInfoByUserID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
