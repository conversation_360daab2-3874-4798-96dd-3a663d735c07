<template>
  <medical-document-base
    :wen-shu-l-x="wenShuLX"
    document-tab-label="其他记录"
    document-name-label="其他记录名称"
    empty-selection-text="请选择其他记录"
    no-content-text="请从左侧选择其他记录"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import MedicalDocumentBase from '@/components/medical-record/MedicalDocumentBase.vue'

export default {
  name: 'OtherRecords',
  components: {
    MedicalDocumentBase
  },
  data() {
    return {
      wenShuLX: '29' // 其他记录的文书类型代码
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('其他记录保存成功')
    },
    handleDeleteSuccess() {
      console.log('其他记录删除成功')
    },
    handlePrintSuccess() {
      console.log('其他记录打印成功')
    }
  }
}
</script>
