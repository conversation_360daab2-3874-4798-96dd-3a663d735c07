<!-- 化验医嘱 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div style="width: 250px">
        <!-- <el-button type="primary" @click="test">测试</el-button> -->
      </div>
      <div class="table-background" style="flex-grow: 1">
        <div style="display: flex; min-height: 550px">
          <div class="table-component" style="width: 22%">
            <div class="table-title">
              <div>
                <span class="bar" />
                化验医嘱列表
              </div>
            </div>
            <div class="table-select">
              <el-select v-model="searchValue" filterable placeholder="请输入拼音首字母查找">
                <el-option
                  v-for="item in searchOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-button type="primary" class="search-button">查找</el-button>
            </div>
            <div style="max-height: 520px; overflow-y: auto">
              <template v-for="(treeItem, index) in adviceTree">
                <div
                  :key="'tree' + index"
                  class="fold-title"
                  :style="index ? { borderTop: 'none' } : {}"
                  @click="openAssayItem(treeItem)"
                >
                  {{ treeItem.yiZhuMLMC }}
                  <button
                    v-if="treeItem.children"
                    type="text"
                    @click="
                      foldState = foldState === treeItem.yiZhuMLID ? null : treeItem.yiZhuMLID
                    "
                  >
                    <i
                      :class="[
                        foldState === treeItem.yiZhuMLID
                          ? 'el-icon-arrow-up'
                          : 'el-icon-arrow-right'
                      ]"
                      class="fold-icon"
                    />
                  </button>
                </div>
                <template v-if="foldState === treeItem.yiZhuMLID">
                  <div
                    v-for="(itemChild, indexChild) in treeItem.children"
                    :key="'child' + indexChild"
                    class="fold-title fold-child"
                    @click="openAssayItem(itemChild)"
                  >
                    {{ itemChild.yiZhuMLMC }}
                  </div>
                </template>
              </template>
            </div>
          </div>
          <div class="table-component" style="margin-left: 12px; width: 35%">
            <template v-if="selectMuLu.yiZhuMLMC">
              <div class="table-title">
                <div>
                  <span class="bar" />
                  {{ selectMuLu.yiZhuMLMC }}
                </div>
              </div>

              <div style="max-height: 570px; overflow-y: auto">
                <table>
                  <thead>
                    <tr style="text-align: left">
                      <th style="width: 40px; text-align: center"></th>
                      <th style="width: 90px">控制级别</th>
                      <th style="width: 200px">化验名称</th>
                      <th>单价</th>
                      <th>样本类型</th>
                    </tr>
                  </thead>
                  <tbody>
                    <template v-for="(item, index) in assayDetailList">
                      <tr
                        :key="'assay' + index"
                        :class="[index % 2 == 1 ? 'tr-two' : 'tr-one']"
                        @click="selectAdvice(item)"
                      >
                        <td style="text-align: center">
                          <el-checkbox v-model="item.isSelect" style="pointer-events: none" />
                        </td>
                        <td>
                          <div style="display: flex; align-items: center">
                            {{ kongZhiJBDict[item.xiangMuLB[0].kongZhiJB] }}
                            <div
                              v-if="item.lianDongHao !== undefined"
                              class="round-div"
                              :style="{ backgroundColor: colorList[item.lianDongHao % 5] }"
                            />
                            <!-- {{ item.lianDongHao }} -->
                          </div>
                        </td>
                        <td>
                          <!-- 套餐项目或项目有备注显示额外信息 -->
                          <el-tooltip
                            v-if="item.shiFouTC || item.beiZhu"
                            placement="bottom"
                            effect="light"
                          >
                            <div slot="content">
                              <template v-if="item.shiFouTC">
                                【{{ item.huaYanMC }}】包括
                                <br />
                                <div
                                  v-for="(xiangMu, xiangMuIndex) in item.xiangMuLB"
                                  :key="'xiangMu' + xiangMuIndex"
                                >
                                  {{ xiangMu.xiangMuMC }} ￥{{ xiangMu.danJia }}
                                </div>
                              </template>
                              <template v-else>
                                {{ item.beiZhu }}
                              </template>
                            </div>
                            <i class="el-icon-question"></i>
                          </el-tooltip>
                          {{ item.huaYanMC }}
                        </td>
                        <td>
                          {{
                            item.xiangMuLB.reduce(
                              (accumulator, xiangMu) => accumulator + xiangMu.danJia,
                              0
                            )
                          }}
                        </td>
                        <td>
                          {{ getSampleType(item.yangBenLX) }}
                        </td>
                      </tr>
                    </template>
                  </tbody>
                </table>
              </div>
            </template>
          </div>
          <div class="table-component" style="margin-left: 12px; width: 43%">
            <div class="table-title">
              <div>
                <span class="bar" />
                已选择医嘱
              </div>
              <div>
                执行病区：
                <el-select v-model="selectBingQu" placeholder="请选择">
                  <el-option
                    v-for="item in bingQuOptions"
                    :key="item.buMenDM"
                    :label="item.buMenMC"
                    :value="item.buMenDM"
                  ></el-option>
                </el-select>
                <el-button type="primary" @click="saveAssay">保存</el-button>
                <el-button type="primary" @click="deleteAdviceList">删除</el-button>
              </div>
            </div>
            <div style="max-height: 570px; overflow: auto">
              <table style="width: 800px">
                <thead>
                  <tr style="text-align: left">
                    <th style="width: 40px; text-align: center"></th>
                    <th>目录</th>
                    <th style="width: 200px">名称(双击项目填写备注)</th>
                    <th>计划开始时间</th>
                    <th>医嘱备注</th>
                    <th>序号</th>
                    <th>首次录入时间</th>
                  </tr>
                </thead>
                <tbody>
                  <template v-for="(item, index) in selectAdviceList">
                    <tr :key="'advice' + index" :class="[index % 2 == 1 ? 'tr-two' : 'tr-one']">
                      <td style="text-align: center">
                        <el-checkbox
                          v-model="item.isCheck"
                          @change="
                            (isCheck) => {
                              //同步复选框
                              selectAdviceList.forEach((advice) => {
                                if (item.params.muLuXH === advice.params.muLuXH) {
                                  advice.isCheck = isCheck
                                }
                              })
                            }
                          "
                        />
                      </td>
                      <td>
                        {{ item.params.muLuMC }}
                      </td>
                      <td @dblclick="reviseBZ(item)">
                        <i class="el-icon-question" />
                        {{ item.params.huaYanXMMC }}
                      </td>
                      <td>
                        <el-date-picker
                          v-model="item.params.kaiShiSJ"
                          type="datetime"
                          value-format="yyyy-MM-dd hh:mm:ss"
                          placeholder="选择日期时间"
                        ></el-date-picker>
                      </td>
                      <td>
                        {{ item.params.yiZhuBZ }}
                      </td>
                      <td>
                        {{ item.params.muLuXH }}
                      </td>
                      <td>
                        <!-- {{ item.field5 }} -->
                      </td>
                    </tr>
                  </template>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div style="margin-top: 12px">
          <table>
            <thead>
              <tr style="text-align: left">
                <th>导出时间</th>
                <th>导出人员</th>
                <th>项目名称(5天内已导出医嘱)</th>
                <th>样本类型</th>
                <th>医嘱时间</th>
                <th>医嘱医师</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in yiDaoRuAdviceList">
                <tr :key="'yiDaoRu' + index" :class="[index % 2 == 1 ? 'tr-two' : 'tr-one']">
                  <td>
                    {{ item.daoChuSJ }}
                  </td>
                  <td>
                    {{ item.daoChuRYID }}
                  </td>
                  <td>
                    {{ item.huaYanXMMC }}
                  </td>
                  <td>
                    {{ getSampleType(item.yangBenLX) }}
                  </td>
                  <td>
                    {{ item.yiZhuSJ }}
                  </td>
                  <td>
                    <!-- {{ item.field6 }} -->
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <remarks-dialog :dialog-ref="dialogStore['remarks']" />
    <qi-xue-fen-xi-remarks-dialog :dialog-ref="dialogStore['qiXueFenXiRemarks']" />
    <nei-sheng-ji-gan-remarks-dialog :dialog-ref="dialogStore['neiShengJiGanRemarks']" />
    <xing-xian-ji-su-remarks-dialog :dialog-ref="dialogStore['xingXianJiSuRemarks']" />

    <el-dialog :visible.sync="dialogStore['message'].visible" width="350px">
      <span slot="title">
        <span style="font-size: 16px">
          <i class="el-icon-question"></i>
          信息窗口
        </span>
      </span>
      <div class="dialog-component">
        {{ dialogStore['message'].data }}
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogStore['message'].resolve(true)">确 认</el-button>
        <el-button @click="dialogStore['message'].resolve(false)">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTemplateDetailByMBID,
  getAssayAllCatalogTree,
  getItemsByCatalogId,
  getSampleTypeDescribeData,
  saveAssayInpatientAdvice,
  getWeiDaoRuAssayAdvice,
  getYiDaoRuAssayAdvice,
  deleteByYzidList,
  getAssayInpatientExecuteDeptList,
  checkHytcRepeatItem
} from '@/api/doctors-advice'
import module from '@/store/modules/theme'
import RemarksDialog from './components/RemarksDialog.vue'
import QiXueFenXiRemarksDialog from './components/QiXueFenXiRemarksDialog.vue'
import NeiShengJiGanRemarksDialog from './components/NeiShengJiGanRemarksDialog.vue'
import XingXianJiSuRemarksDialog from './components/XingXianJiSuRemarksDialog.vue'
import { deepClone } from '@/utils'
import { mapState } from 'vuex'

class DialogRef {
  constructor() {
    this.visible = false
    this.data = null
    this.resolve = () => {}
    this.reject = () => {}
  }
  async run(data = '') {
    const result = await new Promise((resolve, reject) => {
      this.data = data
      this.visible = true
      this.resolve = resolve
      this.reject = reject
    })
    this.visible = false
    return result
  }
} //弹窗同步控制器类

export default {
  name: 'LaboratoryMedicalAdvice',
  components: {
    RemarksDialog,
    QiXueFenXiRemarksDialog,
    NeiShengJiGanRemarksDialog,
    XingXianJiSuRemarksDialog
  },
  data() {
    return {
      searchValue: '',
      searchOptions: [],
      selectBingQu: '', //选择病区
      bingQuOptions: [], //病区选项列表
      foldState: null, //控制打开目录
      adviceTree: [], //化验医嘱目录树
      assayDetailList: [], //化验医嘱项目列表
      selectMuLu: {}, //当前化验医嘱项目列表目录
      selectAdviceList: [], //已选择化验医嘱列表
      yiDaoRuAdviceList: [],
      kongZhiJBDict: ['自', '甲', '乙'],
      colorList: ['#356ac5', '#BC0F5E', '#529C91', '#5DD2CF', '#428B23'],
      sampleType: [],
      muLuXH: 0, //目录序号
      dialogStore: {
        message: new DialogRef(),
        remarks: new DialogRef(),
        qiXueFenXiRemarks: new DialogRef(),
        neiShengJiGanRemarks: new DialogRef(),
        xingXianJiSuRemarks: new DialogRef()
      }
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo,
      initInfo: ({ patient }) => patient.initInfo,
      bingQuID: ({ patient }) => patient.bingQuID,
      zhiLiaoZuID: ({ patient }) => patient.zhiLiaoZuID,
      patientInit: ({ patient }) => patient.patientInit
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    async initPage() {
      // console.log('测试数据', {
      //   initInfo: this.initInfo,
      //   bingQuID: this.bingQuID,
      //   zhiLiaoZuID: this.zhiLiaoZuID,
      //   patientInit: this.patientInit
      // })
      // return
      const res1 = await getAssayInpatientExecuteDeptList()

      console.log('获取执行病区', res1)
      if (res1.hasError === 0) {
        this.selectBingQu = this.bingQuID
        this.bingQuOptions = [{ buMenDM: this.bingQuID, buMenMC: '病人当前病区' }, ...res1.data]
      }

      const res2 = await getAssayAllCatalogTree({
        jiuZhenZKID: 39,
        zhenLiaoLX: 11
      })

      console.log('获取检验医嘱目录树', res2)
      if (res2.hasError === 0) {
        this.adviceTree = res2.data[0].children
      }

      const res3 = await getSampleTypeDescribeData()

      console.log('获取样本类型', res3)
      if (res3.hasError === 0) {
        this.sampleType = res3.data
      }

      const res4 = await getYiDaoRuAssayAdvice({
        bingLiID: this.bingLiID
      })

      console.log('获取近期导出数据', res4)
      if (res4.hasError === 0) {
        this.yiDaoRuAdviceList = res4.data
      }

      this.getAdviceList()
    },
    async openAssayItem(data) {
      console.log(data)
      // this.selectAdviceList = []
      this.selectMuLu = data
      //判断子菜单并排除模板根节点
      if (!data.children && data.yiZhuMLID > 0) {
        let res1 = null
        let template = null
        //判断是否模板项目
        if (data.fuYiZhuMLID > -1) {
          res1 = await getItemsByCatalogId({
            yiZhuMLID: data.yiZhuMLID,
            yuanQuDM: this.userInfo.yuanQuDM
          })
          template = res1.data
        } else {
          res1 = await getTemplateDetailByMBID({
            moBanID: data.yiZhuMLID,
            yuanQuDM: this.userInfo.yuanQuDM
          })
          template = res1.data.moBanMX
        }

        console.log('模板项目', template)
        if (res1.hasError === 0) {
          // this.adviceTree = res.data
          this.assayDetailList = template.map((item) => {
            return {
              ...item,
              muLuDetails: data,
              isSelect: false,
              isCheck: false
              // detail: res2.data[0]
            }
          })
          this.syncAssaySelect()
        }
      }
    },

    getSampleType(yangBenLX) {
      const item = this.sampleType.find((item) => {
        return item.daiMa === yangBenLX
      })
      if (item) {
        return item.mingCheng
      }
      return ''
    },

    //医嘱项目添加入右侧列表
    async addAdvice(advice) {
      const item = deepClone(advice)
      this.selectAdviceList.push({
        isCheck: false,
        lianDongHao: item.lianDongHao,
        params: {
          yiZhuID: null,
          bingLiID: this.bingLiID,
          bingQuID: this.selectBingQu,
          zhuanKeID: this.initInfo.zhuanKeID,
          bingRenXM: this.patientInit.bingRenXM,
          yiZhuLBDM: '09',
          shiFouHB: '1',
          muLuID: item.muLuDetails.yiZhuMLID,
          muLuMC: item.muLuDetails.yiZhuMLMC,
          muLuXH: this.muLuXH,
          huaYanXMID: item.huaYanID,
          huaYanXMMC: item.huaYanMC,
          yiZhuJB: item.xiangMuLB[0].kongZhiJB, //医嘱级别
          yiZhuBZ: item.yiZhuBZ,
          shouFeiZHID: item.xiangMuLB[0].shouFeiZHID,
          shouFeiXMID: item.xiangMuLB[0].shouFeiXMID,
          yangBenLX: item.xiangMuLB[0].yangBenLX,
          shiFouZF: null,
          buMenID: this.initInfo.buMenID,
          zhuangTaiBZ: item.xiangMuLB[0].zhuangTaiBZ,
          yiZhuYHID: this.initInfo.yiShiYHID,
          shouCiLRSJ: '2025-01-20 15:28:51',
          xiuGaiYHID: null,
          yiZhuSJ: '2025-01-20 15:29:57',
          zhiLiaoZuID: this.zhiLiaoZuID,
          kaiShiZXSJ: null,
          danJia: item.xiangMuLB[0].danJia,
          shiFouTC: item.shiFouTC
        }
      })
    },

    //编辑特殊备注 普通备注返回null
    async editRemarks(huaYanMC, data = '') {
      if (huaYanMC.indexOf('血气分析') >= 0 || huaYanMC.indexOf('全血乳酸测定(静脉血气)') >= 0) {
        return await this.dialogStore['qiXueFenXiRemarks'].run(data)
      } else if (huaYanMC.indexOf('内生肌酐') >= 0) {
        return await this.dialogStore['neiShengJiGanRemarks'].run(data)
      } else if (huaYanMC.indexOf('生殖激素常规检查') >= 0) {
        return await this.dialogStore['xingXianJiSuRemarks'].run(data)
      }
      return null
    },

    async selectAdvice(advice) {
      console.log('选择医嘱', advice)

      //弹窗-套餐存在重复
      if (advice.shiFouTC === 1) {
        const res = await checkHytcRepeatItem({
          huaYanID: advice.huaYanID,
          huaYanIds: this.selectAdviceList.map((item) => {
            return item.params.huaYanXMID
          })
        })
        if (
          res.hasError === 0 &&
          res.data &&
          !(await this.dialogStore['message'].run(`${res.data}，是否仍需添加？`))
        )
          return
      }

      //弹窗-重复添加
      if (
        advice.isSelect === true &&
        !(await this.dialogStore['message'].run(`项目【${advice.huaYanMC}】已开，是否仍需添加？`))
      )
        return

      if (advice.lianDongHao === undefined) {
        let deepAdvice = deepClone(advice)
        deepAdvice.yiZhuBZ = (await this.editRemarks(deepAdvice.huaYanMC)) || ''
        await this.addAdvice(deepAdvice)
      } else {
        let remarksFlag = false //记录是否弹出过备注窗口
        const addAdviceList = []

        //倒序遍历保证末尾备注
        for (let i = this.assayDetailList.length - 1; i > -1; i--) {
          if (advice.lianDongHao === this.assayDetailList[i].lianDongHao) {
            let deepAdvice = deepClone(this.assayDetailList[i])
            //弹窗输入特殊备注
            let remarksValue = remarksValue ? '' : await this.editRemarks(deepAdvice.huaYanMC)
            deepAdvice.yiZhuBZ = remarksValue || ''
            if (remarksValue !== null) remarksFlag = true
            addAdviceList.push(deepAdvice)
          }
        }

        for (let i = addAdviceList.length - 1; i > -1; i--) {
          await this.addAdvice(addAdviceList[i])
        }
      }
      this.syncAssaySelect()
    },

    //根据已选择医嘱列表 同步化验医嘱复选框与当前序号
    syncAssaySelect() {
      this.assayDetailList.forEach((assay) => {
        assay.isSelect = false
        let XHMax = 0
        this.selectAdviceList.forEach((advice) => {
          if (assay.huaYanID === advice.params.huaYanXMID) {
            assay.isSelect = true
          }
          if (advice.params.muLuXH > XHMax) {
            XHMax = advice.params.muLuXH
          }
        })
        this.muLuXH = XHMax + 1
      })
    },

    //保存医嘱
    async saveAssay() {
      if (
        this.selectBingQu !== this.bingQuID &&
        !(await this.dialogStore['message'].run(
          `医嘱开到的病区与病人当前病区不一致，是否继续保存?`
        ))
      )
        return

      const huaYanYZLB = this.selectAdviceList.map((item) => {
        item.params.bingQuID = this.selectBingQu
        return item.params
      })

      const res = await saveAssayInpatientAdvice({
        bingLiID: this.bingLiID,
        buMenID: this.initInfo.buMenID,
        huaYanYZLB,
        yuanQuDM: this.userInfo.yuanQuDM,
        zhuanKeID: this.initInfo.zhuanKeID,
        zhiLiaoZuID: this.zhiLiaoZuID
      })

      console.log('保存住院化验医嘱', res)
      if (res.hasError === 0) {
        this.getAdviceList()
        this.$message({
          message: '保存成功',
          type: 'success',
          duration: 700
        })
      }
    },

    //获取未导入医嘱列表
    async getAdviceList() {
      const res = await getWeiDaoRuAssayAdvice({
        bingLiID: this.bingLiID
      })

      if (res.hasError === 0) {
        this.selectAdviceList = res.data.map((item) => {
          return {
            isCheck: false,
            params: item
          }
        })
        this.syncAssaySelect()
      }
    },
    //删除医嘱
    async deleteAdviceList() {
      const newList = []
      const saveAdviceList = [] //选中的已保存医嘱列表
      this.selectAdviceList.forEach((advice) => {
        if (advice.isCheck === false) {
          newList.push(advice)
        } else if (advice.params.yiZhuID !== null) {
          saveAdviceList.push(advice.params.yiZhuID)
        }
      })
      if (saveAdviceList.length) {
        if (await this.dialogStore['message'].run(`包含已保存医嘱，是否删除？`)) {
          const res = await deleteByYzidList({ yiZhuIdList: saveAdviceList })
          if (res.hasError !== 0) {
            return
          }
        } else {
          return
        }
      }
      this.selectAdviceList = newList
      this.syncAssaySelect()
      this.$message({
        message: '删除成功',
        type: 'success',
        duration: 700
      })
    },

    //修改备注
    async reviseBZ(advice) {
      const adviceGroup = this.selectAdviceList.filter((item) => {
        return advice.params.muLuXH === item.params.muLuXH
      })
      //判断该项为同组最后一项时，启用特殊备注弹窗
      let newData =
        adviceGroup[adviceGroup.length - 1].params.huaYanXMID === advice.params.huaYanXMID
          ? await this.editRemarks(advice.params.huaYanXMMC, advice.params.yiZhuBZ)
          : null
      if (newData === null) {
        newData = await this.dialogStore['remarks'].run(advice.params.yiZhuBZ)
      }

      if (newData !== false) {
        advice.params.yiZhuBZ = newData
      }
    },

    async test(a) {
      console.log('测试', this.patientInit, this.initInfo)
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  overflow-y: auto;
  .system-page {
    .table-component {
      padding: 12px;
      background-color: #eaf0f9;
      border-radius: 4px;
      border: 1px solid #ddd;
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        margin-bottom: 10px;
        .bar {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
        .sub-title {
          font-weight: 400;
        }
      }
      .table-select {
        margin-bottom: 12px;
        .el-select {
          width: calc(100% - 60px);
        }
      }
      .round-div {
        width: 14px;
        height: 14px;
        border-radius: 7px;
        margin-left: 6px;
      }
      button.search-button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover).search-button {
        background: #a66dd4;
        border-color: #a66dd4;
      }
      :deep(.el-button--primary:focus).search-button {
        background: #ce8be0;
        border-color: #ce8be0;
      }

      .fold-title {
        display: flex;
        justify-content: space-between;
        padding: 10px;
        background-color: #f6f6f6;
        border: 1px solid #ddd;
        button {
          background: none;
        }
      }
      .fold-child {
        background-color: #eff3fb;
        border-top: none;
      }
      .fold-icon {
        margin-right: 3px;
        color: #8590b3;
        font-size: 12px;
      }
    }
    .table-background {
      border-radius: 2px;
    }

    table {
      margin-top: 10px;
      width: 100%;
    }
    th,
    td {
      border: 1px solid #ddd;
      border-collapse: collapse; /* 移除表格内边框间的间隙 */
      height: 35px;
      padding: 5px 8px;
    }
    .tr-one {
      background-color: #f6f6f6;
    }
    th,
    .tr-two {
      background-color: #eaf0f9;
    }
  }

  :deep(.el-dialog__footer) {
    border-top: none;
  }

  .el-icon-question {
    font-size: 14px;
    color: #356ac5;
  }
  .dialog-component {
    padding-left: 30px;
    color: #96999e;
    a {
      color: #356ac5;
    }
  }
}
</style>
