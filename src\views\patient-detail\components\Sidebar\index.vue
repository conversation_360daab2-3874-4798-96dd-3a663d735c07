<template>
  <div class="side-container">
    <el-scrollbar>
      <el-menu ref="menuRef" :collapse-transition="false">
        <sidebar-item
          v-for="(route, index) in sidebarMenu"
          :key="index"
          :item="route"
          :base-path="route.path"
          :item-click-native-val="itemClickNativeVal"
          @itemClickNative="itemClickNative"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import SidebarItem from './components/SidebarItem'
import { menuClickLog } from '@/api/log'
import store from '@/store'
import { EventBus } from '@/utils/event-bus'
import { isMyPatient, joinOrRemoveMyPatient } from '@/api/patient'

export default {
  name: 'Sidebar',
  components: { SidebarItem },
  data() {
    return {
      itemClickNativeVal: '',
      sidebarMenu: [],
      isLoadingPatient: false // 防止重复加载标志
    }
  },

  computed: {
    ...mapGetters(['permission_routes', 'sidebar', 'sidebarRouters']),
    ...mapState({
      yongHuID: ({ user }) => user.yongHuID,
      moduleName() {
        return `patientDetail/${this.bingLiID}`
      }
    }),
    patientInfo() {
      if (this.$store.hasModule(this.moduleName)) {
        return this.$store.state[this.moduleName]?.patientInit || {}
      }
      return {}
    },
    patientDetailSiderBarRouter() {
      const indexRouter = this.sidebarRouters.filter((f) => f.path === '/patient-inside')
      return indexRouter[0].children
    },
    bingLiID() {
      return this.$route.params.id
    }
  },
  watch: {
    // activeMenu(val) {
    //   const title = val === this.$route.path ? this.$route.meta.title : ''
    //   const data = {
    //     appKey: getDefaultState().app_key,
    //     xiangXiMS: title,
    //     shuJuLX: 1,
    //     caoZuoLX: 4
    //   }
    //   addWebAuditLog(data)
    // }
  },
  activated() {
    // this.isMyPatient()
  },
  mounted() {
    this.isMyPatient()

    // 使用带病人ID的事件名
    EventBus.$on(`activeRoute_${this.bingLiID}`, this.handleActiveRouteChange)
  },
  beforeDestroy() {
    // 确保移除正确的事件监听
    EventBus.$off(`activeRoute_${this.bingLiID}`, this.handleActiveRouteChange)
    // 重置加载状态
    this.isLoadingPatient = false
  },
  methods: {
    toggleSideBar() {
      store.dispatch('app/toggleSideBar')
    },
    async itemClickNative(item) {
      console.log(item, this.patientDetailSiderBarRouter, '这里是itemClickNative')
      if (item.caiDanLX === 'F') {
        //按钮类型的操作
        switch (item.name) {
          case 'Join-mypatient': //加入我的病人
            joinOrRemoveMyPatient({
              bingLiID: this.bingLiID,
              leiBie: item.meta.title === '加入我的病人' ? 1 : 0
            }).then((res) => {
              if (res.hasError === 0) {
                this.$message.success('操作成功')
                this.isMyPatient()
              }
            })
            break
          case 'Join-day-surgery': //加入日间手术
            this.isJoinDaySurgery()
            break
          case 'supervisory-control': //院感监控
            window.open(
              `http://172.16.200.88/nis/cdc?userid=${WRT_config.BrMainLeft.YHXX.YSYHID}&patientid=${WRT_config.BrMainLeft.BRJBXX.ZYH}`
            )
            break
          case 'patient360': //患者360
            let url = `http://172.16.203.141:8080/winsso/c/${WRT_config.BrMainLeft.YHXX.YSYHID}/${
              WRT_config.BrMainLeft.BRJBXX.SFZH || ''
            }/2/${WRT_config.BrMainLeft.BRJBXX.BRBH}/${WRT_config.BrMainLeft.BRJBXX.BLID}/0/0/${
              WRT_config.BrMainLeft.BRJBXX.BRBH
            }/-1/47000592-2/0/63/zyysclient`
            window.open(url)
            break
          case 'Discharge': //预出院
            console.log(this.users)
            break
        }
      } else {
        this.itemClickNativeVal = item.name
        await this.$store.dispatch(`${this.moduleName}/setActiveMenuItem`, item)
        await this.$store.dispatch('patient/setSideBarRoute', item.name)
      }
      // 记录菜单点击
      if (item?.meta?.caiDanID) {
        menuClickLog({
          caiDanID: item?.meta?.caiDanID
        })
      }
    },
    // 判断是否我的病人
    async isMyPatient() {
      // 防止重复调用
      if (this.isLoadingPatient) {
        return
      }
      this.isLoadingPatient = true

      try {
        if (this.$store.hasModule(this.moduleName)) {
          await this.$store.dispatch(`${this.moduleName}/getPatientInit`, this.bingLiID)
        }
        //判断是否加入临床路径
        this.isJoinClincalPath()
        this.sidebarMenu = [].concat(this.patientDetailSiderBarRouter)

        const res = await isMyPatient({ bingLiID: this.bingLiID, yongHuID: this.yongHuID })
        if (res.hasError === 0) {
          this.changeMenuTitle(
            this.sidebarMenu,
            'Join-mypatient',
            res.data ? '移除我的病人' : '加入我的病人'
          )
        }
      } catch (error) {
        console.error('加载病人信息失败:', error)
      } finally {
        this.isLoadingPatient = false
      }
    },
    // 判断是否加入日间手术
    isJoinDaySurgery() {
      this.sidebarMenu = [].concat(this.patientDetailSiderBarRouter)
      for (let item of this.sidebarMenu) {
        if (item.children && item.children.length > 0) {
          item.children.map(function (key, index) {
            if (key.name === 'Join-day-surgery') {
              item.children.splice(index, 1)
            }
          })
        }
      }
      return false
    },
    // 判断是否加入临床路径
    isJoinClincalPath() {
      this.sidebarMenu = [].concat(this.patientDetailSiderBarRouter)
      let title = ''
      if (this.patientInfo.specialSign && this.patientInfo.specialSign['临床路径'] === '1') {
        title = 'Join-Clinical-Path'
      } else {
        title = 'Clinical-Path'
      }
      for (let item of this.sidebarMenu) {
        if (item.children && item.children.length > 0) {
          item.children.map(function (key, index) {
            if (key.name === title) {
              item.children.splice(index, 1)
            }
          })
        }
      }
      return false
    },
    //更新菜单栏
    changeMenuTitle(menu, name, newTitle) {
      for (let item of menu) {
        if (item.name === name) {
          item.meta.title = newTitle
          return true
        }
        if (item.children && item.children.length > 0) {
          const found = this.changeMenuTitle(item.children, name, newTitle)
          if (found) return true
        }
      }
      return false
    },
    //删除菜单栏
    deleteMenuTitle(menu, name) {
      menu.map((item, index) => {
        if (item.name === name) {
          menu.splice(index, 1)
          return true
        }
        if (item.children && item.children.length > 0) {
          const found = this.changeMenuTitle(item.children, name)
          if (found) return true
        }
      })
      return false
    },
    // 将事件处理函数定义为方法，避免匿名函数导致无法正确解绑
    handleActiveRouteChange(val) {
      if (this.$refs.menuRef) {
        this.$refs.menuRef.activeIndex = val
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.side-container {
  .el-scrollbar {
    height: calc(100% + 7px) !important;
  }

  .change-icon {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 40px;
    margin-left: 25px;
    font-size: 14px;
    line-height: 100%;
    color: #fff;
    text-align: center;
    cursor: pointer;

    .icon_box {
      width: 14px;
      height: 14px;
      font-size: 14px;
    }
  }

  .el-scrollbar {
    height: calc(100% - 40px);
  }
}

.el-menu {
  margin-top: 10px;
}
</style>
