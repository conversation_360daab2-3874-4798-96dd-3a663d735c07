import request from '@/utils/request'

/**
 * 根据病历ID获取已书写手术记录列表
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @returns {Promise}
 */
export function getAllYiShuXieShouShuJiLuByBLID(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/getAllYiShuXieShouShuJiLuByBLID',
    method: 'post',
    params: params
  })
}

/**
 * 根据专科ID获取已书写手术记录列表
 * @param {Object} params 参数对象
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */
export function getKeShuXieShouShuJLListByzhuanKeID(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/getKeShuXieShouShuJLListByzhuanKeID',
    method: 'post',
    params: params
  })
}

/**
 * 新增手术记录时候判断是否需要绑定手术通知单
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.geShiDM 格式代码
 * @param {string} params.shouShuCX 手术次序
 * @returns {Promise}
 */
export function checkTZDBind(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/checkTZDBind',
    method: 'get',
    params: params
  })
}

/**
 * 根据文书类型获取病人文书列表
 * @param {Object} params 参数对象
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export function getWenShuGeShiListByWslx(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/getWenShuGeShiListByWslx',
    method: 'get',
    params: params
  })
}

/**
 * 查询_根据病历ID获取所有手术
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @returns {Promise}
 */
export function getShouShuTZDByBLID(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/getShouShuTZDByBLID',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}
