<!-- 病案首页多值表维护 -->
<template>
  <div class="system-canvas">
    <div class="system-multivalued">
      <div class="table-select">
        <el-select v-model="selectTable" placeholder="请选择">
          <el-option
            v-for="item in tableOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button type="primary">查询</el-button>
      </div>
      <div class="table-background">
        <div class="table-title">
          <div>
            <span />
            病案首页多值表维护
          </div>
          <el-button type="primary">新增</el-button>
        </div>
        <div class="table-component">
          <table>
            <thead>
              <tr style="text-align: left">
                <th>代码</th>
                <th>名称</th>
                <th>排序</th>
                <th style="width: 70px">状态</th>
                <th style="width: 70px; text-align: center">操作</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in multivaluedList">
                <tr :key="index" :class="[index % 2 == 1 ? 'tr-two' : 'tr-one']">
                  <td>
                    {{ item.field1 }}
                  </td>
                  <td>
                    {{ item.field2 }}
                  </td>
                  <td>
                    {{ item.field3 }}
                  </td>
                  <td>
                    <el-tag v-if="item.field4">启用</el-tag>
                    <el-tag v-else type="danger">停用</el-tag>
                  </td>
                  <td style="text-align: center">
                    <el-button type="text" @click="openEditDrawer(item)">编辑</el-button>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <multivalued-drawer
      :visible.sync="multivaluedVisible"
      :multivalued-data.sync="multivaluedData"
    />
  </div>
</template>

<script>
import MultivaluedDrawer from '@/views/system-maintenance/multivalued/components/MultivaluedDrawer.vue'
export default {
  name: 'SystemMultivalued',
  components: {
    MultivaluedDrawer
  },
  data() {
    return {
      multivaluedVisible: false,
      multivaluedData: null,
      selectTable: 0,
      tableOptions: [
        {
          value: 0,
          label: '病案首页目录'
        },
        {
          value: 1,
          label: '测试选项'
        }
      ],
      multivaluedList: [
        {
          field1: 'BASY',
          field2: '病案首页目录',
          field3: 1,
          field4: true
        },
        {
          field1: 'SSWY',
          field2: '损伤中毒的外部原因',
          field3: 2,
          field4: true
        },
        {
          field1: 'HYZK',
          field2: '婚姻状态',
          field3: 3,
          field4: true
        },
        {
          field1: 'RWGX',
          field2: '联系人关系',
          field3: 4,
          field4: true
        },
        {
          field1: 'ZYDM',
          field2: '职业代码',
          field3: 5,
          field4: true
        },
        {
          field1: 'QJJL',
          field2: '抢救记录',
          field3: 6,
          field4: false
        },
        {
          field1: 'HXJ',
          field2: '呼吸机使用',
          field3: 7,
          field4: true
        },
        {
          field1: 'HXJ',
          field2: '入住ICU情况',
          field3: 8,
          field4: true
        }
      ]
    }
  },
  methods: {
    openEditDrawer(data) {
      this.multivaluedData = data
      this.multivaluedVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-multivalued {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .table-select {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        width: 670px;
        span {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        margin-top: 10px;
        width: 670px;
        table {
          margin-top: 10px;
          width: 100%;
        }
        th,
        td {
          border: 1px solid #ddd;
          border-collapse: collapse; /* 移除表格内边框间的间隙 */
          height: 35px;
          padding: 5px 8px;
        }
        .tr-one {
          background-color: #f6f6f6;
        }
        th,
        .tr-two {
          background-color: #eff3fb;
        }
        .el-tag {
          border-radius: 2px;
        }
      }
    }
  }
}
</style>
