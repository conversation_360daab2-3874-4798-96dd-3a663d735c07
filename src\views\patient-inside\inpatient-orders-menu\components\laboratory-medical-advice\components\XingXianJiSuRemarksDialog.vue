<template>
  <el-dialog :visible="dialogRef.visible" width="600px" :show-close="false" @open="initFormData()">
    <span slot="title">
      <span class="dialog-title">
        <i class="el-icon-menu"></i>
        化验备注
      </span>
    </span>
    <div v-if="formData" class="dialog-component">
      需要以下参数,请填写!
      <div class="dialog-input">
        <el-form ref="formData" :model="formData" :rules="rules">
          <el-form-item label="预期抽血的生理周期: " prop="SG">
            <el-radio-group v-model="formData.ZQ">
              <el-radio label="卵泡期">卵泡期</el-radio>
              <el-radio label="排卵期">排卵期</el-radio>
              <el-radio label="黄体期">黄体期</el-radio>
              <el-radio label="绝经期">绝经期</el-radio>
              <el-radio label="无">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm()">确 认</el-button>
      <el-button v-if="dialogRef.data !== ''" @click="dialogRef.resolve(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { deepClone } from '@/utils'

export default {
  name: 'RemarksDialog',
  props: {
    dialogRef: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: null,
      rules: {
        ZQ: [{ required: true, message: '请选择生理周期', trigger: 'blur' }]
      }
    }
  },
  methods: {
    initFormData() {
      this.formData = {
        ZQ: '' //预期抽血的生理周期
      }
      if (this.dialogRef.data) {
        let yzbzAll = this.dialogRef.data.split(' ')
        let fjbz = []
        yzbzAll.map((e) => {
          const item = e.split(':')
          if (item[0] === '预期抽血的生理周期') {
            this.formData.ZQ = item[1]
          }
        })
      }
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.dialogRef.resolve(`预期抽血的生理周期:${this.formData.ZQ}`)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-title {
  font-size: 16px;
}
.dialog-component {
  padding: 0 30px;
}
.dialog-input {
  margin-top: 10px;
  .el-input {
    width: 65%;
    margin-left: 10px;
  }
}

:deep(.el-dialog__footer) {
  border-top: none;
}
</style>
