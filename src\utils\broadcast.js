/**此版本使用sessionStorge->vuex模式,广播功能需控制 */

import store from '@/store'
import { MessageBox } from 'element-ui'
import router from '@/router'
import { getYongHuID, clearAllUserAuth } from '@/utils/auth'

let message

export const Channel = window.BroadcastChannel && new window.BroadcastChannel('user-logout')

Channel &&
  Channel.addEventListener('message', (e) => {
    console.log('includes', location.href.includes('/web/auth/#/login'))
    if (location.href.includes('/web/auth/#/login')) {
      return
    }
    if (
      e.data?.logout &&
      e.data?.paramObj?.yongHuID === getYongHuID() &&
      e.data?.paramObj?.yingYongDM === store.state.user.systemID
    ) {
      console.error('更换用户')
      !message &&
        (message = MessageBox.confirm('已退出登录', '警告', {
          closeOnClickModal: false,
          showClose: false,
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning',
          customClass: 'broadcast-msg-wrapper'
        })
          .then(() => {
            store.dispatch('user/logOut')
            message = undefined
            clearMessageBoxZIndex()
            clearAllUserAuth() // 重置授权信息
            store.commit('RESET_STATE') // 重置个人信息
          })
          .catch(() => {
            router.push('/home')
            message = undefined
            clearMessageBoxZIndex()
          }))
      const messageBox = document.querySelector('.broadcast-msg-wrapper').parentElement
      // 清除层级覆盖
      const clearMessageBoxZIndex = function () {
        if (messageBox) {
          messageBox.style.cssText = 'z-index:2004'
        }
      }
      setTimeout(() => {
        messageBox.style.cssText = 'z-index:99999'
      })
    }
  })
