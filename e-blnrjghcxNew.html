<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<title>病历内容结构化查询(新)</title>
	<meta name="description" content="病历内容结构化查询(新)">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
	<link rel="icon" href="favicon.ico" type="image/x-icon">
	<!-- 公用模块CSS -->
	<!-- <link rel="stylesheet" href="css/e-common.css"> -->
	<script type="text/javascript">
		document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
	</script>
	<!-- 页面CSS -->
	<!-- <link rel="stylesheet" href="css/e-style.css"> -->
	<script type="text/javascript">
		document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
	</script>
</head>
<style>
	#blnrjghcxNew {
		position: relative;
		font-size: 16px;
		padding: 10px 5px 10px;
		width: 80%;
		margin: 0 auto;
		/* border: 1px solid #000; */
	}
	#topNow {
		position: relative;
	}
	.topLineNow {
		display: flex;
    align-items: center;
    justify-content: space-between;
		border-bottom: 1px solid #ccc;
	}
	.topInput {
		position: relative;
		padding: 5px 0 15px;
		/* border-bottom: 1px solid #ccc; */
		display: flex;
	}
	.btnCz {
		position: relative;
		float: left;
		display: flex;
		/* bottom: 4px; */
		justify-content: flex-start;
		/* padding: 20px 0 25px; */
		padding: 5px 0 15px;
	}

	.group_titleBtn,.group_titleBtn:hover {
		border: 1px solid rgba(24, 144, 255, 0.2);
		background-color: rgba(24, 144, 255, 0.2);
		color: #5e5e5e;
		font-size: 15px;
		padding: 5px 10px;
		border-radius: 4px;
		width: 140px;
		margin: 5px;
		text-align: center;
		cursor: pointer;
	}

	.group_titleBtn_active,.group_titleBtn_active:hover {
		width: 140px;
		margin: 5px;
		border: 1px solid #096dd9;
		background-color: #096dd9;
		color: #fff;
		font-size: 15px;
		padding: 5px 10px;
		border-radius: 4px;
		margin: 5px;
		text-align: center;
		cursor: pointer;
	}
	.flex-line {
		display: flex;
    align-items: flex-end;
    justify-content: flex-start;
	}
	.label-name {
		font-weight: 800;
	}
	.flex-select {
		position: relative;
		padding: 0 15px 0 10px;
		display: flex;
    align-items: center;
    justify-content: space-between;
	}
	.ensureWsAZk {
		cursor: pointer;
	}

	#bottomShow {
		position: relative;
		width: 100%;
		display: flex;
    flex-wrap: nowrap;
		margin-top: 25px;
		/* height: 100vh; */
	}
	.showLeft {
		position: relative;
		width: 20%;
		min-width: 230px;
		/* height: 50vh; */
		min-height: 80vh;
		border-right: 1px solid #ccc;
	}
	.showRight {
		position: relative;
		width: 80%;

		min-height: 80vh;
		/* border: 1px solid rgb(68, 73, 125); */
	}
	.Ltitle, .Rtitle {
		position: relative;
		padding: 0 16px;
		font-size: 18px;
    border-left: 3px solid #155bd4;
    height: 18px;
    line-height: 14px;
	}
	.Rtitle {
		margin: 0 20px;
	}
	.lData {
		position: relative;
		padding: 30px 40px;
	}
	.list_name {
		position: relative;
		padding-bottom: 5px;
		cursor: pointer;
	}
	
	.list_name:hover {
		color: #155bd4;
		/* color: #4b80dd; */
	}
	.btnlist {
		color: #5e5e5e;
		text-decoration: none;
	}
	.RInputForm {
		position: relative;
		/* border-bottom: 1px solid #ccc; */
	}
	.qCondition {
		position: relative;
		display: flex;
		/* width: 98%; */
		width: calc(100% - 10px);
    margin: auto;
    border-bottom: 1px solid #ccc;
		justify-content: space-between;
	}
/* 查询条件 */
	.inputSelect {
		position: relative;
		width: 80%;
		padding: 35px 0px 35px 30px;
		/* border: 1px solid red */
	}
	.initCondition {
		position: relative;
		/* margin-left: 20px; */
		padding-bottom: 10px;
		width:100%;
		display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: baseline;
	}
	.control_cyrq {
		position: relative;
		display: flex;
    flex-direction: row;
    align-items: center;
	}
	.control_ryzd {
		width: calc(100% - 570px);
	}
	/* 条件 */
	.addCondition {
		position: relative;
		width: 100%;
	}
	/* 新增行 */
	.addLine {
		position: relative;
		padding-bottom: 10px;
		display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: baseline;
	}
	/* 按钮 */
	.allBtn {
		position: relative;
		width: 20%;
		padding: 20px 0px;
		/* display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-end; */
	}
	.btnLine {
		position: relative;
		padding: 5px 0 10px;
    float: right;
		display: flex;
    flex-direction: row;
		/* display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center; */
	}
	.btnLine button {
		position: relative;
		width: 80px;
    margin: 0 5px 0 5px;
    font-size: 16px;
    line-height: 32px;
	}
	/* .searchT, .reset {
		display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 8px;
	} */
	.Rtable{
		position: relative;
		width: 100%;
		margin: 0 auto;
		padding: 50px 0;
	}
	
	/* 表格 */
	.overall_table_frame {
		position: relative;
		width: calc(100% - 40px);
		border: 1px solid #B7C3DA;
		margin: 0 auto;
		/* margin-bottom: 10px; */
	}
	.overall_table_frame> table {
		position: relative;
		width: 100%;
	}
	#table_internal {
		position: relative;
		border: 1px solid #B7C3DA;
	}
	.row_head >th{
		/* background: #85A2DB; */
		background: #f4f6fe;
		/* color: #FFFFFF; */
		color: #000;
		text-align: center;
		font-family: Microsoft YaHei;
		font-size: 18px;
		font-style: normal;
		font-weight: 400;
		line-height: 40px;
		letter-spacing: 0em;
	}
	.row_nr >td {
		text-align: center;
		font-family: Microsoft YaHei;
		font-size: 16px;
		font-style: normal;
		font-weight: 400;
		line-height: 36px;
		letter-spacing: 0em;
	}
	
	.btnCz {
		position: relative;
		float: left;
		display: flex;
		bottom: 4px;
		justify-content: flex-start;
		padding: 20px 0 25px;
	}

	.group_title {
		border: 1px solid #096dd9;
		padding: 3px 5px;
		border-radius: 4px;
		width: 140px;
		margin: 5px;
		text-align: center;
	}

	.group_title_active {
		width: 140px;
		margin: 5px;
		border: 1px solid #096dd9;
		background-color: #096dd9;
		color: #fff;
		padding: 3px 5px;
		border-radius: 4px;
		margin: 5px;
		text-align: center;
	}
</style>

<body style="padding:10px">
	<div id="blnrjghcxNew">
		<!-- 2个输入框 -->
		<div id="topNow">
		</div>
		<div id="bottomShow">
			<!-- 左 -->
			<div class="showLeft">
				<div class="Ltitle label-name">
					病例结构化查询
				</div>
				<div class="Llist"></div>
			</div>
			<!-- 右 -->
			<div class="showRight">
				<div class="RTop">
					<div class="Rtitle label-name">
						条件查询
					</div>
					<div class="RInputForm">

					</div>
				</div>
				<div class="Rtable">

				</div>
			</div>
		</div>
	</div>
	<!-- 配置文件 -->
	<script src="./e-config.js"></script>
  <!-- <script src="./lib/echarts/echarts.min.js"></script>
  <script src="./lib/charts/echarts.js"></script> -->
	<!-- 公用模块JS -->
	<!-- <script src="js/e-common.js"></script> -->
	<script type="text/javascript">
		document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
	</script>
	<!-- 临床营养中心会诊js文件 -->
	<script type="text/javascript">
		document.write("<script type='text/javascript' src='js/e-blnrjghcxNew.js?v=" + Date.now() + "'><\/script>");
	</script>
  
</body>

</html>