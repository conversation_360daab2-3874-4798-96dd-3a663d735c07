let comlum1=''
let comlum1_arr={}
//统一页面启动
$(document).ready(() => {
  //获取关键字数据
  WRT_e.api.blcxgj.getinit({
    success(data) {
      if (data.Code == 1) {
        WRT_config.init=data.Result
        app.init()
      }
    }
  })
})

var app = {
  init: function () {
    //定义时间格式
    $.fn.datebox.defaults.formatter = function (date) {
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      var d = date.getDate();
      return y + '-' + m + '-' + d;
    }
    var Main = new Main_View();
    Main.$el = $("#container_blcxgj");
    Main.init({data:WRT_config.init}).render();
  },
  
}

var page = {
  // 设置查询条件按钮
  brcx_open:function(){
    document.getElementById('light').style.display='block',
    document.getElementById('fade').style.display='block',
    $("#light").html(
      new brcxgjTable_View().init({
        // data: blcxgj
        data: WRT_config.init
      }).render().$el
    )
    comlum1_arr={}
  },
  // 表格内容选择切换
  optionChg:function(){
    // let RQ
    var comlum=$("#table_internal tr").length-1;
    var id= ("fieldName_data"+comlum)
    var obj = document.getElementById(id); 
    var val2 = obj.options[obj.selectedIndex].id
    if (val2 !='') {
      // 输入框(姓名，住院号，入院诊断，床位号)
      if ((val2 == 'xm')||(val2 == 'ryzd')||(val2 == 'cwh')) {
        $(".qery_val_in"+comlum).html(`<input type="text" name="input_field" class="input_SR"  autocomplete="off" id="qery_val_data`+comlum+`">`)
        $(".comparison_operator_in"+comlum).html(`
        <select class="comparison_operators_data" id="comparison_operators_data`+comlum+`">
          <option value="=">等于</option>
          <option value="!=">不等于</option>
          <option value=">">大于</option>
          <option value=">=">大于等于</option>
          <option value="<">小于</option>
          <option value="<=">小于等于</option>
          <option value="left">左包含</option>
          <option value="right">右包含</option>
          <option value="like">包含</option>
        </select>
        `)
      }
      if (val2 == 'empi') { // 输入框
        $(".qery_val_in"+comlum).html(`<input type="text" name="input_field" class="input_SR"  autocomplete="off" id="qery_val_data`+comlum+`">`)
        $(".comparison_operator_in"+comlum).html(`
        <select class="comparison_operators_data" id="comparison_operators_data`+comlum+`">
          <option value="=">等于</option>
        </select>
        `)
      }
      // 下拉选择框（性别）
      if (val2 == 'xb') { 
        $(".qery_val_in"+comlum).html(`
        <select class="qery_val_data" id="qery_val_data`+comlum+`">
          <option value="1">男</option>
          <option value="2">女</option>
        </select>
        `)
      }
      // 下拉选择框（病区）
      if (val2 == 'bqdm') {
        $(".qery_val_in"+comlum).html(`
        <select class="qery_val_data" id="qery_val_data`+comlum+`">
          <option value ="病区" selected></option>
          ${_.map(WRT_config.init['BQXX'], (obj)=> `<option value="${obj.VAL}" id="${obj.jglx}">${obj.TXT}</option>`).join('')}
        </select>
        `)
      }
      // 下拉选择框（专科）
      if (val2 == 'zkdm') {
        $(".qery_val_in"+comlum).html(`
        <select class="qery_val_data" id="qery_val_data`+comlum+`">
          <option value ="专科" selected></option>
          ${_.map(WRT_config.init['ZKXX'], (obj)=> `<option value="${obj.VAL}" id="${obj.jglx}">${obj.TXT}</option>`).join('')}
        </select>
        `)
      }
      if((val2 == 'xb')||(val2 == 'bqdm')||(val2 == 'zkdm')){ // 选择框的比较符选择
        $(".comparison_operator_in"+comlum).html(`
        <select class="comparison_operators_data" id="comparison_operators_data`+comlum+`">
          <option value="=">等于</option>
          <option value="!=">不等于</option>
        </select>
        `)
      }
      // 时间框
      if ((val2 == 'csrq')||(val2 == 'ryrq')||(val2 == 'cyrq')) {
        $(".qery_val_in"+comlum).html(`
          <input id="qery_val_data`+comlum+`" class="dt${comlum}" type="text" value="" name="birthday" onclick="page.born(${comlum})">
        `)
        $(".comparison_operator_in"+comlum).html(`
        <select class="comparison_operators_data" id="comparison_operators_data`+comlum+`">
          <option value="=">等于</option>
          <option value="!=">不等于</option>
          <option value=">">大于</option>
          <option value=">=">大于等于</option>
          <option value="<">小于</option>
          <option value="<=">小于等于</option>
        </select>
        `)
      }
    }
  },
  // 日期
  born(index){
    // var comlum=$("#table_internal tr").length-1;
    let RQ
    // $('.dt').datetimebox({
    //   value: v,
    //   required:true,
    //   showSeconds: true,
    // });
    comlum1=index
    var currentDt=new Date();
    var y=currentDt.getFullYear();
    var m=currentDt.getMonth()+1;
    if(m<10){m="0"+m;};
    var d=currentDt.getDate();
    if(d<10){d="0"+d;}
    var hour = change(currentDt.getHours());
    var minute = change(currentDt.getMinutes());
    var second = change(currentDt.getSeconds());
    function change(t) {
      if (t < 10) {
          return "0" + t;
      } else {
          return t;
      }
    }
    var times=y+'-'+m+'-'+d + ' ' + hour + ':' + minute +':'+second;
    // $('.dt').datetimebox('setValue', times);	// set datebox value
    $(`.dt${index}`).datetimebox({
      value: times,
      required:true,
      showSeconds: true,
    });
    let field=document.getElementById("fieldName_data"+comlum1).value
    comlum1_arr[comlum1]={title:field,value:times,type:"times"}
    var v = $(`.dt${index}`).datetimebox('getValue');
    $(`.dt${index}`).datetimebox({
      stopFirstChangeEvent: true,
      onChange: function (ev) {
        let field=document.getElementById("fieldName_data"+comlum1).value
        comlum1_arr[comlum1]={title:field,value:ev,type:"times"}
      }
    });
  }
}

// 页面
var Main_View=WRT_e.view.extend({
  render:function(){
    let html=`
      <div class="title_patientbl">
        <span>按病人基本信息查询病历（高级）</span>
      </div>
      <button id="open_btn" type="submit"><span>设置查询条件</span></button>
      <div id="light" class="inner_table"></div>
      <div id="fade" class="black_overlay"></div>
      <div id="resultShow"></div>`
    this.$el.html(html)
    return this;
  },
  events:{
    "click #open_btn":"open_btn"
  },
  open_btn(){
    page.brcx_open()
  }
})

// 病人查询表格
var brcxgjTable_View = WRT_e.view.extend({
  render:function(){
    this.$el.html(`
    <div id="br_search_table" class="br_search_table">
      <div class="table-header">
        <span>按病人基本信息查询病历（高级）</span>
      </div>
        <div class="overall_table_frame">
          <table border="2" id="table_internal" class="table_internal">
          <thead>
            <tr class="row_head">
              <th class="left_bracket">左括号</th>
              <th class="fieldName">字段名</th>
              <th class="comparison_operators">比较符</th>
              <th class="qery_val">查询值</th>
              <th class="right_bracket">右括号</th>
              <th class="logical_operator">逻辑符</th>
            </tr>
          </thead>
          <tbody class="add_line" id="add_line">
            
          </tbody>
          </table>
        </div>
      <div class="table-footer">
        <button class="add"><span>新增</span></button>
        <button class="delete"><span>删除</span></button>
        <button class="countermand"><span>取消</span></button>
        <button class="ok""><span>确定</span></button>
      </div>
    </div>
    `);
    return this
  },
  events:{
    "click .add":"brcx_add",
    "click .delete":"brcx_delete",
    "click .countermand":"brcx_close",
    "click .ok":"brcx_ok",
  },
  // 新增
  brcx_add:function(){
    $("#add_line").append(
      new brcxgjAdd_View().init({
        data: WRT_config.init
      }).render().$el
    )
  },
  // 删除
  brcx_delete:function(){
    var tab = $("#table_internal tr:last");
    // 删除此行
    tab.remove();
    $("#s").remove();
  },
  // 取消
  brcx_close:function(){
    document.getElementById('light').style.display='none';
    document.getElementById('fade').style.display='none'
  },
  // 确定
  brcx_ok:function(){
    document.getElementById('light').style.display='none';
    document.getElementById('fade').style.display='none';
    var comlum = $("#table_internal tr").length-1; //行
    var tcount=$("#table_internal tr").length;
    var tb = document.getElementById('table_internal');    // table 的 id
    var rows = tb.rows; // 获取表格所有行
    let as_where=''
    var arrayAll=[];
    for(var i = 1; i<rows.length; i++ ){
      for(var j = 0; j<rows[i].cells.length; j++ ){    // 遍历该行的 td
        // 输出每个td的内容
        // 左括号
        var zkh1=document.getElementById("left_bracket_data"+i).value
        // 字段名
        var zdm1=document.getElementById("fieldName_data"+i).value
        // 比较符
        var bjf1=document.getElementById("comparison_operators_data"+i).value
        // 查询值
        // if (condition) {
          
        // }
        // document.getElementsByName("brithday")
        var cxz1=document.getElementById("qery_val_data"+i).value
        // 右括号
        var ykh1=document.getElementById("right_bracket_data"+i).value
        // 逻辑符
        var ljf1=document.getElementById("logical_operator_data"+i).value
      }
      var hang={"zkh":zkh1,"zdm":zdm1,"bjf":bjf1,"cxz":cxz1,"ykh":ykh1,"ljf":ljf1}
      arrayAll.push(hang)
    }
    for(let i in comlum1_arr){
      if(comlum1_arr[i]){
        arrayAll[i-1].zdm=comlum1_arr[i].title
        arrayAll[i-1].cxz=comlum1_arr[i].value
      }
      if(comlum1_arr[i].type&&comlum1_arr[i].type=='times'){
        arrayAll[i-1].cxz=`to_date('${arrayAll[i-1].cxz}','yyyy-mm-dd hh24:mi:ss')`
        arrayAll[i-1].type=comlum1_arr[i].type
      }
    }
    // 结果拼接判断
      for(var c=0;c<arrayAll.length;c++){
        if(as_where){
          as_where+=` ${arrayAll[c-1].ljf||' and '} `
        }
        if(arrayAll[c].zdm=="empi"){
          as_where+=`(a.mzh in (select brbh from cw_khxx where global_pid = '${arrayAll[c].cxz}') )`
        }else if(arrayAll[c].bjf=="like"){
          as_where+=`${arrayAll[c].zkh||"("} a.${arrayAll[c].zdm} ${arrayAll[c].bjf} '%${arrayAll[c].cxz}%' ${arrayAll[c].ykh||")" }`
        }else if (arrayAll[c].bjf=="left"){
          as_where+=`${arrayAll[c].zkh||"("} a.${arrayAll[c].zdm} ${arrayAll[c].bjf} '%${arrayAll[c].cxz}%' ${arrayAll[c].ykh||")"}`
        }else if(arrayAll[c].bjf=="right"){
          as_where+=`${arrayAll[c].zkh||"("} a.${arrayAll[c].zdm} like '${arrayAll[c].cxz}%' ${arrayAll[c].ykh||")"}`
        }else if(arrayAll[c].type&&arrayAll[c].type=="times"){
          as_where+=`${arrayAll[c].zkh||"("} a.${arrayAll[c].zdm} ${arrayAll[c].bjf} ${arrayAll[c].cxz} ${arrayAll[c].ykh||")"}`
        }else{
          as_where+=`${arrayAll[c].zkh||"("} a.${arrayAll[c].zdm} ${arrayAll[c].bjf} '${arrayAll[c].cxz}' ${arrayAll[c].ykh||")"}`
        }
      }

    WRT_e.api.blcxgj.getSearch({
      params: {
        "as_tjyj":as_where
      },
      success(data) {
        if (data.Code == 1) {
          WRT_config.search=data.Result
          $("#resultShow").html(
            new result_View().init({
              data: WRT_config.search
            }).render().$el
          )
        } else {
          // WRT_config.search=[]
          WRT_e.ui.message({
            title: '提示信息',
            content: `该条件下没有数据，请重新查询!`,
            onOk() {
              // $("#resultShow").html(
              //   new result_View().init({
              //     data: []
              //   }).render().$el
              // )
            }
          })
        }
      }
    })
  }
})
// 添加表格内容
var brcxgjAdd_View = WRT_e.view.extend({
  render:function(){
    var tcount=$("#table_internal tr").length;
    var comlum = $("#table_internal tr").length
    var html=`
    <tr id="s`+tcount+`" class= "line_add">
      <td class="left_bracket`+comlum+`">
        <select class="left_bracket_data" name="left_bracket_data"  placeholder="" id="left_bracket_data`+comlum+`">
          <option value ="" selected></option>
          <option value ="(">(</option>
          <option value ="((">((</option>
        </select>
      </td>
      <td class="fieldName`+comlum+`">
        <select class="fieldName_data" name="fieldName_data"  id="fieldName_data`+comlum+`" onchange="page.optionChg()">
          <option value ="" selected></option>
          ${_.map(WRT_config.init['CXGJZ'], (obj)=> `<option value="${obj.VAL}" id="${obj.VAL}">${obj.TXT}</option>`).join('')}
        </select>
      </td>
      <td class="comparison_operator_in`+comlum+`" id="comparison_operator_in`+comlum+`"></td>

      <td class="qery_val_in`+comlum+`" id="qery_val_in`+comlum+`"></td>
      <td class="right_bracket`+comlum+`">
        <select class="right_bracket_data" name="right_bracket_data"  id="right_bracket_data`+comlum+`">
          <option value ="" selected></option>
          <option value =")">)</option>
          <option value ="))">))</option>
        </select>
      </td>
      <td class="logical_operator`+comlum+`">
        <select class="logical_operator_data" name="logical_operator_data"  id="logical_operator_data`+comlum+`">
          <option value ="" selected></option>
          <option value ="and">且</option>
          <option value ="or">或</option>
        </select>
      </td>
    </tr>`;
    $("#table_internal").append(html)
    return this;
  },

})
// 结果展示
var result_View = WRT_e.view.extend ({
  render:function(){
    var date = new Date(parseInt(("/Date(1415169703000)/").substr(6)))
    Date.prototype.toLocaleString = function() {
      return this.getFullYear() + "-" + (this.getMonth() + 1) + "-" + this.getDate() ;
    };
    var comlum = WRT_config.search.length; //行
    this.$el.html(`
    <div id="Object_list" class="Object_list">
      <table border="2" cellpadding="100px" bordercolor="#B7C3DA" class="table_searchContent">
        <tr class="search_table">
          <th>住院ID</th>
          <th>病例ID</th>
          <th>病案号</th>
          <th>姓名</th>
          <th>性别</th>
          <th>现存放病区名称</th>
          <th>床位号</th>
          <th>专科代码</th>
          <th>专科名称</th>
          <th>入院诊断</th>
          <th>出生日期</th>
          <th>财务入院时间</th>
          <th>财务出院时间</th>
          <th>门诊号</th>
          <th>是否是新版病人</th>
        </tr>
        ${_.map(WRT_config.search, (obj)=> `
          <tr class="search_content">
            <td value="${obj.IDB}">${obj.IDB}</td>
            <td value="${obj.BLID}">${obj.BLID}</td>
            <td value="${obj.EMPI||''}">${obj.EMPI||''}</td>
            <td value="${obj.XM}"><a href="javascript:void(0)" class="brName" name="${obj.IDB}">${obj.XM}</a></td>
            <td value="${obj.XB}">${obj.XB==1?`男`:`女`}</td>
            <td value="${obj.BQDM}">${obj.BQDM}</td>
            <td value="${obj.CWH}">${obj.CWH}</td>
            <td value="${obj.ZKDM}">${obj.ZKDM}</td>
            <td value="${obj.ZKMC}">${obj.ZKMC}</td>
            <td value="${obj.RYZD}">${obj.RYZD||""}</td>
            <td value="${(new Date(parseInt((obj.CSRQ).substr(6)))).toLocaleString()}">${(new Date(parseInt((obj.CSRQ).substr(6)))).toLocaleString()}</td>
            <td value="${(new Date(parseInt((obj.RYRQ).substr(6)))).toLocaleString()}">${(new Date(parseInt((obj.RYRQ).substr(6)))).toLocaleString()}</td>
            <td value="${(new Date(parseInt((obj.CYRQ).substr(6)))).toLocaleString()}">${(new Date(parseInt((obj.CYRQ).substr(6)))).toLocaleString()}</td>
            <td value="${obj.MZH}">${obj.MZH}</td>
            <td value="${obj.ISNEW}">${obj.ISNEW==null?`否`:`是`}</td>
          </tr>`).join('')}
      </table>
      <div class="znum">总记录数: ${comlum}</div>
    </div>`)
    return this
  },
  events: {
    "click .brName": function (e) {
      var a = WRT_config.search
      var idb = a.find(ev =>ev.IDB == e.target.name)
      if(idb.BRLB==1){
        WRT_e.ui.message({
          title:'提示',
          content:"您无权操作该病人"
        })
        return;
      } else {
        var zd = WRT_config.init['BQXX']
        var zdbh = zd.find(ev =>ev.TXT == idb.BQDM)
        var jslx = idb.JSLX 
        // var jflx = jslx == 00 ?'自费':jslx == 01?'社保':jslx == 02?'农保':'工费'
        var jflx = jslx == 00 ?'自费':'社保'
        let url = `e-BrMainLeft.html?ls_idb=`+idb.IDB+`&al_blid=`+idb.BLID+``
        parent.page_iframe.add(`bl_${idb.BLID}`,`${jflx} ${zdbh.VAL}-${idb.CWH} ${idb.XM}`,url)
      }
    }
  }
})