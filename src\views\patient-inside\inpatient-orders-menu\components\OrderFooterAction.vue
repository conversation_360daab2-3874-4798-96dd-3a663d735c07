<template>
  <div>
    <el-button type="primary" @click="clickBtn('submit')">审核提交(T)</el-button>
    <el-button type="primary" @click="clickBtn('save')">保存(S)</el-button>
    <el-button type="primary" @click="clickBtn('addGroup')">新增组(1)</el-button>
    <el-button type="primary" @click="clickBtn('add')">新增项(2)</el-button>
    <el-button type="primary" @click="clickBtn('insert')">插入项(2)</el-button>
    <el-button type="primary" @click="clickBtn('delGroup')">删除组(3)</el-button>
    <el-button type="primary" @click="clickBtn('del')">删除项(4)</el-button>
    <el-button type="primary" @click="clickBtn('personal')">药品个人模版(5)</el-button>
    <el-button type="primary" @click="clickBtn('common')">常用药(6)</el-button>
    <el-button type="primary" @click="clickBtn('heal')">治疗医嘱模板(7)</el-button>
    <el-button type="primary" @click="clickBtn('comprehensive')">综合医嘱模板(8)</el-button>
    <el-button type="primary" @click="clickBtn('saveMB')">保存模板</el-button>
  </div>
</template>

<script>
export default {
  name: 'OrderFooterAction',
  data() {
    return {}
  },

  methods: {
    clickBtn(action) {
      this.$emit('actionClicked', action)
    }
  }
}
</script>

<style lang="scss" scoped></style>
