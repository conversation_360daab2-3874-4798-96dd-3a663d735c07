<template>
  <div class="shen-qing-hui-zhen">
    <el-table :data="countByType" border stripe size="mini">
      <el-table-column prop="huiZhenBZMC" label="会诊名称"></el-table-column>
      <el-table-column prop="count" label="人数" width="80" align="center"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getApplyConsultationCount } from '@/api/patient'
import fetchData from '@/views/patient-home/components/mixin/fetchData'

export default {
  name: 'RequestConsultationPatients',
  mixins: [fetchData],
  data() {
    return {
      countByType: [] // 统计类型
    }
  },
  methods: {
    fetchData() {
      getApplyConsultationCount({ zhuanKeID: this.zhuanKeID }).then((res) => {
        if (res.hasError === 0) {
          this.countByType = res.data.reduce((acc, cur) => {
            const huiZhenBZMC = cur.huiZhenBZMC
            const exist = acc.find((item) => item.huiZhenBZMC === huiZhenBZMC)
            if (exist) {
              exist.count++
            } else {
              acc.push({ huiZhenBZMC, count: 1 })
            }
            return acc
          }, [])
          this.$emit('update:count', res.data.length)
        }
      })
    },
    showHzd() {}
  }
}
</script>

<style lang="scss" scoped>
.shen-qing-hui-zhen {
}
</style>
