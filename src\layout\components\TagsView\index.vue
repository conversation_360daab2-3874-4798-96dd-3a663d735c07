<template>
  <div
    ref="tagsViewContainer"
    class="tags-view-container"
    :style="{ paddingLeft: $route.path === '/home' ? '10px' : 0 }"
  >
    <el-tabs
      v-model="activeRoute"
      type="card"
      @tab-click="handleSelect"
      @tab-remove="closeSelectedTag"
      @contextmenu.prevent.native="openMenu($event)"
    >
      <el-tab-pane
        v-for="tag in visitedViews"
        :key="tag.fullPath"
        :name="tag.path"
        :label="formatTitle(tag)"
        :closable="!tag.meta?.affix"
      ></el-tab-pane>
    </el-tabs>
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li @click="refreshSelectedTag(selectedTag)">刷新</li>
      <li @click="closeOthersTags">关闭其它</li>
      <li @click="closeAllTags(selectedTag)">关闭所有</li>
    </ul>
  </div>
</template>

<script>
import path from 'path'

export default {
  components: {},
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      activeRoute: ''
    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    routes() {
      return this.$store.state.permission.routes
    }
  },
  watch: {
    $route(val) {
      this.addTags()
      this.activeRoute = val.path
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  mounted() {
    this.initTags()
    this.addTags()
    this.activeRoute = this.$route.path
  },
  methods: {
    formatTitle(tag) {
      return tag.query?.title || tag.title
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    handleSelect(tab) {
      const route = this.visitedViews.find((item) => item.path === tab.name)
      if (route) {
        this.$router.push(route)
      }
    },
    filterAffixTags(routes, basePath = '/') {
      let tags = []
      if (routes) {
        routes.forEach((route) => {
          if (route.meta && route.meta.affix) {
            const tagPath = path.resolve(basePath, route.path)
            tags.push({
              fullPath: tagPath,
              path: tagPath,
              name: route.name,
              meta: { ...route.meta }
            })
          }
          if (route.children) {
            const tempTags = this.filterAffixTags(route.children, route.path)
            if (tempTags.length >= 1) {
              tags = [...tags, ...tempTags]
            }
          }
        })
      }
      return tags
    },
    initTags() {
      const affixTags = (this.affixTags = this.filterAffixTags(this.routes))
      if (affixTags.length > 0) {
        for (const tag of affixTags) {
          // Must have tag name
          if (tag.path) {
            this.$store.dispatch('tagsView/addVisitedView', tag)
          }
        }
      }
    },
    addTags() {
      const { path } = this.$route
      if (
        path &&
        (path === '/home' ||
          path.startsWith('/patient-detail') ||
          path.startsWith('/department-menus') ||
          path.startsWith('/information') ||
          path.startsWith('/medical-quality') ||
          path.startsWith('/my-menus') ||
          path.startsWith('/system-maintenance'))
      ) {
        this.$store.dispatch('tagsView/addView', this.$route)
      }
      return false
    },
    refreshSelectedTag(view) {
      this.$store.dispatch('tagsView/delCachedView', view).then(() => {
        const { fullPath } = view
        console.log('🚀 ~ this.$store.dispatch ~ fullPath:', fullPath)
        this.$nextTick(() => {
          this.$router.replace({
            path: '/redirect',
            query: { path: fullPath }
          })
        })
      })
    },
    closeSelectedTag(name) {
      const view = this.visitedViews.find((v) => v.path === name)
      this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag)
      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {})
    },
    closeAllTags(view) {
      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {
        if (this.affixTags.some((tag) => tag.path === view.path)) {
          return
        }
        this.toLastView(visitedViews, view)
      })
    },
    toLastView(visitedViews) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView.fullPath)
      } else {
        this.$router.push('/home')
      }
    },
    openMenu(e) {
      if (e.srcElement.id) {
        this.left = e.clientX
        this.top = e.clientY
        this.visible = true
        console.log(e.srcElement.id.split('tab-')[1])
        let id = e.srcElement.id.split('tab-')[1]
        this.selectedTag = this.visitedViews.find((f) => f.path === id)
      }
    },
    closeMenu() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.tags-view-container {
  margin-top: 10px;
  ::v-deep .el-tabs--card > .el-tabs__header .el-tabs__nav {
    border: 1px solid #dcdfe6;
    .el-tabs__item {
      height: 32px;
      line-height: 32px;
      background: #f6f6f6;
      &.is-active {
        background: #ffffff;
      }
    }
  }

  .contextmenu {
    position: fixed;
    z-index: 3000;
    padding: 5px 0;
    margin: 0;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    list-style-type: none;
    background: #fff;
    border-radius: 4px;
    box-shadow: 2px 2px 3px 0 rgb(0 0 0 / 30%);

    li {
      padding: 7px 16px;
      margin: 0;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }

  // }
}
</style>
