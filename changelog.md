﻿### 3.1.1 2021-08-20
+ 新增：病人详情界面侧边栏收放功能;肠外营养套餐。
+ 修复：长期医嘱药品和治疗医嘱保存;加入临床路径功能。
+ 变更：滴速问题。
+ 样式：病程界面展示改回老版样式。
+ 优化：出院界面切换;分辨率显示。

### 3.1.2 2021-08-26
+ 新增：撤销执行医嘱功能
+ 新增：临床路径回调函数
+ 修复：饮食保存再保存出错;临床路径回调功能。
+ 样式：病人体温单血压显示不下

### 3.1.3 2021-09-01
+ 新增：增加界面回调函数，刷新

### 3.1.3 2021-09-02
+ 排查：标签页切换，滚动台置顶
+ 排查：正式服务器上病程记录关闭提醒
+ 修复：本治疗组化验单打印
+ 修复：临床路径刷新问题
+ 优化：出院病人病理打印回调
+ 优化：登录版本低版本提示问题

### 3.1.4 2021-09-07
+ 修复：化验医嘱病区选中
+ 优化：院感预警窗口保留弹窗提示
+ 删除：已保存文书保存提示（暂时）
+ 优化：病程记录选中默认打开
+ 测试：出院病人病理提醒打开窗口
+ 排查：临床入径强制出径后

### 3.1.5 2021-09-16
+ 新增：改造华法令调整表
+ 新增： 高警示药品提醒
+ 优化：病历文书编辑模式自动判断窗口大小
+ 修复：出院病人病理提示
+ 修复：本治疗组病例打印参数缺失
+ 修复：出院病人列表点击病人弹窗

### 3.1.6 2021-09-23
+ 新增： 抗菌药物权限管理
+ 优化：院感提示弹框显示问题
+ 修复：临床路径界面
+ 修复：门诊复诊预约
+ 排查：临时医嘱选中频率问题

### 3.1.7 2021-09-27
+ 优化：体温单字体大小
+ 修复：临床确认刷新问题
+ 修复：化验医嘱逻辑问题
+ 修复：门诊复诊预约打开事件

### 3.1.8 2021-09-29
+ 修复：滚动条置顶问题

### 3.2.1 2021-10-12
+ 修复：G值修改
+ 增加：DRG信息图标
+ 增加：增加病人基本信息界面侧边栏展示信息
+ 修复：医嘱表头搜索
+ 修复：出入量平衡显示控制

+ 优化：医嘱表格样式
+ 修复：标签页切换，层级问题
+ 修复：标签页层级问题
+ 新增：侧边栏病人基本信息字段

### 3.2.2 2021-10-18
+ 新增：我的病人打印
+ 新增：临时抗菌药物急诊专科判断
+ 排查：申请单问题

### 3.2.3 2021-10-19
+ 新增：时间针频率
+ 新增：简式心生量表替换成PHQ-4
+ 新增：判断病人VTE评分是否填写
+ 优化：病程记录列表优化
+ 优化：首页信息展示排版优化

### 3.2.4 2021-10-25
+ 新增：表格配置存储 
+ 修复：抗菌药物判断

### 3.2.5 2021-10-26
+ 修复：体温单显示
+ 排查：在院病人列表

### 3.2.6 2021-11-01
+ 修复：病程记录文件展示框收分辨影响
+ 修复：文书侧边栏标题颜色判断
+ 修复：医嘱界面接口增加新参数报错
+ 排查：首页侧边点击打开病人信息结算类型问题，接口缺少返回字段

### 3.2.7 2021-11-04
+ 修复：3.0修改主管医生查询不到内容，且没有“本专科”选项
+ 排查：3.0外购药历史界面点击详细，弹出窗口定位有问题，并且标签页关掉再打开弹窗依然存在

### 3.2.8 2021-11-05
+ 新增：中成药辩证流程改造
+ 优化：急诊抢救专科简化菜单-隐藏项目同步

### 3.2.8 2021-11-09
+ 新增：时间针医嘱提醒
+ 新增：病人基本信息评分信息
+ 优化：中成药辩证流程改造

### 3.2.9 2021-11-10
+ 新增：护理级别颜色修改
+ 新增：病人页左侧菜单新增指南速查功能
+ 新增：麻醉药品权限控制

### 3.2.10 2021-11-18
+ 新增：ICU开具转科医嘱（属于治疗医嘱）时提示ApacheII评估表
+ 修复：时间针医嘱提醒
+ 修复：组号标志bug
+ 优化：急诊抢救的病人不需要质控提醒
+ 优化：医嘱增加“药品滴速”列
+ 优化：急诊抢救病人不需要显示长期医嘱

### 3.2.10 2021-11-24
+ 新增：儿童生长发育曲线
+ 新增：ICU开具转科医嘱（属于治疗医嘱）时提示ApacheII评估表
+ 优化：简化菜单点击“开住院单”
+ 优化：菜单下拉模块

### 3.2.11 2021-11-25
+ 新增：青霉素钠没有抗菌药品使用方法选择
+ 修复：同组出院带药的数量可以不一样

### 3.2.12 2021-11-30
+ 新增：术前医嘱自动开皮试医嘱
+ 修复：病程记录vte判断逻辑
+ 修复：临床路径问题
+ 修复：同组药品时间同步

### 3.2.13 2021-12-04
+ 修复：病程记录危急值问题
+ 修复：简化菜单链接跳转问题
+ 优化：儿童曲线图表
<!-- + 超2天未归档的病人性别显示，需要后台增加字段 -->

### 3.2.14 2021-12-07
+ 变更：门诊复诊预约功能

### 3.2.15 2021-12-16
+ 新增：新增血糖管理病人列表
+ 新增：草药医嘱回车逻辑
+ 修复：医嘱开药逻辑判断
+ 优化：病程记录界面样式
+ 排查：界面加载缓慢问题

### 3.2.16 2021-12-23
+ 新增：二维码登录功能
+ 新增：短信发送功能
+ 优化：血糖列表界面
+ 修复：归档后辅检记录问题
+ 修复：医嘱开药会诊单逻辑，回车问题

### 3.2.16.1  2021-12-27
+ 新增：二维码登录功能
+ 优化：短信发送功能

### 3.2.17 2021-12-30
+ 新增：医嘱双击备注内容
+ 新增：道路标识
+ 修复：重复开药逻辑
+ 修复：历史在用口服药导入后，日期还有显示空
+ 修复：主管医生修改
+ 修复：同组药时间同步
+ 优化：简化菜单

### 3.2.18 2022-1-18
+ 增加：用户验证
+ 修复：二维码登录
+ 修复：化验医嘱保存问题
+ 排查：指南链接错误问题

### 3.2.21 2021-1-21
+ 修复：草药开药问题

### 3.2.22 2021-1-25
+ 修复：留观医嘱判断
+ 优化：道路图标更换

### 3.2.23 2022-2-7
+ 修复：病人入院时间计算

### 3.2.24 2022-2-9
+ 新增：跨科治疗按钮
+ 修复：医嘱执行判断
+ 修复：次数数据显示

### 3.2.25 2022-2-10
+ 新增：病人状态判断
+ 新增：新冠评估
+ 修复：封装药品逻辑
+ 修复：院感监控路径

### 3.2.25 2022-2-11
+ 新增：治疗组病人分组

### 3.2.26 2022-2-16
+ 修改：饮食多次保存规格错误

### 3.2.27 2022-2-17
+ 修复：新冠评估按钮
+ 修复：规格单价数据显示

### 2022-2-18
+ 修复：改化验医嘱时间同步
+ 修复：临时医嘱默认频率
+ 修改：日间手术保存

### 2022-2-21
+ 修复：护理级别无法更改
+ 修复：日间手术病人
+ 变更：新冠评估

### 2022-2-22
+ 新增：新生儿预防用药
+ 变更：增加病人详情入参

### 2022-3-1
+ 新增：保存外购药品的记录ID
+ 新增：急诊留观新增“开住院单”菜单

### 3.2.29 2022-3-2
+ 修复：医嘱刷新按钮长药输液量问题
+ 修复：保存医嘱字段类型处理

### 3.2.30 2022-3-5
+ 新增：分装药品判断
+ 修复：出入量保存出错

### 3.2.32 2022-3-9
+ 修复：化验医嘱勾选多个审批判断

### 3.2.33 2022-3-9
+ 修复：化验医嘱勾选
+ 优化：虚拟药品模版

### 3.2.34 2022-3-10
+ 修复：一级护理ADL点击后护理级别变成了未设置
+ 修复：化验医嘱时间同族点击修改问题
+ 新增：化验医嘱全选功能
+ 新增：搜索到的化验医嘱直接定位


### 3.2.35 2022-3-11
+ 新增：化验医嘱社保审批缺少逻辑

###  3.2.36 2022-3-15
+ 新增：版本号显示
+ 新增：急诊抢救开启长期医嘱（治疗、饮食）
+ 新增：显示母乳库存量

+ 新增：CDSS开发

### 3.2.37 2022-3-23
+ 新增：化验医嘱特检申请单判断
+ 修复：化验医嘱备注长度
+ 修复：化验医嘱病区id不变问题
+ 修复：医嘱开开抗菌药物中选药品判断
+ 排查：草药带药数量问题
+ 排查：病程记录文书接口调用

### 3.2.37 2022-3-24
+ 新增：首页需要增加一个 未填报单病种
+ 修复：化验医嘱已导出列表排序
+ 修复：PHQ量表不填不能新增首程
+ 排查：测试病人化验医嘱列表为空


### 3.2.37 2022-3-25
+ 新增：首页需要增加一个 未填报单病种
+ 修复：PHQ量表不填不能新增首程

### 3.2.38 2022-3-28
新增：首页需要增加一个 未填报单病种
修复：化验医嘱已导出列表排序
修复：PHQ量表不填不能新增首程
排查：测试病人化验医嘱列表为空

### 3.2.39 2022-3-30
新增：文件后面增加后缀
修复：化验医嘱CDSS判断控制
修复：化验医嘱保存是病区ID
修复：新增文书判断
优化：病人详情页质控显示


### 3.2.40 2022-4-1
+ 新增：时间针显示
+ 新增：病人详情界面消息提醒（首程，大病历，血液）
+ 修复：化验医嘱CDSS保存
+ 修复：新营养医嘱显示

### 3.2.41 2022-4-7
+ 变更：专科日志展示（接口获取）
+ 修复：退出日间病人不能正常保存
+ 修复：临时医嘱界面化验类别显示检查的问题
+ 优化：系统名称

### 3.2.42 2022-4-11
+ 新增：医嘱时间针判断显示
+ 新增：综合留观的简化菜单判断
+ 新增：综合留观和急诊抢救专科的病人24小时内显示小时数，超过24小时显示天数
+ 新增： 病程记录列表能像“病人基本信息”一样，向左缩进
+ 修复：综合留观专科禁止调用CheckBrTsxx


### 3.2.43 2022-4-12
新增：手术通知单跳转
新增：医嘱菜单不良事件上报按钮
变更：综合留观和急诊抢救专科的病人24小时内显示小时数，超过24小时显示天数

### 3.2.43 2022-4-13
+ 修改：病案号标识号
+ 修改：时间针只显示在综合留观和急诊抢救

### 3.2.44 2022-4-18
+ 新增：限制聚集标志
+ 修复：历史用药模板导入，医嘱类型出错
+ 修复：药品说明书传参问题
+ 修复：同组药修改医嘱类型同步
+ 优化：处理首页过长科室数据

### 3.2.45 2022-4-22
新增：化验医嘱中文查询
修复：化验医嘱导出排序
优化：危机值记录模板
优化：病程记录列表排版显示

### 3.2.46 2022-5-7
修改：锁屏界面刷新问题
修复：模板导入出现的时间，名称，组号等显示问题
新增：开住院单
修复：光标输入后消失（如一次用量）

### 3.2.47 2022-5-7
增加：新老病人判断
修改：回车判断

### 3.2.48 2022-5-7
新增：ICU转科通知功能
新增：已填写的报告卡菜单未显示
新增：知情记录控制无法新增
变更：模板导入逻辑
修复：社保病人未弹出审批
修复：流速同组判断
优化：登录页名称单词

### 3.2.48 2022-5-9
修复：特殊药品模板导入
优化：菜单维护

### 3.2.49 2022-5-11
修复：保存真实组号引起的组号混乱
新增：急诊专科不提示首程
优化：急诊专科增加手术通知单
修复：输入检索关键字直接定位还是定位在第一条选项上 
修复：只显示医嘱界面时，提交后显示空白问题； 

### 3.2.50 2022-5-17
新增：全局页面对frbb.aspx判断
修复：点击病人跳转较慢，优化接口
修复：治疗医嘱模板中新增治疗医嘱数量会变空
优化：左侧栏目名称字体颜色调整 

### 3.2.51 2022-5-19
新增：医嘱增加自备字段显示
新增：VVST_message判断
新增：IE浏览器判断
修复：医嘱嘱托时药品可自定义

### 3.2.52 2022-5-25
修复：已有出院记录的情况下，新增转科记录没有提示
修复：病人基本信息门诊号显示不全 
修复：编辑文书的情况下关闭标签页没有提示 
修复：治疗模板显示有问题 
修复：3.0切换标签页会自动跑到顶端的内容，不能定位在之前切换的位置 

修复：特殊抗菌药品保存IDstring类型
修复：医嘱提交成功提示方式修改

###  2022-5-27
新增：更新内容提示
新增：医嘱提交失败提示
修复：同组要逻辑高于默认逻辑
修复：化验医嘱全选问题
修复：治疗模板导入特殊用法

### 3.2.52 2022-5-31
长期，临时医嘱 治疗和综合模板导入区分特殊用法和备注说明

3.0版本的知情记录没有医生代码或签名图片-控制无法新增
化验医嘱项目显示 ￥单价*数量，套餐显示 ￥总价
化验医嘱CDSS增加计时器自动关闭10分钟
化验医嘱处理备注为0情况
开立的医嘱，需支持部分医嘱提交
启用菜单权限，调微服务接口

### 3.2.53 2022-6-6
变更：启用菜单权限，调微服务接口
变更：DRG无法打开
修复：化验医嘱备注信息身高体重保存
修复：草药医嘱/草药带药修改数量后同一组的不会一起变过来
修复：3.0打开2.0页面，tempid未发生变化 
修复：出院带药，采用模板导入后会出现医嘱类别不是出院带药的情况

###  2022-6-9
新增：单点登录功能
新增：tab右键菜单功能
新增：编辑文书关闭提示
新增：密码过期提醒
修复：开药途中更改药品，后面用法频率需要对应变化

### 3.2.55 2022-6-16
变更：去除【住院号】，保留病案号 
优化：医嘱界面闭环默认窗口能显示查看按钮 
修复：知情记录汇总标签页无法关闭
修复：草药医嘱插入项或新增项出现剂数未同步问题；
修复：提示underfined信息窗口后无法关闭窗口 
修复：导入一堆出院带药后，修改数量时，鼠标光标会跳走
修复：电子病历3.0，右键开化验每次点进去都是同一个人的 
修复：化验医嘱加个显示不对
修复：长期，临时保存医嘱错误
新增：医嘱开具血制品时，跳出提醒
新增：病历归档提示框新增【查看详情】按钮，详情页制作（显示一个表格）
新增：单点登录登出

### 3.2.56 2022-6-24
新增：开药时需选择处方诊断界面
新增：【刷新本页】按钮需把长药输液量一起刷新
新增：归档详情跳转
新增：知情文书新增判断提示
变更：【预计出院】/【是否立取】显示
修复：同组药用法没有和第一个对应 
修复：医嘱导入名称出错
修复：首页修改治疗组，调整接口
修复：单点登录后左下角不显示服务器地址，数据缓存

### 3.2.57 2022-6-27
新增：控制病人详情界面病人状态按钮显示（未病区入院不要显示）
新增：未病区入院病案号显示绿色
修复：专科常用知情记录列表维护界面
修复：手动输入计划开始时间同步同组时间

### 3.2.58 2022-6-30
新增：EHR3.0登陆后只弹一个框，集合各种提醒
新增：已归档文书修改加载地址
修复：医嘱快捷键操作不灵敏
修复：出院带药预计出院修改
优化：病人详情界面字体颜色

### 3.2.59 2022-7-6
新增：EHR3.0登陆后只弹一个框，集合各种提醒
新增：已归档文书修改加载地址
修复：医嘱快捷键操作不灵敏
修复：出院带药预计出院修改
优化：病人详情界面字体颜色

### 3.2.60 2022-7-13
新增：化验医嘱新增化验单总价功能
新增：未病区入院无法下达医嘱出院
新增：显示乙级病历列表
新增：出院带药预计出院修改
新增：体温单血压同一时间段取有数据的最新值判断
新增：病人状态修改前置判断（未病区入院无法下达医嘱出院 ）
修复：模板导入出现问题
修复：vte升级到2.0版本
修复：出院带药数量合法值修改后同步持续天数
优化：首页病人一览变展示（底色）

### 3.2.61 2022-7-29
新增：护理级别修改提示
新增：文书tab页关闭提醒修改
新增：治疗组病人增加打印功能
新增：我的病人取消专科排序规则，增加病区分割符
新增：麻醉药选择用途
修复：选择特殊抗生素药品后再选择其他药品无法导入（特殊类抗生素会诊单未申请时）
修复：文字菜单上没有显示手式图标
排查：锁屏功能

### 3.2.62 2022-8-11
新增：麻醉药选择用途
新增：临床路径界面
新增: 草药带药新增快递功能
新增：跨科病例修正申请单动态获取
修复：菜单栏标签关闭后打开新标签顺序
修复:知情记录列表维护修改，删除功能
修复：化验菜单上没有显示手式图标
修复:化验医嘱修复全选功能项目不全问题
删除:锁屏功能(暂时隐藏)
优化:病人详情界面背景色

### 3.2.63 2022-8-19
新增：伤病器官切除审批电子化
新增：开二甲双胍时给予提醒
新增：留观病人左侧菜单栏增加一个
新增：添加不良反应上报按钮
新增：提醒内容中增加查看会诊单功能
新增：医嘱判断是否已有饮食医嘱开药逻辑
新增: 医嘱康复治疗医嘱开立提醒
修复: 电子病历常用知情记录维护
修复：化验医嘱全选导入，弹出框点击取消后，数据不全
修复：手机短信发送界面
修复：病例记录文书多个编辑关闭时合并醒
变更：院感监控预警列表单独弹框，与所有消息提示区别开
变更：隐藏草药出院带药快递功能

### 3.2.64 2022-9-01
新增：医务处伤病审批界面
新增：首页消息提示增加CA签章提醒
新增：手机短信发送页面模板弹出
新增：首页增加临床辅助决策系统提示
新增：长期，临时医嘱增加医嘱ID字段显示
新增：药品个人模板新增模糊检索功能
修复：医嘱开药保存后插入组号标志错误
删除：急诊留观专科取消隐藏质控提醒
新增：过敏判断修改
新增：出院带药和草药带药，若医嘱已收费医生执行撤销时给予弹窗提醒

### 3.2.64 2022-9-30
新增：反馈中心链接地址
新增：医嘱开药国家码显示
新增：翻新药品审批界面
新增：病人详情页左侧营养评估单按钮
修复：化验医嘱总价显示问题
修复：医务处审批界面文书显示数据问题
优化：急诊留观专科病人页左侧菜单显示修改

### 3.2.65 2022-10-12
新增：首页加入接口，完成切换专科时，记录日志
修复：体温天数修改

### 3.2.65 2022-10-14
修复：伤病器官切除审批（医务处）页面样式
修复：医嘱开药时切换医嘱类型，预计出院和是否代煎字段没有同步

### 3.2.65 2022-10-20
变更：接科记录的跳地址修改

### 3.2.66 2022-10-25
新增：首页加入个性化按钮弹窗修改

### 3.2.66 2022-10-27
优化：病人左侧隐藏无评分结果数据

### 3.2.66 2022-11-1
新增：特殊情况（最后一次抗菌药物医嘱是术后预防用药）下禁止出院带药开抗菌药物

### 3.2.66 2022-11-2
优化：计划近期出院按钮优化（字体加粗变红）
新增：病人信息部分加入3个评分（APACHE评分、SOFA评分、预计死亡率）

### 3.2.66 2022-11-3
优化：有打开打印预览窗口，也允许关闭标签页(病程记录)
优化：新增时知情记录标题名称是否可左对齐（按钮就放右边）

### 3.2.66 2022-11-9
优化：计划近期出院 要改成 预出院
修复：处方诊断输入如为空时提示
修复：麻醉药品用途输入如为空时提示
修复：一次用量为0可以保存
修复：出院带药选中是频率默认逻辑
优化：计划近期出院改成预出院
排查：化验医嘱模板导入不全问题

### 3.2.66 2022-11-14
新增：病人操作栏增加 再次授权金额 按钮

### 3.2.66 2022-11-15
优化：日间病历菜单只在日间病人情况下展示

### 3.2.66 2022-11-16
优化：预出院按钮增加时间判断
优化：病历知情标题按钮样式修改
优化：预计死亡修改
优化：病人操作栏 再次授权金额 按钮暂时隐藏

### 3.2.66 2022-11-17
优化：预出院按钮修改

### 2022-11-21
新增：电子病历读取新老版限制范围
修复：入院记录标签关闭时，状态判断

### 3.2.67 2022-11-23
优化：临床路径调整更新

### 2022-11-24
优化：预出院未填改成未保存
优化：草药带药预计出院只显示患者自取选项
优化：个性化记录与移动简易审批合并

### 3.2.68 2022-12-6
优化：病程记录时间展示优化

### 3.2.68 2022-12-7
新增：预缴金额新增圆形红底白字【K】(针对困难人群)
新增：电子病历病人信息栏增加“P”标记

### 3.2.68 2022-12-15
新增：临床路径地址换成3.0

### 3.2.69 2022-12-24
新增：治疗医嘱强制TNM评分
新增：医嘱关联手术通知单

### 3.2.69 2022-12-30
新增：病人详情页一键下转功能
优化：首页本病区本周有病人AKI、CKD发生概率显示为90%，点击出错问题

### 3.2.69 2023-1-5
+ 新增：病人页左侧菜单增加再次授权金额按钮
+ 新增：化验医嘱血气分析规则判断

### 3.2.69 2023-1-5
+ 新增：临床路径入径名称，时间，人员信息
+ 修复：临床路径强制出径提示出错
+ 修复：临床路径检索出错
+ 修复：勾选医疗及护理任务保存后未显示红勾，还是显示复选框+
+ 修复：临床路径中保存后，长期医嘱未显示问
+ 优化：临床路径医疗及护理任务字体偏小
+ 排查：重新评估不能进入路径后，查看临床路径内容一片空白
+ 排查：操作重新评估，点进入路径无反应

### 3.2.69 2023-1-10
+ 修复：登录错误提示问题

### 3.2.69 2023-1-17
新增: 再次授权金额加入验证码的获取和校验
优化：化验医嘱多个类型血气分析项目展示新版体温吸氧
优化：长期，临时医嘱开药手术通知单同组同步

### 3.2.69 2023-1-28
修复：删除危急值部分多余测试判断

### 3.2.69 2023-1-30
优化：病例知情记录汇总页面搜索新增加入弹窗控制

### 3.2.70 2023-2-3
新增：新增新冠核酸阳性提醒

### 3.2.70 2023-2-6
优化: 病例知情增加新冠知情同意书(2F)
优化：首页病人一览表中，社保/自费下移一行
优化：首页计算日期差方法修改，住院时长展示调整为xx天xx小时（于 2023-2-8 改回原来样式）
修复：手术通知单和入院记录标签页关闭权限判断

### 3.2.70 2023-2-7
优化：临床辅助决策系统，加载样式优化
优化：临床路径修改病人基本信息展示字段
优化：临床路径按钮增加LJZT判断

### 3.2.71 2023-2-8
新增：病人一览表新增【公共床位】菜单

### 3.2.71 2023-2-14
新增：强制出径弹窗修改，增加出径理由

### 3.2.71 2023-2-15
新增：新增丙肝阳性提醒，跳转传染病报告卡
新增：化验医嘱血气分析增加备注字段判断（针对动脉血气分析）
优化：临床路径医嘱内容任务完成人员姓名字段替换

### 3.2.70 2023-2-16
修复：公共床位右侧科室病区检索功能增加判断(2023-2-22对判断进行修改)

### 3.2.71 2023-2-17
新增：临床路径医嘱内容和医疗及护理任务增加'!!'展示判断
新增：临床路径医嘱内容'!!'增加tooltip(完成时间+实际完成时间+超出)

### 3.2.71 2023-2-22
修复：公共床位页面右侧科室病区切换判断进行修改

### 3.2.71 2023-2-28
新增：转日间、转普通申请
修复：aki点击跳转
新增：临床路径页面

### 3.2.71 2023-3-2
修复：临床路径增加ljzt判断

### 3.2.71 2023-3-6
修复：临床路径单位修改
修复：临床路径表格医嘱内容和右侧联动

### 3.2.71 2023-3-8
修复：急诊抢救的再次授权验证码校验逻辑修改

### 3.2.71 2023-3-10
优化：丙肝和新冠弹窗跳转路径修改

### 3.2.71 2023-3-13
优化：化验医嘱生殖激素弹窗修改

### 3.2.71 2023-3-15
优化：化验医嘱血气分析弹窗增加备注字段判断

### 3.2.71 2023-3-16
修复：化验医嘱医嘱修改用户展示字段修改

### 3.2.71 2023-3-17
新增：化验医嘱生殖激素弹窗逻辑
新增：新营养医嘱页面接口对接（完成部分）
修复：化验医嘱血气分析弹窗增加备注字段判断
修复：化验医嘱医嘱修改用户展示字段修改
修复：病人页左侧提示信息修改
修复：临床路径页面逻辑调整

### 3.2.71 2023-3-24
优化：化验医嘱增加对内生肌酐备注信息的判断

### 3.2.72 2023-3-30
优化：化验医嘱血气分析弹窗增加吸氧计算
新增：化验医嘱页面保存增加e_checkZxbg接口，判断校验医嘱病区（保存前调用）
新增：长期，临时保存校验医嘱的病区

### 3.2.72 2023-4-10
优化：化验医嘱血气分析弹窗控制增加一个医嘱项

### 3.2.72 2023-4-13
新增：在体温单上增加BMI显示
新增：长期，临时医嘱关联手术通知单
新增：长期，临时开药时需选择处方诊断调整提示

### 2023-4-19
新增：长期临时医嘱过敏药物提醒
优化：化验医嘱生殖激素医嘱备注展示数据调整

### 3.2.73 2023-4-26
优化：体温单血压tooltip修改

### 3.2.73 2023-5-04
新增：伤病gdbz字段判断
新增：医嘱页面增加惠每质控

### 3.2.73 2023-5-05
新增：化验医嘱页面增加判断开关控制

### 3.2.73 2023-5-09
新增：病人基本信息眼科增加困难人群白内障

### 3.2.73 2023-5-12
+ 新增：首页qc判断
新增：临床路径在病人基础信息框内增加完成按钮

### 74 2023-5-16
+ 修复：中选药品读取单位判断

+ 新增：体温单握力显示
+ 新增：首页危急值样式变更
+ 新增：病人详情界面侧边栏增加动态血糖监测报告
+ 新增：伤病器官切除手术审批单作废按钮
+ 修复：惠每质控js调用
+ 修复：GLIM评估量表调用

### 76 2023-7-11
+ 修改：修改药品闭环地址
+ 新增：术后首程超时提醒
+ 优化：首页右下角图片

### 77 2023-7-26
+ 新增：电子病历增加惠每链接

### 77 2023-7-27
+ 新增：外购药显示社保代码
+ 新增：外购药显示甲类

### 77 2023-7-31
+ 新增：ICU开转科治疗医嘱提醒

### 77 2023-8-02
<!-- + 新增：临床营养中心会诊页面(e_yykhz.html) -->
+ 新增：病人详情页面侧边栏增加营养诊疗执行单弹框判断(e_yykhz.html)

### 77 2023-8-03
+ 修改：外购药弹出限制范围
+ 优化：去掉ICU开转科治疗医嘱提醒
+ 修改：修改惠每单点登录url

### 77 2023-8-04
+ 优化：惠每质控系统改为病历质控系统
+ 优化：对惠每质控进行判断（e_bqxz.html 和e_yz_sz.html）
+ 修改：修改住院病人状态

### 77 2023-8-04
+ 修改：医务处伤病器官切除手术审批作废按钮

### 77 2023-8-15
+ 新增：病人详情页日间标志边加上安宁疗护标志

### 77 2023-8-17
+ 修改：去除医务处伤病器官切除手术审批弹窗不通过按钮

### 77 2023-8-21
+ 新增：病人详情页日间标志边加上黑名单标识

### 77 2023-8-22
+ 新增：病人详情页计划出院（预出院）弹框增加清空按钮

###  2023-9-04(不确定算不算2期)
+ 新增：电子病历病人信息页新增放射性粒子植入提醒图标





# 二期
###  2023-9-06
+ 化验医嘱开单套餐重复项目提示  
+ 化验医嘱联动项加载过程中点击保存按钮缺少项目问题增加对应控制

###  2023-9-11
+ 修改：电子病历危急值提醒规则修改

###  2023-9-14
+ 新增：外购药品限量控制

###  2023-9-25
+ 新增：病人页左侧菜单，营养风险筛查下方增加营养诊疗执行单链接
+ 新增：临时医嘱执行增加执行人员

###  2023-9-28
+ 新增：病人左侧菜单营养菜单修改整合（把病人左侧菜单的营养风险筛查，营养诊疗执行单，营养病历及营养食谱，营养评估单，整合到一个新标签页里）
+ 新增：病人页新增病人欠费信息显示
+ 新增：病人左侧菜单增加 电子病历新增温州市区域医疗平台链接
+ 修改：临床营养中心页面内容修改（发起营养科会诊 改为 发起营养会诊；改为竖向排列）

###  2023-10-10
+ 新增：临时医嘱页面增加预住院检查用药
+ 修复：草药带药和出院带药的预计出院不应该联动

###  2023-10-12   v80版本开始
+ 修改：病人页病人如果有欠费的话，把【预缴金额】变成红的，然后显示负数金额，原任务里的【当前欠费】栏取消，同时提示信息移至【预缴金额】上
+ 新增：长期临时医嘱抗菌药物管理页面增加参数（抗菌药物管理页面增加参数）
+ 修复：信息查询：部分病人打不开

###  2023-10-13
+ 新增：住院医嘱右上方，“长药输液量”左侧显示摄入热量和蛋白质
+ 修改：病人详情页隐藏住院特检预约菜单

###  2023-10-26
+ 修改：化验备注获取修改

###  2023-10-30
+ 新增：住院医嘱界面增加一键开单跳转

###  2023-11-01
+ 修改：化验医嘱联动号医嘱保存控制

###  2023-11-02
+ 修改：病人详情页已财务出院的病人显示已出院

###  2023-11-04  v80版本结束
+ 修改：临床营养中心会诊页面内容修改

###  2023-11-09
+ 新增：营养管理菜单界面 增加营养图表选项，点击营养图表进入新页面https://tower.im/teams/660087/todos/103023/
+ 修复：化验医嘱页面 化验医嘱填写备注内容后，保存不了https://tower.im/teams/660087/todos/103157/
+ 修复：病人详情页 病人状态已财务出院显示已出院-（文字没变化）https://tower.im/teams/660087/todos/103161/

###  2023-11-10
+ 优化：病人详情页 排查 修改主管医生；优化 打开列表就是本专科的医生，不用再点查询https://tower.im/teams/660087/todos/103298/

###  2023-11-13
+ 新增：病人详情页 新增VTE中、高危提示医生签署《静脉血栓栓塞症风险告知书》https://tower.im/teams/660087/todos/103171/

###  2023-11-14
+ 新增：病人详情页 增加性病会诊提醒（提醒字段BrMainLeft.aspx/e_init 接口返回的XbHzd_message字段）https://tower.im/teams/660087/todos/103774/
+ 新增：病人详情页 增加GLIM评估弹窗提醒（提醒字段BrMainLeft.aspx/e_init 接口返回的glim_message字段）https://tower.im/teams/660087/todos/103625/
+ 新增：病人详情页 总自付金额下方增加干细胞回输的文字显示吗

###  2023-11-21 v81版本结束
+ 新增：住院医嘱右上方，显示摄入热量和蛋白质样式修改https://tower.im/teams/660087/todos/103910/ 
+ 修改：病人详情页 修改原本进入病人页后根据glim_url返回值弹出的那个提示窗口; 把专科id==19这个判断改成通过glim_open_signal判断，其他判断不变改
+ 修复：化验医嘱页面 项目加载过程中保存界面增加（请等待项目加载完成后点击保存）提示

###  2023-11-23
+ 修改：病人基本信息界面-主管医生列表 前端显示中文职称，终身码null值不显示https://tower.im/teams/660087/todos/104675/

###  2023-11-24
+ 修复：病人基本信息界面-主管医生列表 bug修复-终身码，拼音，五笔都查不到想要的医生https://tower.im/teams/660087/todos/104677/

###  2023-11-28
+ 新增：病人详情页 增加Nutric评分提醒 https://tower.im/teams/660087/todos/105121/

###  2023-11-29
+ 修改：病程记录页面  填了出院记录，仍允许转科记录https://tower.im/teams/660087/todos/104575/（经核实前端没做修改由后端接口控制）

### 2023-11-24
+ 新增：多个外购药品无法成组 ( https://tower.im/teams/660087/todos/104867 )

### 2023-12-01
+ 新增： ICU病人Nutric评分提醒 ( https://tower.im/teams/660087/todos/105498 )

### 2023-12-04
+ 修复：化验医嘱界面 全选化验项目，在加载时点保存，会出现漏项的情况

### 2023-12-05
+ 修复：临时医嘱界面 药品个人模板勾选复制上条时间后，选择药品个人模板选项后新增数据的计划开始时间保持一致

### 2023-12-07   v82版本结束
+ 新增：首页信息查询下拉菜单 新增全院清点单自查表跳转

### 2023-12-11
+ 修改：病人详情页 弹窗点击链接跳转至性病会诊页面  https://tower.im/teams/660087/todos/106265/

### 2023-12-15
+ 修复：临时医嘱界面 处于中医科时，预计出院列实现选项批量

### 2023-12-18
+ 修复：临时医嘱界面 多个外购药品无法成组 https://tower.im/teams/660087/todos/107582/

### v83 2023-12-22
+ 新增：住院医嘱界面 新增新版外购药品  (https://tower.im/teams/660087/todos/107938/)( https://tower.im/teams/660087/todos/109752 )

### 2023-12-25
+ 新增：住院医嘱界面 新增自费提醒 (https://tower.im/teams/660087/todos/108058/)

+ 新增：医嘱开药规则引擎判断
+ 新增：开发界面并保存医嘱 ( https://tower.im/teams/660087/todos/108862 )
+ 新增：病人主页第一次出现VTE弹窗提醒填写VTE不良事件 ( https://tower.im/teams/660087/todos/109550 )
+ 新增：病人主页易致低钾药物在用信息提示 ( https://tower.im/teams/660087/todos/109548 )
+ 新增：病人主页认知风险提醒 ( https://tower.im/teams/660087/todos/109534 )
+ 新增： vte机械预防医嘱（治疗医嘱） ( https://tower.im/teams/660087/todos/108358 )
+ 优化: 临床营养中心会诊页面文字修改 ( https://tower.im/teams/660087/todos/108898 )

### 2024-1-10
+ 病人主页认知风险提醒
+  病人基本信息-认知风险提醒没有显示（接口有返回，前端未显示）
### 2024-1-17
+ 有时间点握力未显示bug修复（需马上修复）
+ 体温单死亡显示问题
+ 已护士病区出院的病人开医嘱没有提醒


### v84 2024-02-19
新增：病人详情页病人检查查询菜单增加检查预约
新增：医生信息显示不一致bug，需要刷新 ( https://tower.im/teams/660087/todos/112859 )
新增：住院处方用法用量审批功能 ( https://tower.im/teams/660087/todos/112454 )
变更：手机发送短信页面改版
修复：医嘱保存时规则引擎判断
修复：草药带药预计出院字段同组之前不一致，保存前没有校验
修复：药物警戒的链接需要换一下 ( https://tower.im/teams/660087/todos/114682 )
新增：体温单修改（落实护理文书规范2023版） ( https://tower.im/teams/660087/todos/112409 )
新增： 医嘱界面点击一行需要变色 ( https://tower.im/teams/660087/todos/114421 )
修复：医疗安全事件-VTE相关事件提醒方式改变 ( https://tower.im/teams/660087/todos/113890 )
新增: 预出院已申请则显示【预出院（已保存）】 ( https://tower.im/teams/660087/todos/112298 )
新增: 医生信息显示不一致bug，需要刷新 ( https://tower.im/teams/660087/todos/112859 )
优化: 透析记录单位置调整 ( https://tower.im/teams/660087/todos/111429 )
新增: 体温单增加下肢血压显示 ( https://tower.im/teams/660087/todos/111000 )

### 2024-02-26
新增: 营养图表页面 增加全周期体重趋势图（https://tower.im/teams/660087/todos/115181/）

### 2024-02-27
新增: 营养图表页面 增加白蛋白趋势图

### 2024-02-28
新增: 体温单疼痛显示、日期需调整（https://tower.im/teams/660087/todos/115392/）

### 2024-03-05
新增: 手机短信通知页面 新增一条消息编号为 90052 的模板（https://tower.im/teams/660087/todos/115904/）

### 2024-03-06
新增: 手机短信通知页面 新增一条消息编号为 90053 的模板（https://tower.im/teams/660087/todos/116416/）

### 2024-03-07
新增: 手机发短信页 新增按钮“发送入院通知”（https://tower.im/teams/660087/todos/115606/）

### 2024-03-08  v84版本结束
新增: 首页的信息查询->病例相关查询下拉选项中 新增病历内容结构化查询（高级）页面（https://tower.im/teams/660087/todos/115218/）

### 2024-03-14
新增: 首页的信息查询->病例相关查询下拉选项中的 病历内容结构化查询（高级）页面 结构化查询集成门诊文书查询（https://tower.im/teams/660087/todos/117263/#subtodo-104214889）
优化：营养图表-BMI-右侧的标识说明有重叠修改（https://tower.im/teams/660087/todos/117815/）

### 2024-03-20
变更：病人卡片列表营养图标显示修改（https://tower.im/teams/660087/todos/117408/）

### 2024-3-21
+ 新增：专科功能维护
+ 临床路径自费问题和特检弹窗

### 2024-3-22
+ 新增：营养图标页面 增加血红蛋白趋势图（https://tower.im/teams/660087/todos/117815/）

+ 新增：医嘱今日临停
+ 新增：2024-03-26外购药品调整
+ 新增：新增预出院计划批量录入菜单

### 2024-04-01
优化: 病历内容结构化查询（高级）页面  病例结构化查询结果显示结果变为mc+(dm) 

新增: 血透闭环展示改造 ( https://tower.im/teams/660087/todos/119149 )
新增: 营养会诊左侧新增会诊单列表 ( https://tower.im/teams/660087/todos/116265 )
新增: 当天医嘱暂停执行申请审批 ( https://tower.im/teams/660087/todos/118885 )
新增: 母乳喂养情况预警提醒 ( https://tower.im/teams/660087/todos/118386 )
新增: 长期新增自动生成临时医嘱功能 ( https://tower.im/teams/660087/todos/119144 )
新增：长期医嘱停止功能修改（前端） ( https://tower.im/teams/660087/todos/120144 )
新增：新版临床路径医嘱允许修改预计开始时间 ( https://tower.im/teams/660087/todos/117906 )

### 2024-04-02
优化: 病历内容结构化查询（高级）页面  查询结果病案号字段修改，门诊病例查询结果入院日期改为就诊日期，删除出院日期字段以及操作字段的打开病人按钮，

### 2024-04-17
优化: 病人详情页面， 病人基本信息下方信息展示处删除专科id为39时展示的就诊卡类型

### 2024-5-7
+ 新增：鼠标移到病案号上面的时候，显示blid ( https://tower.im/teams/660087/todos/121193 )
+ 修复: 体温单尿量等于0不显示的bug ( https://tower.im/teams/660087/todos/121182 )
+ 保存预住院单后，点击开医嘱，显示的一键开单界面，无法直接加载出来数据，手动切换菜单才能成功加载，需要优化 ( https://tower.im/teams/660087/todos/121058 )
+ 新增: 自费病人也走社保病人的社保审批 ( https://tower.im/teams/660087/todos/121021 )
+ 新增: 会诊菜单再加一项MDT会诊 ( https://tower.im/teams/660087/todos/120897 )
+ 新增 当天医嘱暂停执行申请审批 ( https://tower.im/teams/660087/todos/118885 )
+ 临床路径开特检单子没保存下来 ( https://tower.im/teams/660087/todos/120677 )

### 2024-5-16
+ 新增：电子病历增加维护页面 ( https://tower.im/teams/660087/todos/124972 )

### v88 2024-5-28
+ 前后端对接嵌入 ( https://tower.im/teams/660087/todos/105517 )
+ 医嘱立即停时检查是否已冲配 ( https://tower.im/teams/660087/todos/126041 )
+ 病人详情页增加GCP标识 ( https://tower.im/teams/660087/todos/125981 )
+ 优化：点击开医嘱按钮跳转到一键开单模板默认没有显示 ( https://tower.im/teams/660087/todos/125776 )
+ 电子病历-病人一览表-右键点C击增加排床功能 ( https://tower.im/teams/660087/todos/122313 )

### 2024-8-29
+ 新增：住院医嘱新增Crrt页面（https://tower.im/teams/660087/todos/128078/）
+ 新增：新增新营养医嘱页面
+ 新增：历史限定范围查询 ( https://tower.im/teams/660087/todos/137962 )
+ 新增：主页增加不良事件链接 ( https://tower.im/teams/660087/todos/136360 )
+ 优化：药品的默认用法用量 ( https://tower.im/teams/660087/todos/136405 )
+ 优化：手术患者围手术期术后预防性用药 24 小时内自动停药时间修改 ( https://tower.im/teams/660087/todos/133579 )
+ 优化：外配药新增厂家信息，在社保代码和国谈药品中间 ( https://tower.im/teams/660087/todos/133409 )

### 2024-09-19
+ 优化：知情同意书的医师签名或患方签名如果没有签字，“知情记录汇总”中以红色字体提醒 ( https://tower.im/teams/660087/todos/134283 )

### 2024-10-15
+ 优化：临床营养中心会诊标签页去掉该弹窗，并修改弹窗关闭控制

### 2024-10-17
+ 优化：crrt页面保存表单，下方表格新增一条数据

+ 开医嘱后，多次点击审核提交，医嘱界面仍留有药品医嘱 ( https://tower.im/teams/660087/todos/149951 )
+ OA：RJKF202411110001 外配处方信息结束时间修改 ( https://tower.im/teams/660087/todos/148118 )
+ 外购药品嵌入规则引擎 ( https://tower.im/teams/660087/todos/148251 )


### v94 2025-1-14
+ 新增：新生儿电子病历提醒 ( https://tower.im/teams/660087/todos/152612 )
+ 新增：电子病历169登录找不到token，token不存在不弹框 ( https://tower.im/teams/660087/todos/154394 )
+ 新增：龙港血透记录单地址变更 ( https://tower.im/teams/660087/todos/152538 )
+ 新增：开化验医嘱界面增加 一键清空 按钮 ( https://tower.im/teams/660087/todos/152302 )
+ 新增：医嘱界面，保存医嘱时不显示病历质控窗口 ( https://tower.im/teams/660087/todos/152303 )
