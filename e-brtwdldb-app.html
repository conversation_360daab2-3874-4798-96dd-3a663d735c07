<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>体温单</title>
  <meta name="description" content="体温单">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
  <style>
    #twd_list {
      max-height: 175px;
      overflow-x: hidden;
      overflow-y: auto;
      width: 100%;
    }

    .twd_list {
      display: flex;
      /* flex-flow: wrap; */
      margin: 0 8px;
      overflow: auto
    }

    .twd_list::-webkit-scrollbar {
      width: 0;
      height: 0;
    }

    .twd_list .item {
      /* border: 1px solid #ddd; */
      /* border-left-width: 5px; */
      /* border-left-color: #3D6CC8; */
      /* border-radius: 3px; */
      /* padding: 8px 10px; */
      cursor: pointer;
      margin-bottom: 10px;
      margin: 6px;
      text-decoration: none;
      display: flex;
      align-items: center;
      /* color: #aaa; */
      color: rgba(0, 0, 0, .65);
      margin-left: 0;
      margin-right: 5px;
      height: 25px;
      box-sizing: border-box;
      /* border-bottom: 5px solid #4ECEE3; */
      /* width: 300px; */
    }

    /* .twd_list .item:hover {
      color: #4ECEE3 !important;
    }
    .twd_list .item .item_title1:hover {
      border: 1px solid #4ECEE3;
      color: #4ECEE3;
    }
    .twd_list .item .e-tag1:hover {
      color: #4ECEE3;
      border-bottom: 3px solid #4ECEE3;
      font-weight: 700;
    } */

    .twd_list .item_title {
      font-size: 16px;
      margin-bottom: 2px;
    }

    .item_title1 {
      /* width: 12px; */
      height: 12px;
      line-height: 12px;
      border-radius: 35px;
      font-size: 10px !important;
      text-align: center;
      border: 1px solid gray;
      /* color: gray; */
      padding: 0 2px;
    }

    .twd_list .item_inner {
      /* color: #aaa; */
      /* color: #4ECEE3; */
    }

    .e-tag1 {
      box-sizing: border-box;
      /* height: 22px !important; */
      /* color: rgba(0, 0, 0, .65); */
      font-size: 14px;
      font-variant: tabular-nums;
      line-height: 1.5;
      list-style: none;
      font-feature-settings: "tnum";
      display: inline-block;
      height: auto;
      /* margin: 0 8px 0 0; */
      font-size: 12px;
      padding: 0 3px;
      line-height: 20px;
      white-space: nowrap;
      /* background: #fafafa; */
      /* border: 1px solid #d9d9d9; */
      /* border-radius: 4px; */
      opacity: 1;
      /* transition: all .3s cubic-bezier(.78, .14, .15, .86); */
    }

    /* .e-tag1 .item:hover{
      font-weight: 700;
    } */

    .container-twd {
      /* overflow: hidden; */
      /* height: 100%; */
      /* display: flex; */
      /* flex-flow: column; */
    }

    .container-twd .fixed_btn {
      position: fixed;
      bottom: 15px;
      right: 20px;
      display: flex;
      z-index: 10;
    }

    .selected_item {
      color: #4ECEE3 !important;
      border-bottom: 3px solid #4ECEE3 !important;
      font-weight: 700 !important;
      height: 25px !important;
      box-sizing: border-box;
      padding-top: 3px;
    }

    .selected_item1 {
      border: 1px solid #4ECEE3;
    }

    .container-twd .fixed_btn .item {
      width: 40px;
      line-height: 40px;
      font-size: 16px;
      text-align: center;
      border-radius: 100%;
      background: #eee;
      border: 1px solid rgba(0, 0, 0, 0.1);
      cursor: pointer;
      margin-right: 10px;
    }

    .twd_iframe {
      flex: 1;
      /* margin-top: 1px; */
      overflow: auto;
      white-space: nowrap;
    }
  </style>
</head>

<body>
  <div class="container-twd">
    <!-- <p class="bg-info" style="flex:0 0 20px;padding:0 4px;margin: 0;">
      自2019-01-09零点开始，生命体征24h出入量将自动显示到体温单前一天相应的空格内，2019-01-09零点之前的数据照旧规则显示。</p> -->
    <div id="twd_list"></div>
    <div class="twd_iframe">
      <iframe id="fr_twd" frameborder="0" width="700px" height="1020px"></iframe>
    </div>
    <!-- <div class="fixed_btn">
      <div class="item" data-toggle="tooltip" data-placement="top" title="放大" onclick="ZoomIt('+')">
        <i class="glyphicon glyphicon-zoom-in"></i>
      </div>
      <div class="item" data-toggle="tooltip" data-placement="top" title="缩小" onclick="ZoomIt('-')">
        <i class="glyphicon glyphicon-zoom-out"></i>
      </div>
      <div class="item" data-toggle="tooltip" data-placement="top" title="打印"
        onclick="$('#fr_twd')[0].contentWindow.preview()">
        <i class="glyphicon glyphicon-print"></i>
      </div>
    </div> -->
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 登录页js文件 -->
  <script>
    var params = {} //url带参
    //统一页面启动
    $(document).ready(() => {
      //初始化
      init()
    })
    /********************初始化********************/
    function init() {
      //分析url
      for (let i of window.location.href.split("?")[1].split("&")) {
        let fd = i.split("=")
        params[fd[0]] = fd[1]
        if (params['token']) {
          sessionStorage.setItem("token", params['token'])
        }
      }
      var isiOS = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
      var browser = {
        versions: function () {
          var u = navigator.userAgent, app = navigator.appVersion;
          return {
            webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
            iPhone: u.indexOf('iPhone') > -1, //是否为iPhone或者QQHD浏览器
            iPad: u.indexOf('iPad') > -1, //是否iPad
          };
        }(),
        language: (navigator.browserLanguage || navigator.language).toLowerCase()
      }
      if (isiOS || browser.versions.ios || browser.versions.iPhone) {
        alert('苹果手机系统正在搭建中...')
      }
      //提示
      $('[data-toggle="tooltip"]').tooltip()
      //获取体温单列表
      // let data = {
      //   "hasError": 0,
      //   "errorMessage": "请求成功",
      //   "data": [
      //     "2017-06-30",
      //   ]
      // }
      // WRT_e.api.brtwdlb.getTwdList({
      WRT_e.api.brtwdlb.getTwdList_app({
      params: params['as_blid'],
      success(data) {
      if (data.hasError == 0) {
        let tempArr = []
        data.data.map((item, index) => {
          let obj = {}
          obj.as_kssj = item
          obj.as_jssj = addDay(item)
          obj.as_week = index + 1
          tempArr.push(obj)
        })
        let arr = tempArr
        $("#twd_list").html(
          new twdList_View().init({
            data: arr
          }).render().$el
        )
        if (arr[arr.length - 1].as_kssj <= new Date().Format("yyyy-MM-dd") && arr[arr.length - 1].as_jssj >= new Date().Format("yyyy-MM-dd")) {
          let obj = arr[arr.length - 1]
          $('.twd_iframe').html(
            `<iframe id="fr_twd" src="e-showtwd-app.html?as_blid=${params['as_blid']}&as_kssj=${obj['as_kssj']}&as_jssj=${obj['as_jssj']}&as_week=${obj['as_week']}" frameborder="0" width="100%" height="580px" style="zoom: 1.5;"></iframe>`
          )
          setTimeout(() => {
            $(".twd_list .item").last().addClass("selected_item").siblings().removeClass("selected_item");
            $(".twd_list .item").last().children('.item_title1').addClass("selected_item1")
            $(".twd_list .item").siblings().children('.item_title1').removeClass("selected_item1");
          }, 500)
        } else {
          let obj = arr[arr.length - 1]
          $('.twd_iframe').html(
            `<iframe id="fr_twd" src="e-showtwd-app.html?as_blid=${params['as_blid']}&as_kssj=${obj['as_kssj']}&as_jssj=${obj['as_jssj']}&as_week=${obj['as_week']}" frameborder="0" width="100%" height="580px" style="zoom: 1.5;"></iframe>`
          )
          setTimeout(() => {
            $(".twd_list .item").last().addClass("selected_item").siblings().removeClass("selected_item");
            $(".twd_list .item").last().children('.item_title1').addClass("selected_item1")
            $(".twd_list .item").siblings().children('.item_title1').removeClass("selected_item1");
          }, 500)
        }
      } else {
        if (data.errorMessage) {
          alert(data.errorMessage)
        }
      }
      }
      })
    }
    function addDay(time) {
      var month = new Date().getMonth() + 1;
      var day = new Date().getDay();
      let dateTime = new Date(time).getTime();
      let obj = {}
      obj.year = new Date(dateTime + 24 * 60 * 60 * 1000 * 6).getFullYear();
      month = new Date(dateTime + 24 * 60 * 60 * 1000 * 6).getMonth() + 1;
      if (month < 10) {
        month = "0" + month
      } else {
        month = new Date(dateTime + 24 * 60 * 60 * 1000 * 6).getMonth() + 1;
      }
      obj.month = month
      date = new Date(dateTime + 24 * 60 * 60 * 1000 * 6).getDate();
      if (date < 10) {
        date = "0" + date
      } else {
        date = new Date(dateTime + 24 * 60 * 60 * 1000 * 6).getDate();
      }
      obj.date = date
      obj.day = obj.year + "-" + obj.month + "-" + obj.date;
      return obj.day
    }
    //体温单列表
    var twdList_View = WRT_e.view.extend({
      render: function () {
        this.$el.html(`
          <div class="twd_list">
            ${_.map(this.data, obj => `
            <a class="item" data-as_kssj=${obj.as_kssj} data-as_jssj=${obj.as_jssj} data-as_week=${obj.as_week}">
              <div class="item_title item_title1">${obj.as_week}</div>
              <div class="e-tag1">${obj.as_kssj}</div>
            </a>  
            `).join('')}
          </div>
        `)
        return this
      },
      events: {
        "click .twd_list>.item": "showTwd",
      },
      showTwd(event) {
        $(event.currentTarget).addClass("selected_item").siblings().removeClass("selected_item");
        $(event.currentTarget).children('.item_title1').addClass("selected_item1")
        $(event.currentTarget).siblings().children('.item_title1').removeClass("selected_item1");
        let obj = $(event.currentTarget).data()
        // let obj2 = $(event.currentTarget).next.data()
        // ${(document.body.clientWidth >= 1440 && $(event.currentTarget).next().length > 0)
        //如果宽度大于1440显示当前点击体温单和下一条体温单
        $('.twd_iframe').html(
          `<iframe id="fr_twd" src="e-showtwd-app.html?as_blid=${params['as_blid']}&as_kssj=${obj['as_kssj']}&as_jssj=${obj['as_jssj']}&as_week=${obj['as_week']}" frameborder="0" width="100%" height="580px" style="zoom: 1.5;"></iframe>`
        )
      },
    })
    // 放大缩小
    function ZoomIt(flag) {
      var zoom = Number($('#fr_twd').css('zoom'))
      if (!(zoom >= 2 && flag == '+') && !(zoom <= 0.5 && flag == '-')) {
        $('#fr_twd').css('zoom', eval('zoom' + flag + '=0.5;'));
        $('#fr_twd')[0].contentWindow.ZoomIt(flag)
      }
    }
  </script>
</body>

</html>