<!-- 病案首页损伤外因ICD维护 -->
<template>
  <div class="system-canvas">
    <div class="system-icd">
      <div class="table-background">
        <div class="table-title">
          <div>
            <span />
            病案首页损伤外因ICD维护
          </div>
          <el-button
            type="primary"
            @click="
              openEditDrawer({
                id: 0,
                daiMa: '',
                mingChen: '',
                paiXu: 1,
                zhuangTaiBZ: '0'
              })
            "
          >
            新增
          </el-button>
        </div>
        <div class="table-component">
          <table>
            <thead>
              <tr style="text-align: left">
                <th style="width: 110px">ID</th>
                <th>代码</th>
                <th>名称</th>
                <th>排序</th>
                <th style="width: 70px">状态</th>
                <th style="width: 70px; text-align: center">操作</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in icdList">
                <tr :key="index" :class="[index % 2 == 1 ? 'tr-two' : 'tr-one']">
                  <td>
                    {{ item.id }}
                  </td>
                  <td>
                    {{ item.daiMa }}
                  </td>
                  <td>
                    {{ item.mingChen }}
                  </td>
                  <td>
                    {{ item.paiXu }}
                  </td>
                  <td>
                    <el-tag v-if="item.zhuangTaiBZ === '1'">启用</el-tag>
                    <el-tag v-else type="danger">停用</el-tag>
                  </td>
                  <td style="text-align: center">
                    <el-button type="text" @click="openEditDrawer(item)">编辑</el-button>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <icd-drawer
      :visible.sync="icdVisible"
      :icd-data.sync="icdData"
      @upload-success="initSystemIcd"
    />
  </div>
</template>

<script>
import IcdDrawer from './components/IcdDrawer.vue'
import { getYlBasySswyByPage } from '@/api/system-maintenance'
export default {
  name: 'SystemExternalCauseIcd',
  components: {
    IcdDrawer
  },
  data() {
    return {
      icdVisible: false,
      icdData: null,
      selectTable: 0,
      icdList: []
    }
  },
  async mounted() {
    this.initSystemIcd()
  },
  methods: {
    initSystemIcd() {
      getYlBasySswyByPage({
        pageIndex: 1,
        pageSize: 10,
        wenBen: '31'
      }).then((res) => {
        if (res.hasError === 0) {
          this.icdList = res.data
          console.log('病案首页损伤外因ICD维护:', res)
        }
      })
    },
    openEditDrawer(data) {
      this.icdData = data
      this.icdVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-icd {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .table-background {
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        width: 670px;
        span {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        margin-top: 10px;
        width: 670px;
        table {
          margin-top: 10px;
          width: 100%;
        }
        th,
        td {
          border: 1px solid #ddd;
          border-collapse: collapse; /* 移除表格内边框间的间隙 */
          height: 35px;
          padding: 5px 8px;
        }
        .tr-one {
          background-color: #f6f6f6;
        }
        th,
        .tr-two {
          background-color: #eff3fb;
        }
        .el-tag {
          border-radius: 2px;
        }
      }
    }
  }
}
</style>
