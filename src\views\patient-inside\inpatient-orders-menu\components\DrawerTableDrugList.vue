<template>
  <el-popover
    v-model="visible"
    popper-class="drug-popover-class"
    width="700"
    trigger="manual"
    placement="bottom"
    :append-to-body="true"
  >
    <!--<el-radio-group v-model="yaoPinLeiXing" style="margin-bottom: 10px">-->
    <!--  <el-radio-button label="全院用药">全院用药</el-radio-button>-->
    <!--  <el-radio-button label="个人常用药">个人常用药</el-radio-button>-->
    <!--  <el-radio-button label="科室常用药">科室常用药</el-radio-button>-->
    <!--</el-radio-group>-->
    <el-table
      ref="drugListTable"
      v-loading="loading"
      height="250"
      :data="drugList"
      highlight-current-row
      border
      stripe
      size="mini"
      :row-style="{ cursor: 'pointer' }"
      @row-click="handleRowClick"
      @current-change="handleCurrentChange"
    >
      <el-table-column prop="mingCheng" label="药品名称/治疗模板名称/膳食名称" width="320px">
        <template #default="{ row: drugRow }">
          <div>{{ drugRow.leiXingMC }} {{ drugRow.mingCheng }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="jiCai" label="集采" width="70px"></el-table-column>
      <el-table-column prop="guiGe" label="规格" width="120px"></el-table-column>
      <el-table-column prop="shouJia" label="售价"></el-table-column>
      <el-table-column prop="biaoZhun" label="标准"></el-table-column>
    </el-table>
    <el-input
      slot="reference"
      v-model="query"
      clearable
      @input="debouncedHandleInput"
      @focus="handleFocus"
      @blur="handleBlur"
    />
  </el-popover>
</template>

<script>
import { debounce } from 'lodash'
import { EventBus } from '@/utils/event-bus'
import { searchYiZhu } from '@/api/inpatient-order'
export default {
  name: 'DrawerTableDrugList',
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    column: {
      type: Object,
      default: () => {
        return {}
      }
    },
    tabActive: {
      type: String,
      default: ''
    },
    inpatientInit: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      moHuSS: '0',
      visible: false,
      yaoPinLeiXing: '全院用药',
      drugList: [],
      tempValue: '', // 添加临时存储值
      hasSelected: false, // 添加选中标记
      loading: false
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    query: {
      get() {
        return this.row[this.column.value]
      },
      set(value) {
        this.$emit('updateRow', { prop: this.column.value, updateValue: value })
      }
    }
  },
  watch: {
    'column.moHuSS': {
      handler(newValue) {
        this.moHuSS = newValue
      },
      immediate: true
    }
  },
  methods: {
    // 判断查询医嘱时医嘱种类
    getYaoPinLeiXing() {
      const { zhuanKeID } = this.inpatientInit.ezyblbrVo || {}
      const { leiBie, yiZhuLX } = this.row
      if (zhuanKeID === 49 && yiZhuLX === 'cq') {
        return '0011'
      }
      switch (leiBie) {
        case '1':
          return '0010'
        case '2':
          return yiZhuLX === 'cq' ? '0001' : '0000'
        case '3':
        case '4':
          if (yiZhuLX === 'cq') {
            return '1000'
          } else if (yiZhuLX === 'cy' || yiZhuLX === 'zcydy') {
            return '0100'
          } else if (yiZhuLX === 'ls' || yiZhuLX === 'cydy') {
            return '1000'
          }
          break
        default:
          return yiZhuLX === 'cq' ? '1011' : '1010'
      }
    },
    handleInput(value) {
      this.drugList = []
      if (value.trim() === '') {
        this.visible = false
        return
      }
      this.visible = true
      const key = value.trim().toUpperCase()
      /**
       * 搜索医嘱信息
       *
       * @param {Object} searchParams - 搜索参数对象
       * @param {string} searchParams.leiXing - 类型：1111 第1位=药品，第2位=草药，第3位=治疗，第4位=饮食
       * @param {string} searchParams.key - 搜索关键词
       * @param {string} searchParams.moHuSS - 模糊搜索（1=是，0=否）
       * @param {string} searchParams.jiXing - 剂型
       * @param {string} searchParams.yiZhuLB - 医嘱类别（1=西药，2=中成药，3=草药）
       * @param {string} searchParams.yiZhuLX - 医嘱类型（cq=长期，ls=临时）
       * @param {string} searchParams.bingLiID - 病历ID
       * @param {Array} searchParams.drugTakePharmacyVos - 药房代码列表
       */
      this.loading = true
      searchYiZhu({
        leiXing: this.getYaoPinLeiXing(),
        key,
        moHuSS: this.moHuSS,
        yiZhuLB: '1', // 长期医嘱默认西药，中成药模板导入为中成药，临时医嘱草药医嘱为草药
        yiZhuLX: this.tabActive,
        bingLiID: this.bingLiID,
        drugTakePharmacyVos: this.inpatientInit.yaoFangDMs
      })
        .then((res) => {
          if (res.hasError === 0) {
            this.drugList = res.data
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleFocus(event) {
      this.tempValue = this.query
      this.hasSelected = false
      this.query = ''
    },
    handleBlur(event) {
      if (!this.hasSelected) {
        // 如果没有选择药品，恢复原始值
        this.query = this.tempValue
      }
      this.visible = false
    },
    // 搜索防抖
    debouncedHandleInput: debounce(function (value) {
      this.handleInput(value)
    }, 300),
    // 选中某条搜索医嘱
    setCurrent(row) {
      this.$refs.drugListTable.setCurrentRow(row)
      this.hasSelected = true
    },
    // 点击医嘱
    async handleRowClick(row) {
      this.visible = false
      this.query = row.mingCheng
      // 执行开药判断逻辑
      EventBus.$emit('startYZLogic', { row: this.row, drugRow: row })
    },
    handleCurrentChange(currentRow, oldCurrentRow) {
      console.log(currentRow)
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.drug-popover-class) {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 5px !important;
}
</style>
