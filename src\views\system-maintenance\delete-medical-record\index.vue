<!-- 删除病人病历 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="head-search">
        <span class="search-label">请输入病人病案号:</span>
        <el-input v-model="searchValue" style="width: 170px" />
        <el-button class="search-button" type="primary">查询</el-button>
      </div>
      <div class="table-background">
        <div class="table-title">
          <div>
            <span class="bar" />
            删除病人病历
          </div>
          <span class="hint-info">
            <i class="el-icon-warning" />
            <span>
              电子病历删除前注意事项: 1.请确定该病历是需要删除的; 2.病历一旦删除无法恢复。
            </span>
          </span>
        </div>
        <div class="table-component">
          <table>
            <thead>
              <tr style="text-align: left">
                <th>姓名</th>
                <th>病案号</th>
                <th>专科</th>
                <th>病区-床位号</th>
                <th style="text-align: center">入院日期</th>
                <th style="text-align: center">出院日期</th>
                <th style="width: 200px">入院诊断</th>
                <th style="width: 90px; text-align: center">病历状态</th>
                <th style="width: 90px; text-align: center">复印状态</th>
                <th style="width: 100px; text-align: right">住院费用</th>
                <th style="width: 100px; text-align: center">操作</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in dataList">
                <tr :key="index" :class="[index % 2 == 1 ? 'tr-two' : 'tr-one']">
                  <td>
                    {{ item.field1 }}
                  </td>
                  <td>
                    {{ item.field2 }}
                  </td>
                  <td>
                    {{ item.field3 }}
                  </td>
                  <td>
                    {{ item.field4 }}
                  </td>
                  <td style="text-align: center">
                    {{ item.field5 }}
                  </td>
                  <td style="text-align: center">
                    {{ item.field6 }}
                  </td>
                  <td>
                    {{ item.field7 }}
                  </td>
                  <td style="text-align: center">
                    <el-tag :type="item.field8 === '可书写' ? null : 'danger'">
                      {{ item.field8 }}
                    </el-tag>
                  </td>
                  <td style="text-align: center">
                    <el-tag :type="item.field9 === '未复印' ? 'danger' : null">
                      {{ item.field9 }}
                    </el-tag>
                  </td>
                  <td style="color: #f35656; text-align: right">￥{{ item.field10 }}</td>
                  <td style="text-align: center">
                    <el-button type="text">删除病历</el-button>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DeleteMedicalRecord',
  data() {
    return {
      multivaluedVisible: false,
      multivaluedData: null,
      searchValue: '003498036',
      dataList: [
        {
          field1: '周福道',
          field2: '**********',
          field3: '肾内科',
          field4: '423-001',
          field5: '2018-11-26 14:36:03',
          field6: '2024-01-24 16:02:00',
          field7: '高钾血症',
          field8: '可书写',
          field9: '未复印',
          field10: 1925.21
        }
      ]
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .head-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        font-weight: 600;
        .bar {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }

        .hint-info {
          margin-left: 10px;
          display: flex;
          background-color: #e4ecf9;
          border: 1px solid #86abe8;
          border-radius: 3px;
          padding: 3px 10px;
          font-weight: 400;
          .el-icon-warning {
            margin: 3px 3px 0 0;
            color: #356ac5;
          }
        }
      }
      .table-component {
        margin-top: 10px;
        width: 1360px;
        table {
          margin-top: 10px;
          width: 100%;
        }
        th,
        td {
          border: 1px solid #ddd;
          border-collapse: collapse; /* 移除表格内边框间的间隙 */
          height: 35px;
          padding: 5px 8px;
        }
        .tr-one {
          background-color: #f6f6f6;
        }
        th,
        .tr-two {
          background-color: #eff3fb;
        }
        .el-tag {
          border-radius: 2px;
        }
      }
    }
  }
}
</style>
