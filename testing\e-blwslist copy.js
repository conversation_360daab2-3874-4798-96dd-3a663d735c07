    let url =window.location.href.split("?")||[]
    let text=url[1].split("&")
    let params={}
    for(let i of text){
        let fd=i.split("=")
        params[fd[0]]=fd[1]
    }
    console.log(params) 
    //统一页面启动
    $(document).ready(() => {
      //初始化
      //病程记录/入院记录初始化
      WRT_e.api.blwslist.getBlwslist({
        params:{
          "as_blid":params["as_blid"],
          "as_zyid":params["as_zyid"],
          "as_lx": params["as_lx"],
          "as_zkid":params["as_zkid"],
          "as_zkdm":params["as_zkdm"],
          "as_baseurl":"http://localhost:8080/"},
        success(data) {
          if (data.Code == 1) {
            // console.log("初始化:",data)
            WRT_config.BrwsCsh=data.Result
            console.log("初始化BrwsCsh:",WRT_config.BrwsCsh)
            //初始化专科病程录选项
            WRT_e.api.blwslist.getWslbItem({
              params: {
                as_zkid: params["as_zkid"],
              },
              success(data) {
                if (data.Code == 1) {
                  WRT_config.WslbItem=data.Result
                  console.log("初始化专科病程录选项",WRT_config.WslbItem)
                }
              }
            })
            // // 获取文书类型
            // WRT_e.api.blwslist.GetWslx({
            //   params: {"as_gsdm":"5361"},
            //   // params: {
            //   //   "as_gsdm":params["as_gsdm"],
            //   //   "as_url":"http://localhost:8080/",
            //   //   "as_blid":params["as_blid"],
            //   //   "as_zyid":params["as_zyid"],
            //   //   "as_wsid":params["as_wsid"]
            //   // },
            //   success(data) {
            //     console.log(params)
            //     if (data.Code == 1) {
            //       console.log("获取文书类型",data)
            //       WRT_config.BrwsnrList=data.Result
            //       // console.log("获取文书详情",WRT_config.BrwsnrList)
            //       // $("#type1").append(
            //       //   new progress_noteContent().init({
            //       //     data: data.Result
            //       //   }).render().$el
            //       // )
            //     }
            //   }
            // })
            init()
          }
        }
      })
      
    })
    /********************初始化********************/
    function init() { 
      //根据页面类型获取病人已书写文书列表
      WRT_e.api.blwslist.getBrWssByLx({
        params: {
          "as_blid":params["as_blid"],
          "as_zyid":params["as_zyid"],
          "as_lx":`0${params["as_lx"]}`,
          "as_zkdm":params["as_zkdm"],
          "as_baseurl":"http://localhost:8080/",
          "as_rjss":"1"},
        success(data) {
          console.log(params)
          if (data.Code == 1) {
            // console.log("病人已书写文书列表:",data)
            WRT_config.BrwsList=data.Result
            console.log("根据页面类型获取病人已书写文书列表",WRT_config.BrwsList)
            let json=WRT_config.BrwsList.dvm_wslist.map(function(item){
              item.two=0
              item.urlBlws= 0
              // item.as_gsdm=WRT_config.WslbItem.GSDM
              return item
            })
            console.log(json)
          
            // 左
            WRT_e.api.blwslist.getWslbItem({
              params: {
                as_zkid: '88',
              },
              success(data) {
                if (data.Code == 1) {
                  WRT_config.WslbItem=data.Result
                  console.log("初始化专科病程录选项",WRT_config.WslbItem)
                  $("#sidebar").append(
                    new progress_noteList().init({
                      data: WRT_config.BrwsList
                    }).render().$el
                  )
                }
              }
            })

            // 右
            $(".panel-group").append(
              new progress_noteContent().init({
                data: WRT_config.BrwsList
              }).render().$el
            )
            
            $(function () {
              $('.panel-collapse').collapse('show')
            });

          }
        }
      })
      // 新增文书
      WRT_e.api.blwslist.getNewWsHtml({
        params:{"as_gsdm":"5361","as_blid":"1746080","as_zyid":"1746080","as_tmpid":"t2"},
        success(data) {
          if (data.Code == 1) {
            console.log("获取文书详情",data)
            WRT_config.NewWsHtml=data
            console.log("获取文书详情",WRT_config.NewWsHtml)
          }
        }
      })
      // // 获取文书详情
      // // WRT_config.WslbItem
      // WRT_e.api.blwslist.getWsDetail({
      //   params: {
      //     "as_gsdm":params["as_gsdm"],
      //     "as_url":"http://localhost:8080/",
      //     "as_blid":params["as_blid"],
      //     "as_zyid":params["as_zyid"],
      //     "as_wsid":params["as_wsid"]},
      //   success(data) {
      //     if (data.Code == 1) {
      //       console.log("获取文书详情",data)
      //       WRT_config.BrwsnrList=data.Result
      //       // console.log("获取文书详情",WRT_config.BrwsnrList)
      //       // $("#type1").append(
      //       //   new progress_noteContent().init({
      //       //     data: data.Result
      //       //   }).render().$el
      //       // )
      //     }
      //   }
      // })
      // //初始化专科病程录选项
      // WRT_e.api.blwslist.getWslbItem({
      //   params: {
      //     as_zkid: params["as_zkid"],
      //   },
      //   success(data) {
      //     if (data.Code == 1) {
      //       WRT_config.WslbItem=data.Result
      //       console.log("初始化专科病程录选项",WRT_config.WslbItem)
      //     }
      //   }
      // })
    }
    // 左侧病程记录列表
  var progress_noteList = WRT_e.view.extend({
    render:function(){
      this.$el.html(`
      <div class="left_nr_line1">
        <select class="xz_nr" name="xz_nr"  id="xz_nr">
          ${_.map(WRT_config.WslbItem, (obj)=> `<option value ="${obj.GSMC}">${obj.GSMC}</option>`)}      
        </select>
        <span>
          <a class="bcadd">新增</a>
          <a class="right_hide">折叠</a>
          <a class="right_show">弹开</a>
        </span>
        <span>
      </div>
      <ul class="list_inner">
        ${_.map(this.data.dvm_wslist, (obj)=> `
        <li class="submenu" id="submenu${obj.WSID}">
          <a href="#panel${obj.WSID}" class="btnlist" id="type_title${obj.WSID}">
            <i class="glyphicon glyphicon-triangle-right"></i>
            ${obj.JLSJ} ${obj.GSMC}(${obj.SXRY})
          </a>
        </li>`).join('')}
      </ul>`)
      return this
    },
    events:{
      "click .right_hide":function(){
        console.log('hide')
        $('.panel-collapse').collapse('hide')
      },
      "click .right_show":function(){
        console.log('show')
        $('.panel-collapse').collapse('show')
      },
      "click .bcadd":function(){
        console.log('新增')
        WRT_e.ui.message({
          title: '提示',
          content: `完成内或外科住院患者静脉血栓栓塞症的风险评估(VTE)后才可书写首次病程记录`,
          onOk() {
            // let url = WRT_config.server + `/pfbgl/pfblist.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}`
            // page_iframe.add("评分表",url)
          }
        })
      }
    }
  })
   // 右侧显示
  var progress_noteContent = WRT_e.view.extend({
    render:function(){
      this.$el.html(`
      ${((WRT_config.BrwsCsh.DV_WJZ === undefined))?`
      <div class="right_top"></div>
      `:`
      <div class="right_top">
        ${WRT_config.BrwsCsh.DV_WJZ}
      </div>`}
      
      <div class="panel" id="panel">
        ${_.map(WRT_config.BrwsList.dvm_wslist, (obj)=> `
        <div class="panel-title_line" data-toggle="collapse" data-parent="#accordion" href="#type${obj.WSID}" id="type_title${obj.WSID}">
          <span id="title_nr${obj.WSID}">${obj.JLSJ} ${obj.GSMC}(书写人员：${obj.SXRY})</span>
          ${obj.two== 0 ?`
          <span class="imgManage"><a class="imgclick" name="${obj.WSID}" id="img${obj.WSID}">图片管理</a></span>
          <span class="edit"><a class="editclick" name="${obj.WSID}" id="edit${obj.WSID}" data-key="${obj.WSID}"">编辑</a></span>
          `:`<span class="save"><a class="saveclick" name="${obj.WSID}"id="save${obj.WSID}">保存</a></span>
          <span class="del"><a class="delclick" name="${obj.WSID}" id="del${obj.WSID}">删除</a></span>
          <span class="print"><a class="printclick" name="${obj.WSID}" id="print${obj.WSID}">打印</a></span>
          <span class="imgManage"><a class="imgclick" name="${obj.WSID}" id="img${obj.WSID}">图片管理</a></span>
          <span class="edit"><a class="editback" name="${obj.WSID}" id="edit${obj.WSID}">编辑</a></span>
          `}
        </div>
        <div id="type${obj.WSID}" class="panel-collapse collapse">
          <div class="panel-content${obj.WSID} " id="panel-content${obj.WSID}" value="${obj.WSID}" name="${obj.WSID}">
          ${obj.urlBlws == 0?`
          ${obj.PREVIEW_HTML}`:`
          <iframe id="if_${obj.WSID}" src="${WRT_config.server}/zyblws/blwsdetail.aspx?`+obj.urlBlws+`" rameborder="0" width="100%" height="700px"></iframe>
          `}
          </div>
        </div>`).join('')}
      </div>
      `)
      return this   
    },
    events:{
      // 编辑（2个时）
      "click .editclick":function(ev){
        console.log(ev)
        console.log("按钮名字：",ev.target.name)
        // console.log(ev.view.brwsbynr.dvm_wslist.WSTD)
        let json=this.data.dvm_wslist.map(function(item){
          if(item.WSID == ev.target.name){
            item.two=1
            item.urlBlws= "as_blid="+WRT_config.BrwsCsh.BRXX.BLID+"&as_gsdm="+item.GSDM+"&as_zyid="+WRT_config.BrwsCsh.BRXX.ZYID+"&as_wsid="+item.WSID+"&as_wslx="+item.WSLX+"&as_tmpid=-192&tmpid=0.054136330412483336"
            return item
          }
        })
        $(".panel-group").html(
          new progress_noteContent().init({
            data: WRT_config.BrwsList
          }).render().$el
        )
      },
      // 编辑（5个时）
      "click .editback":function(ev){
        // 原内容
        console.log(ev)
        let json=this.data.dvm_wslist.map(function(item){
          if(item.WSID == ev.target.name){
            item.two=0
            item.urlBlws=0
            return item
          }
        })
        $(".panel-group").html(
          new progress_noteContent().init({
            data: WRT_config.BrwsList
          }).render().$el
        )
        // var a = json.find(e => e.WSID)
        // console.log(a)
        // if (($('#type'+a.WSID).collapse('hide'))&&(a.two==0)) {
        //   console.log('hide123',ev.target.name)
        //   $('#type'+a.WSID).collapse('hide')
        //   if ($('#type'+a.WSID).collapse('hide')) {
        //     console.log('hidekk ')
        //     $('#type'+a.WSID).collapse('hide')
        //   } 
        // } 
      },
      "click .imgclick":function(imggl) {
        WRT_e.ui.message({
          title: '提示',
          content: `图片管理`,
          onOk() {
          }
        })
      },
      "click .saveclick":function(saveBtn) {
        console.log("保存")
        console.log(saveBtn)
        console.log(saveBtn.target.name)
        var childWindow = $("#if_"+saveBtn.target.name)[0].contentWindow; 
        childWindow.ehr_save();
        WRT_e.ui.message({
          title: '提示',
          content: `保存`,
          onOk() {
          }
        })
      },
      "click .delclick":function(delBtn) {
        WRT_e.ui.message({
          title: '提示',
          content: `删除`,
          onOk() {
          }
        })
      },
      "click .printclick":function(printBtn) {
        // alert("打印")
        console.log(printBtn)
        console.log("按钮名字：",printBtn.target.name)

        if (document.getElementById("if_"+printBtn.target.name).src.indexOf("blwsdetail") >= 0)
        {
          try{
            document.getElementById("if_"+printBtn.target.name).contentWindow.ehr_print();
          }
          catch(e){
            $.messager.alert("提示信息", "打印出错!", "error");
          }
        }
        else {
          document.getElementById("if_content").contentWindow.print();
        }
        // let json=this.data.dvm_wslist.map(function(item){
        //   if(item.WSID == printBtn.target.name){
        //     item.urlBlwsdy= "as_blid="+WRT_config.BrwsCsh.BRXX.BLID+"&as_gsdm="+item.GSDM+"&as_zyid="+WRT_config.BrwsCsh.BRXX.ZYID+"&as_wsid="+item.WSID+"&as_wslx="+item.WSLX+"&as_tmpid=-192&tmpid=0.054136330412483336"
        //     // blwsdetail.aspx?as_blid=1492768&as_gsdm=0828&as_zyid=1492768&as_wsid=-192&as_wslx=1C&as_tmpid=-192&tmpid=0.22055092820227928
        //     return item
        //   }
        // })
        // $("#type"+printBtn.target.name).html(
        //   new progress_noteContent_dy().init({
        //     data: WRT_config.BrwsList
        //   }).render().$el
        // )
        
      }
    }
  })
  // var progress_noteContent_dy = WRT_e.view.extend({
  //   render:function(){
  //     $('.panel-collapse').collapse('show')
  //     this.$el.html(`
  //     ${_.map(WRT_config.BrwsList.dvm_wslist, (obj)=> `
  //     <div class="panel-content${obj.WSID} " id="panel-content${obj.WSID}" value="${obj.WSID}">
  //     <iframe id="if_${obj.WSID}" src="${WRT_config.server}/zyblws/blwsdetail.aspx?`+obj.urlBlwsdy+`" rameborder="0" width="100%" height="700px"></iframe>
  //     </div>`).join(``)}
  //     `)
  //     return this
  //   }
  // })