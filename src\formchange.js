import router from './router'
import store from './store'
import { confirm } from './global/dialog'

store.dispatch('app/setCurrentDataChange', { value: false })
router.beforeEach((to, from, next) => {
  if (store.state.app.isCurrentDataChange) {
    confirm(
      '数据未保存,保存已修改的数据吗?',
      async () => {
        await store.dispatch('app/useCallback')
        store.dispatch('app/setCurrentDataChange', { value: false })
        next()
      },
      {
        popType: 'save',
        closeCallback: () => {
          store.dispatch('app/setCurrentDataChange', { value: false })
          // 变化,可以关闭tag
          store.dispatch('app/setTagClose', true)
          next()
        }
      }
    )
  } else {
    next()
  }
})
