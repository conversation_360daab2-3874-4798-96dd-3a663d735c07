<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>临床营养中心会诊</title>
  <meta name="description" content="临床营养中心会诊">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
  <script src="./lib/echarts/echarts.min.js"></script>
</head>
<style>
#yykhz {
    font-size: 16px;
    padding: 20px 50px;    
    /* width: 28%; */
    width: 100%;
    /* margin: 0 auto; */
}
#yykhz_form {
    padding-bottom: 12px
}
#yykhz_form > label{
    font-size: 18px;
    padding: 5px 0;
}
#yykhz_form > span{
    padding: 5px;
    padding-right: 20px;
}
#btnOkNow {
    /* transform: translateY(-3px); */
    position: relative;
    float: left;
    color: rgb(255, 255, 255);
    background: rgb(61, 108, 200);
    /* box-shadow: 0 3px 6px 2px rgb(61 108 200 / 70%); */
    border: none;
    padding: 5px 20px;
    border-radius: 4px;
}
</style>
<body style="padding:10px">
    <div id="yykhz">
        <div id="yykhz_form">
            <label>请选择发起营养会诊：</label><br/>
            <input type="radio" name="fqhz0" value="发起肠外营养会诊">
                <span>发起肠外营养会诊（预计经口或管饲摄入不能满足机体50%热卡和蛋白需求量达3-7天；肠缺血、肠梗阻、穿孔等肠功能障碍）</span>
            </input><br/>
            <input type="radio" name="fqhz0" value="发起营养会诊">
                <span>发起营养科会诊（特医食品和肠内营养制剂）</span>
            </input>
        </div>
        <button id="btnOkNow" onclick="btnTrue()">确  定</button>
    </div>
    <!-- 配置文件 -->
    <script src="./e-config.js"></script>
    <!-- 公用模块JS -->
    <!-- <script src="js/e-common.js"></script> -->
    <script type="text/javascript">
        document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
    </script>
    <!-- 临床营养中心会诊js文件 -->
    <script type="text/javascript">
        document.write("<script type='text/javascript' src='js/e-yykhz.js?v=" + Date.now() + "'><\/script>");
    </script>
</body>

</html>