import { MessageBox, Message } from 'element-ui'
export function confirmFunc(
  txt = '此操作将永久删除该数据, 是否继续?',
  callback,
  cancelMsg = '取消删除'
) {
  MessageBox.confirm(txt, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    iconClass: 'el-icon-warning'
  })
    .then(() => {
      callback()
    })
    .catch(() => {
      this.messageFunc(cancelMsg, 'info')
    })
}

export function messageFunc(txt, type = 'success') {
  Message({
    message: txt,
    type: type
  })
}

export default {
  confirmFunc,
  messageFunc
}
