<template>
  <medical-document-base
    :wen-shu-l-x="wenShuLX"
    document-tab-label="病历完整性自查表"
    document-name-label="病历完整性自查表名称"
    empty-selection-text="请选择病历完整性自查表"
    no-content-text="请从左侧选择病历完整性自查表"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import MedicalDocumentBase from '@/components/medical-record/MedicalDocumentBase.vue'

export default {
  name: 'RecordCompletenessCheck',
  components: {
    MedicalDocumentBase
  },
  data() {
    return {
      wenShuLX: '2B' // 病历完整性自查表的文书类型代码
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('病历完整性自查表保存成功')
    },
    handleDeleteSuccess() {
      console.log('病历完整性自查表删除成功')
    },
    handlePrintSuccess() {
      console.log('病历完整性自查表打印成功')
    }
  }
}
</script>
