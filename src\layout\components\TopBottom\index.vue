<template>
  <!-- 筛选框 -->
  <el-container
    class="top-bottom-layout"
    :style="{
      '--padding': padding,
      '--width': width,
      '--bottomBgColor': bottomBgColor,
      '--gap': gap
    }"
  >
    <el-header class="header-area">
      <div class="filter-box">
        <div class="inputs-box" :style="{ '--heights': heights }">
          <slot name="top"></slot>
          <slot name="search"></slot>
          <slot name="extraSearch"></slot>
          <slot name="export"></slot>
        </div>
        <div class="add-button">
          <slot name="add"></slot>
        </div>
      </div>
      <div v-if="Object.keys($slots).includes('extraFilter')" class="filter-box">
        <div class="inputs-box" :style="{ '--heights': heights }">
          <slot name="extraFilter"></slot>
        </div>
      </div>
    </el-header>
    <el-main class="bottom-area">
      <normal-card :title="title" background-color="" :padding="padding" :fulfill="fulfill">
        <template>
          <slot name="bottomTitle"></slot>
        </template>
        <template #extra>
          <slot name="bottomTitleExtra"></slot>
        </template>
        <template #content>
          <slot name="bottomContent"></slot>
        </template>
      </normal-card>
    </el-main>
  </el-container>
</template>
<script>
import NormalCard from '@/components/NormalCard'

export default {
  name: 'TopBottomLayout',
  components: { NormalCard },
  props: {
    title: {
      type: String,
      default: ''
    },
    searchLoading: {
      type: Boolean,
      default: false
    },
    padding: {
      type: String,
      default: '12px'
    },
    width: {
      type: String,
      default: ''
    },
    bottomBgColor: {
      type: String,
      default: '#eff3fb'
    },
    heights: {
      type: String,
      default: ''
    },
    gap: {
      type: String,
      default: '10px'
    },
    fulfill: {
      type: Boolean,
      default: false
    }
  },
  emits: ['search']
}
</script>
<style lang="scss" scoped>
.top-bottom-layout {
  display: flex;
  flex-direction: column;
  min-width: 1000px;
  height: 100%;

  .header-area {
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: unset !important;
    padding: 10px;
    background-color: #eff3fb;
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgb(0 0 0 / 15%);

    .filter-box {
      display: flex;
      align-items: center;
      justify-content: space-between;

      /* 输入框120px;下拉框120px */
      .inputs-box {
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        gap: var(--gap);
        align-items: center;
        width: var(--width); //新加的
        height: var(--heights);
        margin-right: 2px;
      }

      .add-button {
        align-items: start;
        justify-content: end;
      }
    }
  }

  .bottom-area {
    padding: 0;
    display: flex;
    flex: 1;
    flex-direction: column;
    margin-top: 12px;
    overflow: hidden;
    background-color: var(--bottomBgColor);
    border-radius: 4px;
  }
}
</style>
