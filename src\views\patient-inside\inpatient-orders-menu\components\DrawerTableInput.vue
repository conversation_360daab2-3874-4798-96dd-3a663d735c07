<template>
  <el-input v-model="inputValue" class="row-input" @blur="handleBlur" @focus="handleFocus" />
</template>

<script>
export default {
  name: 'DrawerTableInput',
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    column: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    inputValue: {
      get() {
        return this.row[this.column.value]
      },
      set(value) {
        this.$emit('updateRow', { prop: this.column.value, updateValue: value })
      }
    }
  },
  methods: {
    handleBlur() {
      this.$emit('inputBlur', { prop: this.column.value, inputValue: this.inputValue })
    },
    handleFocus() {
      this.$emit('inputFocus', { prop: this.column.value, inputValue: this.inputValue })
    }
  }
}
</script>

<style lang="scss" scoped>
.row-input {
  ::v-deep .el-input__inner {
    padding: 0 5px;
  }
}
</style>
