<template>
  <el-table :data="tableData" border stripe size="mini">
    <el-table-column prop="xingMing" label="病人名称"></el-table-column>
    <el-table-column prop="bingQuMC" label="病区"></el-table-column>
    <el-table-column prop="chuang<PERSON><PERSON>ao" label="床位号"></el-table-column>
  </el-table>
</template>

<script>
import { getNotClassifyCount } from '@/api/patient'
import fetchData from '@/views/patient-home/components/mixin/fetchData'
import { mapState } from 'vuex'

export default {
  name: 'UndocumentedPatients',
  mixins: [fetchData],
  data() {
    return {
      tableData: []
    }
  },
  computed: {
    ...mapState({
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  methods: {
    fetchData() {
      getNotClassifyCount({
        zhuanKeID: this.zhuanKeID,
        yongHuID: this.yongHuID
      }).then((res) => {
        if (res.hasError === 0) {
          this.tableData = res.data.inPatientList
          this.$emit('update:count', res.data.count)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
