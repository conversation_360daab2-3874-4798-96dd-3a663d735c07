{
  "root": true,
  "env": {
    "browser": true,
    "node": true,
    "es6": true
  },
  "parserOptions": {
    "parser": "babel-eslint",
    "sourceType": "module"
  },
  "extends": [
    "plugin:prettier/recommended",
    "plugin:vue/recommended",
    "@vue/prettier"
  ],
  // add your custom rules here
  //it is base on https://github.com/vuejs/eslint-config-vue
  "rules": {
    // "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    // "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-unused-vars": "off",
    "vue/no-v-html": "off",
    "vue/component-name-in-template-casing": [
      "error",
      "kebab-case"
    ],
    "vue/multi-word-component-names": "off",
    "vue/return-in-emits-validator": "off",
    "prettier/prettier": [
      "error",
      {
        // pettier crlf 不报错
        "endOfLine": "auto"
      }
    ],
    "vue/no-lone-template": "off"
  }
}
