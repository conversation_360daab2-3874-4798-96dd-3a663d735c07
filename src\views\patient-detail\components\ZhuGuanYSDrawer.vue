<template>
  <el-dialog
    :visible="visible"
    width="25%"
    @close="updateVisible(false)"
    @open="initDoctorListByZKID()"
  >
    <span slot="title">
      <span class="drawer-title">
        <i class="el-icon-menu"></i>
        修改主管医师
      </span>
    </span>
    <span>
      <div class="drawer-component">
        <div class="search-head">
          <el-input
            v-model="searchValue"
            style="width: 65%"
            placeholder="请输入人员终身码或拼音/五笔码"
          ></el-input>
          <el-checkbox :value="true">本专科</el-checkbox>
          <el-button type="primary">查询</el-button>
        </div>
        <table>
          <thead>
            <tr style="text-align: left">
              <th>姓名</th>
              <th>终身码</th>
              <th>职称</th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(item, index) in zhuGuanYSCurrentList">
              <tr
                :key="index"
                :class="{
                  'tr-two': index % 2 == 1,
                  'select-tr': selectZhuGuanYS === item.yongHuID
                }"
                @click="selectZhuGuanYS = item.yongHuID"
              >
                <td>
                  {{ item.xingMing }}
                </td>
                <td>
                  {{ item.zhongShenDM }}
                </td>
                <td>
                  {{ item.guaPaiZCMC || '未确定医师' }}
                </td>
              </tr>
            </template>
          </tbody>
        </table>
        <el-pagination
          background
          layout="total, prev, pager, next"
          :current-page.sync="zhuGuanYSPage"
          :page-size="11"
          :pager-count="5"
          :total="zhuGuanYSList.length"
        ></el-pagination>
      </div>
    </span>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="setZhuGuanYS">保 存</el-button>
      <el-button @click="updateVisible(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getDoctorListByZKID, setZhuGuanYS } from '@/api/patient-info'

export default {
  name: 'ZhuGuanYSDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    zhuYuanID: {
      type: Number,
      default: null
    },
    zhuanKeID: {
      type: Number,
      default: null
    },
    zhuGuanYSID: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      zhuGuanYSList: [],
      searchValue: '',
      zhuGuanYSPage: 1,
      selectZhuGuanYS: null
    }
  },
  computed: {
    zhuGuanYSCurrentList() {
      const start = (this.zhuGuanYSPage - 1) * 11
      return this.zhuGuanYSList.slice(start, start + 11)
    }
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    initDoctorListByZKID() {
      getDoctorListByZKID(this.zhuanKeID).then((res) => {
        if (res.hasError === 0) {
          this.zhuGuanYSList = res.data
          this.selectZhuGuanYS = this.zhuGuanYSID
          console.log('doctor:', res)
        }
      })
    },
    setZhuGuanYS() {
      setZhuGuanYS({
        zhuGuanYSID: this.selectZhuGuanYS,
        zhuYuanID: this.zhuYuanID //住院ID
      }).then((res) => {
        if (res.hasError === 0) {
          this.updateVisible(false)
          this.$emit('upload-success')
          this.$message({
            message: '保存成功',
            type: 'success',
            duration: 700
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-title {
  font-size: 16px;
}

.drawer-component {
  padding: 0 10px;
  .search-head {
    button {
      --color-primary: #a66dd4;
    }
    :deep(.el-button--primary:hover),
    :deep(.el-button--primary:focus) {
      background: #ce8be0;
      border-color: #ce8be0;
    }
    display: flex;
    align-items: center;
    label {
      margin: 0 10px 0 10px;
    }
  }
  table {
    margin-top: 10px;
    width: 100%;
  }
  th,
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 35px;
    padding: 10px;
  }
  th,
  .tr-two {
    background-color: #eaf0f9;
  }
  .select-tr {
    background-color: #6787cc;
    color: #ffffff;
  }
}

:deep(.el-dialog__footer) {
  border-top: none;
}
</style>
