<template>
  <el-dialog
    :visible="visible"
    width="440px"
    @open="initHuLiJBBaseInfo()"
    @close="updateVisible(false)"
  >
    <span slot="title">
      <span class="drawer-title">
        <i class="el-icon-menu"></i>
        修改护理级别
      </span>
    </span>
    <span>
      <div class="drawer-component">
        <el-radio-group v-model="selectIndex">
          <table>
            <tbody>
              <template v-for="(item, index) in huLiJBList">
                <tr :key="index" :class="{ 'tr-two': index % 2 == 1 }">
                  <td>
                    <el-radio :label="index">
                      {{ item.mingCheng }}
                    </el-radio>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </el-radio-group>
        <span class="hint-info">
          <i class="el-icon-warning" />
          <span>
            温馨提示：
            <br />
            {{ huLiMsg }}
          </span>
        </span>
      </div>
    </span>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="uploadForm">保 存</el-button>
      <el-button @click="updateVisible(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
import router from '@/router'
import { updateHuLiJB, getHuLiJBBaseInfo } from '@/api/patient-info'

export default {
  name: 'HuLiDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    zhuYuanID: {
      type: Number,
      default: null
    },
    huLiJBMC: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      huLiJBList: [],
      huLiMsg: '',
      selectIndex: null
    }
  },
  computed: {
    ...mapState({
      initInfo: ({ patient }) => patient.initInfo
    })
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    initHuLiJBBaseInfo() {
      getHuLiJBBaseInfo(this.zhuYuanID).then((res) => {
        if (res.hasError === 0) {
          this.huLiJBList = res.data.huLiJBList
          this.huLiMsg = res.data.msg
          this.selectIndex = this.huLiJBList.findIndex((item) => item.mingCheng === this.huLiJBMC)
        }
      })
    },
    uploadForm() {
      updateHuLiJB({
        ...this.huLiJBList[this.selectIndex],
        zhuYuanID: this.zhuYuanID //住院ID
      }).then((res) => {
        if (res.hasError === 0) {
          this.updateVisible(false)
          this.$emit('upload-success')
          this.$message({
            message: '保存成功',
            type: 'success',
            duration: 700
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-title {
  font-size: 16px;
}
.drawer-component {
  padding: 0px 16px;
  .el-radio-group {
    width: 100%;
  }
  table {
    width: 100%;
  }
  th,
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 35px;
    padding: 10px;
  }
  th {
    background-color: #eaf0f9;
  }
  .tr-two {
    background-color: #eaf0f9;
  }
  .hint-info {
    margin-top: 10px;
    display: flex;
    .el-icon-warning {
      margin: 3px 3px 0 0;
      color: #356ac5;
    }
  }
  :deep(.el-radio__label) {
    --color-primary: #171c28;
  }
}

:deep(.el-dialog__footer) {
  border-top: none;
}

:deep(.el-radio) input[aria-hidden='true'] {
  display: none !important;
}
</style>
