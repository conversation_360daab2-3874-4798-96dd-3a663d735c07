import request from '@/utils/request'

/**
 * 根据病历ID和文书类型获取文书列表
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export function getWenShuListByWslx(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/getWenShuListByWslx',
    method: 'get',
    params: params
  })
}

/**
 * 获取出院记录格式代码
 * @param {Object} params 参数对象
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */
export function getChuYuanJLGSDM(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/getChuYuanJLGSDM',
    method: 'get',
    params: params
  })
}

/**
 * 判断病人是否需要书写改良Rankin(mRS)量表
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export function checkRankinStatus(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/checkRankin',
    method: 'get',
    params: params
  })
}

/**
 * ICU特殊检查
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export function checkICUStatus(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/checkICU',
    method: 'get',
    params: params
  })
}

/**
 * 常规科室，新增出院/24小时入出院记录时新增判断
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export function checkChuYuanJL(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/checkChuYuanJL',
    method: 'get',
    params: params
  })
}
