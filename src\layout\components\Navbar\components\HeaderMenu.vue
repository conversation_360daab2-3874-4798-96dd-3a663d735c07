<template>
  <el-menu ref="menuRef" mode="horizontal" @select="handleSelect">
    <template v-for="(item, index) in headerMenuRouters">
      <el-menu-item
        v-if="!item.children || !item.children.length"
        :key="index"
        :index="resolvePath(item.path)"
      >
        {{ item.meta.title }}
      </el-menu-item>
      <el-submenu
        v-else
        ref="subMenu"
        :key="index"
        popper-class="popper-submenu"
        :index="resolvePath(item.path)"
      >
        <template #title>
          {{ item.meta.title }}
        </template>
        <sub-menu-item
          v-for="(child, childIndex) in item.children"
          :key="childIndex"
          :item="child"
          :base-path="resolvePath(item.path)"
        />
      </el-submenu>
    </template>
  </el-menu>
</template>

<script>
import SubMenuItem from './SubMenuItem.vue'
import { mapGetters } from 'vuex'
import { getDefaultState } from '@/store/modules/user'
import { isExternal } from '@/utils/validate'
import path from 'path'

export default {
  name: 'HeaderMenu',
  components: { SubMenuItem },
  data() {
    return {
      itemClickNativeVal: ''
    }
  },
  computed: {
    ...mapGetters(['permission_routes', 'sidebar', 'sidebarRouters']),
    headerMenuRouters() {
      return this.sidebarRouters.filter(
        (item) => item.path !== '/' && item.path !== '/patient-inside' && !item.hidden
      )
    }
  },
  watch: {
    // activeMenu(val) {
    //   const title = val === this.$route.path ? this.$route.meta.title : ''
    //   const data = {
    //     appKey: getDefaultState().app_key,
    //     xiangXiMS: title,
    //     shuJuLX: 1,
    //     caoZuoLX: 4
    //   }
    //   addWebAuditLog(data)
    // }
  },
  mounted() {
    console.log(this.headerMenuRouters, 'headerMenuRouters')
  },
  methods: {
    itemClickNative(path) {
      this.itemClickNativeVal = ''
      this.itemClickNativeVal = path
    },
    handleSelect(key, keyPath) {
      console.log(key, keyPath, '这里是keyPath')
      if (isExternal(key)) {
        window.open(key)
      } else {
        if (key == '/system-maintenance/zyyz/zlyzmb.aspx') {
          this.$router.push({ path: '/treat-medical-advice', query: { title: '治疗医嘱模板' } })
        } else if (key == '/system-maintenance/SpecialPatient/special_patient_manage.aspx') {
          this.$router.push({
            path: '/special-patient',
            query: { title: '特殊病人维护' }
          })
        } else if (key == '/system-maintenance/trafficAccidentPatient/trafficPatientManage.aspx') {
          this.$router.push({
            path: '/road-traffic-patient',
            query: { title: '道路交通患者维护' }
          })
        } else if (key == '/system-maintenance/blcx/zkzyzwh.aspx') {
          this.$router.push({
            path: '/inpatient-maintenance',
            query: { title: '住院总维护' }
          })
        } else if (key == '/system-maintenance/yz_sz/wcyytcwh.aspx') {
          this.$router.push({
            path: '/gastrointestinal-nutrition-meal',
            query: { title: '胃肠道营养餐维护' }
          })
        } else if (key == '/system-maintenance/ylzm/ylzmgl.aspx') {
          this.$router.push({
            path: '/medical-certificate-management',
            query: { title: '医疗证明管理' }
          })
        } else if (key == '/system-maintenance/zyblws/wzhsybr.aspx') {
          this.$router.push({
            path: '/paperless-trial-patient',
            query: { title: '添加无纸化试用病人' }
          })
        } else if (key == '/system-maintenance/treat-group/zlzbrlb.aspx') {
          this.$router.push({
            path: '/treat-group/treat-group-patient-list',
            query: { title: '治疗组病人列表' }
          })
        } else if (key == '/system-maintenance/treat-group/zlzgl/zlzgl.aspx') {
          this.$router.push({
            path: '/treat-group/treat-group-maintenance',
            query: { title: '治疗组维护' }
          })
        } else if (key == '/system-maintenance/treat-group/zlzgl/zlzpbsh.aspx') {
          this.$router.push({
            path: '/treat-group/treat-group-scheduling-review',
            query: { title: '治疗组排班审核' }
          })
        } else if (key == '/system-maintenance/treat-group/zlzgl/zlzpbtj.aspx') {
          this.$router.push({
            path: '/treat-group/treat-group-scheduling-statistics',
            query: { title: '治疗组排班统计' }
          })
        } else if (key == '/system-maintenance/treat-group/zlzgl/zlzcwwh.aspx') {
          this.$router.push({
            path: '/treat-group/treat-group-bed-maintenance',
            query: { title: '治疗组床位维护' }
          })
        } else if (key == '/department-menus/15TNZKBR.aspx') {
          this.$router.push({
            path: '/transfer-patient-list',
            query: { title: '本专科转科病人列表' }
          })
        } else if (key == '/department-menus/zybl_tjsj/zybrjctx.aspx') {
          this.$router.push({
            path: '/pathological-result-remind',
            query: { title: '出院病人病理结果提醒' }
          })
        } else if (key == '/department-menus/yz_sz/xytxsp.aspx') {
          this.$router.push({
            path: '/emodialysis-advice-approve',
            query: { title: '血透医嘱审批' }
          })
        } else if (key == '/department-menus/tjbg/ZLZTjBr.aspx') {
          this.$router.push({
            path: '/inspection-report-query',
            query: { title: '专科本治疗组特检报告查询' }
          })
        } else if (key == '/department-menus/sxd/sxsp.aspx') {
          this.$router.push({
            path: '/blood-application-approve',
            query: { title: '输血申请单审批' }
          })
        } else if (key == '/system-maintenance/newbasy/rjssdzwh.aspx') {
          this.$router.push({
            path: '/day-surgery',
            query: { title: '日间手术与贯标国家码的对照维护' }
          })
        } else if (key == '/system-maintenance/zyblgd/blgdcx.aspx') {
          this.$router.push({
            path: '/case-filing',
            query: { title: '病例归档' }
          })
        } else if (key == '/system-maintenance/zkzqjl/zkzqjlwh.aspx') {
          this.$router.push({
            path: '/specialist-know-record',
            query: { title: '专科知情记录' }
          })
        } else if (key == '/department-menus/wjz/wjzcx.aspx') {
          this.$router.push({
            path: '/specialist-emergency',
            query: { title: '本专科危急值查询' }
          })
        } else if (key == '/department-menus/DrShiftRecord/ShiftRecord.aspx') {
          this.$router.push({
            path: '/specialized-handover-record',
            query: { title: '专科交接班记录' }
          })
        } else if (key == '/department-menus/zyblws/blxztzdlist.aspx') {
          this.$router.push({
            path: '/case-correction-application-form',
            query: { title: '跨科病历修正申请单管理' }
          })
        } else {
          this.$router.push({ path: key })
        }
      }
    },
    resolvePath(_path) {
      if (isExternal(_path)) {
        return _path
      }
      return path.resolve('/', _path)
    }
  }
}
</script>

<style lang="scss" scoped>
.center-menu .el-menu--horizontal {
  .el-menu-item,
  .el-submenu ::v-deep .el-submenu__title {
    height: 51px;
    line-height: 51px;
    font-size: 14px;
    color: #333333;
  }
}
</style>
