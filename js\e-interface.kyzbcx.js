WRT_e.api = WRT_e.api || {}

WRT_e.api.kyzbcx = {
  // 住院医生站查询根据empi获取病人住院列表信息(传参：empi	病案号	query	true	string	)
  getEzyblbrByEmpi: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    }else{
      $.ajax({
        url: WRT_config.server2+'/patient/v1/ezyblbr/getEzyblbrByEmpi?empi='+ o.params.empi,
        type: 'get',
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
        },
        data: '',
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if(o.success) o.success(msg)
        },
        error:function(error){
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
    }
  },

  // (不需要)查询手术通知单科研标识 // 10.104.141.230:38081/medicaladvice/v1/operationInpatient/getKeYanBS?tongZhiDanID=
  getKeYanBS: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+'/medicaladvice/v1/operationInpatient/getKeYanBS',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
    
  },

  // 根据病历ID获取所有手术 //  10.104.141.230:38081/medicaladvice/v1/operationInpatient/getShouShuTZDByBLID?bingLiID= ?bingLiID='+ o.params.bingLiID
  getShouShuTZDByBLID: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server2+'/medicaladvice/v1/operationInpatient/getShouShuTZDByBLID?bingLiID='+o.params,
        type: 'POST',
        data: JSON.stringify({bingLiID:o.params}),
        dataType: "json",
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          // XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          
          console.log(error);
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: error.responseText!=''?JSON.parse(error.responseText).errorMessage:'接口调用失败'
          })
        }
      })
    }
  },

  // 修改手术通知单科研标识 // 10.104.141.230:38081/medicaladvice/v1/operationInpatient/updateKeYanBS?keYanBS=&tongZhiDanID=
  // updateKeYanBS: function (o = {}) {
  //   o.params = o.params || {}
  //     $.ajax({
  //       url:  WRT_config.server2+'/medicaladvice/v1/operationInpatient/updateKeYanBS?keYanBS=' + o.params.keYanBS + '&tongZhiDanID=' + o.params.tongZhiDanID,
  //       type: 'post',
  //       dataType: "json",
  //       data: JSON.stringify(o.params),
  //       beforeSend: function (XMLHttpRequest) {
  //         XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
  //         // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
  //         XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
  //       },
  //       // cache:false,
  //       // crossDomain:true, //设置跨域为true
  //       // xhrFields: {
  //       //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
  //       // },
  //       contentType: "application/json; charset=utf-8",
  //       success: function (msg) {
  //         if (o.success) o.success(msg)
  //       },
  //       error: function (error) {
  //         if (o.error) o.error(error)
  //         WRT_e.ui.hint({
  //           type: 'error',
  //           msg: JSON.parse(error.responseText).errorMessage
  //         })
  //       }
  //     })
    
  // },

  updateKeYanBS: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server2+'/medicaladvice/v1/operationInpatient/updateKeYanBS?keYanBS='+o.params.skeYanBS+'&tongZhiDanID='+o.params.tongZhiDanID,
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          // XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          console.log(error);
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
    }
  },

}