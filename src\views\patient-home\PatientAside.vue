<template>
  <div class="patient-aside">
    <div class="aside-top">
      <div class="aside-top-title">
        <i class="el-icon-document-copy"></i>
        <span>专科日志</span>
      </div>
    </div>
    <div class="aside-top-content">
      <div class="top-content-inner">
        <div class="top-filter">
          <el-select
            :value="bingQuID"
            placeholder="选择病区"
            size="small"
            @change="handleBingQuChange"
          >
            <el-option
              v-for="item in bingQuList"
              :key="item.buMenID"
              :label="item.buMenMC"
              :value="item.buMenID"
            />
          </el-select>
          <el-select
            :value="zhiLiaoZuID"
            placeholder="选择治疗组"
            size="small"
            @change="handleZhiLiaoZuChange"
          >
            <el-option
              v-for="item in zhiLiaoZuList"
              :key="item.zhiLiaoZuID"
              :label="item.zhiLiaoZuMC"
              :value="item.zhiLiaoZuID"
            />
          </el-select>
        </div>
        <div class="top-content">
          <div v-for="(item, index) in zhuanKeRiZhi" :key="index" class="top-content-item">
            <div class="top-content-item-title">{{ item.label }}</div>
            <div class="top-content-item-value">{{ item.value }}</div>
          </div>
        </div>
        <div class="top-extra">
          <p>出院病人病理结果提示</p>
          <p @click="showYuanGan = true">院感监控预警({{ yuanGanList.length }})</p>
        </div>
      </div>
    </div>
    <el-scrollbar>
      <el-collapse accordion>
        <el-collapse-item v-for="(item, index) in collapseItems" :key="index">
          <template #title>
            <i class="el-icon-menu"></i>
            <span>{{ item.title }}</span>
            <el-badge v-if="item.badgeValue" :value="item.badgeValue" :max="99" class="mark" />
          </template>
          <!-- 动态渲染展开内容 -->
          <component
            :is="item.component"
            v-if="zhuanKeID && zhiLiaoZuID && bingQuID"
            :count.sync="item.badgeValue"
          ></component>
        </el-collapse-item>
      </el-collapse>
    </el-scrollbar>
    <default-dialog
      append-to-body
      :visible.sync="showYuanGan"
      width="700px"
      title="院感监控预警列表"
      pop-type="tip"
    >
      <el-table :data="yuanGanList">
        <el-table-column prop="bingQuMC" label="病区"></el-table-column>
        <el-table-column prop="bingRenXM" label="患者"></el-table-column>
        <el-table-column prop="empi" label="病案号"></el-table-column>
        <el-table-column prop="chuangWeiHao" label="床位"></el-table-column>
        <el-table-column prop="action" label="填写院感监控"></el-table-column>
      </el-table>
    </default-dialog>
    <default-dialog
      append-to-body
      :visible.sync="showChuanRan"
      width="700px"
      title="院感提示传染列表"
      pop-type="tip"
    >
      <el-table :data="chuanRanList">
        <el-table-column prop="bingQuMC" label="病区"></el-table-column>
        <el-table-column prop="bingRenXM" label="患者"></el-table-column>
        <el-table-column prop="zhuYuanHao" label="住院号"></el-table-column>
        <el-table-column prop="chuangWeiHao" label="床位"></el-table-column>
        <el-table-column prop="zhenDuan" label="诊断"></el-table-column>
        <el-table-column prop="geLiBS" label="隔离标识"></el-table-column>
      </el-table>
    </default-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getZhuanKeRZStatistics, getGanRanJKData, getChuanRanList } from '@/api/patient'
import { EventBus } from '@/utils/event-bus'
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'

export default {
  name: 'PatientAside',
  components: {
    DefaultDialog
  },
  data() {
    return {
      zhuanKeRiZhi: [
        { label: '在院', key: 'currentCensus', value: 0 },
        { label: '新入院', key: 'dailyAdmissions', value: 0 },
        { label: '出院', key: 'dailyDischarges', value: 0 },
        { label: '转入', key: 'transfersIn', value: 0 },
        { label: '转出', key: 'transfersOut', value: 0 },
        { label: '病危', key: 'criticallyIllPatients', value: 0 },
        { label: '特级护理', key: 'intensiveCarePatients', value: 0 },
        { label: '一级护理', key: 'levelOneNursingPatients', value: 0 },
        { label: '二级护理', key: 'levelTwoNursingPatients', value: 0 }
      ],
      collapseItems: [
        {
          title: '临床辅助决策系统提示',
          badgeValue: 0,
          component: () => import('./components/CDSSAlert.vue')
        },
        {
          title: '今日手术病人',
          badgeValue: 0,
          component: () => import('./components/TodaySurgeryPatients.vue')
        },
        {
          title: '今日到期抗生素病人',
          badgeValue: 0,
          component: () => import('./components/TodayExpiredAntibioticsPatients.vue')
        },
        {
          title: '阳性皮试结果病人',
          badgeValue: 0,
          component: () => import('./components/SkinTestPositivePatients.vue')
        },
        {
          title: '申请会诊病人',
          badgeValue: 0,
          component: () => import('./components/RequestConsultationPatients.vue')
        },
        {
          title: 'ICU转出病人',
          badgeValue: 0,
          component: () => import('./components/ICUTransferPatients.vue')
        },
        {
          title: '未填报单病种',
          badgeValue: 0,
          component: () => import('./components/UnfilledDisease.vue')
        },
        {
          title: '超2天医生未归类的病人',
          badgeValue: 0,
          component: () => import('./components/UndocumentedPatients.vue')
        },
        {
          title: '图标说明',
          badgeValue: 0,
          component: () => import('./components/IconDescription.vue')
        }
      ],
      showYuanGan: false,
      yuanGanList: [],
      showChuanRan: false,
      chuanRanList: []
    }
  },
  computed: {
    ...mapState({
      yongHuID: ({ user }) => user.yongHuID,
      bingQuList: ({ patient }) => patient.bingQuList,
      zhiLiaoZuList: ({ patient }) => patient.zhiLiaoZuList,
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      bingQuID: ({ patient }) => patient.bingQuID,
      zhiLiaoZuID: ({ patient }) => patient.zhiLiaoZuID
    }),
    isIDsExist() {
      return this.zhuanKeID && this.bingQuID && this.zhiLiaoZuID
    }
  },
  watch: {
    zhuanKeID() {
      if (this.isIDsExist) {
        this.getZhuanKeRZStatistics()
      }
    }
  },
  async mounted() {
    await this.getGanRanJKList()
    await this.getChuanRanList()
  },
  methods: {
    // 获取院感监控数据
    async getGanRanJKList() {
      getGanRanJKData({ yongHuID: this.yongHuID }).then((res) => {
        if (res.hasError === 0) {
          this.yuanGanList = res.data.map((item) => {
            return item.zybrSimpleVo
          })
          if (this.yuanGanList?.length) {
            this.showYuanGan = true
          }
        }
      })
    },
    // 获取院感提示传染列表
    async getChuanRanList() {
      getChuanRanList({ yongHuID: this.yongHuID }).then((res) => {
        if (res.hasError === 0) {
          this.chuanRanList = res.data
          if (this.chuanRanList?.length) {
            this.showChuanRan = true
          }
        }
      })
    },
    // 获取专科日志
    async getZhuanKeRZStatistics() {
      const res = await getZhuanKeRZStatistics({
        zhuanKeID: this.zhuanKeID,
        bingQuID: this.bingQuID,
        zhiLiaoZuID: this.zhiLiaoZuID
      })
      if (res.hasError === 0) {
        this.zhuanKeRiZhi.forEach((item) => {
          item.value = res.data[item.key]
        })
      }
    },
    async handleBingQuChange(val) {
      await this.$store.dispatch('patient/setBingQuID', val)
      await this.getZhuanKeRZStatistics()
      // EventBus.$emit('updatePatientList')
    },
    async handleZhiLiaoZuChange(val) {
      await this.$store.dispatch('patient/setZhiLiaoZuID', val)
      await this.getZhuanKeRZStatistics()
      EventBus.$emit('updatePatientList')
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/theme/normal';

.patient-aside {
  width: 100%;
  height: 100%;
  background-color: $menuBg;
  overflow-y: auto;

  .aside-top {
    width: 100%;
    height: 44px;
    padding: 11px 20px;

    .aside-top-title {
      color: #fff;
      font-size: $--font-size-medium;
      font-weight: 700;

      .el-icon-document-copy {
        margin-right: 8px;
      }
    }
  }

  .aside-top-content {
    background: $color-primary-blue2;
    padding: 8px 8px 8px 0;

    .top-content-inner {
      background: #fff;
      padding: 10px 20px 8px;
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;

      .top-filter {
        display: flex;
        justify-content: center;
        gap: 4px;

        ::v-deep .el-select:first-child .el-input .el-input__inner {
          padding: 0 25px 0 10px;
          width: 100px;
        }

        ::v-deep .el-select:last-child .el-input .el-input__inner {
          padding: 0 25px 0 10px;
          direction: rtl;
        }
      }

      .top-content {
        margin: 16px 0;
        display: flex;
        flex-flow: wrap;
        gap: 10px 25px;

        .top-content-item {
          width: 60px;

          .top-content-item-title {
            font-size: $--font-size-regular;
            color: #000;
            text-align: center;
          }

          .top-content-item-value {
            height: 42px;
            line-height: 42px;
            font-size: $--font-size-large;
            color: $--color-primary;
            background: #f2f3f5;
            text-align: center;
          }
        }
      }

      .top-extra {
        font-size: $--font-size-regular;
        color: $--color-primary;
        display: flex;
        flex-direction: column;
        p {
          cursor: pointer;
        }
      }
    }
  }

  .el-scrollbar ::v-deep .el-scrollbar__wrap {
    overflow-y: auto;
    max-height: 410px;
  }

  .el-collapse {
    .el-collapse-item {
      ::v-deep .el-collapse-item__header {
        padding-left: 16px;
        height: 44px;
        line-height: 44px;
        position: relative;

        i,
        span {
          color: #fff;
          font-size: $--font-size-regular;
        }

        span {
          margin-left: 8px;
        }
      }

      ::v-deep .el-collapse-item__wrap {
        background: $color-primary-blue2;
        border-bottom: none;
      }

      .mark {
        position: absolute;
        right: 32px;

        ::v-deep .el-badge__content {
          top: 0;
          background-color: #f7832f;
          margin: auto;
          border-radius: 11px;
          color: #ffffff;
          font-size: 14px;
          height: 22px;
          min-width: 22px;
          line-height: 22px;
          text-align: center;
          white-space: nowrap;
          border: none;
        }
      }
      ::v-deep .el-collapse-item__content {
        padding: 0;
        background-color: #fff;
      }
    }
  }
}
</style>
