<template>
  <div class="temperature-sheet-view">
    <el-container>
      <el-aside class="left-aside">
        <div class="left">
          <div class="left-tips">
            <i class="el-icon-warning"></i>
            <span class="el-icon-title">提示:</span>
            <span class="el-icon-content">
              自2019-01-09零点开始，生命体征24h出入量将自动显示到体温单前一天相应的空格内，2019-01-09零点之前的数据照旧规则显示。
            </span>
          </div>
          <div class="left-header">
            <span class="title">体温单选择列表</span>
          </div>
          <div>
            <el-table
              :data="temperatureDayList"
              stripe
              size="mini"
              max-height="4180px"
              @row-click="handleWenkClick"
            >
              <el-table-column prop="kaiShiDay" label="测量日期" width="190">
                <template #default="{ row }">
                  {{ row.kaiShiDay }}&nbsp;至&nbsp;{{ row.jieShuDay }}
                </template>
              </el-table-column>
              <el-table-column label="名称">
                <template #default="{ row }">第{{ row.weekNumber }}星期</template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-aside>
      <el-aside class="right-aside">
        <div class="right">
          <div class="right-button">
            <el-button type="primary" style="padding: 10px 20px" @click="ZoomIt('+')">
              放大
            </el-button>
            <el-button type="primary" style="padding: 10px 20px" @click="ZoomIt('-')">
              缩小
            </el-button>
            <el-button type="primary" style="padding: 10px 20px" @click="preview">打印</el-button>
          </div>
          <div
            class="temperature-sheet-box"
            id="twd_table"
            style="zoom: 1; overflow: auto; padding-bottom: 80px"
            v-if="isTwdShow"
          >
            <div class="twd-data">
              <div class="twd-data-info">
                <table class="record-table" cellspacing="0" border="0" cellpadding="0">
                  <tbody>
                    <tr class="header-row">
                      <td colspan="14" class="hospital-title">
                        <span class="hospital-name">温州医科大学附属第一医院</span>
                        <span class="record-type">&nbsp;&nbsp;&nbsp;&nbsp;体温记录单</span>
                      </td>
                    </tr>
                    <tr class="patient-info-row">
                      <td width="35" class="info-label">姓名</td>
                      <td width="100" class="info-value">{{ patientBasic.bingRenXM }}</td>
                      <td width="35" class="info-label">性别</td>
                      <td width="20" class="info-value">
                        {{ patientBasic.xingBie == '1' ? '男' : '女' }}
                      </td>
                      <td width="35" class="info-label">年龄</td>
                      <td width="50" class="info-value">{{ patientBasic.nianLing }}岁</td>
                      <td width="35" class="info-label">科别</td>
                      <td width="134" class="info-value">{{ patientBasic.zhuanKeMC }}</td>
                      <td width="214" class="ward-info">
                        {{ patientBasic.bingQuMC }}&nbsp;床号{{ patientBasic.chuangWeiHao }}
                        <br />
                        病案号{{ patientBasic.bingAnHao }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div class="twd-data-table">
                <table class="e-table">
                  <tbody>
                    <tr>
                      <td>日期</td>
                      <td v-for="(item, index) in formattedDates" :key="index">{{ item }}</td>
                    </tr>
                    <tr>
                      <td>住院天数</td>
                      <td v-for="(item, index) in tableData.ZYTS" :key="index">{{ item }}</td>
                    </tr>
                    <tr>
                      <td>手术后日期</td>
                      <td v-for="(item, index) in tableData.SSTS" :key="index">{{ item }}</td>
                    </tr>
                    <tr>
                      <td>时间</td>
                      <td
                        v-for="(item, index) in tableData.SSTS"
                        :key="index"
                        style="border-right: 2px solid #d81e06"
                      >
                        <dl class="table-flex">
                          <dd>2</dd>
                          <dd>6</dd>
                          <dd>10</dd>
                          <dd>14</dd>
                          <dd>18</dd>
                          <dd>22</dd>
                        </dl>
                      </td>
                    </tr>
                    <tr style="height: 60%">
                      <td>
                        <dl class="table-flex">
                          <dd class="mb">
                            <span>
                              脉搏
                              <br />
                              (次/分)
                            </span>
                            <span>200</span>
                            <span>180</span>
                            <span>160</span>
                            <span>140</span>
                            <span>120</span>
                            <span>100</span>
                            <span>80</span>
                            <span>60</span>
                            <span>40</span>
                          </dd>
                          <dd class="ssd">
                            <span>
                              摄氏度
                              <br />
                              42
                            </span>
                            <span>41</span>
                            <span>40</span>
                            <span>39</span>
                            <span>38</span>
                            <span>37</span>
                            <span>36</span>
                            <span>35</span>
                            <span
                              style="
                                display: flex;
                                line-height: 0.95;
                                position: absolute;
                                bottom: 0px;
                                margin: 0px;
                              "
                              class="flex-row justify-content-center"
                            >
                              <span>
                                疼
                                <br />
                                痛
                                <br />
                                评
                                <br />
                                分
                              </span>
                              <span style="font-size: 20px; margin-left: 4px">
                                10
                                <br />
                                8
                                <br />
                                6
                                <br />
                                4
                                <br />
                                2
                                <br />
                                0
                              </span>
                            </span>
                          </dd>
                        </dl>
                      </td>
                      <td
                        v-for="(item, index) in tableData.SSTS"
                        style="border-right: 2px solid #d81e06"
                        :key="index"
                      >
                        <dl class="table-flex">
                          <dd>
                            <canvas
                              class="cavsTable1"
                              style="position: absolute; left: -0.5px; top: -0.5px; z-index: 9"
                            ></canvas>
                          </dd>
                          <dd></dd>
                          <dd></dd>
                          <dd></dd>
                          <dd></dd>
                          <dd></dd>
                        </dl>
                        <dl class="table-flex table-colnum">
                          <dd
                            v-for="(item, index) in [
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              '',
                              ''
                            ]"
                            :key="index"
                          >
                            {{ item }}
                          </dd>
                        </dl>
                      </td>
                    </tr>
                    <tr :style="width1 === 1280 && height1 === 1024 ? '' : 'height: 3%'">
                      <td>呼吸(次/分)</td>
                      <td
                        v-for="(item, index) in tableData.HX"
                        :key="index"
                        :style="
                          index + 1 !== tableData.HX.length ? 'border-right: 2px solid #D81E06' : ''
                        "
                      >
                        <dl class="table-flex" style="color: #d81e06">
                          <dd
                            v-for="(subItem, subIndex) in item"
                            :key="subIndex"
                            class="align-items-start"
                            :class="{ 'align-items-end': subItem && subItem.type !== 'up' }"
                            style="font-size: 16px"
                          >
                            {{ subItem ? subItem.val || '' : '' }}
                          </dd>
                        </dl>
                      </td>
                    </tr>
                    <tr style="height: 6%">
                      <td>血压(mmHg)</td>
                      <td
                        v-for="(item, i) in tableData.SYXY"
                        :key="i"
                        :style="
                          i + 1 !== tableData.SYXY.length ? 'border-right: 2px solid #D81E06' : ''
                        "
                      >
                        <dl class="table-flex">
                          <dd
                            v-for="(e_2, index) in item"
                            :key="index"
                            class="flex-column"
                            style="line-height: 1.2"
                          >
                            <span
                              @mouseout="sytool('sy', false, (e_2 && e_2.SY) || '', i, index)"
                              @mouseover="sytool('sy', true, (e_2 && e_2.SY) || '', i, index)"
                              style="font-size: 12px; height: 20px"
                            >
                              {{ (e_2 && e_2.SY) || '' }}
                            </span>
                            <span :id="'syShow' + i + '_' + index"></span>
                            <span
                              style="
                                border-top: 2px solid rgba(0, 0, 0, 0.3);
                                transform: skewY(-30deg);
                                width: 100%;
                                margin: 6px 0;
                              "
                            ></span>
                            <span
                              @mouseout="sytool('xy', false, (e_2 && e_2.XY) || '', i, index)"
                              @mouseover="sytool('xy', true, (e_2 && e_2.XY) || '', i, index)"
                              style="font-size: 12px; height: 20px"
                            >
                              {{ (e_2 && e_2.XY) || '' }}
                            </span>
                            <span
                              v-if="e_2 && (e_2.XYBW === '下左' || e_2.XYBW === '下右')"
                              style="
                                font-size: 14px;
                                color: red;
                                position: absolute;
                                bottom: 0;
                                writing-mode: vertical-lr;
                                text-orientation: upright;
                              "
                            >
                              下
                            </span>
                            <span :id="'xyShow' + i + '_' + index"></span>
                          </dd>
                        </dl>
                      </td>
                    </tr>
                    <tr>
                      <td>体重(Kg)/BMI</td>
                      <td v-for="(item, index) in tableData.TZ" :key="index">{{ item }}</td>
                    </tr>
                    <tr>
                      <td>总入量</td>
                      <td v-for="(item, index) in tableData.RL" :key="index">{{ item }}</td>
                    </tr>
                    <tr>
                      <td style="font-size: 20px">总出量/尿量(ml)</td>
                      <td v-for="(item, index) in tableData.ZCL" :key="index">
                        {{ tableData.ZCL[index] || '' }} / {{ tableData.NL[index] || '' }}
                      </td>
                    </tr>
                    <tr>
                      <td style="font-size: 20px">其他排出量/引流量(ml)</td>
                      <td v-for="(item, index) in tableData.CL" :key="index">
                        {{ tableData.CL[index] || '' }} / {{ tableData.YLL[index] || '' }}
                      </td>
                    </tr>
                    <tr>
                      <td>大便次数</td>
                      <td v-for="(item, index) in tableData.DBCS" :key="index">{{ item }}</td>
                    </tr>
                    <tr>
                      <td>身高/腹围</td>
                      <td v-for="(item, index) in tableData.SG" :key="index">
                        {{ tableData.SG[index] || '' }}
                        <span v-if="tableData.SG[index]">/</span>
                        {{ tableData.FW[index] || '' }}
                      </td>
                    </tr>
                    <tr>
                      <td style="font-size: 20px">经皮胆红汞值(mg/dl)</td>
                      <td v-for="(item, index) in tableData.QT1" :key="index">{{ item }}</td>
                    </tr>
                    <tr>
                      <td>其他/握力</td>
                      <td v-for="(item, index) in tableData.QT2" :key="index">
                        <template v-if="tableData.QT2[index] || tableData.WL || tableData.WOLI">
                          {{ tableData.QT2[index] }} /
                          <template v-if="tableData.WL && tableData.WL[index] !== undefined">
                            {{ tableData.WL[index] }}
                          </template>
                          <template
                            v-else-if="tableData.WOLI && tableData.WOLI[index] !== undefined"
                          >
                            {{ tableData.WOLI[index] }}
                          </template>
                        </template>
                        <template v-else>/</template>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div class="twd-data-page">
                第
                <span>1</span>
                页
              </div>
            </div>
          </div>
        </div>
      </el-aside>
    </el-container>
  </div>
</template>

<script>
import {
  getHeightByBingRenBH,
  getPatientBasic,
  getShiJianFWByBlid,
  getTemperatureListVo
} from '@/api/temperature-sheet'
import { format, parseISO, addDays, startOfWeek, endOfWeek, differenceInDays } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      heart_o: require('./images/heart_o.png'), //空心爱心
      heart: require('./images/heart.png'), //实心爱心
      height1: '', //屏幕的高度
      width1: '', //屏幕的宽度
      isTwdShow: false, //默认是否展示体温单
      kaiShiSJ: format(startOfWeek(new Date()), 'yyyy-MM-dd'), //开始时间
      jieShuSJ: format(endOfWeek(new Date()), 'yyyy-MM-dd'), //结束时间
      temperatureDayList: [], //体温单选择列表周
      patientBasic: {}, //病人基本信息
      CLSJarr: [],
      tableData: {
        CLSJ: [], //日期
        ZYTS: [], //天数
        SSTS: [], //手术天数
        TW: [], //体温
        XLMB: [], //脉搏&&心率
        TTCD: [], //疼痛评分
        HX: [], //呼吸
        SYXY: [], //血压
        TZ: [], //体重
        RL: [], //入量
        NL: [], //尿量
        ZCL: [], //总出量
        CL: [], //出量
        YLL: [], //引流量
        DBCS: [], //大便次数
        SG: [], //身高
        FW: [], //腹围
        QT1: [], //经皮胆红汞值
        QT2: [] //其他
      } //数据基础数据
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit
    }),
    bingLiID() {
      return this.$route.params.id
    },
    // 处理日期方法
    formattedDates() {
      return this.tableData.CLSJ.map((dateStr, index) => {
        if (index === 0) return dateStr
        const currentDate = new Date(dateStr)
        const prevDate = new Date(this.tableData.CLSJ[index - 1])
        const isNewMonth = currentDate.getMonth() !== prevDate.getMonth()
        const isNewYear = currentDate.getFullYear() !== prevDate.getFullYear()
        return isNewMonth || isNewYear ? dateStr : String(currentDate.getDate()).padStart(2, '0')
      })
    }
  },
  async mounted() {
    await this.init()
    console.log(this.patientDetail)
  },
  methods: {
    // 页面初始化
    async init() {
      this.getPatientBasic()
      this.getShiJianFWByBlid()
    },
    // 点击体温单列表某一周
    handleWenkClick(e) {
      this.kaiShiSJ = e.kaiShiDay
      this.jieShuSJ = e.jieShuDay
      this.getTemperatureListVo()
    },
    // 根据病人编号获取最近一次住院的最新的身高体重
    async getHeightByBingRenBH(data) {
      try {
        const res = await getHeightByBingRenBH({
          bingRenBH: this.patientDetail.bingRenBH
        })
        if (res.hasError === 0) {
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 根据病例ID获取更基础信息
    async getPatientBasic(data) {
      try {
        const res = await getPatientBasic({
          bingLiID: this.patientDetail.bingLiID
        })
        if (res.hasError === 0) {
          this.patientBasic = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 查询一个BingLiID的体温单时间范围
    async getShiJianFWByBlid(data) {
      try {
        const res = await getShiJianFWByBlid({
          bingLiID: this.patientDetail.bingLiID
        })
        if (res.hasError === 0) {
          let arr = res.data
          const startDate = parseISO(res.data[0])
          this.temperatureDayList = arr.map((dateStr, index) => {
            const currentDate = parseISO(dateStr)
            const daysDiff = differenceInDays(currentDate, startDate)
            const nextWeekDate = addDays(startDate, daysDiff + 6)
            const weekNumber = index + 1
            return {
              kaiShiDay: dateStr,
              jieShuDay: format(nextWeekDate, 'yyyy-MM-dd'),
              weekNumber: weekNumber,
              daysFromStart: daysDiff + 1
            }
          })
          this.kaiShiSJ = this.temperatureDayList[this.temperatureDayList.length - 1].kaiShiDay
          this.jieShuSJ = this.temperatureDayList[this.temperatureDayList.length - 1].jieShuDay
          this.getTemperatureListVo()
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 查询一个时间范围的一个BingLiID的体温单数据
    async getTemperatureListVo(data) {
      try {
        const res = await getTemperatureListVo({
          bingLiID: this.patientDetail.bingLiID,
          kaiShiSJ: this.kaiShiSJ + ' 00:00:00',
          jieShuSJ: this.jieShuSJ + ' 23:59:59'
        })
        if (res.hasError === 0) {
          this.temperatureInfo = res.data
          this.isTwdShow = true
          this.changeTemperature()
          this.$nextTick(() => {
            this.cavsTable()
          })
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 获取体温单数据
    changeTemperature() {
      this.tableData = {
        CLSJ: [], //日期
        ZYTS: [], //天数
        SSTS: [], //手术天数
        TW: [], //体温
        XLMB: [], //脉搏&&心率
        TTCD: [], //疼痛评分
        HX: [], //呼吸
        SYXY: [], //血压
        TZ: [], //体重
        RL: [], //入量
        NL: [], //尿量
        ZCL: [], //总出量
        CL: [], //出量
        YLL: [], //引流量
        DBCS: [], //大便次数
        SG: [], //身高
        FW: [], //腹围
        QT1: [], //经皮胆红汞值
        QT2: [] //其他
      }
      for (var i = 0; i < 7; i++) {
        //当前选择日期
        const startDate = new Date(this.kaiShiSJ)
        const nextDate = addDays(startDate, i)
        this.tableData.CLSJ.push(format(nextDate, 'yyyy-MM-dd'))

        //当前住院天数
        const targetItem = this.temperatureDayList.find((item) => item.kaiShiDay == this.kaiShiSJ)
        this.tableData.ZYTS.push(targetItem.daysFromStart + i)

        //手术天数
        let ssjl = ''
        let ssjl_arr = this.temperatureInfo.shouShuTZDSJ.sort((a, b) => {
          a.shouShuKSSJ = `/Date(${new Date(a.shouShuKSSJ).getTime()})/`
          b.shouShuKSSJ = `/Date(${new Date(b.shouShuKSSJ).getTime()})/`
          if (a.shouShuKSSJ == null) {
            a.shouShuKSSJ = ''
          }
          if (b.shouShuKSSJ == null) {
            b.shouShuKSSJ = ''
          }
          return parseInt(b.shouShuKSSJ.slice(6, 19)) - parseInt(a.shouShuKSSJ.slice(6, 19))
        })
        for (let [index, item] of ssjl_arr.entries()) {
          let day1 =
            parseInt(`/Date(${new Date(this.kaiShiSJ).getTime()})/`.slice(6, 19)) -
            parseInt(
              `/Date(${new Date(item.shouShuKSSJ).getTime()})/` == null
                ? ''
                : `/Date(${new Date(item.shouShuKSSJ).getTime()})/`.slice(6, 19)
            )
          let day = Math.floor(day1 / (24 * 60 * 60 * 1000)) + i + 1
          if ((day > 0) & (day <= 7)) {
            ssjl += `/${day}`
          }
        }
        this.tableData.SSTS.push(ssjl.substr(1))

        //初始化this.tableData
        this.tableData.TW[i] = [] //体温
        this.tableData.XLMB[i] = [] //脉搏&&心率
        this.tableData.TTCD[i] = [] //脉搏&&心率
        this.tableData.HX[i] = ['', '', '', '', '', ''] //呼吸
        this.tableData.SYXY[i] = ['', '', '', '', '', ''] //血压
        this.tableData.TZ[i] = '' //体重
        this.tableData.RL[i] = '' //入量
        this.tableData.NL[i] = '' //尿量
        this.tableData.ZCL[i] = '' //总出量
        this.tableData.CL[i] = '' //出量
        this.tableData.YLL[i] = '' //引流量
        this.tableData.DBCS[i] = '' //大便次数
        this.tableData.SG[i] = '' //身高
        this.tableData.FW[i] = '' //腹围
        this.tableData.QT1[i] = '' //经皮胆红汞值
        this.tableData.QT2[i] = '' //其他
      }

      let arr = this.temperatureInfo.shengMingTZSJ
      // let arr = JSON.parse(this.tempInfo.smtzData)
      arr = arr.sort(function (a, b) {
        let fd = new Date(a.chuLiSJ).getTime() - new Date(b.chuLiSJ).getTime()
        if (fd < 0) return -1
        else return 1
      })
      this.CLSJarr = arr
      let idx = 1

      for (let obj of arr) {
        obj.shiJianDian < 99 ? idx++ : ''
        //判断这条是否同一天
        let index = this.tableData.CLSJ.indexOf(obj.chuLiSJ.split(' ')[0])
        let time = obj.chuLiSJ.split(' ')[1].split(':')
        let SJD = Number(time[0]) + Number(time[1]) / 60 + Number(time[2]) / 3600
        //体温
        obj.tiWen
          ? this.tableData.TW[index].push({
              SJD: SJD,
              TW: obj.tiWen,
              FCTW: obj.fuCeTW,
              TWCLBW: obj.tiWenCLBW
            })
          : ''
        //脉搏
        obj.maiBo
          ? this.tableData.XLMB[index].push({
              SJD: SJD,
              VAL: obj.maiBo,
              type: 'heart'
            })
          : ''
        //心率
        obj.xinLv
          ? this.tableData.XLMB[index].push({
              SJD: SJD,
              VAL: obj.xinLv,
              type: 'heart_o'
            })
          : ''
        //疼痛程度
        obj.tengTongCD != null
          ? this.tableData.TTCD[index].push({
              SJD: SJD,
              VAL: obj.tengTongCD,
              type: 'circular_o_xs'
            })
          : ''
        //呼吸(分上下)
        obj.shiJianDian < 99
          ? (this.tableData.HX[index][
              (Number(obj.shiJianDian < 98 ? obj.shiJianDian : SJD) + 2) / 4 - 1
            ] = {
              type: idx % 2 == 0 ? `up` : `down`,
              val: obj.huXi
            })
          : ''
        //血压(分上下)
        if (obj.shuZhangYa > 0 && obj.shouSuoYa > 0) {
          obj.shiJianDian < 99
            ? (this.tableData.SYXY[index][
                Math.ceil(Number(obj.shiJianDian < 98 ? obj.shiJianDian : SJD) / 6) - 1
              ] = {
                SY: obj.shuZhangYa,
                XY: obj.shouSuoYa,
                XYBW:
                  obj.xieYaCLBW == '00'
                    ? '上左'
                    : obj.xieYaCLBW == '01'
                    ? '上右'
                    : obj.xieYaCLBW == '10'
                    ? '下左'
                    : obj.xieYaCLBW == '11'
                    ? '下右'
                    : ''
              })
            : ''
        }
        // 体重
        obj.tiZhong
          ? (this.tableData.TZ[index] =
              obj.tiZhong == -1
                ? '平车'
                : obj.tiZhong == -2
                ? '轮椅'
                : obj.tiZhong == -3
                ? '卧床'
                : `${obj.tiZhong} / ${
                    obj.shenGao && obj.tiZhong
                      ? (obj.tiZhong / Math.pow(obj.shenGao / 100, 2)).toFixed(1)
                      : ''
                  }`)
          : ''
        obj.zongRuLiang ? (this.tableData.RL[index] = obj.zongRuLiang) : '' //入量
        obj.niaoLiang ? (this.tableData.NL[index] = obj.niaoLiang) : '' //尿量
        obj.zongChuLiang ? (this.tableData.ZCL[index] = obj.zongChuLiang) : '' //总出量
        obj.qiTaPCL ? (this.tableData.CL[index] = obj.qiTaPCL) : '' //出量
        obj.yinLiuLiang ? (this.tableData.YLL[index] = obj.yinLiuLiang) : '' //引流量
        obj.daBianCS ? (this.tableData.DBCS[index] = obj.daBianCS) : '' //大便次数
        obj.shenGao ? (this.tableData.SG[index] = obj.shenGao) : '' //身高
        obj.fuWei ? (this.tableData.FW[index] = obj.fuWei) : '' //腹围
        obj.jingPiDHSZ ? (this.tableData.QT1[index] = obj.jingPiDHSZ) : '' //经皮胆红汞值
        obj.qiTa2 ? (this.tableData.QT2[index] = obj.qiTa2) : '' //其他
      }
    },
    // 绘制线条
    cavsTable() {
      var canvas = document.querySelector('.cavsTable1')
      var ctx = canvas.getContext('2d')
      let width = 1090 //表格宽
      let height = 1096 //表格高
      canvas.width = width
      canvas.height = height + 20
      ctx.lineWidth = 2
      this.height1 = window.screen.height
      this.width1 = window.screen.width
      var TW = [] //体温
      var FCTW = [] //复测体温
      this.tableData.TW.map((item, i) => {
        item.map((el, idx) => {
          if (el.FCTW) {
            FCTW.push({
              x: width * ((Number(el.SJD) + 24 * i) / 168),
              y: (height * (42.4 - Number(el.FCTW))) / 10.4,
              oldY: (height * (42.4 - Number(el.TW))) / 10.4 //原来的点位
            })
          }
          TW.push({
            x: width * ((Number(el.SJD) + 24 * i) / 168),
            y: (height * (42.4 - Number(el.TW))) / 10.4,
            type:
              el.TWCLBW == 1
                ? 'circular'
                : el.TWCLBW == 2
                ? 'circular_o'
                : el.TWCLBW == 3
                ? 'fork'
                : el.TWCLBW == 4
                ? 'triangle_o'
                : el.TWCLBW == 5
                ? 'square'
                : ''
          })
        })
      })
      this.drawPoints(ctx, TW)
      this.drawPointsFCTW(ctx, FCTW)
      var XLMB = [] //心率脉搏
      this.tableData.XLMB.map((item, i) => {
        item.map((el, idx) => {
          XLMB.push({
            x: width * ((Number(el.SJD) + 24 * i) / 168),
            y: (height * (252 - Number(el.VAL))) / 212,
            type: el.type
          })
        })
      })
      this.drawPoints(ctx, XLMB)
      var TTCD = [] //疼痛程度
      this.tableData.TTCD.map((item, i) => {
        item.map((el, idx) => {
          TTCD.push({
            x: width * ((Number(el.SJD) + 24 * i) / 168),
            y: ((height - 1) * (104 - Number(el.VAL))) / 104,
            type: el.type
          })
        })
      })
      this.drawPoints(ctx, TTCD)
      //显示文字
      let time = function (e) {
        if (e) {
          return new Date(parseInt(e.replace('/Date(', '').replace(')/', '')))
        } else {
          return new Date()
        }
      }
      let textList = []

      //入院
      let bqryrq = new Date(this.temperatureInfo.bingQuRYSJ).getTime()
      textList.push({
        name: '入院' + this.chineseDate(time(`/Date(${bqryrq})/`)),
        time: format(new Date(bqryrq), 'yyyy-MM-dd'),
        SJD: this.getSjd(format(new Date(bqryrq), 'HH'))
      })
      // 出院
      let bqcyrq = new Date(this.temperatureInfo.bingQuCYSJ).getTime()
      textList.push({
        name: '出     院',
        time: format(new Date(bqcyrq), 'yyyy-MM-dd'),
        SJD: this.getSjd(format(new Date(bqcyrq), 'HH'))
      })
      //转科
      for (let item of this.temperatureInfo.zhuanChuangJLSJ) {
        textList.push({
          name:
            '转科' + this.chineseDate(time(`/Date(${new Date(item.zhuanChuangSJ).getTime()})/`)),
          time: format(new Date(new Date(item.zhuanChuangSJ).getTime()), 'yyyy-MM-dd'),
          SJD: this.getSjd(format(new Date(new Date(item.zhuanChuangSJ).getTime()), 'HH'))
        })
      }

      //手术
      for (let item of this.temperatureInfo.shouShuTZDSJ) {
        if (item.shenPiYJ !== 0 && [0, 9].indexOf(item.zhuangTaiBZ) == -1) {
          textList.push({
            name:
              item.maZuiHZ == 0 && ['05', '06'].indexOf(item.shouShuShiDM) > -1
                ? '介     入'
                : '手     术',
            time: format(new Date(item.shouShuKSSJ || ''), 'yyyy-MM-dd'),
            SJD: this.getSjd(format(new Date(item.shouShuKSSJ), 'HH'))
          })
        }
      }

      //操作信息
      for (let item of this.temperatureInfo.shengMingTZSJ) {
        let name = ''
        if (item.caoZuoXX) {
          switch (item.caoZuoXX) {
            case '1':
              textList.push({
                name: `${this.chineseDate(new Date(item.chuLiSJ))}分娩`,
                time: item.chuLiSJ.split(' ')[0],
                SJD: item.shiJianDian
              })
              break
            case '2':
              let same = textList.find(
                (e) => e.SJD == item.shiJianDian && e.time == item.chuLiSJ.split(' ')[0]
              )
              if (same) {
                same.name = same.name.replace(/\s*/g, '') + '╱机械通气'
              } else {
                textList.push({
                  name: '机 械 通 气',
                  time: item.chuLiSJ.split(' ')[0],
                  SJD: item.shiJianDian
                })
              }
              break
            case '3':
              textList.push({
                name: `${this.chineseDate(new Date(item.chuLiSJ))}死亡`,
                time: item.chuLiSJ.split(' ')[0],
                SJD: item.shiJianDian
              })
              break
          }
        }
      }
      for (let obj of textList) {
        let i = this.tableData.CLSJ.indexOf(obj.time)
        if (i > -1) {
          ctx.font = '24px Georgia'
          ctx.fillStyle = '#ff0000'
          for (let [index, item] of obj.name.split('').entries()) {
            ctx.fillText(item, width * ((Number(obj.SJD) + 24 * i) / 168) - 12, 65 + index * 24)
          }
        }
      }
      console.log(textList)
    },
    //曲线点坐标
    drawPoints(ctx, data) {
      let that = this
      // 设置坐标点的大小  dotSize
      var dotSize = 14
      //上一次循环的点
      var oldPoint = {
        x: 0,
        y: 0
      }
      ctx.setLineDash([])
      //排序
      // 遍历点的坐标,以及绘画点
      data.sort(this.compare('x')).forEach(function (item, i) {
        // data.sort().forEach(function (item, i) {
        // 设置坐标点的中心圆点位置（x0，y0）
        var x = item.x
        var y = item.y
        // 绘画坐标点
        switch (item.type) {
          case 'circular_o': //空心圆
            ctx.beginPath()
            ctx.arc(x, y, dotSize / 2, 0, 2 * Math.PI)
            ctx.strokeStyle = '#4E6EF2'
            ctx.stroke()
            break
          case 'circular_o_xs': //小空心圆红
            ctx.beginPath()
            ctx.arc(x, y, dotSize / 3.5, 0, 2 * Math.PI)
            ctx.strokeStyle = '#D81E06'
            ctx.stroke()
            break
          case 'circular': //实心圆
            ctx.beginPath()
            ctx.arc(x, y, dotSize / 2, 0, 2 * Math.PI)
            ctx.fillStyle = '#4E6EF2'
            ctx.fill()
            break
          case 'fork': //叉叉
            ctx.beginPath()
            ctx.moveTo(x - dotSize / 2, y - dotSize / 2)
            ctx.lineTo(x + dotSize / 2, y + dotSize / 2)
            ctx.moveTo(x - dotSize / 2, y + dotSize / 2)
            ctx.lineTo(x + dotSize / 2, y - dotSize / 2)
            ctx.strokeStyle = '#4E6EF2'
            ctx.stroke()
            break
          case 'triangle_o': //空心三角形
            ctx.beginPath()
            ctx.moveTo(x, y - dotSize * 0.6)
            ctx.lineTo(x + dotSize / 2, y + dotSize * 0.4)
            ctx.lineTo(x - dotSize / 2, y + dotSize * 0.4)
            ctx.closePath() //闭合路径
            ctx.strokeStyle = '#4E6EF2'
            ctx.stroke()
            break
          case 'square': //实心正方形
            ctx.fillStyle = '#4E6EF2'
            ctx.fillRect(x - dotSize / 2, y - dotSize / 2, dotSize, dotSize)
            break
          case 'heart_o': //空心爱心
            var img = new Image()
            img.src = that.heart_o
            img.onload = function () {
              ctx.drawImage(img, x - dotSize / 2, y - dotSize / 2, dotSize * 1.2, dotSize * 1.2)
            }
            break
          case 'heart': //实心爱心
            var img = new Image()
            img.src = that.heart
            img.onload = function () {
              ctx.drawImage(img, x - dotSize / 2, y - dotSize / 2, dotSize * 1.2, dotSize * 1.2)
            }
            break
        }
        //绘制线
        if (i > 0) {
          ctx.beginPath()
          ctx.moveTo(oldPoint.x, oldPoint.y)
          ctx.lineTo(x, y)
          ctx.strokeStyle = ['heart_o', 'heart'].indexOf(item.type) > -1 ? '#D81E06' : '#4E6EF2'
          ctx.stroke()
        }
        oldPoint = {
          x,
          y
        }
      })
    },
    drawPointsFCTW(ctx, data) {
      // 设置坐标点的大小  dotSize
      var dotSize = 14
      // 遍历点的坐标,以及绘画点
      data.forEach(function (item, i) {
        ctx.setLineDash([])
        // 设置坐标点的中心圆点位置（x0，y0）
        var x = item.x
        var y = item.y
        //空心圆红
        ctx.beginPath()
        ctx.arc(x, y, dotSize / 2, 0, 2 * Math.PI)
        ctx.strokeStyle = '#D81E06'
        ctx.stroke()
        //绘制虚线
        ctx.beginPath()
        ctx.setLineDash([4, 2])
        ctx.moveTo(item.x, item.oldY)
        ctx.lineTo(item.x, item.y)
        ctx.strokeStyle = '#D81E06'
        ctx.stroke()
      })
    },
    //打印
    preview() {
      window.print()
    },
    // 放大缩小
    ZoomIt(flag) {
      const element = document.getElementById('twd_table')
      const currentZoom = parseFloat(element.style.zoom)
      if (flag == '+') {
        element.style.zoom = Math.min(currentZoom + 0.5, 3)
      } else if (flag === '-' && currentZoom > 0.5) {
        element.style.zoom = Math.max(currentZoom - 0.5, 0.5)
      }
    },
    //中文数字
    chineseDate(date) {
      var hours = date.getHours()
      var minutes = date.getMinutes()
      // 组装小时
      hours = this.formatNumber(hours) + '时'
      // // 组装分钟
      if (minutes < 10) {
        minutes = '零' + this.formatNumber(minutes) + '分'
      } else {
        minutes = this.formatNumber(minutes) + '分'
      }
      return `${hours}${minutes}`
    },

    // 中文汉字
    formatNumber(n) {
      var arr = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
      if (n <= 10) {
        // 几
        return arr[n]
      } else if (n < 20) {
        // 十几
        return '十' + arr[n - 10]
      } else if (n >= 20 && n % 10 === 0) {
        // 几十
        return arr[n / 10] + '十'
      } else {
        // 几十几
        var a = parseInt(n / 10)
        var b = n % 10

        return arr[a] + '十' + arr[b]
      }
    },
    //时间点
    getSjd(hour) {
      let Numhour = parseInt(hour)
      if (Numhour < 4) {
        return 2
      } else if (Numhour < 8) {
        return 6
      } else if (Numhour < 12) {
        return 10
      } else if (Numhour < 16) {
        return 14
      } else if (Numhour < 20) {
        return 18
      } else if (Numhour <= 24) {
        return 22
      }
    },
    //对象数组按属性排序
    compare(key) {
      return function (value1, value2) {
        var val1 = value1[key]
        var val2 = value2[key]
        return val1 - val2
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.temperature-sheet-view {
  background-color: #fff;
  padding: 8px;
  height: 100%;

  .left-aside {
    width: 19% !important;
    margin-right: 10px;
  }

  .left {
    background-color: #eff3fb;
    border-radius: 4px;
    padding: 12px;
    position: relative;
    // height: 100%;

    .left-tips {
      display: flex;
      font-size: 14px;
      padding: 6px 10px;
      border: 1px solid #a2bae5;
      background-color: #e9f1ff;

      position: sticky; // 使用 sticky 定位
      top: 0; // 距离容器顶部0px
      z-index: 10; // 适当层级
      background-color: #e9f1ff; // 保持背景色
      margin-bottom: 10px; // 保持原有间距

      .el-icon-warning {
        font-size: 16px;
        color: #356ac5;
        margin-right: 4px;
        margin-top: -2px;
      }

      .el-icon-title {
        width: 60px;
      }

      .el-icon-content {
        width: 130%;
        line-height: 20px;
        margin-top: -3px;
      }
    }

    .left-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 10px 0;

      .title {
        position: relative;
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        margin-left: 9px;
      }

      .title::before {
        position: absolute;
        left: -9px;
        width: 3px;
        height: 14px;
        content: '';
        background-color: #356ac5;
      }

      ::v-deep .el-button {
        padding: 6px 10px;
        background-color: #3b76ef;
      }
    }
  }
}

.right-aside {
  width: 80.3% !important;

  .right {
    background-color: #eff3fb;
    border-radius: 4px;
    padding: 12px;
    position: relative;
    // height: 100%;
  }

  .right-button {
    display: flex;
    font-size: 14px;
    padding: 14px;
    margin-bottom: 14px;
    border: 1px solid #dadee5;
    background-color: #e9f1ff;

    position: sticky; // 使用 sticky 定位
    top: 0; // 距离容器顶部0px
    z-index: 10; // 适当层级
    background-color: #e9f1ff; // 保持背景色
    margin-bottom: 14px; // 保持原有间
  }

  .temperature-sheet-box {
    background: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .twd-data-info {
      zoom: 1.94;
      .hospital-title {
        text-align: center;
        padding: 30px 0 16px;

        .hospital-name {
          font-size: 26px;
          font-weight: bold;
          color: black;
          font-family: '楷体';
          text-align: center;
        }
      }

      .record-type {
        font-size: 16px;
      }

      .patient-info-row {
        font-size: 16px;
      }

      .info-value {
        font-family: '楷体';
      }
    }
  }
}

.twd-data {
  width: 1400px;
  height: 2040px;
  padding: 30px 80px 60px;
  zoom: 0.5;
  color: #000;
}

.twd-data-info {
  zoom: 1.94;
}

.twd-data-table .e-table {
  width: 1248px;
  height: 1828px;
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;
  font-size: 20px;
  line-height: 1;
}

.twd-data-table .e-table table {
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;
  width: 100%;
}

.twd-data-table .e-table > tbody > tr {
  height: 46px;
  overflow: hidden;
}

.twd-data-table .e-table > tbody > tr > td {
  border: 2px solid #000;
  text-align: center;
  padding: 0;
  position: relative;
}

.twd-data-table .e-table > tbody > tr > td:first-child {
  font-size: 24px;
  // width: 126px;
}

.twd-data-table .e-table dl.table-flex {
  display: flex;
  height: 100%;
  margin: 0;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  left: 0;
}

.twd-data-table .e-table dl.table-flex.table-colnum {
  flex-flow: column;
}

.twd-data-table .e-table dl.table-flex > dd {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-left: 2px solid rgba(0, 0, 0, 0.3);
  letter-spacing: -2px;
}

.twd-data-table .e-table dl.table-flex > dd.ssd {
  overflow: inherit;
}

.twd-data-table .e-table dl.table-flex.table-colnum > dd {
  border-top: 2px solid rgba(0, 0, 0, 0.3);
  border-left: none;
}

.twd-data-table .e-table dl.table-flex > dd:first-child,
.twd-data-table .e-table dl.table-flex.table-colnum > dd:first-child {
  border: none;
}

.twd-data-table .e-table dl.table-flex.table-colnum > dd:nth-child(5n-2) {
  border-color: #000;
}

.twd-data-table .e-table dl.table-flex.table-colnum > dd:nth-child(28),
.twd-data-table .e-table dl.table-flex.table-colnum > dd:nth-child(43) {
  border-color: #d81e06;
}

.twd-data-table .e-table .mb,
.twd-data-table .e-table .ssd {
  display: flex;
  flex-flow: column;
  line-height: 1;
  justify-content: flex-start !important;
}

.twd-data-table .e-table .mb > span {
  margin-bottom: 81px;
}

.twd-data-table .e-table .mb > span:first-child {
  margin-top: 10px;
  margin-bottom: 186px;
}

.twd-data-table .e-table .mb > span:nth-last-child(2) {
  margin-bottom: 69px;
}

.twd-data-table .e-table .ssd > span {
  margin-bottom: 81px;
}

.twd-data-table .e-table .ssd > span:first-child {
  margin-top: 10px;
  margin-bottom: 81px;
}

.twd-data-table .e-table .ssd > span:nth-last-child(2) {
  margin-bottom: 190px;
}

.twd-data-page {
  text-align: center;
  font-size: 24px;
}

.twd-data-page > span {
  display: inline-block;
  width: 70px;
}

::v-deep .el-table th.el-table__cell.is-leaf {
  padding: 8px;
}

::v-deep .el-table--mini .el-table__cell {
  padding: 12px 0;
  padding-left: 10px;
}

::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}

::v-deep .el-table__row {
  cursor: pointer;
}
.align-items-start {
  align-items: flex-start !important;
}
.align-items-end {
  align-items: flex-end !important;
}
.flex-column {
  flex-direction: column !important;
}
</style>
