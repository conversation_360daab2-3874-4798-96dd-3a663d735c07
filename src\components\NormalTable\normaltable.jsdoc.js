/**
 * @typedef {Object} TableConfig
 * @property {boolean?} isSelection 是否显示复选框
 * @property {boolean?} isSelectMore 是否多选
 * @property {boolean?} isRowClick 是否使用行点击
 * @property {boolean?} isStripe 隐藏斑马纹，与背景色不可同时用
 * @property {boolean?} isShowSummary 是否显示共计
 * @property {boolean?} isIndex 自定义序号
 * @property {string?} tableHeight
 * @property {String?} rowKey 行的 key
 * @property {Object? } treeProps
 * @extends {import('element-ui/types/table').ElTable}
 *
 */

/**
 * @typedef {Object} TableHeaderItem
 * @property {string} prop 字段
 * @property {string} colWidth 宽度
 * @property {string} title 表头
 * @property {boolean} isSort 是否排序
 * @property {'link'| 'button'|'tag'|'process'|'slot'|'price'} type 类型
 * @property {string} link 跳转
 * @property {string} button 文字按钮
 * @property {string} tag 标签
 * @property {string} process 进度条
 * @property {string} slot 插槽
 * @property {string} price 三位隔开
 * @property {string} priceLink 三位隔开&&跳转
 * @property {string} customIndex 自定义序号
 * @property {string} colMinWidth 最小宽度
 * @property {string} resizeable 是否可重新计算大小
 * @property {boolean} tooltip 是否需要显示文字提示
 * @property {Function} colorRule 根据入参的item.prop名称,返回对应的style
 * @property {Function} tagTypeRule 根据入参的item.prop名称,返回对应的tag-type
 * @property {Function} formatter
 * @property {Boolean} isRide 是否小数
 * @property {TableHeader} children 子数据
 * @extends {import('element-ui/types/table-column').ElTableColumn}
 */

/**
 * @typedef {Array<TableHeaderItem>} TableHeader
 */

/**
 * @typedef {Object} NormalTableProps
 * @property {TableConfig} config
 * @property {TableHeader} tableHeader
 * @property {Function} summaryMethod
 * @property {Function} spanMethod
 * @property {Function | String} rowClassName
 * @property {Function | Object} rowStyle
 * @property {Function | String} cellClassName
 * @property {Function | Object} cellStyle
 * @property {Boolean} loading
 * @property {Boolean} showPage
 * @property {Array<Number>} pageSizes
 * @property {Number} pageNum
 * @property {Number} pageSize
 * @property {Number} total
 * @property {Number} pagerCount
 * @property {Boolean} paginationSmall
 * @property {String} paginationLayout
 * @property {Array} disabledArr
 * @property {Function} search
 * @property {Array<Object>} data
 * @extends {import('element-ui/types/table').ElTable}
 */
