<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>化验医嘱</title>
  <meta name="description" content="化验医嘱">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
  .container_bqxz {
    overflow: hidden;
  }

  .bqxz_header {
    background: #4682b4;
    border: 1px solid #3D6CC8;
    color: #fff;
    padding: 8px 20px 5px 10px;
    width: 1500px;
  }

  .bqxz_header .bqxz_title {
    color: #333333;
  }

  .bqxz_item>table {
    width: 100%;
    table-layout: fixed;
  }

  .bqxz_item>table label,
  .bqxz_item>table input {
    margin: 0;
  }

  .bqxz_item>table label {
    display: flex;
    align-items: center;
  }

  .bqxz_item>table tr th,
  .bqxz_item>table tr td {
    padding: 2px 5px;
    line-height: 1.1;
  }

  .bqxz_item>table tr th {
    line-height: 1.5;
    /* background: rgba(61, 108, 200, 0.2); */
    background: #85A2DB;
    font-weight: 700;
  }

  .bqxz_item>table tr th:nth-child(1) {
    width: 24px;
  }

  .bqxz_item>table tr th:nth-child(2) {
    width: 80px;
  }

  .bqxz_item>table tr th:nth-child(3) {
    width: 180px;
  }

  .bqxz_item>table tr th:nth-child(4) {
    width: 80px;
  }

  .bqxz_item>table tr:nth-child(2n+1) {
    background: #EFF3FB;
  }

  .bqxz_item>table tr td {
    cursor: pointer;
    border-bottom: 1px solid #DADADA;
  }

  .bqxz_item>table tr td .icon_circular {
    margin-left: 6px;
    margin-bottom: -2px;
    display: inline-block;
    width: 13px;
    height: 13px;
    border-radius: 100%;
  }

  .bqxz_item>table tr td input {
    cursor: pointer;
    display: block;
  }
  .bqxz_item>table>tbody>.bqxz_table_tr{
    background: #3D6CC8;
    color: #fff;
  }

  .bqxz_save>table {
    width: 1500px;
    border: none;
    border-color: grey;
    table-layout: fixed;
  }

  .bqxz_save>table label,
  .bqxz_save>table input {
    margin: 0;
  }

  .bqxz_save>table label {
    display: flex;
    align-items: center;
  }

  .bqxz_save>table tr:nth-child(2n+1) {
    background: #EFF3FB;
  }

  .bqxz_save>table tr th,
  .bqxz_save>table tr td {
    padding: 2px 5px;
    line-height: 1.1;
  }

  .bqxz_save>table tr th {
    background: #85A2DB;
    font-weight: 700;
    color: #fff;
    line-height: 1.5;
  }

  .bqxz_save>table tr td .csskssj {
    height: 24px;
  }

  .bqxz_save>table tr td>span,
  .bqxz_item>table tr td>span {
    display: block;
    padding: 5px 0;
    border-radius: 3px;
  }

  #treeDemo.ztree * {
    font-size: 14px;
  }
  .bqxz_save>table tr .delbtn_not{
    border: 1px solid rgb(133, 127, 127);
    width: 37px;
    color: rgb(133, 127, 127);
    padding: 3px 3px;
  }
  .bqxz_save>table tr .delbtn{
    border: 1px solid red;
    width: 37px;
    color: red;
    padding: 3px 3px;
  }
  .bqxz_save>table tr .delbtn:hover{
    color: #fff;
    background: red;
    /* color: rgb(0 0 0 / 50%);
    border: 1px solid rgb(0 0 0 / 50%); */
  }
  .form_input{
    width: 300px;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%);
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%);
    -webkit-transition: border-color ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
  }
 .form_search{
    float:right;
    margin-right:20px;
    height: 40px;
    padding-top: 2px;
  }
  #treeDemo{
    height: 450px;
    overflow: auto;
    width: 100%;
  }
  #treeDemo .firstli{
    background: rgb(209 209 209 / 40%);
    border: 1px solid #9A9A9A;
    box-sizing: border-box;
    /* padding-left: 5px; */
    font-size: 15px;
    padding: 2px 5px;
    cursor:context-menu;
    /* width: 315px; */
  }
  #treeDemo .firstli i{
    float: right;
    right: 5px;
  }
  #treeDemo .firstactive{
    background: linear-gradient(180deg, #3D6CC8 0%, rgba(61, 108, 200, 0.6) 100%);
    border: 1px solid #3D6CC8;
    color: #fff;
  }
  #treeDemo .boxul{
    background: #F4F4F4;
  }
  #treeDemo .boxli{
    /* padding-left: 5px; */
    font-size: 15px;
    padding: 2px 5px;
    cursor: pointer;
    color: #333333;
  }
  #treeDemo .boxactive{
    color: #1890FF;
  }
  #contain_footer{
    padding-top: 12px;
  }
  .foot_trc{
    font-weight: 700;
    /* background: #85A2DB;
    color: #fff; */
    line-height: 1.5;
  }
  .none{
    display: none;
  }
  .btn_save1{
    padding: 0 15px;
    font-size: 14px;
    color: rgba(0, 0, 0, .65);
    border: 1px solid #d9d9d9;
    border-radius: 4px;
  }
  .row {
    position: relative;
  }
  #tssm_input{
  border: 1px solid;
  width: 315px;
  height: 30px;
  margin: 5px 0;
}
</style>

<body>
  <div id="container-bqxz" class="container_bqxz">
    <div id="bqxz_radio" style="min-height: 40px;height: auto;"></div>
    <div class="row">
      <div class="col-md-2">
        <div id="treeDemo" style="height: 450px;overflow: auto;"></div>
      </div>
      <div class="col-md-5">
        <div id="table_lists" style="border: 1px solid #3D6CC8;height: 450px;overflow-x: hidden;">
          <div id="bqxz_item"></div>
        </div>
        <div class="form-inline flex-row align-items-center" style="margin-top: 15px;">
          <div class="form-group">
            <label for="searchBqxz">输入拼音首字母查找：</label>
            <input type="text" class="form-control" name="searchBqxz" onkeydown="calAge(event)">
          </div>
          <button style="height: 34px;margin-left: 6px;" type="submit" class="e_btn" onclick="searchBqxz()">查找</button>
        </div>
      </div>
      <div class="col-md-5">
        <div style="border: 1px solid #3D6CC8;height: 450px;overflow: auto;">
          <div class="bqxz_header">
            <span id="sel_title" class="title">已选择医嘱</span>
          </div>
          <div id="bqxz_save"></div>
        </div>
        <button style="margin-top: 15px;height: 34px;" class="e_btn btn_save" onclick="getCDSS()">保存</button>
        <button class="btn_save1 none" style="margin-top: 15px;height: 34px;" disabled="disabled">保存</button>
        <button class="e_btn" onclick="window.location.reload()">刷新</button>
        <button class="e_btn" onclick="allClear()">一键清空</button>
        <button style="margin-top: 15px;height: 34px;" id="btn_song" class="e_btn none" onclick="song()">发送短信</button>
      </div>
      <!-- <div class="">
      <table class="table table-hover">
        <tr class="bqxz_table_back">
          <th>序号</th>
          <th>导出时间</th>
          <th>导入人员</th>
          <th>项目名称（五天内已导出医嘱）</th>
          <th>样本类似</th>
          <th>医嘱时间</th>
          <th>医嘱医师</th>
        </tr>
        <tr>
          <th>1</th>
          <th>2021-02-02</th>
          <th>张某某</th>
          <th>*********</th>
          <th>******</th>
          <th>2021-02-02</th>
          <th>某某</th>
        </tr>
      </table>
    </div> -->
    </div>
  </div>
  <div id="contain_footer">
    <table>
      <tbody>
        <tr class="foot_trc">
          <td style="width: 200px; height: 19px">导出时间</td>
          <td style="width: 150px; height: 19px">导出人员</td>
          <td style="width: 350px; height: 19px">项目名称<span style="color: Red"> (5天内已导出医嘱)</span></td>
          <td style="width: 180px; height: 19px">样本类型</td>
          <td style="width: 150px; height: 19px" align="left">医嘱时间</td>
          <td style="width: 100px; height: 19px">医嘱医师</td>
      </tr>
    </tbody>
    </table>
  </div>
  <div class="box_foot none" style="bottom: 0;position: absolute;background: #c0afaf;right: 0;min-height: 200px;width: 250px;">
    <div style="padding: 5px;background: #4682b4;color: #fff;">CDSS智能提示</div>
    <div id="box_lists">
        
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <script type="text/javascript" src="./lib/jquery/base64.js"></script>
  <!-- 树状控件引用-->
  <link rel="stylesheet" href="./lib/zTree/css/zTreeStyle.css" type="text/css">
  <script type="text/javascript" src="./lib/zTree/js/jquery.ztree.core.js"></script>
  <!-- 登录页js文件 -->
  <!-- <script src="js/e-bqxz.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-bqxz.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 惠每质控js -->
  <script>
    
  if(WRT_config.hmzkZW==undefined || !WRT_config.hmzkZW){
    document.write("<script type='text/javascript' src='http://**************/cdss/jssdk?v=4.0&ak=CCD9D75192ACC4529888523BCD5AB9E2'><\/script>");
    // document.write("<script type='text/javascript' src='http://"+  WRT_config.server + "/zyblws/masonQC/wyyyMason.js?v=" + Date.now() + "'><\/script>");
    document.write("<script type='text/javascript' src='../zyblws/masonQC/wyyyMason.js?v=" + Date.now() + "'><\/script>");
  }
  </script>
</body>

</html>