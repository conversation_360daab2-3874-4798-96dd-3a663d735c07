/*
 * @Author: Raven
 * @Date: 2023-02-15 10:43:24
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-02-05 09:08:06
 * @FilePath: \base-wyyy-template\src\store\index.js
 * @Description:
 *
 * Copyright (c) 2024 by 附一医信息处, All Rights Reserved.
 */
import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'

// import lockscreen from './modules/lockscreen'
import createPersistedState from 'vuex-persistedstate'

Vue.use(Vuex)

// https://webpack.js.org/guides/dependency-management/#requirecontext
const modulesFiles = require.context('./modules', true, /\.js$/)

// you do not need `import app from './modules/app'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  // set './app.js' => 'app'
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})

/**
 * store实例
 * @returns {import('vuex').Store}
 */
const store = new Vuex.Store({
  modules,
  getters,
  plugins: [
    createPersistedState({
      key: require('@/settings').vuexSpaceName,
      storage: sessionStorage,
      paths: ['app', 'user', 'lockscreen', 'auth', 'theme', 'patient', 'patientDetail']
    })
  ]
})

export default store
