let params = {}
let url = window.location.href.split("?") || []
if(url[1]){
  let text = url[1].split("&")
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
}

WRT_config.url = params || {}
var obj1 = []
var isShow = false // false:展示血液净化医嘱（初始表格）；true:展示血液净化处方单
var yongHutype = -1
var yzcfTb = [] // 医嘱表单未保存
WRT_config.XQXFYZTBData = []
WRT_config.noxgjcHyjg = []
var allYZData = [] //医嘱表单全部拼接
var keyid = 0
var YFormData = {}

// 统一页面启动
$(document).ready(() => {
  if (params['token']) {
    sessionStorage.setItem('token', params['token'])
  }
  WRT_e.api.CrrtYzjld.getMedicalStaffType({
    params:{ 
      yongHuID: parseInt(params["yongHuID"])
    },
    success(msg){
      console.log('1234567',msg.data);
            
      if (msg.hasError == 0) {
        yongHutype = msg.data
        WRT_e.api.CrrtYzjld.getDoctorSimpleByYongHuID({
          params:{ 
            yongHuID: parseInt(params["yongHuID"])
          },
          success(res){
            console.log('getDoctorSimpleByYongHuID',res.data);
            WRT_config.YHXX = res.data
          }
        })
      }
    }
  })
  
  WRT_e.api.CrrtYzjld.getSMTZ({
    params:{ 
      bingLiID: parseInt(params["blid"])
    },
    success(msg){
      if (msg.hasError == 0) {
        WRT_config.SMTZ = msg.data
        WRT_e.api.CrrtYzjld.getbrSMTZ({
          params:{ 
            zhuYuanID: parseInt(params["blid"])
          },
          success(msg){
            if(msg.hasError ==0){
              WRT_config.brInfo=msg.data || {}
              // 处方单表单
              init()
            }else{
              WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
            }
          }
        })
      }
    }
  })
  // WRT_config.brInfo = 
  // init()
})
/********************初始化********************/
function init() {
  WRT_e.api.CrrtYzjld.getinit({
    params:{ "bingLiID": params["blid"] },
    success(msg){
      if(msg.data){
        WRT_config.yzTbData=msg.data // 根据病例ID查询处方单
        // 患者基本信息
        var BasicInfo = new brBasicInfo_View();
        BasicInfo.$el = $(".LInfo");
        BasicInfo.init({ data: WRT_config.brInfo }).render();
        // BasicInfo.init().render();
        // 获取 表1数据，并绘制 表1 血液净化医嘱
        var CRRTYZTb = new MenuYZTB_View()
        CRRTYZTb.$el = $(".CRRTYzTb");
        CRRTYZTb.init({ data: WRT_config.yzTbData }).render();
      }else{
        WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
      }
    }
  })
}

/********************公共方法********************/
// 点击新增 进入详情设置处方  展示 表2 血液净化处方单  按钮跳转已完成
function addCRRTcfd() {
  this.isShow = !this.isShow
  if (!this.isShow) {
    // 绘制外部框架
    $('.CRRTyz').html(`
      <div class="brBasicInfo">
				<div class="Ltitle label-name">
					<span style="font-weight: bold;">患者基本信息</span>
					<span style="position: relative; float: right;">
						<button class="e_btn addBtn" onclick="addCRRTcfd()">新增</button>
					</span>
				</div>
				<div class="LInfo"> </div>
			</div>
			<div class="CRRTYzTb"></div>
    `)
    // 患者基本信息
    var BasicInfo = new brBasicInfo_View();
    BasicInfo.$el = $(".LInfo");
    BasicInfo.init({ data: WRT_config.brInfo }).render();
    // BasicInfo.init().render();
    // 重新绘制 表1 血液净化医嘱
    var CRRTYZTb = new MenuYZTB_View()
    CRRTYZTb.$el = $(".CRRTYzTb");
    CRRTYZTb.init({ data: WRT_config.yzTbData }).render();
    
    $('.CFDForm').html(``)
    $('.CFDTB').html(``)
    $('.xgjyxmTb').html(``)
  } else {
    // 添加返回按钮
    $('.CRRTyz').html(`
      <button class="e_btn backBtn" onclick="addCRRTcfd()">返回</button>
    `)
    keyid = 0
    // 绘制  表2 血液净化处方单
    // 填写
    let first = WRT_config.yzTbData[0]
    first.id=''
    // keyid = first.id
    // YFormData = {...first}
    var CRRTCFDForm = new MenuCFDForm_View()
    CRRTCFDForm.$el = $(".CFDForm");
    CRRTCFDForm.init({data:first}).render();

    // 展示医嘱表格
    var CRRTCFDForm = new MenuCFDTb_View()
    CRRTCFDForm.$el = $(".CFDTB");
    CRRTCFDForm.init({data: []}).render();
    
    // 相关检验项目
    WRT_e.api.CrrtYzjld.getXGJYXMByID({
      params:{ 
        bingLiID: params["blid"]
      },
      success(res){
        if(res.hasError == 0){
          WRT_config.noxgjcHyjg = res.data
          // 相关检验项目
          var XGJYXMTb = new XGJYXMTb_View()
          XGJYXMTb.$el = $(".xgjyxmTb");
          XGJYXMTb.init({data: WRT_config.noxgjcHyjg}).render();
        } else {
          WRT_e.ui.hint({
            type: 'error',
            msg: res.errorMessage
          })
        }
      }
    })

  }
}
// 血液净化医嘱 按钮
// 详情(编辑按钮) 按钮跳转已完成
function detailsBtn(item){
  keyid = item.id
  this.isShow = !this.isShow
  YFormData = {...item}
  // 添加返回按钮
  $('.CRRTyz').html(`
    <button class="e_btn backBtn" onclick="addCRRTcfd()">返回</button>
  `)
  // 绘制  表2 血液净化处方单
  // 填写
  var CRRTCFDForm = new MenuCFDForm_View()
  CRRTCFDForm.$el = $(".CFDForm");
  CRRTCFDForm.init({ data: item }).render();
  //  医嘱表格
  WRT_e.api.CrrtYzjld.getMedAdviceListByID({
    params:{ 
      ID: parseInt(item.id)
    },
    success(msg){
      if (msg.hasError == 0) {
        WRT_config.XQXFYZTBData = msg.data
        // 展示医嘱表格
        var CRRTCFDForm = new MenuCFDTb_View()
        CRRTCFDForm.$el = $(".CFDTB");
        CRRTCFDForm.init({data: WRT_config.XQXFYZTBData}).render();
        // 相关检验项目
        WRT_e.api.CrrtYzjld.getXGJYXMByID({
          params:{ 
            bingLiID: params["blid"]
          },
          success(res){
            if(res.hasError == 0){
              WRT_config.noxgjcHyjg = res.data
              // 相关检验项目
              var XGJYXMTb = new XGJYXMTb_View()
              XGJYXMTb.$el = $(".xgjyxmTb");
              XGJYXMTb.init({data: WRT_config.noxgjcHyjg}).render();
            } else {
              WRT_e.ui.hint({
                type: 'error',
                msg: res.errorMessage
              })
            }
          }
        })
      }
    }
  })
  
}
// 调整（暂时无功能）
function adjustBtn(){}
// 停止（只有医生能操作）  已完成
function stopBtn(item){
  if (!item.tingZhiSJ) {
    if (yongHutype != '04') { // 目前是医生操作
      // WRT_e.api.CrrtYzjld.getDoctorSimpleByYongHuID({
      //   params:{ 
      //     yongHuID: parseInt(params["yongHuID"])
      //   },
      //   success(msg){
      //     if (msg.hasError == 0) {
            let gxdata = {
              ...item,
              tingZhiSJ: new Date().Format("yyyy-MM-dd HH:mm:ss"), // 停止时间
              tingZhiRYYHID: WRT_config.YHXX.yongHuID || '', // 停止人员id
              tingZhiRYYHXM: WRT_config.YHXX.yongHuXM || '' // 停止人员姓名
            }
            WRT_e.api.CrrtYzjld.updateCrrt({
              params: gxdata,
              success(res){
                if (res.data) {
                  init()
                }
              }
            })
      //     }
      //   }
      // })
    } else {
      WRT_e.ui.hint({
        type: 'warning',
        msg: '该按钮只有医生能进行操作'
      })
    }
  }
}
// 结束 （护士操作）  已完成
function endBtn(item){
  if (!item.jieShuSJ) {
    if (yongHutype == '04') { // 目前是护士操作
      // WRT_e.api.CrrtYzjld.getDoctorSimpleByYongHuID({
      //   params:{ 
      //     yongHuID: parseInt(params["yongHuID"])
      //   },
      //   success(msg){
      //     if (msg.hasError == 0) {
            let gxdata = {
              ...item,
              jieShuSJ: new Date().Format("yyyy-MM-dd HH:mm:ss"), // 结束时间
              jieShuRYYHID: WRT_config.YHXX.yongHuID || '', // 结束人员id
              jieShuRYYHXM: WRT_config.YHXX.yongHuXM || '' // 结束人员姓名
            }
            WRT_e.api.CrrtYzjld.updateCrrt({
              params: gxdata,
              success(res){
                if (res.data) {
                  init()
                }
              }
            })
          // }
        // }
      // })
    } else {
      WRT_e.ui.hint({
        type: 'warning',
        msg: '该按钮只有护士能进行操作'
      })
    }
  }
}

// 血液净化处方单 
// 保存（表单保存） 已完成
function saveCFD (){
  // 适应症参数获取？
  // 适应症
  var syz = document.querySelectorAll('input[name=syz_radio]:checked');
  var SYZvalues = [];
  syz.forEach(function(checkbox) {
    if (checkbox.value.indexOf('其他：')!=-1) {
      SYZvalues.push(checkbox.value + $('.showForm input[name=SYZQT]').val());
    } else {
      SYZvalues.push(checkbox.value);
    }
  });
  // 禁忌症
  var jjz = document.querySelectorAll('input[name=jjz_radio]:checked');
  var JJZvalues = [];
  jjz.forEach(function(checkbox) {
    JJZvalues.push(checkbox.value);
  });
  // 管路预冲（药品）
  var gdycYP = document.querySelectorAll('input[name=guanLuYCYP]:checked');
  var gdycYPvalues = [];
  gdycYP.forEach(function(checkbox) {
    gdycYPvalues.push(checkbox.value);
  });
  // 置换液
  var ZHY = document.querySelectorAll('input[name=zhiHuanYe]:checked');
  var ZHYvalues = [];
  ZHY.forEach(function(checkbox) {
    ZHYvalues.push(checkbox.value);
  });
  // 稀释方式 TXAXSQHVal
  var XSFS = document.querySelectorAll('input[name=txxsq_check]:checked');
  var TXAXSQHVal = [];
  XSFS.forEach(function(checkbox) {
    TXAXSQHVal.push(checkbox.value);
  });
  // 血浆置换
  var XJCS = $('.showForm input[name=xjzhXJ]').val()!=''?'血浆：'+$('.showForm input[name=xjzhXJ]').val():''
  var XJZHCheckbox = document.querySelectorAll('input[name=ZJZH]:checked');
  var XJZHvalue = XJCS==''?[]:[XJCS];
  XJZHCheckbox.forEach(function(checkbox) {
    if (checkbox.value == 'NS1000ml：') {
      XJZHvalue.push(checkbox.value + $('.showForm input[name=xjzhNSVal]').val());
    } else if (checkbox.value == '20%白蛋白：') {
      XJZHvalue.push(checkbox.value + $('.showForm input[name=xjzhBDBVal]').val());
    } else if (checkbox.value == '10%葡萄糖酸钙针：') {
      XJZHvalue.push(checkbox.value + $('.showForm input[name=xjzhPPTSGVal]').val());
    } else if (checkbox.value == '人工胶体：') {
      XJZHvalue.push(checkbox.value + $('.showForm input[name=zjzhRGJTVal]').val());
    } else if (checkbox.value == '其他：') {
      XJZHvalue.push(checkbox.value + $('.showForm input[name=xjzhQTVal]').val());
    }
  });

  let param = {
    "aki": $('.showForm input[name=AKIfq]:checked').val(), // AKI分期：
    "aptt": $('.showForm input[name=KNFAAPPT]').val(), // APTT目标值：
    "id":"", // 新增保存 id传空 这里没序号
    "bingLiID": WRT_config.url["blid"], // 病例ID
    // "bingQuID":"",
    "shouCiLRSJ": new Date().Format("yyyy-MM-dd HH:mm:ss"), // 首次录入时间
    // "xiuGaiSJ":"", // 修改时间
    // "yiZhuYHID":"", // 医嘱用户ID
    // "yiZhuYHXM":"", // 医嘱用户姓名
    // "xiuGaiYHID":"", // 修改用户ID
    // "xiuGaiYHXM":"", // 修改用户姓名
    "yiShengYHID": yongHutype != '04'? WRT_config.YHXX.yongHuID:"", // 医生用户ID
    // "yiShengQM":"", // 医生签名
    "huShiYHID": yongHutype == '04'? WRT_config.YHXX.yongHuID:"", // 护士用户ID
    // "huShiQM":"", // 护士签名
    // "jiGouDM":"", // 机构代码
    "zhenDuanXX": $('.showForm input[name=zhenDuanXX]').val(), //诊断信息
    "shenGao": $('.showForm input[name=shenGao]').val(), // 身高（cm）？
    "tiZhong": $('.showForm input[name=tiZhong]').val(),  // 体重（kg）？
    "tiWen": $('.showForm input[name=tiWen]').val(), // T 体温
    "xueYa": $('.showForm input[name=xueYaSY]').val() + '/' + $('.showForm input[name=xueYaXY]').val(), // 血压 上压/下压
    "xinTiao": $('.showForm input[name=xinTiao]').val(), // P 心跳
    "huXi": $('.showForm input[name=huXi]').val(), // R 呼吸
    "xueGuanTLLX": $('.showForm input[name=xueGuanTLLX]:checked').val() && $('.showForm input[name=xueGuanTLLX]:checked').val().indexOf('其他')!=-1?$('.showForm input[name=xueGuanTLLX]:checked').val() + $('.showForm input[name=xueGuanTLLXVal').val():$('.showForm input[name=xueGuanTLLX]:checked').val(), // 血管通路(类型)
    "xueGuanTLBW": $('.showForm input[name=xueGuanTLBWLR]:checked').val()+','+$('.showForm input[name=xueGuanTLBW]:checked').val(), // 血管通路(部位)
    "shiYingZheng": SYZvalues.join(','), // 适应症
    "jinJiZheng": JJZvalues.join(','), // 禁忌症
    "moShi": $('.showForm input[name=ms_radio]:checked').val() && $('.showForm input[name=ms_radio]:checked').val().indexOf('其他')!=-1?$('.showForm input[name=ms_radio]:checked').val() + $('.showForm input[name=MSQTVal').val():$('.showForm input[name=ms_radio]:checked').val(), // 模式
    "lvQi": $('.showForm input[name=nq_radio]:checked').val() && $('.showForm input[name=nq_radio]:checked').val().indexOf('其他')!=-1?$('.showForm input[name=nq_radio]:checked').val() + $('.showForm input[name=LQVal').val():$('.showForm input[name=nq_radio]:checked').val(), // 滤器
    "guanLuYC": $('.showForm input[name=guanLuYC]').val(), // 管路预冲
    "guanLuYCYP": gdycYPvalues.join(','), // 管路预冲（药品）中内容
    "yuChongYeJRTN": $('.showForm input[name=yuChongYeJRTN]:checked').val(), // 预充液是否进入体内
    "zhiHuanYe": ZHYvalues.join(','), // 置换液
    "xiShiFS": $('.showForm input[name=xsfs_check]:checked').val() && $('.showForm input[name=xsfs_check]:checked').val().indexOf('透析+稀释：')!=-1?$('.showForm input[name=xsfs_check]:checked').val() + TXAXSQHVal.join(','):$('.showForm input[name=xsfs_check]:checked').val(), // 稀释方式
    "kangNingFA": $('.showForm input[name=KNFA_radio]:checked').val() && $('.showForm input[name=KNFA_radio]:checked').val().indexOf('其他')!=-1?$('.showForm input[name=KNFA_radio]:checked').val() + $('.showForm input[name=KNFAVAL').val():$('.showForm input[name=KNFA_radio]:checked').val(), // 抗凝方案（对应医嘱药品）
    "fuHeJL": $('.showForm input[name=KNFAFHJL]').val(), // 负荷计量
    "weiChiJL": $('.showForm input[name=KNFAWC]').val(),  // 维持剂量
    "juYuanSuanJL": $('.showForm input[name=KNFAGJSJL]').val(),  //枸橼酸剂量
    "gaiJiLiang":  $('.showForm input[name=KNFAGJSGJL]').val(),  // 钙剂量
    "qiTaJL": $('.showForm input[name=KNFAGJSQTJL]').val(),  // 钙剂量
    "muBiaoXLL": $('.showForm input[name=mbxls]').val(),  // 目标血流速
    "qianZhiHL": $('.showForm input[name=qzh]').val(), // 前置换
    "houZhiHL": $('.showForm input[name=hzh]').val(), // 后置换
    "touXiYe": $('.showForm input[name=txy]').val(), // 透析液
    "tanSuanQN": $('.showForm input[name=tsqn]').val(), // 5%碳酸氢钠
    "lvHuaJia": $('.showForm input[name=lhj]').val(),  // 10%氯化钾
    "muBiaoCLL": $('.showForm input[name=mbcll]').val(),  // 目标超滤量
    "yuQiZLSJ": $('.showForm input[name=yqzlsj]').val(),  // 预期治疗时间
    "jingChaoLMBL": $('.showForm input[name=jclmb]').val(),  // 净超滤目标
    // "tingZhiRYYHID":"", // 停止人员用户ID
    // "tingZhiSJ":"", // 停止时间
    // "zhiXingRYYHID": "", // 执行人员用户ID
    // "zhiXingSJ": "", // 执行时间
    // "tingZhiRYYHXM": "", // 停止人员用户姓名
    // "zhiXingRYYHXM": "", // 执行人员用户姓名
    // "yiShengQMSJ": "", // 医师签名时间
    // "huShiQMSJ": "", // 护士签名时间
    // "jieShuSJ": "", // 结束时间
    // "jieShuRYYHID": "", // 结束人员用户ID
    // "jieShuRYYHXM": "", // 结束人员用户姓名
    "xueJiangZH": XJZHvalue.join(','), // 血浆置换
    "fengGuan": $('.showForm input[name=fg_radio]:checked').val() && $('.showForm input[name=fg_radio]:checked').val().indexOf('其他')!=-1?$('.showForm input[name=fg_radio]:checked').val() + $('.showForm input[name=FGQT').val():$('.showForm input[name=fg_radio]:checked').val(), // 封管
    "xueJiangFLLL": $('.showForm input[name=xjflll]').val(),  // 血浆分离流量
    "xueJiangZLZL": $('.showForm input[name=xjzlzl]').val()  // 血浆治疗总量
  }
  WRT_e.api.CrrtYzjld.addListCrrt({
    params: param,
    success(msg){
      if (msg.hasError == 0) {
        keyid = msg.data
        var CRRTCFDForm = new MenuCFDForm_View()
        CRRTCFDForm.$el = $(".CFDForm");
        CRRTCFDForm.init({ data: param }).render();
        // WRT_e.ui.hint({
        //   type: 'success',
        //   msg: '处方单保存成功'
        // })
        // 1、新增化验结果 insertHuayanjg
        if (WRT_config.noxgjcHyjg.length>0) {
          let data = []
          WRT_config.noxgjcHyjg.forEach(item=>{
            data.push({
              "chuFangDanID": msg.data, //处方单ID
              "testID": item.testID, //testID
              "huaYanJG": item.huaYanJG, //化验结果
              "jianYanSJ": item.jianYanSJ?item.jianYanSJ:'' //检验时间
            })
          })
          
          if (data.length>0) {
            WRT_e.api.CrrtYzjld.insertHuayanjg({
              params: data,
              success(msg){
                if (msg.data) {
                  WRT_e.ui.hint({
                    type: 'success',
                    msg: '化验结果数据保存成功'
                  })
                }
              }
            })
          }
        }
        // 2、重新获取下方表格内容 
        WRT_e.api.CrrtYzjld.getMedAdviceListByID({
          params:{ 
            ID: keyid
          },
          success(msg){
            if (msg.hasError == 0) {
              WRT_config.XQXFYZTBData = msg.data
              
              yzcfTb = []
              allYZData = [...WRT_config.XQXFYZTBData, ...yzcfTb]
              // 展示医嘱表格
              var CRRTCFDForm = new MenuCFDTb_View()
              CRRTCFDForm.$el = $(".CFDTB");
              CRRTCFDForm.init({data: WRT_config.XQXFYZTBData}).render();
            
              WRT_e.ui.hint({
                type: 'success',
                msg: '处方单保存成功'
              })
            }
          }
        })
      } else {
        WRT_e.ui.hint({
          type: 'error',
          msg: '处方单保存失败'
        })
      }
    }
  })
  // 调用新增接口，获取医嘱数据并展示
}
// 保存（表单保存弹窗）未正式保存 已完成
function saveCFDTC (){
  // 适应症参数获取
  var gdycYPAdd = document.querySelectorAll('input[name=guanLuYCYPAdd]:checked');
  var gdycYPval = [];
  gdycYPAdd.forEach(function(checkbox) {
    gdycYPval.push(checkbox.value);
  });
   // 置换液
   var ZHYAdd = document.querySelectorAll('input[name=zhiHuanYeAdd]:checked');
   var ZHYval = [];
   ZHYAdd.forEach(function(checkbox) {
     ZHYval.push(checkbox.value);
   });
   
  // 稀释方式 TXAXSQHVal
  var XSFSAdd = document.querySelectorAll('input[name=txxsqhAdd_check]:checked');
  var TXAXSQHAddVal = [];
  XSFSAdd.forEach(function(checkbox) {
    console.log(checkbox);
    TXAXSQHAddVal.push(checkbox.value);
  });
  // console.log(XSFSAdd,TXAXSQHAddVal,$('.addtc input[name=xsfsAdd_check]:checked').val() && $('.addtc input[name=xsfsAdd_check]:checked').val().indexOf('透析+稀释：')!=-1?$('.addtc input[name=xsfsAdd_check]:checked').val() + TXAXSQHAddVal.join(','):$('.addtc input[name=xsfsAdd_check]:checked').val());
  let yzdata = {
    id:  WRT_config.XQXFYZTBData.length>0?WRT_config.XQXFYZTBData[0].id:keyid,
    xuHao: yzcfTb.length>0?yzcfTb[yzcfTb.length-1].xuHao+1:WRT_config.XQXFYZTBData.length>0?WRT_config.XQXFYZTBData[WRT_config.XQXFYZTBData.length-1].xuHao+1:1,
    shouCiLRSJ: new Date().Format("yyyy-MM-dd HH:mm:ss"),
    xiuGaiSJ: '', // 修改时间  用不到
    yiZhuYHID: WRT_config.yzTbData.length>0?WRT_config.yzTbData[0].yiZhuYHID:'', // 医嘱用户ID用不到
    yiZhuYHXM: WRT_config.yzTbData.length>0?WRT_config.yzTbData[0].yiZhuYHXM:'', // 医嘱用户xm用不到
    // xiuGaiYHID: '', //修改用户ID 用不到
    // xiuGaiYHXM: '', //修改用户姓名 用不到
    // yiShengYHID: yongHutype != '04'? WRT_config.YHXX.yongHuID:"", // 医生用户ID
    yiShengYHID: "", // 医生用户ID
    yiShengQM:"", // 医生签名
    yiShengQMSJ: "", // 医生签名时间
    huShiYHID: yongHutype == '04'? WRT_config.YHXX.yongHuID:"", // 护士用户ID
    huShiQM: "", // 护士签名
    huShiQMSJ: "", // 护士签名时间
    // jiGouDM: "", // 机构代码
    moShi: $('.addtc input[name=ms_radio]:checked').val() && $('.addtc input[name=ms_radio]:checked').val().indexOf('其他')!=-1?$('.addtc input[name=ms_radio]:checked').val() + $('.addtc input[name=MSQTVal').val():$('.addtc input[name=ms_radio]:checked').val(), // 模式
    // yaoPin: $('.addtc input[name=KNFA_radio]').val(), // 抗凝方案（药品）
    kangNingFA:$('.addtc input[name=KNFA_radio]:checked').val() && $('.addtc input[name=KNFA_radio]:checked').val().indexOf('其他')!=-1?$('.addtc input[name=KNFA_radio]:checked').val() + $('.addtc input[name=KNFAQTYP').val():$('.addtc input[name=KNFA_radio]:checked').val(), // 抗凝方案（对应医嘱药品）, //抗凝方案（药品）
    aptt: $('.addtc input[name=KNFAAPPT]').val(), // 抗凝方案(APTT目标值：)
    shouJi: $('.addtc input[name=KNFAFHJL]').val(), // 抗凝方案(负荷计量：shouJi？)
    weiChi: $('.addtc input[name=KNFAWC]').val(), // 维持 (维持剂量)
    juYuanSuan: $('.addtc input[name=KNFAGJSJL]').val(), // 枸橼酸(枸橼酸剂量:juYuanSuan?)
    gaiBuChang: $('.addtc input[name=KNFAGJSGJL]').val(), // 钙剂量__钙补偿
    qiTaJL: $('.addtc input[name=KNFAGJSQTJL]').val(), // 其他计量 
    guanLuYC: $('.addtc input[name=GLYCNS500]').val(), // 管路预冲
    guanLuYCYP: gdycYPval.join(','), // 管路预冲药品
    yuChongYeJRTN: $('.addtc input[name=ycysf_radio]:checked').val() , // 预冲液进入体内
    zhiHuanYe: ZHYval.join(','), // 置换液
    xiShiFS: $('.addtc input[name=xsfsAdd_check]:checked').val() && $('.addtc input[name=xsfsAdd_check]:checked').val().indexOf('透析+稀释：')!=-1?$('.addtc input[name=xsfsAdd_check]:checked').val() + TXAXSQHAddVal.join(','):$('.addtc input[name=xsfsAdd_check]:checked').val(), // 稀释方式
    // xueLiuSu: $('.addtc input[name=]').val(), // 血流速
    muBiaoXLL: $('.addtc input[name=MBXLSAdd]').val(), //目标血流量（目标血流速）
    qianZhiHuan: $('.addtc input[name=QZHAdd]').val(), // 前置换
    houZhiHuan: $('.addtc input[name=HZHAdd]').val(), // 后置换
    touXiYe: $('.addtc input[name=TXYAdd]').val(), // 透析液
    tanSuanQN: $('.addtc input[name=TSQNAdd]').val(), //碳酸氢钠
    lvHuaJia: $('.addtc input[name=LHJAdd]').val(), //氯化钾
    muBiaoCLL: $('.addtc input[name=CLLAdd]').val(), //目标超滤量
    yuQiZLSJ: $('.addtc input[name=YQZLSJAdd]').val(), //预期治疗时间
    jingChaoLMBL: $('.addtc input[name=JCLMBAdd]').val(), //净超滤目标量
    xueJiangFLLL: $('.addtc input[name=XJFLLLAdd]').val(), //血浆分离流量
    xueJiangZLZL: $('.addtc input[name=XJZLZLAdd]').val(), //血浆治疗总量
    zhenDuan: $('.showForm input[name=zhenDuanXX]').val(), // 诊断
    // qianXiShiBL: "", //前稀释比例 用不到？
    // beiZhu: "", //备注 用不到？
  }
  // console.log('单条结果Add',yzdata);
  yzcfTb.push(yzdata) // 临时数据未保存
  // WRT_config.XQXFYZTBData = [...yzcfTb]
  allYZData = [...WRT_config.XQXFYZTBData, ...yzcfTb]
  var CRRTCFDForm = new MenuCFDTb_View()
  CRRTCFDForm.$el = $(".CFDTB");
  CRRTCFDForm.init({data: allYZData}).render();
  $('#addCFYZD').iziModal('destroy') //关闭modal
}
// 删除（1、正式保存调用接口，未正式保存直接去掉数据）
function del(item) {
  let index = yzcfTb.findIndex(e=>e.xuHao == item.xuHao)
  if (index !=-1) { // 未正式保存直接去掉数据
    yzcfTb.splice(index,1)
    WRT_e.ui.hint({
      type: 'success',
      msg: '删除成功'
    })
    allYZData = [...WRT_config.XQXFYZTBData, ...yzcfTb]
    var CRRTCFDForm = new MenuCFDTb_View()
    CRRTCFDForm.$el = $(".CFDTB");
    CRRTCFDForm.init({data: allYZData}).render();
  } else { // 删除已保存数据
    // 1、调用删除接口  2、重新获取WRT_config.XQXFYZTBData数据  3、重新编辑 yzcfTb 的序号，拼接为新的allYZData  4、展示数据
    let delData = {
      ID: item.id,
      xuHao: item.xuHao
    }
   // 1、调用删除接口
    WRT_e.api.CrrtYzjld.delYZByIDAndXuHao({
      params: delData,
      success(msg){
        if (msg.data) {
          WRT_e.ui.hint({
            type: 'success',
            msg: '删除成功'
          })
          // 2、重新获取WRT_config.XQXFYZTBData数据
          WRT_e.api.CrrtYzjld.getMedAdviceListByID({
            params:{ 
              ID: parseInt(item.id)
            },
            success(res){
              if (res.hasError == 0) {
                // console.log('医嘱表格',res,res.data,yzcfTb);
                WRT_config.XQXFYZTBData = res.data
                // 3、重新编辑 yzcfTb 的序号，拼接为新的allYZData
                if (yzcfTb.length>0) {
                  allYZData = [...WRT_config.XQXFYZTBData, ...yzcfTb]
                  // 展示医嘱表格
                  var CRRTCFDForm = new MenuCFDTb_View()
                  CRRTCFDForm.$el = $(".CFDTB");
                  CRRTCFDForm.init({data: allYZData}).render();
                } else {
                  // 展示医嘱表格
                  var CRRTCFDForm = new MenuCFDTb_View()
                  CRRTCFDForm.$el = $(".CFDTB");
                  CRRTCFDForm.init({data: WRT_config.XQXFYZTBData}).render();
                }
              }
            }
          })
        }
      }
    })
  }
}
// 打开新增处方医嘱
function addCFYZ (){
  if (keyid!=0) {
    var addCFDModal = new addCFDModal_View();
    addCFDModal.init({ data: YFormData }).render();
  } else {
    WRT_e.ui.hint({
      type: 'warning',
      msg: '请先填写并保存上方处方单，再进行新增'
    })
  }
}
// 保存（表2 表格格上方保存）
function saveTB (){
  if (keyid!=0) {
    if (yzcfTb.length>0) {
      yzcfTb.forEach(item=>{
        item.yiShengYHID = WRT_config.YHXX.yongHuID
        item.yiShengQM = WRT_config.YHXX.yongHuXM
        item.yiShengQMSJ =  new Date().Format("yyyy-MM-dd HH:mm:ss")
      })
      // console.log('baocun',yzcfTb,WRT_config.YHXX);
      WRT_e.api.CrrtYzjld.saveYZTB({
        params: yzcfTb,
        success(msg){
          if (msg.hasError == 0 && msg.data) {
            // 1、重新绘制表格
            WRT_e.api.CrrtYzjld.getMedAdviceListByID({
              params:{ 
                ID: keyid
              },
              success(msg){
                if (msg.hasError == 0) {
                  WRT_config.XQXFYZTBData = msg.data
                  WRT_e.ui.hint({
                    type: 'success',
                    msg: '保存成功'
                  })
                  yzcfTb = []
                  allYZData = [...WRT_config.XQXFYZTBData, ...yzcfTb]
                  // 展示医嘱表格
                  var CRRTCFDForm = new MenuCFDTb_View()
                  CRRTCFDForm.$el = $(".CFDTB");
                  CRRTCFDForm.init({data: WRT_config.XQXFYZTBData}).render();
                }
              }
            })
          } else {
            WRT_e.ui.hint({
              type: 'error',
              msg: '保存失败'
            })
          }
        }
      })
    } else {
      WRT_e.ui.hint({
        type: 'info',
        msg: '暂无新增处方医嘱数据，请添加处方医嘱后保存'
      })
    }
  } else {
    WRT_e.ui.hint({
      type: 'warning',
      msg: '请先填写并保存上方处方单'
    })
  }
}

let allYZDataModel=null
// 签名弹窗  style="text-transform:uppercase" item
function secondQM(index){
  allYZDataModel=WRT_config.XQXFYZTBData[index]
  let temp=`
	<div class="e_form">
    <div style="display: flex;padding: 0 0 10px;">
      <div class="iTabel" style="width:96px">用户：</div>
      <input class="allInput" type="text" name="username">
    </div>
    <div style="display: flex;padding: 0 0 10px;">
      <div class="iTabel" style="width:96px">二级密码：</div>
      <input class="allInput" type="password" name="password">
    </div>
    <div class="izi_modal_btn text-right">
      <button type="button" class="btn btn-default btn-sm" name="close" onclick="closeModel()">取消</button>
      <button type="button" class="btn btn-primary btn-sm" name="confirm" onclick="handlecheckPwd()">确认</button>
      <button type="button" class="btn btn-default btn-sm" name="clear" onclick="clearInput()">清空</button>
    </div>
  </div>
	`
	WRT_e.ui.model({
		id: 'erjiQMMM',
		title: '护士签名',
		width: "350px",
		content: temp,
		iframe: false,
	})
  // WRT_e.ui.model({
	// 	id: "if_doublesign",
	// 	title: "二次密码校验",
	// 	width: "400px",
	// 	iframeURL: WRT_config.server + "/confirmPWD.aspx?as_tempid=0.****************&as_version=djm",
	// 	iframe: true,
	// })
}
// 取消
function closeModel() {
  $('#erjiQMMM').iziModal('destroy') //关闭modal
}
// 清空
function clearInput() {
  $('.e_form input[name=password]').val('')
  $('.e_form input[name=username]').val('')
}
// 二级签名  http://10.104.141.230:38081/sysmanage/v1/user/checkSecendPassword（接口）
function handlecheckPwd() { // 密码，用户
  let pwd =  $('.e_form input[name=password]').val()
  let account = $('.e_form input[name=username]').val()
  if (pwd && account) {
    // MD5 加密
    let md5_pwd = CryptoJS.MD5(pwd).toString().toUpperCase()  // md5(pwd).toString().toUpperCase()
    let queryStr = `erJiMM=${md5_pwd}&yongHuZH=${account}`
    
    // RSA 加密
    const publicKeystr = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDJKmw0cPKMy1jGi5zQd7T9fPwaI6NYSZakQkHXBHObqRQwhfeAsKLkHn1cKfyf3WxSZq791GoQRMfKrS9SVi7fpDLvGQwLyCa7W46II4+cC0fPYXdHZSaXT59Ox/YSZsXr+Al2NEXPBPOBfL8zfeRKyqhS76Ind7C2nSRckWXmKwIDAQAB'
    let jsRsa = new JSEncrypt()
    jsRsa.setPublicKey(publicKeystr)
    let RSApassword = jsRsa.encrypt(queryStr);

    // 十位时间戳
    let timestamp = Math.round(new Date().getTime()/ 1000).toString();

    //HmacSHA1 签名
    var secret = "wzhospital@^&*()"; // 密钥
    // var hash = CryptoJS.HmacSHA1(RSApassword, secret);
    var signStr = CryptoJS.HmacSHA1(RSApassword, secret).toString(CryptoJS.enc.Base64);  // 将加密结果转换为Base64字符串

    //十位时间戳 + HmacSHA1 签名
    // let ms = timestamp + signStr.replaceAll('+','-').replaceAll('/','_')
    let ms = timestamp + signStr.replace(/\+/g, '-').replace(/\//g, '_')
    // 调用接口(二级签名)
    WRT_e.api.CrrtYzjld.checkPassword({
      params:{
        data: { 
          param: RSApassword
          // param: 'hD0u3x1ZxZiHxwO25oMgfL4yVZlle8sCeKBrMLYfRSsUQ7U1KokoPlxnbI2D6Iuw4pRn+UXcx/4e/vLBHiK1QlDyGvkzAMU8OvRveDyskteB054L13A7LDg5w6MZemALcrt8D9KjBaqU7aKZZiPeYYqvp+rEzx9bZAyWdZRPiWk='
        },
        // ms: '1724815626qgoscy6rdMCV_yxYbmsQ7Yt-AVg='
        ms: ms
      },
      success(msg){
        if (msg.hasError == 0) {
          allYZDataModel.huShiYHID=msg.data.yongHuID
          allYZDataModel.huShiQM=msg.data.yongHuXM
          allYZDataModel.huShiQMSJ=new Date().Format("yyyy-MM-dd HH:mm:ss")
          WRT_e.api.CrrtYzjld.updateMedAdvice({
            params:[allYZDataModel],
            success(key){
              if(key.data){
                // 1、重新绘制表格
                WRT_e.api.CrrtYzjld.getMedAdviceListByID({
                 params:{ 
                   ID: keyid
                 },
                 success(msg){
                   if (msg.hasError == 0) {
                     console.log('医嘱表格',msg,msg.data);
                     WRT_config.XQXFYZTBData = msg.data
                     WRT_e.ui.hint({
                       type: 'success',
                       msg: '签名成功'
                     })
                     // 关闭弹窗
                     $('#erjiQMMM').iziModal('destroy') 
                     // 重绘表格
                     // yzcfTb = []
                     allYZData = [...WRT_config.XQXFYZTBData, ...yzcfTb]
                     // 展示医嘱表格
                     var CRRTCFDForm = new MenuCFDTb_View()
                     CRRTCFDForm.$el = $(".CFDTB");
                     CRRTCFDForm.init({data: allYZData}).render();
                   } else {
                     WRT_e.ui.hint({
                       type: 'error',
                       msg: msg
                     })
                   }
                 }
               })
              }
            }
          })
        } else {
          WRT_e.ui.hint({
            type: 'error',
            msg: msg.errorMessage
          })
          // WRT_e.ui.hint({
          //   type: 'warning',
          //   msg: '验证失败，请输入正确的账户和登陆密码'
          // })
        }
      }
    })
  } else {
    if (!pwd && !account) {
      WRT_e.ui.hint({
        type: 'warning',
        msg: '请输入账户和登陆密码'
      })
    } else {
      if ( !account) {
        WRT_e.ui.hint({
          type: 'warning',
          msg: '请输入账户'
        })
      }
      if (!pwd) {
        WRT_e.ui.hint({
          type: 'warning',
          msg: '请输入登陆密码'
        })
      }
    }
  }
}

// 透析+稀释单选是否选择
function changeNow() {
  var TXAXS = $('.showForm input[type="radio"][name="xsfs_check"]:checked').val()
  var checkbox = document.querySelectorAll('input[name=txxsq_check]');
  checkbox.forEach(function(item) {
    if (TXAXS == '透析+稀释：') {
      item.removeAttribute('disabled')
    } else {
      item.setAttribute('disabled', '')
      item.checked = false
      
    }
    
  })
  // 医嘱新增  
  var TXAXSAdd = $('.addtc input[type="radio"][name="xsfsAdd_check"]:checked').val()
  var checkbox = document.querySelectorAll('input[name=txxsqhAdd_check]');
  checkbox.forEach(function(item) {
    if (TXAXSAdd == '透析+稀释：') {
      item.removeAttribute('disabled')
    } else {
      item.setAttribute('disabled', '')
      item.checked = false
    }
    
  })
  // $('.showForm input[name="txxsq_check"]').removeAttr('disabled')
}
// 封管单选是否选择
function FGchangeNow() {
  var TXAXS = $('.showForm input[type="radio"][name="fg_radio"]:checked').val()
  var checkbox = document.querySelectorAll('input[name=FGQT]');
  checkbox.forEach(function(item) {
    if (TXAXS == '其他：') {
      $('.showForm input[name=FGQT]').removeAttr('disabled')
    } else {
      $('.showForm input[name=FGQT]').attr('disabled', '')
    }
  })
  // $('.showForm input[name="txxsq_check"]').removeAttr('disabled')
}
// 滤器单选是否选择
function LQchangeNow() {
  var TXAXS = $('.showForm input[type="radio"][name="nq_radio"]:checked').val()
  if (TXAXS == '其他：') {
    $('.showForm input[name=LQVal]').removeAttr('disabled')
  } else {
    $('.showForm input[name=LQVal]').attr('disabled', '')
  }
  // $('.showForm input[name="txxsq_check"]').removeAttr('disabled')
}
// 模式单选是否选择
function MSchangeNow() {
  var TXAXS = $('.showForm input[type="radio"][name="ms_radio"]:checked').val()
  if (TXAXS == '其他：') {
    $('.showForm input[name=MSQTVal]').removeAttr('disabled')
  } else {
    $('.showForm input[name=MSQTVal]').attr('disabled', '')
  }
  // 医嘱新增  
  var TXAXSADD = $('.addtc input[type="radio"][name="ms_radio"]:checked').val()
  if (TXAXSADD == '其他：') {
    $('.addtc input[name=MSQTVal]').removeAttr('disabled')
  } else {
    $('.addtc input[name=MSQTVal]').attr('disabled', '')
    $('.addtc input[name=MSQTVal]').val('')
  }
  // $('.showForm input[name="txxsq_check"]').removeAttr('disabled')
}
// 血管通路单选是否选择
function XGTLchange() {
  var TXAXS = $('.showForm input[type="radio"][name="xueGuanTLLX"]:checked').val()
  
  if (TXAXS == '其他：') {
    $('.showForm input[name=xueGuanTLLXVal]').removeAttr('disabled')
  } else {
    $('.showForm input[name=xueGuanTLLXVal]').attr('disabled', '')
  }
  // $('.showForm input[name="txxsq_check"]').removeAttr('disabled')
}
// 血浆置换：多选是否选择
function CJZHchange() {
  var checkbox = document.querySelectorAll('input[name=ZJZH]:checked');
  console.log(checkbox);
  if (checkbox.length==0) {
    ($('.showForm input[name="xjzhNSVal"]')).attr('disabled', '')
    ($('.showForm input[name="xjzhBDBVal"]')).attr('disabled', '')
    ($('.showForm input[name="xjzhPPTSGVal"]')).attr('disabled', '')
    ($('.showForm input[name="zjzhRGJTVal"]')).attr('disabled', '')
    ($('.showForm input[name="xjzhQTVal"]')).attr('disabled', '')
  }
  checkbox.forEach(function(item) {
    if (item.value == 'NS1000ml：' && $('.showForm input[name="xjzhNSVal"]')[0].disabled) {
      ($('.showForm input[name="xjzhNSVal"]')).removeAttr('disabled')
    } else {
      if ($('.showForm input[name="xjzhNSVal"]')[0].disabled) {
        ($('.showForm input[name="xjzhNSVal"]')).removeAttr('disabled')
      } else {
        ($('.showForm input[name="xjzhNSVal"]')).attr('disabled', '')
      }
    }
    if (item.value == '20%白蛋白：' && $('.showForm input[name="xjzhBDBVal"]')[0].disabled) {
      ($('.showForm input[name="xjzhBDBVal"]')).removeAttr('disabled')
    } else {
      // ($('.showForm input[name="xjzhBDBVal"]')).attr('disabled', '')
      if ($('.showForm input[name="xjzhBDBVal"]')[0].disabled) {
        ($('.showForm input[name="xjzhBDBVal"]')).removeAttr('disabled')
      } else {
        ($('.showForm input[name="xjzhBDBVal"]')).attr('disabled', '')
      }
    }
    if (item.value == '10%葡萄糖酸钙针：' && $('.showForm input[name="xjzhPPTSGVal"]')[0].disabled) {
      ($('.showForm input[name="xjzhPPTSGVal"]')).removeAttr('disabled')
    } else {
      // ($('.showForm input[name="xjzhPPTSGVal"]')).attr('disabled', '')
      if ($('.showForm input[name="xjzhPPTSGVal"]')[0].disabled) {
        ($('.showForm input[name="xjzhPPTSGVal"]')).removeAttr('disabled')
      } else {
        ($('.showForm input[name="xjzhPPTSGVal"]')).attr('disabled', '')
      }
    }
    if (item.value == '人工胶体：' && $('.showForm input[name="zjzhRGJTVal"]')[0].disabled) {
      ($('.showForm input[name="zjzhRGJTVal"]')).removeAttr('disabled')
    } else {
      // ($('.showForm input[name="zjzhRGJTVal"]')).attr('disabled', '')
      if ($('.showForm input[name="zjzhRGJTVal"]')[0].disabled) {
        ($('.showForm input[name="zjzhRGJTVal"]')).removeAttr('disabled')
      } else {
        ($('.showForm input[name="zjzhRGJTVal"]')).attr('disabled', '')
      }
    }
    if (item.value == '其他：' && $('.showForm input[name="xjzhQTVal"]')[0].disabled) {
      ($('.showForm input[name="xjzhQTVal"]')).removeAttr('disabled')
    } else {
      // ($('.showForm input[name="xjzhQTVal"]')).attr('disabled', '')
      if ($('.showForm input[name="xjzhQTVal"]')[0].disabled) {
        ($('.showForm input[name="xjzhQTVal"]')).removeAttr('disabled')
      } else {
        ($('.showForm input[name="xjzhQTVal"]')).attr('disabled', '')
      }
    }
  })
  // $('.showForm input[name="txxsq_check"]').removeAttr('disabled')
}
// 适应症多选是否选择
function SYZchange() {
  var checkbox = document.querySelectorAll('input[name=syz_radio]:checked');
  checkbox.forEach(function(item) {
    if (item.value == '其他：') {
      ($('.showForm input[name="SYZQT"]')).removeAttr('disabled')
    } else {
      ($('.showForm input[name="SYZQT"]')).attr('disabled', '')
    }
  })
}
// 医嘱新增  抗凝方案
function KNFAchange() {
  var TXAXS = $('.showForm input[type="radio"][name="KNFA_radio"]:checked').val()
  if (TXAXS == '其他：') {
    $('.showForm input[name=KNFAVAL]').removeAttr('disabled')
  } else {
    $('.showForm input[name=KNFAVAL]').attr('disabled', '')
  }
  // 医嘱新增  
  var TXAXSADD = $('.addtc input[type="radio"][name="KNFA_radio"]:checked').val()
  if (TXAXSADD == '其他：') {
    $('.addtc input[name=KNFAQTYP]').removeAttr('disabled')
  } else {
    $('.addtc input[name=KNFAQTYP]').attr('disabled', '')
    $('.addtc input[name=KNFAQTYP]').val('')
  }
}
// 数字
function validateNumber(el) {
  var value = el.value;
  var valid = /^[0-9]*\.?[0-9]*$/.test(value); // 正则表达式判断是否为数字或小数
  if (!valid) {
    // 如果输入无效，则设置为上一个有效的值
    el.value = el.value.replace(/[^0-9.]/g, ''); 
  } else {
    const numStr = value.toString();
    // 分割小数点前后的字符串
    const parts = numStr.split('.');
    // 如果小数点后面有部分，则返回小数位数，否则返回0
    const partsLong =  parts.length === 2 ? parts[1].length : 0;
    if (partsLong>2) {
      el.value=el.value.toString().match(/^\d+(?:\.\d{0,2})?/)
    } else {
      if (partsLong == 0 && (el.value[0]=='0' && el.value[1])) {
        el.value =  el.value.replace(/^0+([1-9]+)$/, '$1')
      }
    }
  } 
}

/******************** 视图 ********************/
// 外部
// 患者基本信息
var brBasicInfo_View = WRT_e.view.extend({
  render: function () {
    let html=`
      <div class='infoCondition' style='width:8%;min-width:132px;'>
        <label class="control-label label-name" style="font-size: 16px;min-width:50px"> 姓名： </label>
        <div class="control-inner flex-select control_xm"> ${this.data.bingRenXM?this.data.bingRenXM:''} </div>
      </div>
      
      <div class='infoCondition' style='width:6%;min-width:90px;'>
        <label class="control-label label-name" style="font-size: 16px;min-width:50px"> 性别： </label>
        <div class="control-inner flex-select control_xb"> ${this.data.xingBieZW?this.data.xingBieZW:''} </div>
      </div>
      
      <div class='infoCondition' style='width:12%;min-width:176px;'>
        <label class="control-label label-name" style="font-size: 16px;min-width:65px"> 病案号： </label>
        <div class="control-inner flex-select control_bah"> ${this.data.bingAnHao?this.data.bingAnHao:''} </div>
      </div>
      <div class='infoCondition' style='width:10%;min-width:152px;'>
        <label class="control-label label-name" style="font-size: 16px;min-width:65px"> 身高（cm）： </label>
        <div class="control-inner flex-select control_sg"> ${this.data.shenGao?this.data.shenGao:WRT_config.SMTZ.filter(e=>e.shenGao!=null).length>0?WRT_config.SMTZ.filter(e=>e.shenGao!=null)[0].shenGao:''} </div>
      </div>
      
      <div class='infoCondition' style='width:10%'>
        <label class="control-label label-name" style="font-size: 16px;min-width:65px"> 体重（kg）： </label>
        <div class="control-inner flex-select control_tz"> ${this.data.tiZhong?this.data.tiZhong:WRT_config.SMTZ.filter(e=>e.shenGao!=null).length>0?WRT_config.SMTZ.filter(e=>e.tiZhong!=null)[0].tiZhong:''} </div>
      </div>
      
      <div class='infoCondition'>
        <label class="control-label label-name" style="font-size: 16px;min-width:50px"> 诊断： </label>
        <div class="control-inner flex-select control_zd"> ${this.data.ruYuanZD?this.data.ruYuanZD:''} </div>
      </div>
    `
    this.$el.html(html)
    return this;
  },

})
// 表1 血液净化医嘱
{/* <th width="276" style="text-align: center">结束时间</th>
<th width="276" style="text-align: center">停止时间</th> */}
var MenuYZTB_View = WRT_e.view.extend({
  render: function () {
    let html=`
    <div class="overall_table_frame">
      <table id="crrt_table">
        <thead>
          <tr class="row_head">
            <th style="text-align: center;min-width: 100px;">开始时间</th>
            <th style="text-align: center;min-width: 100px;">执行时间</th>
            <th style="text-align: center;min-width: 64px;">结束人员</th>
            <th style="text-align: center;min-width: 146px;">护士结束时间</th>
            <th style="text-align: center;min-width: 64px;">停止人员</th>
            <th style="text-align: center;min-width: 146px;">医生停止时间</th>
            <th width="285" style="text-align: center">操作</th>
          </tr>
        </thead>
        <tbody class="line_nr" id="line_nr">
          ${_.map(this.data,(obj,index)=>`<tr class="row_nr" ${index%2!=0?`style="background: #b7c3da94;"`:''}>
            <td calss="kssj${index}">${obj.shouCiLRSJ?obj.shouCiLRSJ:''}</td>
            <td calss="zxsj${index}">${obj.zhiXingSJ?obj.zhiXingSJ:''}</td>
            <td calss="jssj${index}">${obj.jieShuRYYHXM?obj.jieShuRYYHXM:''}</td>
            <td calss="tzsj${index}">${obj.jieShuSJ?obj.jieShuSJ:''}</td>
            <td calss="jssj${index}">${obj.tingZhiRYYHXM?obj.tingZhiRYYHXM:''}</td>
            <td calss="tzsj${index}">${obj.tingZhiSJ?obj.tingZhiSJ:''}</td>
            <td>
              <a herf="javascript:void(0)" calss='checkXQ' onclick='detailsBtn(${JSON.stringify(obj)})' style='position: relative;padding-right: 36px;cursor: pointer;'>详情</a>
              <a herf="javascript:void(0)" calss='checkXQ' onclick='adjustBtn' style='position: relative;padding-right: 36px;cursor: pointer;'>调整</a>
              <a herf="javascript:void(0)" calss='checkXQ' onclick='endBtn(${JSON.stringify(obj)})' style='position: relative;padding-right: 36px;cursor: pointer;'>结束</a>
              <a herf="javascript:void(0)" calss='checkXQ' onclick='stopBtn(${JSON.stringify(obj)})' style='position: relative;cursor: pointer;'>停止</a>
            </td>
          </tr>
          `).join('')}
        </tbody>
      </table>
    </div>`
    this.$el.html(html)
    return this;
  },
})

// 新增/详情页面
// 表2 血液净化处方单 ———— 填写处方单
var MenuCFDForm_View = WRT_e.view.extend({
  render: function () {
    // let dataNow = JSON.stringify(this.data) || ''
    // console.log('canshu',this.data);
    let html=`
      <div class="showForm" style="padding: 12px 0; color: rgba(0, 0, 0, .65); background-color: #fff; border: 1px solid #767676;">
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold;width: 100px;min-width: 100px;">基础信息：</span>
            <span>
              <span class="iTabel"> 诊断 </span>
              <input class="allInput" name="zhenDuanXX" value="${this.data && this.data.zhenDuanXX?this.data.zhenDuanXX:WRT_config.brInfo.ruYuanZD?WRT_config.brInfo.ruYuanZD:''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px;width: 500px;"> 
            </span>
            <span>
              <span class="iTabel"> 身高 </span>
              <input class="allInput" name="shenGao" type="number" oninput="validateNumber(this)" value="${this.data && this.data.shenGao?this.data.shenGao:WRT_config.SMTZ.filter(e=>e.shenGao!=null).length>0?WRT_config.SMTZ.filter(e=>e.shenGao!=null)[0].shenGao : ''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px;width: 70px;"> 
            </span>
            <span>
              <span class="iTabel"> 体重 </span>
              <input class="allInput" name="tiZhong" type="number" oninput="validateNumber(this)" value="${this.data && this.data.tiZhong?this.data.tiZhong:WRT_config.SMTZ.filter(e=>e.tiZhong!=null).length>0?WRT_config.SMTZ.filter(e=>e.tiZhong!=null)[0].tiZhong : ''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px;width: 70px;"> 
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold;width: 100px;min-width: 100px;">生命体征：</span>
            <span style="padding-right: 20px">
              <span class="iTabel"> T: </span>
              <input class="allInput" name="tiWen" value="${this.data && this.data.tiWen?this.data.tiWen:WRT_config.SMTZ.filter(e=>e.tiWen!=null).length>0?WRT_config.SMTZ.filter(e=>e.tiWen!=null)[0].tiWen : ''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px;width: 56px;"> ℃
            </span>
            <span style="padding-right: 20px">
              <span class="iTabel"> PB: </span>
              <input class="allInput" name="xueYaSY" value="${this.data && this.data.xueYa && String(this.data.xueYa).indexOf('/')!==-1?((this.data.xueYa).split('/'))[0]:WRT_config.SMTZ.filter(e=>e.shangYa!=null).length>0?WRT_config.SMTZ.filter(e=>e.shangYa!=null)[0].shangYa : ''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px;width:60px;"> / 
              <input class="allInput" name="xueYaXY" value="${this.data && this.data.xueYa && String(this.data.xueYa).indexOf('/')!==-1?((this.data.xueYa).split('/'))[1]:WRT_config.SMTZ.filter(e=>e.xiaYa!=null).length>0?WRT_config.SMTZ.filter(e=>e.xiaYa!=null)[0].xiaYa :''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px;width:60px;"> mmHg
            </span>
            <span style="padding-right: 20px">
              <span class="iTabel"> P: </span>
              <input class="allInput" name="xinTiao" value="${this.data && this.data.xinTiao?this.data.xinTiao:WRT_config.SMTZ.filter(e=>e.xinLv!=null).length>0?WRT_config.SMTZ.filter(e=>e.xinLv!=null)[0].xinLv : ''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px;width: 70px;"> 次/分
            </span>
            <span style="padding-right: 20px">
              <span class="iTabel"> R: </span>
              <input class="allInput" name="huXi" value="${this.data && this.data.huXi?this.data.huXi:WRT_config.SMTZ.filter(e=>e.huXi!=null).length>0?WRT_config.SMTZ.filter(e=>e.huXi!=null)[0].huXi : ''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px;width: 70px;"> 次/分
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold;width: 100px;min-width: 100px;">血管通路：</span>
            <span  class="changecheck" style="flex-direction: column;">
              <div style="display: flex;justify-content: flex-start;">
                <span class="iTabel"> 类型 </span>
                <label class="radio-inline">
                  <input name="xueGuanTLLX" type="radio" value="临时导管" onclick="XGTLchange()" ${this.data && this.data.xueGuanTLLX && this.data.xueGuanTLLX=='临时导管'?`checked`:this.data&&this.data.id?`disabled`:``}/>
                  <span class="iTabel"> 临时导管 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLLX" type="radio" value="长期导管" onclick="XGTLchange()"  ${this.data && this.data.xueGuanTLLX && this.data.xueGuanTLLX=='长期导管'?`checked`:this.data&&this.data.id?`disabled`:``}/>  
                  <span class="iTabel"> 长期导管 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLLX" type="radio" value="动静脉瘘" onclick="XGTLchange()"  ${this.data && this.data.xueGuanTLLX && this.data.xueGuanTLLX=='动静脉瘘'?`checked`:this.data&&this.data.id?`disabled`:``}/>  
                  <span class="iTabel"> 动静脉瘘 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLLX" type="radio" value="其他：" onclick="XGTLchange()"  ${this.data && this.data.xueGuanTLLX && this.data.xueGuanTLLX.indexOf('其他：')!=-1?`checked`:this.data&&this.data.id?`disabled`:``}/>  
                  <span class="iTabel"> 其他 </span>
                  &nbsp;&nbsp;<input class="allInput" name="xueGuanTLLXVal" value="${this.data && this.data.xueGuanTLLX && ((this.data.xueGuanTLLX).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?((this.data.xueGuanTLLX).split(',')).filter(item=>item.indexOf('其他：')!=-1)[0].split('：')[1]:''}" style="height:26px" ${this.data&&this.data.id?`readonly`:`disabled`}>
                </label>
              </div>
              <div style="display: flex;justify-content: flex-start;">
                <span class="iTabel"> 部位 </span>
                <label class="radio-inline">
                  <input name="xueGuanTLBWLR" type="radio" value="左侧" ${this.data && this.data.xueGuanTLBW && this.data.xueGuanTLBW.indexOf('左侧')==0?`checked`:this.data&&this.data.id?`disabled`:``}/>
                  <span class="iTabel"> 左侧 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLBWLR" type="radio" value="右侧" ${this.data && this.data.xueGuanTLBW && this.data.xueGuanTLBW.indexOf('右侧')==0?`checked`:this.data&&this.data.id?`disabled`:``} />  
                  <span class="iTabel"> 右侧 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLBW" type="radio" value="股静脉" ${this.data && this.data.xueGuanTLBW && this.data.xueGuanTLBW.indexOf('股静脉')==0?`checked`:this.data&&this.data.id?`disabled`:``} />  
                  <span class="iTabel"> 股静脉 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLBW" type="radio" value="颈内静脉" ${this.data && this.data.xueGuanTLBW && this.data.xueGuanTLBW.indexOf('颈内静脉')==0?`checked`:this.data&&this.data.id?`disabled`:``} />
                  <span class="iTabel"> 颈内静脉 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLBW" type="radio" value="锁骨下静脉" ${this.data && this.data.xueGuanTLBW && this.data.xueGuanTLBW.indexOf('锁骨下静脉')==0?`checked`:this.data&&this.data.id?`disabled`:``} />  
                  <span class="iTabel"> 锁骨下静脉 </span>
                </label>
                <label class="radio-inline">
                  <input name="xueGuanTLBW" type="radio" value="动静脉瘘" ${this.data && this.data.xueGuanTLBW && this.data.xueGuanTLBW.indexOf('动静脉瘘')==0?`checked`:this.data&&this.data.id?`disabled`:``} />  
                  <span class="iTabel"> 动静脉瘘 </span>
                </label>
              </div>
            </span>
            
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;"> 适应症：</span>
            <span class="changeRadio">
              <label class="radio-inline" style="padding-left: 2px;min-width: 120px;">
                <input name="syz_radio" type="checkbox" value="容量过负荷" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item == '容量过负荷')[0] == '容量过负荷'?`checked disabled`:this.data&&this.data.id?`disabled`:``} />
                <span class="iTabel"> 容量过负荷 </span>
              </label>
              <label class="radio-inline">
                <input name="syz_radio" type="checkbox" value="严重酸碱及电解质紊乱" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item == '严重酸碱及电解质紊乱')[0] == '严重酸碱及电解质紊乱'?`checked disabled`:this.data&&this.data.id?`disabled`:``}/>  
                <span class="iTabel" style="min-width: 196px;"> 严重酸碱及电解质紊乱 </span>
              </label>
              <label class="radio-inline">
                <input name="syz_radio" type="checkbox" value="急慢性肾衰竭" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item == '急慢性肾衰竭')[0] == '急慢性肾衰竭'?`checked disabled`:this.data&&this.data.id?`disabled`:``}/>  
                <span class="iTabel" style="min-width: 124px;"> 急慢性肾衰竭 </span>
              </label>
              <label class="radio-inline">
                <input name="syz_radio" type="checkbox" value="多器官功能障碍综合症" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item == '多器官功能障碍综合症')[0] == '多器官功能障碍综合症'?`checked disabled`:this.data&&this.data.id?`disabled`:``} />  
                <span class="iTabel" style="min-width: 196px;"> 多器官功能障碍综合症 </span>
              </label>
              <label class="radio-inline">
                <input name="syz_radio" type="checkbox" value="药物中毒" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item == '药物中毒')[0] == '药物中毒'?`checked disabled`:this.data&&this.data.id?`disabled`:``} />  
                <span class="iTabel" style="min-width: 88px;"> 药物中毒 </span>
              </label>
              <label class="radio-inline">
                <input name="syz_radio" type="checkbox" value="免疫相关性疾病" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item == '免疫相关性疾病')[0] == '免疫相关性疾病'?`checked disabled`:this.data&&this.data.id?`disabled`:``} />  
                <span class="iTabel" style="min-width: 130px;"> 免疫相关性疾病 </span>
              </label>
              <label class="radio-inline">
                <input name="syz_radio" type="checkbox" value="急性肝衰竭" onclick="SYZchange()" ${this.data && this.data.shiYingZheng&&(this.data.shiYingZheng.split(',')).filter(item=>item == '急性肝衰竭')[0] == '急性肝衰竭'?`checked disabled`:this.data&&this.data.id?`disabled`:``} />  
                <span class="iTabel"> 急性肝衰竭 </span>
              </label>
              <label class="radio-inline">
                <input name="syz_radio" type="checkbox" value="其他：" onclick="SYZchange()" ${this.data && this.data.shiYingZheng &&(this.data.shiYingZheng.split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?`checked disabled`:this.data&&this.data.id?`disabled`:``} />  
                <span class="iTabel"> 其他&nbsp;&nbsp;<input class="allInput" name="SYZQT" value="${this.data && this.data.shiYingZheng && (this.data.shiYingZheng.split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?(this.data.shiYingZheng.split(',')).filter(item=>item.indexOf('其他：')!=-1)[0]:''}" ${this.data&&this.data.id?`readonly`:`disabled`} style="height:26px"> </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;"> 禁忌症：</span>
            <span class="changeRadio">
              <label class="radio-inline" style="padding-left: 2px;">
                <input name="jjz_radio" type="checkbox" value="无" ${this.data && this.data.jinJiZheng && (this.data.jinJiZheng.split(',')).filter(item=>item == '无').length!=0?`checked disabled`:this.data&&this.data.id?`disabled`:``} />
                <span class="iTabel"> 无 </span>
              </label>
              <label class="radio-inline">
                <input name="jjz_radio" type="checkbox" value="无法建立合适的血管通路" ${this.data && this.data.jinJiZheng && (this.data.jinJiZheng.split(',')).filter(item=>item == '无法建立合适的血管通路').length!=0?`checked disabled`:this.data&&this.data.id?`disabled`:``} />  
                <span class="iTabel"> 无法建立合适的血管通路 </span>
              </label>
              <label class="radio-inline">
                <input name="jjz_radio" type="checkbox" value="严重凝血功能障碍" ${this.data && this.data.jinJiZheng && (this.data.jinJiZheng.split(',')).filter(item=>item == '严重凝血功能障碍').length!=0?`checked disabled`:this.data&&this.data.id?`disabled`:``} />  
                <span class="iTabel"> 严重凝血功能障碍 </span>
              </label>
              <label class="radio-inline">
                <input name="jjz_radio" type="checkbox" value="严重活动性出血" ${this.data && this.data.jinJiZheng && (this.data.jinJiZheng.split(',')).filter(item=>item == '严重活动性出血').length!=0?`checked disabled`:this.data&&this.data.id?`disabled`:``} />  
                <span class="iTabel"> 严重活动性出血 </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;">AKI分期：</span>
            <span class="changeRadio">
              <label class="radio-inline">
                <input name="AKIfq" type="radio" value="1期" ${this.data && this.data.aki && this.data.aki=='1期'?`checked`:this.data&&this.data.id?`disabled`:``} />
                <span class="iTabel"> Ⅰ期 </span>
              </label>
              <label class="radio-inline">
                <input name="AKIfq" type="radio" value="2期" ${this.data && this.data.aki && this.data.aki=='2期'?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> Ⅱ期 </span>
              </label>
              <label class="radio-inline">
                <input name="AKIfq" type="radio" value="3期" ${this.data && this.data.aki && this.data.aki=='3期'?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> Ⅲ期 </span>
              </label>
              <label class="radio-inline">
                <input name="AKIfq" type="radio" value="无" ${this.data && this.data.aki && this.data.aki==''?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> 无 </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;">模  式：</span>
            <span class="changeRadio">
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="CVVH" onclick="MSchangeNow()" ${this.data && this.data.moShi && this.data.moShi=='CVVH'?`checked`:this.data&&this.data.id?`disabled`:``} />
                <span class="iTabel"> CVVH </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="CVVHD" onclick="MSchangeNow()" ${this.data && this.data.moShi && this.data.moShi=='CVVHD'?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> CVVHD </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="CVVHDF" onclick="MSchangeNow()" ${this.data && this.data.moShi && this.data.moShi=='CVVHDF'?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> CVVHDF </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="血浆置换" onclick="MSchangeNow()" ${this.data && this.data.moShi && this.data.moShi=='血浆置换'?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> 血浆置换 </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="人工肝" onclick="MSchangeNow()" ${this.data && this.data.moShi && this.data.moShi=='人工肝'?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> 人工肝 </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="其他：" onclick="MSchangeNow()" ${this.data && this.data.moShi && this.data.moShi.indexOf('其他：')!=-1?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> 其他&nbsp;&nbsp;<input class="allInput" name="MSQTVal" value="${this.data && this.data.moShi && ((this.data.moShi).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?((this.data.moShi).split(',')).filter(item=>item.indexOf('其他：')!=-1)[0].split('：')[1]:''}" ${this.data&&this.data.id?`readonly`:`disabled`} style="height:26px">  </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;">滤  器：</span>
            <span class="changeRadio">
              <label class="radio-inline">
                <input name="nq_radio" type="radio" value="Prismaflex M150" onclick="LQchangeNow()" ${this.data && this.data.lvQi && this.data.lvQi=='Prismaflex M150'?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> Prismaflex M150 </span>
              </label>
              <label class="radio-inline">
                <input name="nq_radio" type="radio" value="ACF-180W" onclick="LQchangeNow()" ${this.data && this.data.lvQi && this.data.lvQi=='ACF-180W'?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> ACF-180W </span>
              </label>
              <label class="radio-inline">
                <input name="nq_radio" type="radio" value="PlasmafoOP-08" onclick="LQchangeNow()" ${this.data && this.data.lvQi && this.data.lvQi=='PlasmafoOP-08'?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> PlasmafoOP-08 </span>
              </label>
              <label class="radio-inline">
                <input name="nq_radio" type="radio" value="其他：" onclick="LQchangeNow()" ${this.data && this.data.lvQi && this.data.lvQi.indexOf('其他：')!=-1?`checked`:this.data&&this.data.id?`disabled`:``} />
                <span class="iTabel"> 其他&nbsp;&nbsp;<input class="allInput" name="LQVal" value="${this.data && this.data.lvQi && ((this.data.lvQi).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?((this.data.lvQi).split(',')).filter(item=>item.indexOf('其他：')!=-1)[0].split('：')[1]:''}" ${this.data&&this.data.id?`readonly`:`disabled`} style="height:26px"> </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name" style="background: #00B0F0;">
            <span style="font-weight: bold;width: 100px;min-width: 100px;">抗凝方案：</span>
            <span  class="changecheck" style="flex-direction: column;">
              <div style="display: flex;justify-content: flex-start;">
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" onclick="KNFAchange()" value="无" ${this.data && this.data.kangNingFA && (this.data.kangNingFA=='' || this.data.kangNingFA=='无')?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                  <span class="iTabel"> 无 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" onclick="KNFAchange()" value="肝素" ${this.data && this.data.kangNingFA && this.data.kangNingFA=='肝素'?`checked`:this.data&&this.data.id?`disabled`:``} />
                  <span class="iTabel"> 肝素 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" onclick="KNFAchange()" value="枸橼酸" ${this.data && this.data.kangNingFA && this.data.kangNingFA=='枸橼酸'?`checked`:this.data&&this.data.id?`disabled`:``} />
                  <span class="iTabel"> 枸橼酸 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" onclick="KNFAchange()" value="比伐卢定" ${this.data && this.data.kangNingFA && this.data.kangNingFA=='比伐卢定'?`checked`:this.data&&this.data.id?`disabled`:``} />
                  <span class="iTabel"> 比伐卢定 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" onclick="KNFAchange()" value="萘莫司他" ${this.data && this.data.kangNingFA && this.data.kangNingFA=='萘莫司他'?`checked`:this.data&&this.data.id?`disabled`:``} />
                  <span class="iTabel"> 萘莫司他 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" onclick="KNFAchange()" value="其他：" ${this.data && this.data.kangNingFA &&  this.data.kangNingFA.indexOf('其他：')!=-1?`checked`:this.data&&this.data.id?`disabled`:``} />
                  <span class="iTabel"> 其他&nbsp;&nbsp;<input class="allInput" name="KNFAVAL" value="${this.data && this.data.kangNingFA && ((this.data.kangNingFA).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?((this.data.kangNingFA).split(',')).filter(item=>item.indexOf('其他：')!=-1)[0].split('：')[1]:''}" ${this.data&&this.data.id?`readonly`:`disabled`} style="height:26px"> </span>
                </label>
              </div>
              <div style="display: flex;justify-content: flex-start;align-items: center;padding-bottom: 5px;">
                <span class="iTabel" style="width:150px;border-right: 2px solid #fff;min-width:150px;">肝素/萘莫司他/比伐卢定</span>
                <span  class="changecheck" style="flex-direction: row;flex-wrap: wrap;">
                  <label class="radio-inline">
                    APTT目标值：
                    <input class="allInput" name="KNFAAPPT" value="${this.data && this.data.aptt?this.data.aptt:''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px"></input> 
                    &nbsp;s
                  </label>
                  <label class="radio-inline">
                    负荷计量：
                    <input class="allInput" name="KNFAFHJL" value="${this.data && this.data.fuHeJL?this.data.fuHeJL:''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px"></input> 
                    &nbsp;mg
                  </label>
                  <label class="radio-inline">
                    维持剂量：
                    <input class="allInput" name="KNFAWC" value="${this.data && this.data.weiChiJL?this.data.weiChiJL:''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px"></input> 
                    &nbsp;mg/h
                  </label>
                </span>
              </div>
              <div style="display: flex;justify-content: flex-start;padding-bottom: 5px;">
                <span class="iTabel" style="width:150px;border-right: 2px solid #fff;">枸橼酸</span>
                <label class="radio-inline" >
                  枸橼酸剂量：
                  <input class="allInput" name="KNFAGJSJL" value="${this.data && this.data.juYuanSuanJL?this.data.juYuanSuanJL:''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px"></input> 
                  &nbsp;mmol/l
                </label>
                <br/>
                <label class="radio-inline" >
                  钙剂量：
                  <input class="allInput" name="KNFAGJSGJL" value="${this.data && this.data.gaiJiLiang?this.data.gaiJiLiang:''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px"></input> 
                  &nbsp;
                </label>
              </div>
              <div style="display: flex;justify-content: flex-start;padding-bottom: 5px;">
                <span class="iTabel" style="width:150px;border-right: 2px solid #fff;">其他</span>
                <label class="radio-inline" >
                  剂量：
                  <input class="allInput" name="KNFAGJSQTJL" value="${this.data && this.data.qiTaJL?this.data.qiTaJL:''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px"></input> 
                </label>
              </div>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name" >
            <span style="font-weight: bold; width: 100px;min-width: 100px;">封管：</span>
            <span class="changeRadio">
              <label class="radio-inline">
                <input name="fg_radio" type="radio" value="肝素（1:1000U）" onclick="FGchangeNow()" ${this.data && this.data.fengGuan && this.data.fengGuan =='肝素（1:1000U）'?`checked`:this.data&&this.data.id?`disabled`:``} />
                <span class="iTabel"> 肝素（1:1000U） </span>
              </label>
              <label class="radio-inline">
                <input name="fg_radio" type="radio" value="4%枸橼酸" onclick="FGchangeNow()" ${this.data && this.data.fengGuan && this.data.fengGuan =='4%枸橼酸'?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> 4%枸橼酸 </span>
              </label>
              <label class="radio-inline">
                <input name="fg_radio" type="radio" value="其他：" onclick="FGchangeNow()" ${this.data && this.data.fengGuan && this.data.fengGuan && (this.data.fengGuan).indexOf('其他') != -1?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> 其他&nbsp;&nbsp;<input class="allInput" name="FGQT" value="${this.data && this.data.fengGuan && ((this.data.fengGuan).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?((this.data.fengGuan).split(',')).filter(item=>item.indexOf('其他：')!=-1)[0].split('：')[1]:''}" ${this.data&&this.data.id?`readonly`:`disabled`} style="height:26px">  </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;">管路预冲：</span>
            <span class="changeRadio">
              <label class="radio-inline">
                NS500ml+ &nbsp;&nbsp;
                <input class="allInput" name="guanLuYC" value="${this.data && this.data.guanLuYC?this.data.guanLuYC:''}" ${this.data&&this.data.id?`readonly`:``} style="height:26px;width:100px"></input> 
                &nbsp;mg（
                <span  class="changecheck">
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="guanLuYCYP" type="checkbox" value="肝素" ${this.data && this.data.guanLuYCYP && (this.data.guanLuYCYP.split(',')).filter(item=>item == '肝素')[0]=='肝素'?`checked disabled`:this.data&&this.data.id?`disabled`:``} /> 
                    <span class="iTabel"> 肝素 </span>
                  </label>
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="guanLuYCYP" type="checkbox" value="萘莫司他" ${this.data  && this.data.guanLuYCYP && (this.data.guanLuYCYP.split(',')).filter(item=>item == '萘莫司他')[0]=='萘莫司他'?`checked disabled`:this.data&&this.data.id?`disabled`:``} /> 
                    <span class="iTabel"> 萘莫司他 </span>
                  </label>
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="guanLuYCYP" type="checkbox" value="无" ${this.data && this.data.guanLuYCYP && (this.data.guanLuYCYP.split(',')).filter(item=>item == '无')[0]=='无'?`checked disabled`:this.data&&this.data.id?`disabled`:``} /> 
                    <span class="iTabel"> 无 </span>
                  </label>
                </span>
                ）
              </label>
            </span>
            <span>预充液是否进入体内：</span>
            <span class="changeRadio">
              <label class="radio-inline">
                <input name="yuChongYeJRTN" type="radio" value="是" ${this.data && this.data.yuChongYeJRTN && this.data.yuChongYeJRTN=='是'?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> 是 </span>
              </label>
              <label class="radio-inline">
                <input name="yuChongYeJRTN" type="radio" value="否" ${this.data && this.data.yuChongYeJRTN && this.data.yuChongYeJRTN=='否'?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                <span class="iTabel"> 否 </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;">置换液：</span>
              <span class="changecheck" style="flex-direction: column;">
                <div style="display: flex;flex-direction: row;align-items: center;justify-content: flex-start;">
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="zhiHuanYe" type="checkbox" value="血液滤过置换基础液" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '血液滤过置换基础液')[0]=='血液滤过置换基础液'?`checked disabled`:this.data&&this.data.id?`disabled`:``} /> 
                    <span class="iTabel"> 血液滤过置换基础液 </span>
                  </label>
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="zhiHuanYe" type="checkbox" value="氯化钾" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '氯化钾')[0]=='氯化钾'?`checked disabled`:this.data&&this.data.id?`disabled`:``} /> 
                    <span class="iTabel"> 氯化钾 </span>
                  </label>
                </div>
                <div style="display: flex;flex-direction: row;align-items: center;justify-content: flex-start;">
                  <label class="radio-inline" style="border-right: 2px solid #fff;left:-18px;">
                    <input name="zhiHuanYe" type="checkbox" value="PORT配方" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == 'PORT配方')[0] =='PORT配方'?`checked disabled`:this.data&&this.data.id?`disabled`:``} /> 
                    <span class="iTabel"> PORT配方 </span>
                  </label>
                  <label class="radio-inline">
                    <input name="zhiHuanYe" type="checkbox" value="NS3000ml" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == 'NS3000ml')[0]=='NS3000ml'?`checked disabled`:this.data&&this.data.id?`disabled`:``} /> 
                    <span class="iTabel"> NS3000 </span>
                  </label>
                  <label class="radio-inline">
                    <input name="zhiHuanYe" type="checkbox" value="5%GS1000" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '5%GS1000')[0]=='5%GS1000'?`checked disabled`:this.data&&this.data.id?`disabled`:``} /> 
                    <span class="iTabel"> 5%GS1000 </span>
                  </label>
                  <label class="radio-inline">
                    <input name="zhiHuanYe" type="checkbox" value="50%硫酸镁1.6" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '50%硫酸镁1.6')[0]=='50%硫酸镁1.6'?`checked disabled`:this.data&&this.data.id?`disabled`:``} /> 
                    <span class="iTabel"> 50%硫酸镁1.6 </span>
                  </label>
                  <label class="radio-inline">
                    <input name="zhiHuanYe" type="checkbox" value="10%氯化钙10ml" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '10%氯化钙10ml')[0]=='10%氯化钙10ml'?`checked disabled`:this.data&&this.data.id?`disabled`:``} /> 
                    <span class="iTabel"> 10%氯化钙10ml </span>
                  </label>
                </div>
              </span>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;min-width: 100px;">稀释方式：</span>
              <span  class="changeRadio">
                <label class="radio-inline">
                  <input name="xsfs_check" type="radio" value="前稀释" onclick="changeNow()" ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('前稀释')!=-1?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                  <span class="iTabel"> 前稀释 </span>
                </label>
                <label class="radio-inline">
                  <input name="xsfs_check" type="radio" value="后稀释" onclick="changeNow()" ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('后稀释')!=-1?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                  <span class="iTabel"> 后稀释 </span>
                </label>
                <label class="radio-inline">
                  <input name="xsfs_check" type="radio" value="前稀释+后稀释" onclick="changeNow()" ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('前稀释+后稀释')!=-1?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                  <span class="iTabel"> 前稀释+后稀释 </span>
                </label>
                <label class="radio-inline">
                  <input name="xsfs_check" type="radio" value="透析+稀释：" onclick="changeNow()" ${this.data && this.data.xiShiFS && this.data.xiShiFS && (this.data.xiShiFS.split(',')).filter(item=>item.indexOf('透析+稀释') != -1).length!=0?`checked`:this.data&&this.data.id?`disabled`:``} /> 
                  <span class="iTabel"> 透析+稀释 </span>
                  （ 
                  <span  class="changecheck">
                    <label class="radio-inline" style="padding-left: 2px;">
                      <input name="txxsq_check" type="checkbox" value="前" ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('前')!=-1?`checked disabled`:$('.showForm input[name=xsfs_check]:checked').val()=='透析+稀释：'?'':'disabled'} /> 
                      <span> 前 </span>
                    </label>
                    <label class="radio-inline">
                      <input name="txxsq_check" type="checkbox" value="后" ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('前')!=-1?`checked disabled`:$('.showForm input[name=xsfs_check]:checked').val()=='透析+稀释：'?'':'disabled'}/> 
                      <span> 后 </span>
                    </label>
                  </span>
                  ）
                </label>
              </span>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name" style="height:100px;">
            <span style="font-weight: bold; width: 100px;min-width: 100px;padding-right:10px">人工肝/血浆置换：</span>
            <span  class="changecheck" style="flex-direction: row;flex-wrap: wrap;">
              <label class="radio-inline" style="padding-left: 2px;padding-right: 18px;">
                <span> 血浆 </span>
                &nbsp;&nbsp;<input class="allInput" name="xjzhXJ" value="${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('血浆：')!=-1).length!=0?((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('血浆：')!=-1)[0].split('：')[1]:''}" ${this.data&&this.data.id?'readonly':''} style="width:50px" /> &nbsp;ml
              </label>
              <label class="radio-inline" style="padding-left: 2px;">
                <input name="ZJZH" type="checkbox" value="NS1000ml：" onclick="CJZHchange()" ${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('NS1000ml：')!=-1).length!=0?`checked disabled`: this.data&&this.data.id?'disabled':''} /> 
                <span class="iTabel"> NS1000ml </span>
                <input class="allInput" name="xjzhNSVal" value="${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('NS1000ml：')!=-1).length!=0?((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('NS1000ml：')!=-1)[0].split('：')[1]:''}" ${this.data&&this.data.id?'readonly':'disabled'} style="width:50px" /> &nbsp;ml
              </label>
              <label class="radio-inline">
                <input name="ZJZH" type="checkbox" value="20%白蛋白：" onclick="CJZHchange()" ${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('20%白蛋白：')!=-1).length!=0?`checked disabled`:this.data&&this.data.id?'disabled':''} /> 
                <span class="iTabel"> 20%白蛋白 </span>
                <input class="allInput" name="xjzhBDBVal" value="${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('20%白蛋白：')!=-1).length!=0?((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('20%白蛋白：')!=-1)[0].split('：')[1]:''}" ${this.data&&this.data.id?'readonly':'disabled'} style="width:50px" /> &nbsp;ml
              </label>
              <label class="radio-inline">
                <input name="ZJZH" type="checkbox" value="10%葡萄糖酸钙针：" onclick="CJZHchange()"  ${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('20%葡萄糖酸钙针：')!=-1).length!=0?`checked disabled`:this.data&&this.data.id?'disabled':''} /> 
                <span class="iTabel">  10%葡萄糖酸钙针 </span>
                <input class="allInput" name="xjzhPPTSGVal" value="${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('10%葡萄糖酸钙针：')!=-1).length!=0?((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('10%葡萄糖酸钙针：')!=-1)[0].split('：')[1]:''}" ${this.data&&this.data.id?'readonly':'disabled'} style="width:50px" /> &nbsp;ml
              </label>
              <label class="radio-inline">
                <input name="ZJZH" type="checkbox" value="人工胶体：" onclick="CJZHchange()"  ${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('人工胶体：')!=-1).length!=0?`checked disabled`:this.data&&this.data.id?'disabled':''} /> 
                <span class="iTabel"> 人工胶体 </span>
                <input class="allInput" name="zjzhRGJTVal" value="${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('人工胶体：')!=-1).length!=0?((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('人工胶体：')!=-1)[0].split('：')[1]:''}" ${this.data&&this.data.id?'readonly':'disabled'} style="width:50px" /> &nbsp;ml
              </label>
              <label class="radio-inline">
                <input name="ZJZH" type="checkbox" value="其他：" onclick="CJZHchange()"  ${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?`checked disabled`:this.data&&this.data.id?'disabled':''} /> 
                <span class="iTabel"> 其他 </span>
                &nbsp;&nbsp;<input class="allInput" name="xjzhQTVal" value="${this.data && this.data.xueJiangZH && ((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?((this.data.xueJiangZH).split(',')).filter(item=>item.indexOf('其他：')!=-1)[0].split('：')[1]:''}" ${this.data&&this.data.id?'readonly':'disabled'} style="width:300px" />
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name" style="background: #7dbc6ecc;">
            <span style="font-weight: bold; width: 100px;min-width: 100px;">初始参数：</span>
            <span style="flex-direction: column;">
              <div style="display: flex;justify-content: flex-start;">
                <label class="radio-inline" style="padding-left: 2px;">
                  <span> 目标血流速 </span>
                  &nbsp;&nbsp;<input class="allInput" name="mbxls" value="${this.data && this.data.muBiaoXLL?this.data.muBiaoXLL:''}" ${this.data&&this.data.id?'readonly':''} style="width:50px" /> &nbsp;ml/min
                </label>
                <label class="radio-inline">
                  <span> 前置换 </span>
                  &nbsp;&nbsp;<input class="allInput" name="qzh" value="${this.data && this.data.qianZhiHL?this.data.qianZhiHL:''}" ${this.data&&this.data.id?'readonly':''} style="width:50px" /> &nbsp;ml/h
                </label>
                <label class="radio-inline">
                  <span> 后置换 </span>
                  &nbsp;&nbsp;<input class="allInput" name="hzh" value="${this.data && this.data.houZhiHL?this.data.houZhiHL:''}" ${this.data&&this.data.id?'readonly':''} style="width:50px" /> &nbsp;ml/h
                </label>
                <label class="radio-inline">
                  <span> 透析液 </span>
                  &nbsp;&nbsp;<input class="allInput" name="txy" value="${this.data && this.data.touXiYe?this.data.touXiYe:''}" ${this.data&&this.data.id?'readonly':''} style="width:50px" /> &nbsp;ml/h
                </label>
                <label class="radio-inline">
                  <span> 5%碳酸氢钠 </span>
                  &nbsp;&nbsp;<input class="allInput" name="tsqn" value="${this.data && this.data.tanSuanQN?this.data.tanSuanQN:''}" ${this.data&&this.data.id?'readonly':''} style="width:50px" /> &nbsp;ml/h
                </label>
              </div>
              <div style="display: flex;justify-content: flex-start;">
                <label class="radio-inline" style="padding-left: 2px;">
                  <span> 10%氯化钾 </span>
                  &nbsp;&nbsp;<input class="allInput" name="lhj" value="${this.data && this.data.lvHuaJia?this.data.lvHuaJia:''}" ${this.data&&this.data.id?'readonly':''} style="width:50px" /> &nbsp;ml/(4L置换液)
                </label>
                <label class="radio-inline">
                  <span> 目标超滤量 </span>
                  &nbsp;&nbsp;<input class="allInput" name="mbcll" value="${this.data && this.data.muBiaoCLL?this.data.muBiaoCLL:''}" ${this.data&&this.data.id?'readonly':''} style="width:50px" /> &nbsp;ml/h
                </label>
                <label class="radio-inline">
                  <span> 预期治疗时间 </span>
                  &nbsp;&nbsp;<input class="allInput" name="yqzlsj" value="${this.data && this.data.yuQiZLSJ?this.data.yuQiZLSJ:''}" ${this.data&&this.data.id?'readonly':''} style="width:50px" /> &nbsp;h
                </label>
                <label class="radio-inline">
                  <span> 净超滤目标 </span>
                  &nbsp;&nbsp;<input class="allInput" name="jclmb" value="${this.data && this.data.jingChaoLMBL?this.data.jingChaoLMBL:''}" ${this.data&&this.data.id?'readonly':''} style="width:50px" /> &nbsp;ml/d
                </label>
              </div>
              <div style="display: flex;justify-content: flex-start;position: relative;left: -16px;">
                <label class="radio-inline">
                  <span> 血浆分离流量 </span>
                  &nbsp;&nbsp;<input class="allInput" name="xjflll" value="${this.data && this.data.xueJiangFLLL?this.data.xueJiangFLLL:''}" ${this.data&&this.data.id?'readonly':''} style="width:50px" /> &nbsp;ml/min
                </label>
                <label class="radio-inline">
                  <span> 血浆治疗总量 </span>
                  &nbsp;&nbsp;<input class="allInput" name="xjzlzl" value="${this.data && this.data.xueJiangZLZL?this.data.xueJiangZLZL:''}" ${this.data&&this.data.id?'readonly':''} style="width:50px" /> &nbsp;ml
                </label>
              </div>
            </span>
          </span>
        </div>
      </div>
      <div class="formSort">
        <div class="LFormTitle label-name" style="height:130px;">
          <span style="font-weight: bold; width: 100px;min-width: 100px;">抗凝检测：</span>
          <span  class="changecheck" style="flex-direction: column;">
            <div style="display: flex;justify-content: flex-start;align-items: center;padding-bottom: 5px;">
              <span class="iTabel" style="width:150px;">APTT/ACT</span>
              <label class="radio-inline">
                常规q4h，根据病情调整为q1h-qd/q1h-g6h监测
              </label>
            </div>
            <div style="display: flex;justify-content: flex-start;padding-bottom: 5px;    align-items: center;">
              <span class="iTabel" style="width:150px;">枸橼酸抗凝</span>
              <span  class="changecheck" style="flex-direction: column;">
                <label class="radio-inline">
                  动态监测体内和滤器离子钙、PH、Na+、HC03-水平
                </label>
                <label class="radio-inline">
                  血气监测频率:治疗开始后5min，i调整后1h，稳定后q6h
                </label>
                <label class="radio-inline">
                  测总钙gd，目标:总钙<3mmol儿，总钙/离子钙<2.5。
                </label>
              <span>
            </div>
          </span>
        </div>
      </div>
      <div class="formSort" style="height: 32px;">
        <button class="${this.data?'':'e_btn'} backBtn" onclick="saveCFD()" ${this.data&&this.data.id?'disabled':''}>保存</button>
      </div>
    </div>
    `
    this.$el.html(html)
    return this;
  }
})
// 血液净化处方单 按钮+表格 （this.data?`disabled`:``）
var MenuCFDTb_View = WRT_e.view.extend({
  render: function () {
    console.log('表格',this.data);
    let html=`
      <div class="allBtn">
        <button class="e_btn" onclick="addCFYZ()">新增处方医嘱</button>
        <button class="e_btn" onclick="saveTB()">保存</button>
      </div>
      <div class="overall_table_frame" style="overflow-x: auto;scrollbar-width: thin">
        <table border="1" id="crrt_table" class="crrt_tableCFD" style="white-space: nowrap;">
          <thead>
            <tr class="row_head">
              <th rowspan="2" width="54">序号</th>
              <th rowspan="2">时间</th>
              <th colspan="5" style="text-align: center;">抗凝</th>
              <th rowspan="2">血流速 ml/min</th>
              <th rowspan="2">目标超滤量ml/h</th>
              <th rowspan="2">前置换ml/h</th>
              <th rowspan="2">后置换ml/h</th>
              <th rowspan="2">透析液ml/h</th>
              <th rowspan="2">碳酸氢钠ml/h</th>
              <th rowspan="2">氯化钾ml/(4L置换液)</th>
              <th rowspan="2">医生签字时间</th>
              <th rowspan="2">护士签字时间</th>
              <th rowspan="2">医生签名</th>
              <th rowspan="2">护士签名</th>
              <th rowspan="2" class="fixed-columns">操作</th>
            </tr>
            <tr class="row_head" style="text-align: center;">
              <th>药品</th>
              <th>负荷剂量(mg)</th>
              <th>维持剂量(mg/h)</th>
              <th>枸橼酸(mmol/h)</th>
              <th style="background: #85A2DB;">钙补偿(ml/h)</th>
            </tr>
          </thead>
          <tbody class="line_nr" id="line_nr">
            ${this.data.length>0?`
              ${_.map(this.data,(item,index)=>` <tr class="row_nr">
                <td class="xh${index}">${item.xuHao}</td>
                <td class="sj${index}">${item.shouCiLRSJ}</td>
                <td class="ypm${index}">${item.kangNingFA?item.kangNingFA:''}</td>
                <td class="sj${index}">${item.shouJi?item.shouJi:''}</td>
                <td class="wc${index}">${item.weiChi?item.weiChi:''}</td>
                <td class="gys${index}">${item.juYuanSuan?item.juYuanSuan:''}</td>
                <td class="gbc${index}">${item.gaiBuChang?item.gaiBuChang:''}</td>
                <td class="xls${index}">${item.muBiaoXLL?item.muBiaoXLL:''}</td>
                <td class="mbcll${index}">${item.muBiaoCLL?item.muBiaoCLL:''}</td>
                <td class="qzhy${index}">${item.qianZhiHuan?item.qianZhiHuan:''}</td>
                <td class="hzhy${index}">${item.houZhiHuan?item.houZhiHuan:''}</td>
                <td class="txy${index}">${item.touXiYe?item.touXiYe:''}</td>
                <td class="tsqn${index}">${item.tanSuanQN?item.tanSuanQN:''}</td>
                <td class="lhj${index}">${item.lvHuaJia?item.lvHuaJia:''}</td>
                <td class="ysqmsj${index}">${item.yiShengQMSJ?item.yiShengQMSJ:''}</td>
                <td class="ysqmsj${index}">${item.huShiSHSJ?item.huShiSHSJ:''}</td>
                <td class="ysqm${index}">${item.yiShengQM?item.yiShengQM:''}</td>
                <td class="hsqm${index}">
                ${item.huShSHZXM?item.huShSHZXM:WRT_config.url.token?`
                  <a herf="javascript:void(0)" calss='qmBtn' onclick='secondQM(${index})' style='position: relative;cursor: pointer;padding-left:10px;'>护士签字</a>
                `:`${item.huShiSHSJ?item.huShSHZXM:''}`}
                </td>
                <td class="fixed-column">
                  ${item.huShSHZXM && item.huShiSHSJ?``:`
                    <a herf="javascript:void(0)" calss='delBtn' onclick='del(${JSON.stringify(item)})' style='position: relative;cursor: pointer;'>删除</a>
                  `}
                </td>
              </tr>`).join('')}
            `:``}
          </tbody>
        </table>
      </div>
    `
    this.$el.html(html)
    return this;
  },

})
// 新增弹窗
var addCFDModal_View = WRT_e.view.extend({
  render: function () {
    console.log('新增弹窗',this.data);
    let html=`
      <div class='addtc' style="color: rgba(0, 0, 0, .65); background-color: #fff;">
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;">模  式：</span>
            <span class="changeRadio">
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="CVVH" onclick="MSchangeNow()" ${this.data && this.data.moShi && this.data.moShi=='CVVH'?`checked`:``}/>
                <span class="iTabel"> CVVH </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="CVVHD" onclick="MSchangeNow()"  ${this.data && this.data.moShi && this.data.moShi=='CVVHD'?`checked`:``} /> 
                <span class="iTabel"> CVVHD </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="CVVHDF" onclick="MSchangeNow()"  ${this.data && this.data.moShi && this.data.moShi=='CVVHDF'?`checked`:``} /> 
                <span class="iTabel"> CVVHDF </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="血浆置换 onclick="MSchangeNow()""  ${this.data && this.data.moShi && this.data.moShi=='血浆置换'?`checked`:``} /> 
                <span class="iTabel"> 血浆置换 </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="人工肝" onclick="MSchangeNow()"  ${this.data && this.data.moShi && this.data.moShi=='人工肝'?`checked`:``} /> 
                <span class="iTabel"> 人工肝 </span>
              </label>
              <label class="radio-inline">
                <input name="ms_radio" type="radio" value="其他：" onclick="MSchangeNow()" ${this.data && this.data.moShi && this.data.moShi.indexOf('其他：')!=-1?`checked`:``} /> 
                <span class="iTabel"> 其他 &nbsp;&nbsp;<input class="allInput" name="MSQTVal" value="${this.data && this.data.moShi && ((this.data.moShi).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?((this.data.moShi).split(',')).filter(item=>item.indexOf('其他：')!=-1)[0].split('：')[1]:''}" ${this.data && this.data.moShi && ((this.data.moShi).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?``:'disabled'} style="height:26px">  </span>
              </label>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name" style="background: #00B0F0;">
            <span style="font-weight: bold;width: 130px;">抗凝方案：</span>
            <span  class="changecheck" style="flex-direction: column;">
              <div style="display: flex;justify-content: flex-start;">
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" value="无" onclick="KNFAchange()"  ${this.data && this.data.kangNingFA && (this.data.kangNingFA=='' || this.data.kangNingFA=='无')?`checked`:``} /> 
                  <span class="iTabel"> 无 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" value="肝素" onclick="KNFAchange()"  ${this.data && this.data.kangNingFA && (this.data.kangNingFA=='' || this.data.kangNingFA=='肝素')?`checked`:``} />
                  <span class="iTabel"> 肝素 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" value="枸橼酸" onclick="KNFAchange()"  ${this.data && this.data.kangNingFA && (this.data.kangNingFA=='' || this.data.kangNingFA=='枸橼酸')?`checked`:``} />
                  <span class="iTabel"> 枸橼酸 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" value="比伐卢定 onclick="KNFAchange()""  ${this.data && this.data.kangNingFA && (this.data.kangNingFA=='' || this.data.kangNingFA=='比伐卢定')?`checked`:``} />
                  <span class="iTabel"> 比伐卢定 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" value="萘莫司他" onclick="KNFAchange()"  ${this.data && this.data.kangNingFA && (this.data.kangNingFA=='' || this.data.kangNingFA=='萘莫司他')?`checked`:``} />
                  <span class="iTabel"> 萘莫司他 </span>
                </label>
                <label class="radio-inline">
                  <input name="KNFA_radio" type="radio" value="其他：" onclick="KNFAchange()" ${this.data && this.data.kangNingFA &&  this.data.kangNingFA.indexOf('其他：')!=-1?`checked`:``}/>
                  <span class="iTabel"> 其他&nbsp;&nbsp;<input class="allInput" name="KNFAQTYP" value="${this.data && this.data.kangNingFA && ((this.data.kangNingFA).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?((this.data.kangNingFA).split(',')).filter(item=>item.indexOf('其他：')!=-1)[0].split('：')[1]:''}" ${this.data && this.data.kangNingFA && ((this.data.kangNingFA).split(',')).filter(item=>item.indexOf('其他：')!=-1).length!=0?``:'disabled'} style="height:26px"> </span>
                </label>
              </div>
              <div style="display: flex;justify-content: flex-start;align-items: center;padding-bottom: 5px;">
                <span class="iTabel" style="width:150px;border-right: 2px solid #fff;min-width: 150px;">肝素/萘莫司他/比伐卢定</span>
                <span  class="changecheck" style="flex-direction: row;flex-wrap: wrap;">
                  <label class="radio-inline" style="width: 306px;">
                    APTT目标值：
                    <input class="allInput" name="KNFAAPPT" value="${this.data && this.data.aptt?this.data.aptt:''}"  style="height:26px;width: 150px;"></input> 
                    &nbsp;s
                  </label>
                  <label class="radio-inline">
                    负荷计量：
                    <input class="allInput" name="KNFAFHJL" value="${this.data && this.data.fuHeJL?this.data.fuHeJL:''}"  style="height:26px;width: 150px;"></input> 
                    &nbsp;mg
                  </label>
                  <label class="radio-inline">
                    维持剂量：
                    <input class="allInput" name="KNFAWC" value="${this.data && this.data.weiChiJL?this.data.weiChiJL:''}"  style="height:26px;width: 150px;"></input> 
                    &nbsp;mg/h
                  </label>
                </span>
              </div>
              <div style="display: flex;justify-content: flex-start;align-items: center;padding-bottom: 5px;">
                <span class="iTabel" style="width:150px;border-right: 2px solid #fff;min-width: 150px;">枸橼酸</span>
                <span  class="changecheck" style="flex-direction: row;flex-wrap: wrap;">
                  <label class="radio-inline">
                    枸橼酸剂量：
                    <input class="allInput" name="KNFAGJSJL" value="${this.data && this.data.juYuanSuanJL?this.data.juYuanSuanJL:''}"  style="height:26px;width: 150px;"></input> 
                    &nbsp;mmol/l
                  </label>
                  <label class="radio-inline">
                    钙剂量：
                    <input class="allInput" name="KNFAGJSGJL" value="${this.data && this.data.gaiJiLiang?this.data.gaiJiLiang:''}"  style="height:26px;width: 150px;"></input> 
                    &nbsp;
                  </label>
                </span>
              </div>
              <div style="display: flex;justify-content: flex-start;padding-bottom: 5px;">
                <span class="iTabel" style="width:150px;border-right: 2px solid #fff;">其他</span>
                <label class="radio-inline" >
                  剂量：
                  <input class="allInput" name="KNFAGJSQTJL" value="${this.data && this.data.qiTaJL?this.data.qiTaJL:''}"  style="height:26px"></input> 
                </label>
              </div>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;">管路预冲：</span>
            <span class="changeRadio">
              <label class="radio-inline" style="padding-left: 0px;">
                NS500ml+ &nbsp;&nbsp;
                <input class="allInput" name="GLYCNS500" value="${this.data && this.data.guanLuYC?this.data.guanLuYC:''}"  style="height:26px;width:100px"></input> 
                &nbsp;mg（
                <span  class="changecheck">
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="guanLuYCYPAdd" type="checkbox" value="肝素" ${this.data && this.data.guanLuYCYP && (this.data.guanLuYCYP.split(',')).filter(item=>item == '肝素')[0]=='肝素'?`checked`:``} /> 
                    <span class="iTabel"> 肝素 </span>
                  </label>
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="guanLuYCYPAdd" type="checkbox" value="萘莫司他"  ${this.data  && this.data.guanLuYCYP && (this.data.guanLuYCYP.split(',')).filter(item=>item == '萘莫司他')[0]=='萘莫司他'?`checked`:``} /> 
                    <span class="iTabel"> 萘莫司他 </span>
                  </label>
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="guanLuYCYPAdd" type="checkbox" value="无" ${this.data && this.data.guanLuYCYP && (this.data.guanLuYCYP.split(',')).filter(item=>item == '无')[0]=='无'?`checked`:``} /> 
                    <span class="iTabel"> 无 </span>
                  </label>
                </span>
                ）
              </label>
              <span class="changeRadio">
                <span>预充液是否进入体内：</span>
                <label class="radio-inline">
                  <input name="ycysf_radio" type="radio" value="是"${this.data && this.data.yuChongYeJRTN && this.data.yuChongYeJRTN=='是'?`checked`:``} /> 
                  <span class="iTabel"> 是 </span>
                </label>
                <label class="radio-inline">
                  <input name="ycysf_radio" type="radio" value="否"  ${this.data && this.data.yuChongYeJRTN && this.data.yuChongYeJRTN=='否'?`checked`:``} /> 
                  <span class="iTabel"> 否 </span>
                </label>
              </span>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;">置换液：</span>
              <span class="changecheck" style="flex-direction: column;">
                <div style="display: flex;flex-direction: row;align-items: center;justify-content: flex-start;">
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="zhiHuanYeAdd" type="checkbox" value="血液滤过置换基础液"  ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '血液滤过置换基础液')[0]=='血液滤过置换基础液'?`checked`:``} /> 
                    <span class="iTabel"> 血液滤过置换基础液 </span>
                  </label>
                  <label class="radio-inline" style="padding-left: 2px;">
                    <input name="zhiHuanYeAdd" type="checkbox" value="氯化钾" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '氯化钾')[0]=='氯化钾'?`checked`:``} /> 
                    <span class="iTabel"> 氯化钾 </span>
                  </label>
                </div>
                <div style="display: flex;flex-direction: row;align-items: center;justify-content: flex-start;">
                  <label class="radio-inline" style="left: -18px;border-right: 2px solid #fff;">
                    <input name="zhiHuanYeAdd" type="checkbox" value="PORT配方" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == 'PORT配方')[0] =='PORT配方'?`checked`:``} /> 
                    <span class="iTabel"> PORT配方 </span>
                  </label>
                  <label class="radio-inline">
                    <input name="zhiHuanYeAdd" type="checkbox" value="NS3000" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == 'NS3000ml')[0]=='NS3000ml'?`checked`:``} /> 
                    <span class="iTabel"> NS3000 </span>
                  </label>
                  <label class="radio-inline">
                    <input name="zhiHuanYeAdd" type="checkbox" value="5%GS1000"  ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '5%GS1000')[0]=='5%GS1000'?`checked`:``} /> 
                    <span class="iTabel"> 5%GS1000 </span>
                  </label>
                  <label class="radio-inline">
                    <input name="zhiHuanYeAdd" type="checkbox" value="50%硫酸镁1.6" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '50%硫酸镁1.6')[0]=='50%硫酸镁1.6'?`checked`:``}  /> 
                    <span class="iTabel"> 50%硫酸镁1.6 </span>
                  </label>
                  <label class="radio-inline">
                    <input name="zhiHuanYeAdd" type="checkbox" value="10%氯化钙10ml" ${this.data && this.data.zhiHuanYe && (this.data.zhiHuanYe.split(',')).filter(item=>item == '10%氯化钙10ml')[0]=='10%氯化钙10ml'?`checked`:``}/> 
                    <span class="iTabel"> 10%氯化钙10ml </span>
                  </label>
                </div>
              </span>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name">
            <span style="font-weight: bold; width: 100px;">稀释方式：</span>
              <span  class="changeRadio">
                <label class="radio-inline">
                  <input name="xsfsAdd_check" type="radio" value="前稀释" onclick="changeNow()" ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('前稀释')!=-1?`checked`:``} /> 
                  <span class="iTabel"> 前稀释 </span>
                </label>
                <label class="radio-inline">
                  <input name="xsfsAdd_check" type="radio" value="后稀释" onclick="changeNow()" ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('后稀释')!=-1?`checked`:``} /> 
                  <span class="iTabel"> 后稀释 </span>
                </label>
                <label class="radio-inline">
                  <input name="xsfsAdd_check" type="radio" value="前稀释+后稀释" onclick="changeNow()" ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('前稀释+后稀释')!=-1?`checked`:``} /> 
                  <span class="iTabel"> 前稀释+后稀释 </span>
                </label>
                <label class="radio-inline">
                  <input name="xsfsAdd_check" type="radio" value="透析+稀释：" onclick="changeNow()" ${this.data && this.data.xiShiFS && this.data.xiShiFS && (this.data.xiShiFS.split(',')).filter(item=>item.indexOf('透析+稀释') != -1).length!=0?`checked`:``} /> 
                  <span class="iTabel"> 透析+稀释 </span>
                  （ 
                  <span  class="changecheck">
                    <label class="radio-inline" style="padding-left: 2px;">
                      <input name="txxsqhAdd_check" type="checkbox" value="前" ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('前')!=-1?`checked`:$('.showForm input[name=xsfs_check]:checked').val()=='透析+稀释：'?'':'disabled'} /> 
                      <span> 前 </span>
                    </label>
                    <label class="radio-inline">
                      <input name="txxsqhAdd_check" type="checkbox" value="后"  ${this.data && this.data.xiShiFS && this.data.xiShiFS.indexOf('前')!=-1?`checked`:$('.showForm input[name=xsfs_check]:checked').val()=='透析+稀释：'?'':'disabled'} /> 
                      <span> 后 </span>
                    </label>
                  </span>
                  ）
                </label>
              </span>
            </span>
          </div>
        </div>
        <div class="formSort">
          <div class="LFormTitle label-name" style="background: #7dbc6ecc;">
            <span style="font-weight: bold; width: 265px;">初始参数：</span>
            <span style="display: flex;flex-direction: row;flex-wrap: wrap;">
              <label class="radio-inline">
                <span> 目标血流速 </span>
                &nbsp;&nbsp;<input class="allInput" name="MBXLSAdd" value="${this.data && this.data.muBiaoXLL?this.data.muBiaoXLL:''}" style="width:50px" /> &nbsp;ml/min
              </label>
              <label class="radio-inline">
                <span> 前置换 </span>
                &nbsp;&nbsp;<input class="allInput" name="QZHAdd" value="${this.data && this.data.qianZhiHL?this.data.qianZhiHL:''}" style="width:50px" /> &nbsp;ml/h
              </label>
              <label class="radio-inline">
                <span> 后置换 </span>
                &nbsp;&nbsp;<input class="allInput" name="HZHAdd" value="${this.data && this.data.houZhiHL?this.data.houZhiHL:''}" style="width:50px" /> &nbsp;ml/h
              </label>
              <label class="radio-inline">
                <span> 透析液 </span>
                &nbsp;&nbsp;<input class="allInput" name="TXYAdd" value="${this.data && this.data.touXiYe?this.data.touXiYe:''}" style="width:50px" /> &nbsp;ml/h
              </label>
              <label class="radio-inline">
                <span> 5%碳酸氢钠 </span>
                &nbsp;&nbsp;<input class="allInput" name="TSQNAdd" value="${this.data && this.data.tanSuanQN?this.data.tanSuanQN:''}" style="width:50px" /> &nbsp;ml/h
              </label>
              <label class="radio-inline" style="padding-left: 2px;">
                <span> 10%氯化钾 </span>
                &nbsp;&nbsp;<input class="allInput" name="LHJAdd" value="${this.data && this.data.lvHuaJia?this.data.lvHuaJia:''}" style="width:50px" /> &nbsp;ml/(4L置换液)
              </label>
              <label class="radio-inline">
                <span> 目标超滤量 </span>
                &nbsp;&nbsp;<input class="allInput" name="CLLAdd" value="${this.data && this.data.muBiaoCLL?this.data.muBiaoCLL:''}" style="width:50px" /> &nbsp;ml/h
              </label>
              <label class="radio-inline">
                <span> 预期治疗时间 </span>
                &nbsp;&nbsp;<input class="allInput" name="YQZLSJAdd" value="${this.data && this.data.yuQiZLSJ?this.data.yuQiZLSJ:''}" style="width:50px" /> &nbsp;h
              </label>
              <label class="radio-inline">
                <span> 净超滤目标 </span>
                &nbsp;&nbsp;<input class="allInput" name="JCLMBAdd" value="${this.data && this.data.jingChaoLMBL?this.data.jingChaoLMBL:''}" style="width:50px" /> &nbsp;ml/d
              </label>
              <label class="radio-inline">
                <span> 血浆分离流量 </span>
                &nbsp;&nbsp;<input class="allInput" name="XJFLLLAdd" value="${this.data && this.data.xueJiangFLLL?this.data.xueJiangFLLL:''}" style="width:50px" /> &nbsp;ml/min
              </label>
              <label class="radio-inline">
                <span> 血浆治疗总量 </span>
                &nbsp;&nbsp;<input class="allInput" name="XJZLZLAdd" value="${this.data && this.data.xueJiangZLZL?this.data.xueJiangZLZL:''}" style="width:50px" /> &nbsp;ml
              </label>
            </span>
          </span>
        </div>
      </div>
        <div class="formSort" style="height: 32px;">
          <button class="e_btn backBtn" onclick="saveCFDTC()">保存</button>
        </div>
      </div>
    `
    WRT_e.ui.model({
      id: "addCFYZD",
      title: '新增处方医嘱',
      width: "1000px",
      height:"900px",
      content: html,
      // closeButton:false,
      // closeOnEscape:false,
      iframe: false,
    })
    this.$el.html(html)
    return this;
  }
})
// 相关检验项目
var XGJYXMTb_View = WRT_e.view.extend({
  render: function () {
    let html=`
      <table border="1" align="start" class="show_Table">
        <caption style="text-align: center;"><span style="font-weight: 700; font-size: 20px;">相关检验项目</span></caption>
        <tbody>
          <tr>
            <td>
              <tr align="start">
                <td class="table-nr"> 总胆红素: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('总胆红素')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('总胆红素')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('总胆红素')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
                <td class="table-nr"> 直接胆红素: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('直接胆红素')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('直接胆红素')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('直接胆红素')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
                <td class="table-nr"> 总蛋白: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('总蛋白')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('总蛋白')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('总蛋白')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
                <td class="table-nr"> 白蛋白: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('白蛋白')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('白蛋白')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('白蛋白')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
              </tr>
              <tr align="start" colspan="4">
                <td class="table-nr"> 前白蛋白:
                ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('前白蛋白')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('前白蛋白')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('前白蛋白')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
              </tr>
              <tr align="start">
                <td class="table-nr"> 葡萄糖: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('葡萄糖')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('葡萄糖')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('葡萄糖')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
                <td class="table-nr"> 尿素氮: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('尿素氮')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('尿素氮')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('尿素氮')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
                <td class="table-nr"> 肌酐: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('肌酐')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('肌酐')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('肌酐')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
                <td class="table-nr"> 尿酸: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('尿酸')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('尿酸')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('尿酸')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
              </tr>
              <tr align="start">
                <td class="table-nr"> 甘油三脂: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('甘油三脂')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('甘油三脂')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('甘油三脂')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
                <td class="table-nr"> 丙氨酸氨基转移酶: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('丙氨酸氨基转移酶')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('丙氨酸氨基转移酶')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('丙氨酸氨基转移酶')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
                <td class="table-nr"> 天冬酸氨酸氨基转移酶: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('天冬酸氨酸氨基转移酶')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('天冬酸氨酸氨基转移酶')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('天冬酸氨酸氨基转移酶')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
                <td class="table-nr"> 碱性磷酸酶: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('碱性磷酸酶')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('碱性磷酸酶')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('碱性磷酸酶')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
              </tr>
              <tr align="start">
                <td class="table-nr"> 血清钾: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清钾')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清钾')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清钾')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
                <td class="table-nr"> 血清钠: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清钠')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清钠')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清钠')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
                <td class="table-nr">	血清氯: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清氯')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清氯')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清氯')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
                <td class="table-nr"> 血清钙: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清钙')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清钙')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清钙')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
              </tr>
              <tr align="start" colspan="4">
                <td class="table-nr"> 血清磷: 
                  ${ WRT_config.noxgjcHyjg.length>0?WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清磷')!=-1).length>0?`<span ${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清磷')!=-1)[0].tiShi.trim()!=''?`style='color:red'`:``}>${WRT_config.noxgjcHyjg.filter(e=>e.xiangMuMC.indexOf('血清磷')!=-1)[0].jieGuo}</span>`:'':``}
                </td>
              </tr>
            </td>
          <tr>
        <tbody>
      </table>
    `
    this.$el.html(html)
    return this;
  }
})
