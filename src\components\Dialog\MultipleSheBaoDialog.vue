<template>
  <div>
    <el-dialog
      title="医保限制支付范围提示"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="700px"
      custom-class="shenpi-dialog"
    >
      <el-carousel
        v-if="dialogVisible"
        ref="carousel"
        :autoplay="false"
        height="400px"
        indicator-position="none"
        class="insurance-swipe"
        @change="handleSwiperChange"
      >
        <el-carousel-item v-for="(item, index) in displayList" :key="index">
          <div class="carousel-content">
            <el-row class="content-row">
              <el-col>
                <span>项目名称：</span>
                <span class="project-name">{{ item.sheBaoSP.mingCheng }}</span>
              </el-col>
            </el-row>
            <el-row class="content-row">
              <el-col>医保使用范围限制:</el-col>
            </el-row>
            <el-row class="content-row">
              <el-col>
                <div class="text-content-box limit-box">{{ item.sheBaoSP.limit }}</div>
              </el-col>
            </el-row>
            <el-row class="content-row">
              <el-col>当前诊断：</el-col>
            </el-row>
            <el-row class="content-row">
              <el-col>
                <div class="text-content-box diagnose-box">{{ item.sheBaoSP.diagnose }}</div>
              </el-col>
            </el-row>
            <div>
              <!-- 类型1：符合条件/自费选择 -->
              <el-row v-if="item.sheBaoSP.type === '1'" class="content-row">
                <el-col class="center-content margin-top-16">
                  <div>
                    <el-button
                      :type="item.ziFei === '0' ? 'primary' : ''"
                      @click="handleSubmit('0')"
                    >
                      我知道了，符合条件
                    </el-button>
                    <el-button
                      :type="item.ziFei === '1' ? 'primary' : ''"
                      style="margin-left: 32px"
                      @click="handleSubmit('1')"
                    >
                      不符合条件，自费
                    </el-button>
                  </div>
                </el-col>
                <el-col>
                  <div class="warning-text">
                    务必与医保使用范围相符,如选择自费,请务必做好患者自费知情同意工作
                  </div>
                </el-col>
              </el-row>
              <!-- 类型2：审批记账/自费选择 -->
              <el-row v-if="item.sheBaoSP.type === '2'" class="content-row">
                <el-col class="notice-text">
                  该项目须审批后才能记账。选中后该项目会自动插入审批队列。等待【社保窗口】审批。
                </el-col>
                <el-col class="center-content">
                  <el-radio-group v-model="item.ziFei">
                    <el-radio label="0">审批记账</el-radio>
                    <el-radio label="1">自费</el-radio>
                  </el-radio-group>
                </el-col>
                <el-col>
                  <div class="button-container">
                    <el-button type="primary" @click="handleSubmit">确 定</el-button>
                    <el-button @click="closeDialog">取 消</el-button>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </el-dialog>
  </div>
</template>

<script>
import { debounce } from 'lodash'
export default {
  name: 'MultipleSheBaoDialog',
  props: {
    displayList: {
      type: Array,
      default: () => []
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentIndex: 0
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  },
  watch: {
    dialogVisible(val) {
      this.currentIndex = 0
      if (val) {
        this.setZiFei('0')
      }
    }
  },
  methods: {
    debounce,
    setZiFei(value) {
      for (const item of this.displayList) {
        this.$set(item, 'ziFei', value)
      }
    },
    handleSwiperChange(index) {
      this.currentIndex = index
    },
    handleSubmit(value) {
      if (value) {
        this.$set(this.displayList[this.currentIndex], 'ziFei', value)
      }
      const isLastItem = this.currentIndex === this.displayList.length - 1

      if (isLastItem) {
        this.$emit('confirm', this.displayList)
      } else {
        this.$refs.carousel.setActiveItem(this.currentIndex + 1)
      }
    },
    closeDialog() {
      this.dialogVisible = false
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss">
.shenpi-dialog {
  .el-icon-close.el-icon-close {
    display: none;
  }

  .cus-radio-btn {
    margin-left: 32px;

    &.el-radio-button .el-radio-button__inner {
      border-radius: 4px;
      border-left: 1px solid #dbd8d2;
    }
  }

  .el-radio-button .el-radio-button__inner {
    border-radius: 4px;
  }

  .el-dialog {
    height: 550px;

    &__header {
      border-bottom: 1px solid #dadee6;
      padding: 10px 14px;
      font: var(--font-medium);
      font-size: var(--font-size-medium);
      color: #171c28;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        width: 3px;
        height: 16px;
        background: #356ac5;
        margin-right: 6px;
      }

      .el-dialog__headerbtn {
        font-size: 19px;
        right: 14px;
        top: auto;

        .el-dialog__close {
          color: #8590b3;
        }
      }
    }
  }

  .el-carousel__container {
    height: 350px;
  }

  .carousel-content {
    position: relative;
    margin-right: 8px;
    border: 1px solid #dadee6;
    padding: 10px 10px 0;
    height: 100%;
  }

  .content-row {
    margin-bottom: 5px;
  }

  .project-name {
    text-align: center;
    color: #155bd4;
  }

  .text-content-box {
    border: 1px solid #dadee6;
    overflow-y: auto;
    background: #f2f4fa;
    padding: 5px;

    &.limit-box {
      height: 100px;
    }

    &.diagnose-box {
      height: 80px;
    }
  }

  .center-content {
    display: flex;
    justify-content: center;

    &.margin-top-16 {
      margin-top: 16px;
    }
  }

  .warning-text {
    color: red;
    text-align: center;
    padding-top: 6px;
  }

  .notice-text {
    text-align: center;
    color: #f1924e;
  }

  .button-container {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }

  .error-text {
    color: red;
  }
}
</style>
