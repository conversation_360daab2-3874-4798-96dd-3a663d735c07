<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <div class="header-item-title">手术状态:</div>
        <div class="header-item-radio">
          <el-radio-group v-model="radio1">
            <el-radio label="1" size="large">
              <span class="search-label">全部</span>
            </el-radio>
            <el-radio label="2" size="large">
              <span class="search-label">已审批</span>
            </el-radio>
            <el-radio label="3" size="large">
              <span class="search-label">未审批</span>
            </el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">拟手术日期/开单日期:</div>
        <div class="header-item-date">
          <el-date-picker
            v-model="billingDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            size="default"
          />
        </div>
        <div class="header-item-button"><el-button>查询</el-button></div>
        <div class="header-item-button">
          <el-button style="background-color: #356ac5">导出医务处审批报表</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">手术审批</div>
        <div class="filter-box-right">
          <div class="filter-box-right">
            <el-alert type="primary" show-icon plain size="small">
              当前审批:医务处二次审批;您可以审批的手术级别;您可以审批医生已同意的重大手术;审批自己
            </el-alert>
          </div>
        </div>
      </div>
      <div class="table">
        <el-table max-height="648" stripe border :data="tableData">
          <el-table-column prop="approval" width="60" label="审批"></el-table-column>
          <el-table-column prop="operatingRoom" width="70" label="手术间"></el-table-column>
          <el-table-column prop="taiwanSequence" width="60" label="台序"></el-table-column>
          <el-table-column prop="proposedSurgery" width="200" label="拟施手术"></el-table-column>
          <el-table-column
            prop="proposedSurgeryDate"
            width="100"
            label="拟手术日期"
          ></el-table-column>
          <el-table-column prop="ward" width="60" label="病区"></el-table-column>
          <el-table-column prop="beds" width="60" label="床位"></el-table-column>
          <el-table-column prop="name" width="80" label="姓名"></el-table-column>
          <el-table-column prop="surgicalGrade" width="90" label="手术级别"></el-table-column>
          <el-table-column prop="group" width="60" label="小组"></el-table-column>
          <el-table-column prop="taiwanSequence" width="60" label="台序"></el-table-column>
          <el-table-column prop="chiefSurgeon" width="80" label="主刀医师"></el-table-column>
          <el-table-column prop="specialist" width="60" label="专科"></el-table-column>
          <el-table-column prop="medicalRecordNumber" width="90" label="病历号"></el-table-column>
          <el-table-column prop="billingTime" width="150" label="开单时间"></el-table-column>
          <el-table-column prop="billingDoctor" width="80" label="开单医师"></el-table-column>
          <el-table-column prop="approvalPhysician" width="80" label="审批医师"></el-table-column>
          <el-table-column prop="approvalTime" width="150" label="审批时间"></el-table-column>
          <el-table-column prop="remarks" width="152" label="备注"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      radio1: '1',
      billingDate: '',

      tableData: [
        {
          approval: '1',
          operatingRoom: 'A10间',
          taiwanSequence: '2',
          proposedSurgery: '',
          proposedSurgeryDate: '2024-09-27',
          ward: 'A243',
          beds: '541',
          name: '贸畅通',
          surgicalGrade: '四级手术',
          group: 'A',
          chiefSurgeon: '郑高成',
          specialist: '21',
          medicalRecordNumber: '',
          billingTime: '2024-09-26 08:13:12',
          billingDoctor: '郑高成',
          approvalPhysician: '郑高成',
          approvalTime: '2024-09-26 10:47:30',
          remarks: '双屏，123456789'
        }
      ]
    }
  },
  methods: {
    handleClick(row) {
      console.log(row)
      console.log(this.approvalDate)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
    font-weight: bold;
  }
  .header-item-input {
    margin-right: 8px;
  }
  //.header-item-date{
  //  margin-right: 8px;
  //}
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  //justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
  margin-right: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
.filter-box-right {
  .el-alert {
    padding: 6px 10px;
    font-size: 12px;
    border: 1px solid rgba(21, 91, 212, 0.45);
    background-color: rgba(21, 91, 212, 0.05);
  }
}
</style>
