import { mapState } from 'vuex'

export default {
  data() {
    return {}
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      zhiLiaoZuID: ({ patient }) => patient.zhiLiaoZuID,
      bingQuID: ({ patient }) => patient.bingQuID
    }),
    isIDsExist() {
      return this.zhuanKeID && this.bingQuID && this.zhiLiaoZuID
    }
  },
  watch: {
    zhuanKeID: {
      handler() {
        if (this.isIDsExist) {
          this.fetchData()
        }
      },
      immediate: true
    }
    // zhiLiaoZuID: {
    //   handler(val, oldVal) {
    //     if (this.isIDsExist && val !== oldVal && oldVal) {
    //       this.fetchData()
    //     }
    //   },
    //   immediate: true
    // },
    // bingQuID: {
    //   handler(val, oldVal) {
    //     if (this.isIDsExist && val !== oldVal && oldVal) {
    //       this.fetchData()
    //     }
    //   },
    //   immediate: true
    // }
  },
  methods: {}
}
