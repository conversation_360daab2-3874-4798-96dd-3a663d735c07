import request from '@/utils/request'

// 病人主页初始化
export function mainPageInit() {
  return request({
    url: '/app-emrservice/v1/mainpage/getInitInfo',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}

// 保存个性化和移动建议审批
export function savePersonalization(data) {
  return request({
    url: '/app-emrservice/v1/basicInfo/updateYlEhrGxh',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 获取主页整合提醒信息
export function getAlertMessages(params) {
  return request({
    url: '/app-emrservice/v1/mainpage/getAlertMessages',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取右上角未读消息
export function getUnreadMessage(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getUnReadMessage',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取特殊抗菌药物会诊单、三联抗菌药物会诊单通知
export function getKJYaoWuHZD(params) {
  return request({
    url: '/medicaladvice/v1/consulationsheet/getWeiWanChengKjywHzdCount',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取院感监控预警列表
export function getGanRanJKData(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getGanRanJKData',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取院感提示传染列表
export function getChuanRanList(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/showCrbfh',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取我的病人
export function getMyPatientList(params) {
  return request({
    url: '/app-emrservice/v1/mainpage/getInPatientUnionVoListByMyPatient',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 判断是否我的病人
export function isMyPatient(params) {
  return request({
    url: '/app-emrservice/v1/mrInPatient/isMyPatient',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 加入或移除我的病人 0-remove 1-add
export function joinOrRemoveMyPatient(params) {
  return request({
    url: '/app-emrservice/v1/mrInPatient/addOrRemoveMyPatient',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科id（zhuanKeID）获取治疗组列表
export function getZhiLiaoZuListByZhuanKeID(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getZhiLiaoZuListByZhuanKeID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科id（zhuanKeID）获取关联专科
export function getGuanLianZKListByZhuanKeID(params) {
  return request({
    url: '/sysmanage/v1/department/getGuanLianZKList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据用户id（yongHuID）获取治疗组列表
export function getZhiLiaoZuListByYongHuID() {
  return request({
    url: '/app-emrservice/v1/basicInfo/getZhiLiaoZuListByYongHuID',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科ID（zhuanKeID）、治疗组ID（zhiLiaoZuID）获取病人列表
export function getPatientListByZhiLiaoZu(params) {
  return request({
    url: `/app-emrservice/v1/mainpage/getInPatListByZLZID`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科ID获取当前住院病人所有病区列表
export function getBingqQuListByZhuanKeID(params) {
  return request({
    url: `/patient/v1/inPatientDashboard/getBingQuListByCwZybrZKID`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科ID（zhuanKeID）、病区ID（bingQuID）获取病人列表
export function getPatientListByBingQu(params) {
  return request({
    url: `/app-emrservice/v1/mainpage/getInPatListByWardId`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取公共床位病人列表
export function getPublicBedPatientList(params) {
  return request({
    url: `/app-emrservice/v1/mainpage/getInPatientUnionVoListByGgcw`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取科研组病人列表
export function getResearchPatInfoByDeptId(params) {
  return request({
    url: `/app-emrservice/v1/basicInfo/getResearchPatInfoByDeptId`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据BLID获取医生备注
export function getDoctorRemarkByBLID(params) {
  return request({
    url: `/patient/v1/inPatientDashboard/getYiShiBeiZhu`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 设置备注
export function setDoctorRemark(params) {
  return request({
    url: `/patient/v1/inPatientDashboard/setYiShiBeiZhu`,
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科ID（zhuanKeID）、病区ID（bingQuID）、治疗组ID（zhiLiaoZuID）获取主页看板专科日志数据统计
export function getZhuanKeRZStatistics(params) {
  return request({
    url: `/patient/v1/inPatientDashboard/getInPatientDataStatistics`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科ID（zhuanKeID）、病区ID（bingQuID）、治疗组ID（zhiLiaoZuID）获取临床辅助决策信息提示
export function getCDSSForEHR(params) {
  return request({
    url: `/medicalrecord/v1/nurseRecordCDSS/getCDSSForEHR`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科ID（zhuanKeID）查询今日手术病人列表
export function getShouShuListByZhuanKeID(params) {
  return request({
    url: `/medicaladvice/v1/operationInpatient/get`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科ID（zhuanKeID）查询到期抗菌药品
export function getDaoQiKjypListByZhuanKeID(params) {
  return request({
    url: `/medicaladvice/v1/AdviceInpatient/getDaoQiKjypZkid`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科ID（zhuanKeID）、病区ID（bingQuID）、治疗组ID（zhiLiaoZuID）获取皮试阳性结果
export function getPositiveSkinTestRecords(params) {
  return request({
    url: `/app-emrservice/v1/mainpage/getPositiveSkinTestRecords`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据病历ID列表（bingLiIDList）获取皮试阳性结果
export function getPositiveSkinTestRecordsByBingLiIDList(params) {
  return request({
    url: `/patient/v1/skinTest/getAllPatSkinTestResultByBlidList`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科id（zhuanKeID）获取申请会诊病人人数
export function getApplyConsultationCount(params) {
  return request({
    url: `/medicaladvice/v1/consulation/getDaiHuiZhenConsulationsByZKID`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科id（zhuanKeID）获取ICU转出病人数
export function getIcuTransferCount(params) {
  return request({
    url: `/patient/v1/inPatientDashboard/getInPatientVoListToICU`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科id(zhuanKeID)和用户id(yongHuID)获取超2天未归类病人
export function getNotClassifyCount(params) {
  return request({
    url: `/patient/v1/in-patient/getUndocumentedPatList`,
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
