import request from '@/utils/request'
import { getYongHuID } from '@/utils/auth'

// 本专科危急值查询

// 住院医生站_查询_危急值报表查询
export function criticalValueReportSearch(params) {
  return request({
    url: '/app-emrservice/v1/CriticalValueReport/CriticalValueReportSearch',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_初始化科室危急值自查表
export function criticalValueSelfInspectionReportInit(params) {
  return request({
    url: '/app-emrservice/v1/CriticalValueReport/CriticalValueSelfInspectionReportInit',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_保存_保存一份科室危急值自查表
export function criticalValueSelfInspectionReportSave(params) {
  return request({
    url: '/app-emrservice/v1/CriticalValueReport/CriticalValueSelfInspectionReportSave',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_获取科室危急值自查表列表
export function criticalValueSelfInspectionReportSearch(params) {
  return request({
    url: '/app-emrservice/v1/CriticalValueReport/CriticalValueSelfInspectionReportSearch',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}
// 住院医生站_查询_根据jiLuID获取已保存的自查表
// 必填 jiLuID::Interger
export function getCriticalValueSelfInspectionReportByJiLuID(param) {
  return request({
    url: '/app-emrservice/v1/CriticalValueReport/getCriticalValueSelfInspectionReportByJiLuID',
    method: 'get',
    param: param,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_获取科室危急值自查表列表的科室选择框
export function getZhiChaKeShiKuang() {
  return request({
    url: '/app-emrservice/v1/CriticalValueReport/getZhiChaKeShiKuang',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_危急值报表初始化
export function insertHuiZhenWSHD() {
  return request({
    url: '/app-emrservice/v1/CriticalValueReport/insertHuiZhenWSHD',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}

//专科交接班记录

//住院医生站_查询_获得所有或单个专科交接班记录以及查找记录
export function searchAllInfoOfSR(params) {
  return request({
    url: '/app-emrservice/v1/ChangeShifts/SearchAllInfoOfSR',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}

//住院医生站_新增_交接班新增
export function addList(params) {
  return request({
    url: '/app-emrservice/v1/ChangeShifts/addList',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}
