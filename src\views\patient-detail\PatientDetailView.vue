<template>
  <div class="patient-view">
    <el-card class="patient-head" shadow="never" @click.native="openDetails()">
      <div class="patient-head-row">
        <span class="bing-ren">{{ patientDetail.bingRenXM }}</span>
        <span class="bing-ren">{{ patientDetail.bingRenBH }}</span>
        <span>{{ patientDetail.xingBieMC }}</span>
        <span>{{ patientDetail.nianLing }}</span>
        <span>{{ patientDetail.zhuanKeMC }}</span>
        <span>
          {{ formatChuangWeiHao(patientDetail.bingQuMC, patientDetail.chuangWeiHao) }}
        </span>
        <button class="tag-button" type="text" @click.stop="zhiLiaoZuVisible = true">
          <el-tag effect="dark">
            {{ patientDetail.zhiLiaoZuMC }}
          </el-tag>
        </button>
        <button class="tag-button" type="text" @click.stop="zhuGuanYSVisible = true">
          <el-tag effect="dark">主管医师: {{ patientDetail.zhuGuanYSXM }}</el-tag>
        </button>
        <el-tag effect="dark">{{ patientDetail.linChuangZD }}</el-tag>
        <button class="tag-button" type="text" @click.stop="huLiVisible = true">
          <el-tag effect="dark">{{ patientDetail.huLiJBMC }}</el-tag>
        </button>
        <el-tag effect="dark">{{ patientDetail.bingRenYS }}</el-tag>
        <span style="height: 22px">
          <el-popover popper-class="popper-button" placement="bottom" trigger="click">
            <div>
              <el-button style="color: #000" type="text" @click.stop="bingRenZYVisible = true">
                修改
              </el-button>
            </div>
            <div><el-button style="color: #000" type="text">一键下传</el-button></div>
            <button slot="reference" type="text">
              <el-tag effect="dark">{{ patientDetail.bingRenZT }}</el-tag>
            </button>
          </el-popover>
        </span>
        <el-tag>{{ patientDetail.jieSuanLXMC }}</el-tag>
        <el-tag type="danger">{{ patientDetail.zhuYuanSC }}</el-tag>
        <el-tag type="danger">质控</el-tag>

        <img
          v-if="patientDetail.specialSign && patientDetail.specialSign['交通事故']"
          :src="require(`@/assets/images/patient-images/patient-card/道路交通事故患者.png`)"
          alt="交通事故"
        />
        <!-- 近期手术-->
        <img
          v-if="patientDetail.specialSign && patientDetail.specialSign['近期手术']"
          :src="require(`@/assets/images/patient-images/patient-card/sstzd.png`)"
          alt="近期手术"
        />
        <!-- 临床路径-->
        <img
          v-if="patientDetail.specialSign && patientDetail.specialSign['临床路径']"
          :src="require(`@/assets/images/patient-images/patient-card/lclj.png`)"
          alt="临床路径"
        />
        <!-- 已书写入院记录-->
        <img
          v-if="patientDetail.specialSign && patientDetail.specialSign['已书写入院记录']"
          :src="require(`@/assets/images/patient-images/patient-card/已书写.png`)"
          alt="已书写"
        />
        <!-- DRG: 0绿色，1黄色，2红色，-1非DRG病例不显示-->
        <img
          v-if="patientDetail.specialSign?.DRG && patientDetail.specialSign?.DRG !== '-1'"
          :src="
            require(`@/assets/images/patient-images/patient-card/drg${patientDetail.specialSign?.DRG}.png`)
          "
          alt="DRG"
        />
        <!-- 多重耐药性-->
        <img
          v-if="patientDetail.specialSign && patientDetail.specialSign['多重耐药性']"
          :src="require(`@/assets/images/patient-images/patient-card/mdro.png`)"
          alt="多重耐药性"
        />
        <!-- 日间手术 待确认，没文档-->
        <!-- 营养风险: LOW黄色 HIGH红色-->
        <img
          v-if="isYYFXOrVTE(patientDetail, 'YYSC')"
          :src="
            require(`@/assets/images/patient-images/patient-card/营养风险_${isYYFXOrVTE(
              patientDetail,
              'YYSC'
            )}.png`)
          "
          alt="营养风险"
        />
        <!-- 皮试结果-->
        <img
          v-if="patientDetail.piShiJG && patientDetail.piShiJG.length"
          :src="require(`@/assets/images/patient-images/patient-card/过敏及皮试结果.png`)"
          alt="皮试结果"
        />
        <!-- VTE: LOW黄色 HIGH红色-->
        <img
          v-if="isYYFXOrVTE(patientDetail, 'NEW_VTE')"
          :src="
            require(`@/assets/images/patient-images/patient-card/VTE_${isYYFXOrVTE(
              patientDetail,
              'NEW_VTE'
            )}.png`)
          "
          alt="VTE"
        />
      </div>
      <div class="patient-head-row">
        <template v-for="(item, index) in assessmentRecords">
          <el-tag
            :key="index"
            :type="tagTypeMap[item.riskLevel]"
            @click.stop="openNutritional(item)"
          >
            {{ item.name + (item.name.slice(-2) === '评分' ? '' : '评分') }}:
            {{ item.score || '-' }}分
          </el-tag>
        </template>
        <button
          class="tag-button"
          type="text"
          @click.stop="
            () => {
              liquidFormData = liquidValue
              liquidVisible = true
            }
          "
        >
          <el-tag effect="dark">液体出入量平衡设置值:L-G={{ liquidValue }}ml</el-tag>
        </button>
      </div>
    </el-card>
    <tags-view />
    <el-drawer :visible.sync="drawer" size="62%" direction="rtl" :with-header="false">
      <div class="patient-taskbar-box">
        <div class="patient-taskbar-title">
          <span />
          代办/提醒任务栏
        </div>
        <span>
          <!-- {{alertMessages}} -->
          <table>
            <thead>
              <tr style="text-align: left">
                <th>类型</th>
                <th>内容</th>
                <th>处理状态</th>
              </tr>
            </thead>
            <tbody>
              <tr class="tr-one">
                <td :style="{ backgroundColor: '#F35656' }" style="text-align: center">
                  <el-checkbox disabled />
                </td>
                <td>
                  该患者入院后24小时内未完成vte风险评分中高危且未评相关出血评估，请主管医生及时完成！
                </td>
                <td><el-button type="primary">处理</el-button></td>
              </tr>
              <tr class="tr-two">
                <td style="text-align: center">
                  <el-checkbox disabled />
                </td>
                <td>该患者住院超过30天评估，请主管医生尽快完成！</td>
                <td><el-button type="primary">处理</el-button></td>
              </tr>
              <tr class="tr-one">
                <td style="text-align: center">
                  <el-checkbox disabled />
                </td>
                <td>患者NRS-2002评分结果3分，存在营养风险，请医生进行GLIM量表评估。</td>
                <td>
                  <el-button type="primary">GLIM评估</el-button>
                  <el-button type="primary">自行处理</el-button>
                </td>
              </tr>
              <tr class="tr-two">
                <td style="text-align: center">
                  <el-checkbox disabled />
                </td>
                <td>如为院内VTE，请及时上报不良事件，奖励20元/例。</td>
                <td>
                  <el-button type="primary">是</el-button>
                  <el-button type="primary">否</el-button>
                  <el-button type="primary">稍后确认</el-button>
                </td>
              </tr>
              <tr class="tr-one">
                <td style="text-align: center">
                  <el-tag>CDSS</el-tag>
                </td>
                <td colspan="2">
                  07-08 13:00
                  患者使用去甲肾上腺素/阿拉明/多巴胺等药物，但血压已经恢复正常，大于120mmHg,请注意用药剂量，必要时减量或停用！
                </td>
              </tr>
              <tr class="tr-two">
                <td style="text-align: center">
                  <el-tag>质控</el-tag>
                </td>
                <td colspan="2">
                  大病例上级医院签名需在入院后48小时内完成，需在2024-06-25 21:24:35完成。
                </td>
              </tr>
            </tbody>
          </table>
        </span>
      </div>
      {{ zhiLiaoZuList }}
      <button type="text" class="open-drawer-button" style="left: -30px" @click="drawer = false">
        <el-badge :value="6" class="item" />
        <i class="el-icon-news"></i>
      </button>
    </el-drawer>

    <zhi-liao-zu-drawer
      :visible.sync="zhiLiaoZuVisible"
      :zhu-yuan-i-d="patientDetail.zhuYuanID"
      :zhi-liao-zu-i-d="patientDetail.zhiLiaoZuID"
      @upload-success="$store.dispatch('patient/getPatientInit', bingLiID)"
    />
    <hu-li-drawer
      :visible.sync="huLiVisible"
      :zhu-yuan-i-d="patientDetail.zhuYuanID"
      :hu-li-j-b-m-c="patientDetail.huLiJBMC"
      @upload-success="$store.dispatch('patient/getPatientInit', bingLiID)"
    />
    <zhu-guan-y-s-drawer
      :visible.sync="zhuGuanYSVisible"
      :zhu-yuan-i-d="patientDetail.zhuYuanID"
      :zhuan-ke-i-d="patientDetail.zhuanKeID"
      :zhu-guan-y-s-i-d="patientDetail.zhuGuanYSID"
      @upload-success="$store.dispatch('patient/getPatientInit', bingLiID)"
    />
    <bing-ren-z-y-drawer :visible.sync="bingRenZYVisible" />

    <el-dialog :visible.sync="liquidVisible" width="300px">
      <span slot="title">
        <span style="font-size: 16px">
          <i class="el-icon-menu"></i>
          修改液体出入量平衡
        </span>
      </span>
      <span class="liquid-input">
        目标值:
        <el-input v-model="liquidFormData"></el-input>
      </span>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="setFluidBalance">保 存</el-button>
        <el-button @click="liquidVisible = false">取 消</el-button>
      </span>
    </el-dialog>

    <button
      v-if="!drawer"
      type="text"
      class="open-drawer-button"
      style="right: 1px"
      @click="drawer = true"
    >
      <el-badge :value="6" class="item" />
      <i class="el-icon-news"></i>
    </button>
  </div>
</template>

<script>
import {
  getAssessmentRecords,
  getFluidBalanceInfoByCalculate,
  setFluidBalance
} from '@/api/patient-info'
import { mapState } from 'vuex'
import { EventBus } from '@/utils/event-bus'
import ZhiLiaoZuDrawer from '@/views/patient-detail/components/ZhiLiaoZuDrawer.vue'
import HuLiDrawer from '@/views/patient-detail/components/HuLiDrawer.vue'
import ZhuGuanYSDrawer from '@/views/patient-detail/components/ZhuGuanYSDrawer.vue'
import BingRenZYDrawer from '@/views/patient-detail/components/BingRenZYDrawer.vue'
import TagsView from './components/TagsView'

export default {
  name: 'PatientDetailView',
  components: {
    ZhiLiaoZuDrawer,
    HuLiDrawer,
    ZhuGuanYSDrawer,
    BingRenZYDrawer,
    TagsView
  },
  data() {
    return {
      // patientDetail: {},
      assessmentRecords: [],
      alertMessages: {},

      zhiLiaoZuVisible: false,
      huLiVisible: false,
      zhuGuanYSVisible: false,
      bingRenZYVisible: false,

      liquidVisible: false,
      liquidValue: 0,
      liquidFormData: 0,

      tagTypeMap: {
        LOW: 'success',
        MIDDLE: 'warning',
        HIGH: 'danger',
        EXTREMELY_HIGH: 'danger',
        info: 'info'
      },

      drawer: false,
      isLoadingData: false // 防止重复加载数据
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    // 从动态模块获取数据
    patientDetail() {
      return this.$store.state[`patientDetail/${this.bingLiID}`]?.patientInit || {}
    },
    ...mapState({
      zhiLiaoZuList: ({ patient }) => patient.zhiLiaoZuList
    })
  },
  async created() {
    // 首次创建时加载数据
    await this.loadPatientData()
    // 加载其他数据
    this.getAssessmentRecords()
    this.getFluidBalanceInfo()
  },
  // 组件激活时检查数据
  activated() {
    // 如果数据为空，重新加载
    if (!this.patientDetail.bingRenBH && !this.isLoadingData) {
      this.loadPatientData()
    }
  },
  // 组件销毁时清理
  beforeDestroy() {
    // 重置加载状态
    this.isLoadingData = false
  },
  methods: {
    openDetails() {
      EventBus.$emit('sidebarClick', {
        name: 'PatientBasicInfo',
        component: () => import('./PatientBasicInfo'),
        meta: {
          title: '病人详情'
        }
      })
    },
    openNutritional() {
      EventBus.$emit('sidebarClick', {
        name: 'NutritionalRiskScreening',
        component: () =>
          import('../patient-inside/nutritional-screening/NutritionalRiskScreening.vue'),
        meta: {
          title: '营养筛查'
        }
      })
    },
    // 注册动态模块
    registerModule() {
      const moduleName = `patientDetail/${this.bingLiID}`
      // 检查模块是否已注册
      if (!this.$store.hasModule(moduleName)) {
        // 动态注册模块
        this.$store.registerModule(moduleName, {
          namespaced: true,
          state: () => ({
            patientInit: {}
          }),
          mutations: {
            SET_PATIENT_INIT(state, data) {
              state.patientInit = data
            }
          }
        })
      }
    },
    // 加载病人数据
    async loadPatientData() {
      // 防止重复加载
      if (this.isLoadingData) {
        return
      }
      this.isLoadingData = true

      try {
        const moduleName = `patientDetail/${this.bingLiID}`

        // 确保模块已注册
        if (!this.$store.hasModule(moduleName)) {
          console.error('模块未注册:', moduleName)
          return
        }

        // 检查是否需要加载数据
        const moduleState = this.$store.state[moduleName]
        if (moduleState.isLoading) {
          console.log('数据正在加载中，等待完成')
          return
        }

        if (moduleState.isLoaded && moduleState.patientInit.bingRenBH) {
          console.log('数据已加载，无需重新请求')
          return
        }

        console.log('开始加载病人数据')
        const result = await this.$store.dispatch(`${moduleName}/getPatientInit`, this.bingLiID)
        console.log('加载病人数据结果:', result)

        // 检查加载结果
        if (result && result.hasError !== 0) {
          console.error('加载病人数据失败:', result.errorMsg || '未知错误')
          this.$message.error('加载病人数据失败: ' + (result.errorMsg || '未知错误'))
        }
      } catch (error) {
        console.error('loadPatientData方法执行出错:', error)
        this.$message.error('加载病人数据时发生错误')
      } finally {
        this.isLoadingData = false
      }
    },
    async getAssessmentRecords() {
      getAssessmentRecords(this.bingLiID).then((res) => {
        if (res.hasError === 0) {
          this.assessmentRecords = res.data
        }
      })
    },
    async getFluidBalanceInfo() {
      // 根据BLID获取病人当前8小时内出入量平衡信息
      getFluidBalanceInfoByCalculate(this.bingLiID).then((res) => {
        if (res.hasError === 0) {
          this.liquidValue = res.data.fluidBalanceSet
        }
      })
    },
    setFluidBalance() {
      setFluidBalance({
        bingLiID: this.bingLiID,
        fluidBalanceSetValue: this.liquidFormData
      }).then((res) => {
        if (res.hasError === 0) {
          this.liquidVisible = false
          this.liquidValue = this.liquidFormData
          this.$message({
            message: '保存成功',
            type: 'success',
            duration: 700
          })
        }
      })
    },
    formatChuangWeiHao(bingQuMC, chuangWeiHao) {
      return bingQuMC && chuangWeiHao ? `${bingQuMC.replace(/病区$/, '')}-${chuangWeiHao}` : '空'
    },
    isYYFXOrVTE(item, type) {
      if (!item || !item.assessmentRecords) {
        return false
      }
      // 判断是否为营养风险或VTE 找不到返回false
      const obj = item.assessmentRecords.find((i) => i.type === type)
      return obj?.riskLevel ?? false
    }
  }
}
</script>

<style scoped lang="scss">
.patient-view {
  width: 100%;
  .patient-head {
    font-size: 15px;
    border-top: none;
    border-left: none;
    .patient-head-row {
      display: flex;
      align-items: center; /* 将子元素垂直居中 */
      min-height: 30px;
      .tag-button {
        height: 22px;
      }
    }
    .bing-ren {
      font-weight: 600;
    }
    .patient-head-row > span,
    .patient-head-row > button {
      margin-right: 7px;
    }
    img {
      margin-right: 4px;
    }
    .el-tag--dark {
      max-width: 250px;
      height: 22px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .el-tag {
      border-radius: 2px;
    }
  }
  .patient-taskbar-box {
    position: absolute;
    height: 100%;
    width: 100%;
    z-index: 2;
    background-color: #eff3fb;
    padding: 10px;
    .patient-taskbar-title {
      font-weight: 600;
      span {
        border-left: 3px solid #356ac5;
        padding-left: 5px;
      }
    }
    table {
      margin-top: 10px;
      width: 100%;
    }
    th,
    td {
      border: 1px solid #ddd;
      border-collapse: collapse; /* 移除表格内边框间的间隙 */
      height: 35px;
      padding: 7px;
    }
    .tr-one {
      background-color: #f6f6f6;
    }
  }
  .open-drawer-button {
    position: absolute;
    z-index: 1;
    width: 30px;
    height: 60px;
    background-color: #eff3fb;
    top: 55%;
    text-align: left;
    border-radius: 30px 0 0 30px;
    box-shadow: 0px 0px 5px 5px rgba(0, 0, 0, 0.2);
    .el-icon-news {
      line-height: 60px;
      font-size: 28px;
      color: #356ac5;
    }
    .item {
      position: absolute;
      top: 17px;
      left: 10px;
    }
  }
  .liquid-input {
    display: flex;
    align-items: center;
    padding-left: 30px;
    .el-input {
      width: 65%;
    }
  }

  :deep(.el-drawer) {
    overflow: visible;
  }

  :deep(.el-dialog__footer) {
    border-top: none;
  }
}
</style>

<style lang="scss">
.popper-button {
  min-width: 100px;
}
</style>
