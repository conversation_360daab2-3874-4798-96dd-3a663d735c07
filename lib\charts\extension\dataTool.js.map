{"version": 3, "file": "dataTool.js", "sources": ["../../node_modules/zrender/lib/core/util.js", "../../extension/dataTool/gexf.js", "../../extension/dataTool/prepareBoxplotData.js", "../../extension/dataTool/index.js"], "sourcesContent": ["var BUILTIN_OBJECT = {\n    '[object Function]': true,\n    '[object RegExp]': true,\n    '[object Date]': true,\n    '[object Error]': true,\n    '[object CanvasGradient]': true,\n    '[object CanvasPattern]': true,\n    '[object Image]': true,\n    '[object Canvas]': true\n};\nvar TYPED_ARRAY = {\n    '[object Int8Array]': true,\n    '[object Uint8Array]': true,\n    '[object Uint8ClampedArray]': true,\n    '[object Int16Array]': true,\n    '[object Uint16Array]': true,\n    '[object Int32Array]': true,\n    '[object Uint32Array]': true,\n    '[object Float32Array]': true,\n    '[object Float64Array]': true\n};\nvar objToString = Object.prototype.toString;\nvar arrayProto = Array.prototype;\nvar nativeForEach = arrayProto.forEach;\nvar nativeFilter = arrayProto.filter;\nvar nativeSlice = arrayProto.slice;\nvar nativeMap = arrayProto.map;\nvar ctorFunction = function () { }.constructor;\nvar protoFunction = ctorFunction ? ctorFunction.prototype : null;\nvar protoKey = '__proto__';\nvar methods = {};\nexport function $override(name, fn) {\n    methods[name] = fn;\n}\nvar idStart = 0x0907;\nexport function guid() {\n    return idStart++;\n}\nexport function logError() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    if (typeof console !== 'undefined') {\n        console.error.apply(console, args);\n    }\n}\nexport function clone(source) {\n    if (source == null || typeof source !== 'object') {\n        return source;\n    }\n    var result = source;\n    var typeStr = objToString.call(source);\n    if (typeStr === '[object Array]') {\n        if (!isPrimitive(source)) {\n            result = [];\n            for (var i = 0, len = source.length; i < len; i++) {\n                result[i] = clone(source[i]);\n            }\n        }\n    }\n    else if (TYPED_ARRAY[typeStr]) {\n        if (!isPrimitive(source)) {\n            var Ctor = source.constructor;\n            if (Ctor.from) {\n                result = Ctor.from(source);\n            }\n            else {\n                result = new Ctor(source.length);\n                for (var i = 0, len = source.length; i < len; i++) {\n                    result[i] = clone(source[i]);\n                }\n            }\n        }\n    }\n    else if (!BUILTIN_OBJECT[typeStr] && !isPrimitive(source) && !isDom(source)) {\n        result = {};\n        for (var key in source) {\n            if (source.hasOwnProperty(key) && key !== protoKey) {\n                result[key] = clone(source[key]);\n            }\n        }\n    }\n    return result;\n}\nexport function merge(target, source, overwrite) {\n    if (!isObject(source) || !isObject(target)) {\n        return overwrite ? clone(source) : target;\n    }\n    for (var key in source) {\n        if (source.hasOwnProperty(key) && key !== protoKey) {\n            var targetProp = target[key];\n            var sourceProp = source[key];\n            if (isObject(sourceProp)\n                && isObject(targetProp)\n                && !isArray(sourceProp)\n                && !isArray(targetProp)\n                && !isDom(sourceProp)\n                && !isDom(targetProp)\n                && !isBuiltInObject(sourceProp)\n                && !isBuiltInObject(targetProp)\n                && !isPrimitive(sourceProp)\n                && !isPrimitive(targetProp)) {\n                merge(targetProp, sourceProp, overwrite);\n            }\n            else if (overwrite || !(key in target)) {\n                target[key] = clone(source[key]);\n            }\n        }\n    }\n    return target;\n}\nexport function mergeAll(targetAndSources, overwrite) {\n    var result = targetAndSources[0];\n    for (var i = 1, len = targetAndSources.length; i < len; i++) {\n        result = merge(result, targetAndSources[i], overwrite);\n    }\n    return result;\n}\nexport function extend(target, source) {\n    if (Object.assign) {\n        Object.assign(target, source);\n    }\n    else {\n        for (var key in source) {\n            if (source.hasOwnProperty(key) && key !== protoKey) {\n                target[key] = source[key];\n            }\n        }\n    }\n    return target;\n}\nexport function defaults(target, source, overlay) {\n    var keysArr = keys(source);\n    for (var i = 0; i < keysArr.length; i++) {\n        var key = keysArr[i];\n        if ((overlay ? source[key] != null : target[key] == null)) {\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nexport var createCanvas = function () {\n    return methods.createCanvas();\n};\nmethods.createCanvas = function () {\n    return document.createElement('canvas');\n};\nexport function indexOf(array, value) {\n    if (array) {\n        if (array.indexOf) {\n            return array.indexOf(value);\n        }\n        for (var i = 0, len = array.length; i < len; i++) {\n            if (array[i] === value) {\n                return i;\n            }\n        }\n    }\n    return -1;\n}\nexport function inherits(clazz, baseClazz) {\n    var clazzPrototype = clazz.prototype;\n    function F() { }\n    F.prototype = baseClazz.prototype;\n    clazz.prototype = new F();\n    for (var prop in clazzPrototype) {\n        if (clazzPrototype.hasOwnProperty(prop)) {\n            clazz.prototype[prop] = clazzPrototype[prop];\n        }\n    }\n    clazz.prototype.constructor = clazz;\n    clazz.superClass = baseClazz;\n}\nexport function mixin(target, source, override) {\n    target = 'prototype' in target ? target.prototype : target;\n    source = 'prototype' in source ? source.prototype : source;\n    if (Object.getOwnPropertyNames) {\n        var keyList = Object.getOwnPropertyNames(source);\n        for (var i = 0; i < keyList.length; i++) {\n            var key = keyList[i];\n            if (key !== 'constructor') {\n                if ((override ? source[key] != null : target[key] == null)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n    }\n    else {\n        defaults(target, source, override);\n    }\n}\nexport function isArrayLike(data) {\n    if (!data) {\n        return false;\n    }\n    if (typeof data === 'string') {\n        return false;\n    }\n    return typeof data.length === 'number';\n}\nexport function each(arr, cb, context) {\n    if (!(arr && cb)) {\n        return;\n    }\n    if (arr.forEach && arr.forEach === nativeForEach) {\n        arr.forEach(cb, context);\n    }\n    else if (arr.length === +arr.length) {\n        for (var i = 0, len = arr.length; i < len; i++) {\n            cb.call(context, arr[i], i, arr);\n        }\n    }\n    else {\n        for (var key in arr) {\n            if (arr.hasOwnProperty(key)) {\n                cb.call(context, arr[key], key, arr);\n            }\n        }\n    }\n}\nexport function map(arr, cb, context) {\n    if (!arr) {\n        return [];\n    }\n    if (!cb) {\n        return slice(arr);\n    }\n    if (arr.map && arr.map === nativeMap) {\n        return arr.map(cb, context);\n    }\n    else {\n        var result = [];\n        for (var i = 0, len = arr.length; i < len; i++) {\n            result.push(cb.call(context, arr[i], i, arr));\n        }\n        return result;\n    }\n}\nexport function reduce(arr, cb, memo, context) {\n    if (!(arr && cb)) {\n        return;\n    }\n    for (var i = 0, len = arr.length; i < len; i++) {\n        memo = cb.call(context, memo, arr[i], i, arr);\n    }\n    return memo;\n}\nexport function filter(arr, cb, context) {\n    if (!arr) {\n        return [];\n    }\n    if (!cb) {\n        return slice(arr);\n    }\n    if (arr.filter && arr.filter === nativeFilter) {\n        return arr.filter(cb, context);\n    }\n    else {\n        var result = [];\n        for (var i = 0, len = arr.length; i < len; i++) {\n            if (cb.call(context, arr[i], i, arr)) {\n                result.push(arr[i]);\n            }\n        }\n        return result;\n    }\n}\nexport function find(arr, cb, context) {\n    if (!(arr && cb)) {\n        return;\n    }\n    for (var i = 0, len = arr.length; i < len; i++) {\n        if (cb.call(context, arr[i], i, arr)) {\n            return arr[i];\n        }\n    }\n}\nexport function keys(obj) {\n    if (!obj) {\n        return [];\n    }\n    if (Object.keys) {\n        return Object.keys(obj);\n    }\n    var keyList = [];\n    for (var key in obj) {\n        if (obj.hasOwnProperty(key)) {\n            keyList.push(key);\n        }\n    }\n    return keyList;\n}\nfunction bindPolyfill(func, context) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        args[_i - 2] = arguments[_i];\n    }\n    return function () {\n        return func.apply(context, args.concat(nativeSlice.call(arguments)));\n    };\n}\nexport var bind = (protoFunction && isFunction(protoFunction.bind))\n    ? protoFunction.call.bind(protoFunction.bind)\n    : bindPolyfill;\nfunction curry(func) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    return function () {\n        return func.apply(this, args.concat(nativeSlice.call(arguments)));\n    };\n}\nexport { curry };\nexport function isArray(value) {\n    if (Array.isArray) {\n        return Array.isArray(value);\n    }\n    return objToString.call(value) === '[object Array]';\n}\nexport function isFunction(value) {\n    return typeof value === 'function';\n}\nexport function isString(value) {\n    return typeof value === 'string';\n}\nexport function isStringSafe(value) {\n    return objToString.call(value) === '[object String]';\n}\nexport function isNumber(value) {\n    return typeof value === 'number';\n}\nexport function isObject(value) {\n    var type = typeof value;\n    return type === 'function' || (!!value && type === 'object');\n}\nexport function isBuiltInObject(value) {\n    return !!BUILTIN_OBJECT[objToString.call(value)];\n}\nexport function isTypedArray(value) {\n    return !!TYPED_ARRAY[objToString.call(value)];\n}\nexport function isDom(value) {\n    return typeof value === 'object'\n        && typeof value.nodeType === 'number'\n        && typeof value.ownerDocument === 'object';\n}\nexport function isGradientObject(value) {\n    return value.colorStops != null;\n}\nexport function isImagePatternObject(value) {\n    return value.image != null;\n}\nexport function isRegExp(value) {\n    return objToString.call(value) === '[object RegExp]';\n}\nexport function eqNaN(value) {\n    return value !== value;\n}\nexport function retrieve() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    for (var i = 0, len = args.length; i < len; i++) {\n        if (args[i] != null) {\n            return args[i];\n        }\n    }\n}\nexport function retrieve2(value0, value1) {\n    return value0 != null\n        ? value0\n        : value1;\n}\nexport function retrieve3(value0, value1, value2) {\n    return value0 != null\n        ? value0\n        : value1 != null\n            ? value1\n            : value2;\n}\nexport function slice(arr) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    return nativeSlice.apply(arr, args);\n}\nexport function normalizeCssArray(val) {\n    if (typeof (val) === 'number') {\n        return [val, val, val, val];\n    }\n    var len = val.length;\n    if (len === 2) {\n        return [val[0], val[1], val[0], val[1]];\n    }\n    else if (len === 3) {\n        return [val[0], val[1], val[2], val[1]];\n    }\n    return val;\n}\nexport function assert(condition, message) {\n    if (!condition) {\n        throw new Error(message);\n    }\n}\nexport function trim(str) {\n    if (str == null) {\n        return null;\n    }\n    else if (typeof str.trim === 'function') {\n        return str.trim();\n    }\n    else {\n        return str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n    }\n}\nvar primitiveKey = '__ec_primitive__';\nexport function setAsPrimitive(obj) {\n    obj[primitiveKey] = true;\n}\nexport function isPrimitive(obj) {\n    return obj[primitiveKey];\n}\nvar HashMap = (function () {\n    function HashMap(obj) {\n        this.data = {};\n        var isArr = isArray(obj);\n        this.data = {};\n        var thisMap = this;\n        (obj instanceof HashMap)\n            ? obj.each(visit)\n            : (obj && each(obj, visit));\n        function visit(value, key) {\n            isArr ? thisMap.set(value, key) : thisMap.set(key, value);\n        }\n    }\n    HashMap.prototype.get = function (key) {\n        return this.data.hasOwnProperty(key) ? this.data[key] : null;\n    };\n    HashMap.prototype.set = function (key, value) {\n        return (this.data[key] = value);\n    };\n    HashMap.prototype.each = function (cb, context) {\n        for (var key in this.data) {\n            if (this.data.hasOwnProperty(key)) {\n                cb.call(context, this.data[key], key);\n            }\n        }\n    };\n    HashMap.prototype.keys = function () {\n        return keys(this.data);\n    };\n    HashMap.prototype.removeKey = function (key) {\n        delete this.data[key];\n    };\n    return HashMap;\n}());\nexport { HashMap };\nexport function createHashMap(obj) {\n    return new HashMap(obj);\n}\nexport function concatArray(a, b) {\n    var newArray = new a.constructor(a.length + b.length);\n    for (var i = 0; i < a.length; i++) {\n        newArray[i] = a[i];\n    }\n    var offset = a.length;\n    for (var i = 0; i < b.length; i++) {\n        newArray[i + offset] = b[i];\n    }\n    return newArray;\n}\nexport function createObject(proto, properties) {\n    var obj;\n    if (Object.create) {\n        obj = Object.create(proto);\n    }\n    else {\n        var StyleCtor = function () { };\n        StyleCtor.prototype = proto;\n        obj = new StyleCtor();\n    }\n    if (properties) {\n        extend(obj, properties);\n    }\n    return obj;\n}\nexport function hasOwn(own, prop) {\n    return own.hasOwnProperty(prop);\n}\nexport function noop() { }\n", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// @ts-nocheck\n\n/**\n * This is a parse of GEXF.\n *\n * The spec of GEXF:\n * https://gephi.org/gexf/1.2draft/gexf-12draft-primer.pdf\n */\nimport * as zrUtil from 'zrender/lib/core/util';\nexport function parse(xml) {\n  var doc;\n\n  if (typeof xml === 'string') {\n    var parser = new DOMParser();\n    doc = parser.parseFromString(xml, 'text/xml');\n  } else {\n    doc = xml;\n  }\n\n  if (!doc || doc.getElementsByTagName('parsererror').length) {\n    return null;\n  }\n\n  var gexfRoot = getChildByTagName(doc, 'gexf');\n\n  if (!gexfRoot) {\n    return null;\n  }\n\n  var graphRoot = getChildByTagName(gexfRoot, 'graph');\n  var attributes = parseAttributes(getChildByTagName(graphRoot, 'attributes'));\n  var attributesMap = {};\n\n  for (var i = 0; i < attributes.length; i++) {\n    attributesMap[attributes[i].id] = attributes[i];\n  }\n\n  return {\n    nodes: parseNodes(getChildByTagName(graphRoot, 'nodes'), attributesMap),\n    links: parseEdges(getChildByTagName(graphRoot, 'edges'))\n  };\n}\n\nfunction parseAttributes(parent) {\n  return parent ? zrUtil.map(getChildrenByTagName(parent, 'attribute'), function (attribDom) {\n    return {\n      id: getAttr(attribDom, 'id'),\n      title: getAttr(attribDom, 'title'),\n      type: getAttr(attribDom, 'type')\n    };\n  }) : [];\n}\n\nfunction parseNodes(parent, attributesMap) {\n  return parent ? zrUtil.map(getChildrenByTagName(parent, 'node'), function (nodeDom) {\n    var id = getAttr(nodeDom, 'id');\n    var label = getAttr(nodeDom, 'label');\n    var node = {\n      id: id,\n      name: label,\n      itemStyle: {\n        normal: {}\n      }\n    };\n    var vizSizeDom = getChildByTagName(nodeDom, 'viz:size');\n    var vizPosDom = getChildByTagName(nodeDom, 'viz:position');\n    var vizColorDom = getChildByTagName(nodeDom, 'viz:color'); // let vizShapeDom = getChildByTagName(nodeDom, 'viz:shape');\n\n    var attvaluesDom = getChildByTagName(nodeDom, 'attvalues');\n\n    if (vizSizeDom) {\n      node.symbolSize = parseFloat(getAttr(vizSizeDom, 'value'));\n    }\n\n    if (vizPosDom) {\n      node.x = parseFloat(getAttr(vizPosDom, 'x'));\n      node.y = parseFloat(getAttr(vizPosDom, 'y')); // z\n    }\n\n    if (vizColorDom) {\n      node.itemStyle.normal.color = 'rgb(' + [getAttr(vizColorDom, 'r') | 0, getAttr(vizColorDom, 'g') | 0, getAttr(vizColorDom, 'b') | 0].join(',') + ')';\n    } // if (vizShapeDom) {\n    // node.shape = getAttr(vizShapeDom, 'shape');\n    // }\n\n\n    if (attvaluesDom) {\n      var attvalueDomList = getChildrenByTagName(attvaluesDom, 'attvalue');\n      node.attributes = {};\n\n      for (var j = 0; j < attvalueDomList.length; j++) {\n        var attvalueDom = attvalueDomList[j];\n        var attId = getAttr(attvalueDom, 'for');\n        var attValue = getAttr(attvalueDom, 'value');\n        var attribute = attributesMap[attId];\n\n        if (attribute) {\n          switch (attribute.type) {\n            case 'integer':\n            case 'long':\n              attValue = parseInt(attValue, 10);\n              break;\n\n            case 'float':\n            case 'double':\n              attValue = parseFloat(attValue);\n              break;\n\n            case 'boolean':\n              attValue = attValue.toLowerCase() === 'true';\n              break;\n\n            default:\n          }\n\n          node.attributes[attId] = attValue;\n        }\n      }\n    }\n\n    return node;\n  }) : [];\n}\n\nfunction parseEdges(parent) {\n  return parent ? zrUtil.map(getChildrenByTagName(parent, 'edge'), function (edgeDom) {\n    var id = getAttr(edgeDom, 'id');\n    var label = getAttr(edgeDom, 'label');\n    var sourceId = getAttr(edgeDom, 'source');\n    var targetId = getAttr(edgeDom, 'target');\n    var edge = {\n      id: id,\n      name: label,\n      source: sourceId,\n      target: targetId,\n      lineStyle: {\n        normal: {}\n      }\n    };\n    var lineStyle = edge.lineStyle.normal;\n    var vizThicknessDom = getChildByTagName(edgeDom, 'viz:thickness');\n    var vizColorDom = getChildByTagName(edgeDom, 'viz:color'); // let vizShapeDom = getChildByTagName(edgeDom, 'viz:shape');\n\n    if (vizThicknessDom) {\n      lineStyle.width = parseFloat(vizThicknessDom.getAttribute('value'));\n    }\n\n    if (vizColorDom) {\n      lineStyle.color = 'rgb(' + [getAttr(vizColorDom, 'r') | 0, getAttr(vizColorDom, 'g') | 0, getAttr(vizColorDom, 'b') | 0].join(',') + ')';\n    } // if (vizShapeDom) {\n    //     edge.shape = vizShapeDom.getAttribute('shape');\n    // }\n\n\n    return edge;\n  }) : [];\n}\n\nfunction getAttr(el, attrName) {\n  return el.getAttribute(attrName);\n}\n\nfunction getChildByTagName(parent, tagName) {\n  var node = parent.firstChild;\n\n  while (node) {\n    if (node.nodeType !== 1 || node.nodeName.toLowerCase() !== tagName.toLowerCase()) {\n      node = node.nextSibling;\n    } else {\n      return node;\n    }\n  }\n\n  return null;\n}\n\nfunction getChildrenByTagName(parent, tagName) {\n  var node = parent.firstChild;\n  var children = [];\n\n  while (node) {\n    if (node.nodeName.toLowerCase() === tagName.toLowerCase()) {\n      children.push(node);\n    }\n\n    node = node.nextSibling;\n  }\n\n  return children;\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nfunction asc(arr) {\n  arr.sort(function (a, b) {\n    return a - b;\n  });\n  return arr;\n}\n\nfunction quantile(ascArr, p) {\n  var H = (ascArr.length - 1) * p + 1;\n  var h = Math.floor(H);\n  var v = +ascArr[h - 1];\n  var e = H - h;\n  return e ? v + e * (ascArr[h] - v) : v;\n}\n/**\n * See:\n *  <https://en.wikipedia.org/wiki/Box_plot#cite_note-frigge_hoaglin_iglewicz-2>\n *  <http://stat.ethz.ch/R-manual/R-devel/library/grDevices/html/boxplot.stats.html>\n *\n * Helper method for preparing data.\n *\n * @param {Array.<number>} rawData like\n *        [\n *            [12,232,443], (raw data set for the first box)\n *            [3843,5545,1232], (raw data set for the second box)\n *            ...\n *        ]\n * @param {Object} [opt]\n *\n * @param {(number|string)} [opt.boundIQR=1.5] Data less than min bound is outlier.\n *      default 1.5, means Q1 - 1.5 * (Q3 - Q1).\n *      If 'none'/0 passed, min bound will not be used.\n * @param {(number|string)} [opt.layout='horizontal']\n *      Box plot layout, can be 'horizontal' or 'vertical'\n * @return {Object} {\n *      boxData: Array.<Array.<number>>\n *      outliers: Array.<Array.<number>>\n *      axisData: Array.<string>\n * }\n */\n\n\nexport default function (rawData, opt) {\n  opt = opt || {};\n  var boxData = [];\n  var outliers = [];\n  var axisData = [];\n  var boundIQR = opt.boundIQR;\n  var useExtreme = boundIQR === 'none' || boundIQR === 0;\n\n  for (var i = 0; i < rawData.length; i++) {\n    axisData.push(i + '');\n    var ascList = asc(rawData[i].slice());\n    var Q1 = quantile(ascList, 0.25);\n    var Q2 = quantile(ascList, 0.5);\n    var Q3 = quantile(ascList, 0.75);\n    var min = ascList[0];\n    var max = ascList[ascList.length - 1];\n    var bound = (boundIQR == null ? 1.5 : boundIQR) * (Q3 - Q1);\n    var low = useExtreme ? min : Math.max(min, Q1 - bound);\n    var high = useExtreme ? max : Math.min(max, Q3 + bound);\n    boxData.push([low, Q1, Q2, Q3, high]);\n\n    for (var j = 0; j < ascList.length; j++) {\n      var dataItem = ascList[j];\n\n      if (dataItem < low || dataItem > high) {\n        var outlier = [i, dataItem];\n        opt.layout === 'vertical' && outlier.reverse();\n        outliers.push(outlier);\n      }\n    }\n  }\n\n  return {\n    boxData: boxData,\n    outliers: outliers,\n    axisData: axisData\n  };\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// @ts-nocheck\nimport * as echarts from 'echarts';\nimport * as gexf from './gexf';\nimport prepareBoxplotData from './prepareBoxplotData'; // import { boxplotTransform } from './boxplotTransform';\n\nexport var version = '1.0.0';\nexport { gexf };\nexport { prepareBoxplotData }; // export {boxplotTransform};\n// For backward compatibility, where the namespace `dataTool` will\n// be mounted on `echarts` is the extension `dataTool` is imported.\n// But the old version of echarts do not have `dataTool` namespace,\n// so check it before mounting.\n\nif (echarts.dataTool) {\n  echarts.dataTool.version = version;\n  echarts.dataTool.gexf = gexf;\n  echarts.dataTool.prepareBoxplotData = prepareBoxplotData; // echarts.dataTool.boxplotTransform = boxplotTransform;\n}"], "names": ["zrUtil.map", "echarts.dataTool"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;IAsBA,IAAI,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;IAGjC,IAAI,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC;IACnC,IAAI,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC;IAC/B,IAAI,YAAY,GAAG,YAAY,GAAG,CAAC,WAAW,CAAC;IAC/C,IAAI,aAAa,GAAG,YAAY,GAAG,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC;IAiM1D,SAAS,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE;IACtC,IAAI,IAAI,CAAC,GAAG,EAAE;IACd,QAAQ,OAAO,EAAE,CAAC;IAClB,KAAK;IACL,IAAI,IAAI,CAAC,EAAE,EAAE;IACb,QAAQ,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC;IAC1B,KAAK;IACL,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,SAAS,EAAE;IAC1C,QAAQ,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IACpC,KAAK;IACL,SAAS;IACT,QAAQ,IAAI,MAAM,GAAG,EAAE,CAAC;IACxB,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IACxD,YAAY,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1D,SAAS;IACT,QAAQ,OAAO,MAAM,CAAC;IACtB,KAAK;IACL,CAAC;IAuDD,SAAS,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE;IACrC,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;IAClB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;IAClD,QAAQ,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;IACrC,KAAK;IACL,IAAI,OAAO,YAAY;IACvB,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC7E,KAAK,CAAC;IACN,CAAC;IACM,IAAI,IAAI,GAAG,CAAC,aAAa,IAAI,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC;IAClE,MAAM,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;IACjD,MAAM,YAAY,CAAC;IAiBZ,SAAS,UAAU,CAAC,KAAK,EAAE;IAClC,IAAI,OAAO,OAAO,KAAK,KAAK,UAAU,CAAC;IACvC,CAAC;IA4DM,SAAS,KAAK,CAAC,GAAG,EAAE;IAC3B,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;IAClB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;IAClD,QAAQ,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;IACrC,KAAK;IACL,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IACxC;;ICjVO,SAAS,KAAK,CAAC,GAAG,EAAE;IAC3B,EAAE,IAAI,GAAG,CAAC;AACV;IACA,EAAE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;IAC/B,IAAI,IAAI,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;IACjC,IAAI,GAAG,GAAG,MAAM,CAAC,eAAe,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;IAClD,GAAG,MAAM;IACT,IAAI,GAAG,GAAG,GAAG,CAAC;IACd,GAAG;AACH;IACA,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,MAAM,EAAE;IAC9D,IAAI,OAAO,IAAI,CAAC;IAChB,GAAG;AACH;IACA,EAAE,IAAI,QAAQ,GAAG,iBAAiB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAChD;IACA,EAAE,IAAI,CAAC,QAAQ,EAAE;IACjB,IAAI,OAAO,IAAI,CAAC;IAChB,GAAG;AACH;IACA,EAAE,IAAI,SAAS,GAAG,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACvD,EAAE,IAAI,UAAU,GAAG,eAAe,CAAC,iBAAiB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC;IAC/E,EAAE,IAAI,aAAa,GAAG,EAAE,CAAC;AACzB;IACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC9C,IAAI,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IACpD,GAAG;AACH;IACA,EAAE,OAAO;IACT,IAAI,KAAK,EAAE,UAAU,CAAC,iBAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE,aAAa,CAAC;IAC3E,IAAI,KAAK,EAAE,UAAU,CAAC,iBAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC5D,GAAG,CAAC;IACJ,CAAC;AACD;IACA,SAAS,eAAe,CAAC,MAAM,EAAE;IACjC,EAAE,OAAO,MAAM,GAAGA,GAAU,CAAC,oBAAoB,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,UAAU,SAAS,EAAE;IAC7F,IAAI,OAAO;IACX,MAAM,EAAE,EAAE,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC;IAClC,MAAM,KAAK,EAAE,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC;IACxC,MAAM,IAAI,EAAE,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;IACtC,KAAK,CAAC;IACN,GAAG,CAAC,GAAG,EAAE,CAAC;IACV,CAAC;AACD;IACA,SAAS,UAAU,CAAC,MAAM,EAAE,aAAa,EAAE;IAC3C,EAAE,OAAO,MAAM,GAAGA,GAAU,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,UAAU,OAAO,EAAE;IACtF,IAAI,IAAI,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACpC,IAAI,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC1C,IAAI,IAAI,IAAI,GAAG;IACf,MAAM,EAAE,EAAE,EAAE;IACZ,MAAM,IAAI,EAAE,KAAK;IACjB,MAAM,SAAS,EAAE;IACjB,QAAQ,MAAM,EAAE,EAAE;IAClB,OAAO;IACP,KAAK,CAAC;IACN,IAAI,IAAI,UAAU,GAAG,iBAAiB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC5D,IAAI,IAAI,SAAS,GAAG,iBAAiB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IAC/D,IAAI,IAAI,WAAW,GAAG,iBAAiB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AAC9D;IACA,IAAI,IAAI,YAAY,GAAG,iBAAiB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AAC/D;IACA,IAAI,IAAI,UAAU,EAAE;IACpB,MAAM,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;IACjE,KAAK;AACL;IACA,IAAI,IAAI,SAAS,EAAE;IACnB,MAAM,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;IACnD,MAAM,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;IACnD,KAAK;AACL;IACA,IAAI,IAAI,WAAW,EAAE;IACrB,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IAC3J,KAAK;IACL;IACA;AACA;AACA;IACA,IAAI,IAAI,YAAY,EAAE;IACtB,MAAM,IAAI,eAAe,GAAG,oBAAoB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IAC3E,MAAM,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AAC3B;IACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACvD,QAAQ,IAAI,WAAW,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;IAC7C,QAAQ,IAAI,KAAK,GAAG,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAChD,QAAQ,IAAI,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IACrD,QAAQ,IAAI,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;AAC7C;IACA,QAAQ,IAAI,SAAS,EAAE;IACvB,UAAU,QAAQ,SAAS,CAAC,IAAI;IAChC,YAAY,KAAK,SAAS,CAAC;IAC3B,YAAY,KAAK,MAAM;IACvB,cAAc,QAAQ,GAAG,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAChD,cAAc,MAAM;AACpB;IACA,YAAY,KAAK,OAAO,CAAC;IACzB,YAAY,KAAK,QAAQ;IACzB,cAAc,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC9C,cAAc,MAAM;AACpB;IACA,YAAY,KAAK,SAAS;IAC1B,cAAc,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC;IAC3D,cAAc,MAAM;IAGpB,WAAW;AACX;IACA,UAAU,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;IAC5C,SAAS;IACT,OAAO;IACP,KAAK;AACL;IACA,IAAI,OAAO,IAAI,CAAC;IAChB,GAAG,CAAC,GAAG,EAAE,CAAC;IACV,CAAC;AACD;IACA,SAAS,UAAU,CAAC,MAAM,EAAE;IAC5B,EAAE,OAAO,MAAM,GAAGA,GAAU,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,UAAU,OAAO,EAAE;IACtF,IAAI,IAAI,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACpC,IAAI,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC1C,IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC9C,IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC9C,IAAI,IAAI,IAAI,GAAG;IACf,MAAM,EAAE,EAAE,EAAE;IACZ,MAAM,IAAI,EAAE,KAAK;IACjB,MAAM,MAAM,EAAE,QAAQ;IACtB,MAAM,MAAM,EAAE,QAAQ;IACtB,MAAM,SAAS,EAAE;IACjB,QAAQ,MAAM,EAAE,EAAE;IAClB,OAAO;IACP,KAAK,CAAC;IACN,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC1C,IAAI,IAAI,eAAe,GAAG,iBAAiB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IACtE,IAAI,IAAI,WAAW,GAAG,iBAAiB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AAC9D;IACA,IAAI,IAAI,eAAe,EAAE;IACzB,MAAM,SAAS,CAAC,KAAK,GAAG,UAAU,CAAC,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;IAC1E,KAAK;AACL;IACA,IAAI,IAAI,WAAW,EAAE;IACrB,MAAM,SAAS,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IAC/I,KAAK;IACL;IACA;AACA;AACA;IACA,IAAI,OAAO,IAAI,CAAC;IAChB,GAAG,CAAC,GAAG,EAAE,CAAC;IACV,CAAC;AACD;IACA,SAAS,OAAO,CAAC,EAAE,EAAE,QAAQ,EAAE;IAC/B,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IACnC,CAAC;AACD;IACA,SAAS,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE;IAC5C,EAAE,IAAI,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC;AAC/B;IACA,EAAE,OAAO,IAAI,EAAE;IACf,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,WAAW,EAAE,EAAE;IACtF,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;IAC9B,KAAK,MAAM;IACX,MAAM,OAAO,IAAI,CAAC;IAClB,KAAK;IACL,GAAG;AACH;IACA,EAAE,OAAO,IAAI,CAAC;IACd,CAAC;AACD;IACA,SAAS,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE;IAC/C,EAAE,IAAI,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC;IAC/B,EAAE,IAAI,QAAQ,GAAG,EAAE,CAAC;AACpB;IACA,EAAE,OAAO,IAAI,EAAE;IACf,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,WAAW,EAAE,EAAE;IAC/D,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,KAAK;AACL;IACA,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;IAC5B,GAAG;AACH;IACA,EAAE,OAAO,QAAQ,CAAC;IAClB;;;;;;;ICvOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;IACA;IACA;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAAS,GAAG,CAAC,GAAG,EAAE;IAClB,EAAE,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;IAC3B,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC;IACjB,GAAG,CAAC,CAAC;IACL,EAAE,OAAO,GAAG,CAAC;IACb,CAAC;AACD;IACA,SAAS,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE;IAC7B,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACtC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACxB,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACzB,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAChB,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACzC,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;IACe,2BAAQ,EAAE,OAAO,EAAE,GAAG,EAAE;IACvC,EAAE,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;IAClB,EAAE,IAAI,OAAO,GAAG,EAAE,CAAC;IACnB,EAAE,IAAI,QAAQ,GAAG,EAAE,CAAC;IACpB,EAAE,IAAI,QAAQ,GAAG,EAAE,CAAC;IACpB,EAAE,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;IAC9B,EAAE,IAAI,UAAU,GAAG,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,CAAC,CAAC;AACzD;IACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC3C,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IAC1B,IAAI,IAAI,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;IAC1C,IAAI,IAAI,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACrC,IAAI,IAAI,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACpC,IAAI,IAAI,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACrC,IAAI,IAAI,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IACzB,IAAI,IAAI,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC1C,IAAI,IAAI,KAAK,GAAG,CAAC,QAAQ,IAAI,IAAI,GAAG,GAAG,GAAG,QAAQ,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;IAChE,IAAI,IAAI,GAAG,GAAG,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC;IAC3D,IAAI,IAAI,IAAI,GAAG,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC,CAAC;IAC5D,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;AAC1C;IACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC7C,MAAM,IAAI,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAChC;IACA,MAAM,IAAI,QAAQ,GAAG,GAAG,IAAI,QAAQ,GAAG,IAAI,EAAE;IAC7C,QAAQ,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IACpC,QAAQ,GAAG,CAAC,MAAM,KAAK,UAAU,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;IACvD,QAAQ,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/B,OAAO;IACP,KAAK;IACL,GAAG;AACH;IACA,EAAE,OAAO;IACT,IAAI,OAAO,EAAE,OAAO;IACpB,IAAI,QAAQ,EAAE,QAAQ;IACtB,IAAI,QAAQ,EAAE,QAAQ;IACtB,GAAG,CAAC;IACJ;;AC1EU,QAAC,OAAO,GAAG,QAAQ;IAG7B;IACA;IACA;IACA;AACA;IACA,IAAIC,gBAAgB,EAAE;IACtB,EAAEA,gBAAgB,CAAC,OAAO,GAAG,OAAO,CAAC;IACrC,EAAEA,gBAAgB,CAAC,IAAI,GAAG,IAAI,CAAC;IAC/B,EAAEA,gBAAgB,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IAC3D;;;;;;;;"}