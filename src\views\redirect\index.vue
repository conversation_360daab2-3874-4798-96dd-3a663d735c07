<!--
 * @Author: <PERSON>
 * @Date: 2023-02-15 10:43:24
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-01-24 16:25:05
 * @FilePath: \base-wyyy-template\src\views\redirect\index.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by 附一医信息处, All Rights Reserved. 
-->
<script>
export default {
  created() {
    const { params, query } = this.$route
    const { path, ..._query } = query
    this.$router.replace({ path, query: _query })
  },
  render: function (h) {
    return h() // avoid warning message
  }
}
</script>
