<template>
  <el-checkbox v-model="checkValue" @change="handleChange"></el-checkbox>
</template>

<script>
export default {
  name: 'DrawerTableCheckbox',
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    column: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      checkValue: false
    }
  },
  mounted() {
    this.checkValue = this.row[this.column.value] === '1'
  },
  methods: {
    handleChange(value) {
      this.$emit('updateRow', { prop: this.column.value, updateValue: value ? '1' : '0' })
    }
  }
}
</script>

<style lang="scss" scoped></style>
