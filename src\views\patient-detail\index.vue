<template>
  <div class="inner-container">
    <el-container>
      <el-aside :class="{ 'el-aside__collapse': !opened }">
        <sidebar />
      </el-aside>
      <el-main>
        <patient-detail-view />
      </el-main>
    </el-container>
  </div>
</template>

<script>
import PatientDetailView from '@/views/patient-detail/PatientDetailView.vue'
import Sidebar from '@/views/patient-detail/components/Sidebar/index.vue'
import store from '@/store'
import patientDetailModule from '@/store/modules/patientDetail'

export default {
  name: 'PatientDetail',
  components: {
    Sidebar,
    PatientDetailView
  },
  data() {
    return {}
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    sidebar() {
      return store.state.app.sidebar
    },
    opened: {
      get() {
        return this.sidebar.opened
      },
      set(val) {
        if (!val) this.toggleSideBar()
      }
    }
  },
  beforeCreate() {
    // 在组件创建前注册动态模块
    const bingLiID = this.$route.params.id
    const moduleName = `patientDetail/${bingLiID}`

    console.log('注册动态模块:', moduleName)
    if (!this.$store.hasModule(moduleName)) {
      this.$store.registerModule(moduleName, patientDetailModule)
      console.log('动态模块注册成功')
    } else {
      console.log('动态模块已存在，无需重复注册')
    }
  },
  beforeDestroy() {
    // 卸载当前病人的动态模块
    const moduleName = `patientDetail/${this.bingLiID}`
    if (this.$store.hasModule(moduleName)) {
      this.$store.unregisterModule(moduleName)
    }
  },
  methods: {}
}
</script>

<style scoped lang="scss">
.inner-container {
  height: 100%;

  .el-aside {
    flex-basis: 235px;
  }

  .el-main {
    display: flex;
    height: 100%;
    padding: 0px;

    .app-main {
      height: 100%;
      width: 100%;
      overflow: auto;
    }
  }
}
</style>
