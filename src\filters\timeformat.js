const filterHandler = {
  getValueByKey(text, list, key, value) {
    let returnValue = text
    list.forEach((item) => {
      if (item[key] === text) {
        returnValue = item[value] ? item[value] : item[key]
        return false
      }
    })
    return returnValue
  },
  // 转化为 YYYY-MM-DD
  formaDate(value) {
    if (value) {
      return value.split(' ')[0]
    } else {
      return ''
    }
  },
  // 转化为 YYYY-MM-DD 00:00:00
  formaDateTime(val) {
    const year = new Date(val).getFullYear()
    let mon = new Date(val).getMonth() + 1
    let day = new Date(val).getDate()
    mon = mon < 10 ? `0${mon}` : mon
    day = day < 10 ? `0${day}` : day
    return `${year}-${mon}-${day} 00:00:00`
  },
  // 转化为 YYYY-MM-DD hh:mm:ss
  formaDateNowTime(val) {
    const year = new Date(val).getFullYear()
    let mon = new Date(val).getMonth() + 1
    let day = new Date(val).getDate()
    let hours = new Date(val).getHours()
    let min = new Date(val).getMinutes()
    let sec = new Date(val).getSeconds()
    mon = mon < 10 ? `0${mon}` : mon
    day = day < 10 ? `0${day}` : day
    hours = hours < 10 ? `0${hours}` : hours
    min = min < 10 ? `0${min}` : min
    sec = sec < 10 ? `0${sec}` : sec
    return `${year}-${mon}-${day} ${hours}:${min}:${sec}`
  },
  // 转化为 YYYY-MM-DD hh:mm
  formaDateNowTimeShow(val) {
    const year = new Date(val).getFullYear()
    let mon = new Date(val).getMonth() + 1
    let day = new Date(val).getDate()
    let hours = new Date(val).getHours()
    let min = new Date(val).getMinutes()
    mon = mon < 10 ? `0${mon}` : mon
    day = day < 10 ? `0${day}` : day
    hours = hours < 10 ? `0${hours}` : hours
    min = min < 10 ? `0${min}` : min
    return `${year}-${mon}-${day} ${hours}:${min}`
  },
  // 根据出生日期获取年龄
  getAge(strBirthday) {
    if (!strBirthday) {
      return ''
    }
    var returnAge
    var strBirthdayArr = strBirthday.split(' ')[0].split('-')
    var birthYear = strBirthdayArr[0]
    var birthMonth = strBirthdayArr[1]
    var birthDay = strBirthdayArr[2]
    var d = new Date()
    var nowYear = d.getFullYear()
    var nowMonth = d.getMonth() + 1
    var nowDay = d.getDate()
    if (nowYear === birthYear) {
      returnAge = 0 // 同年 则为0周岁
    } else {
      var ageDiff = nowYear - birthYear // 年之差
      if (ageDiff > 0) {
        if (nowMonth === birthMonth) {
          var dayDiff = nowDay - birthDay // 日之差
          if (dayDiff < 0) {
            returnAge = ageDiff - 1
          } else {
            returnAge = ageDiff
          }
        } else {
          var monthDiff = nowMonth - birthMonth // 月之差
          if (monthDiff < 0) {
            returnAge = ageDiff - 1
          } else {
            returnAge = ageDiff
          }
        }
      } else {
        returnAge = 0 // 返回-1 表示出生日期输入错误 晚于今天
      }
    }
    return returnAge // 返回周岁年龄
  }
}
export default (Vue) => {
  Object.keys(filterHandler).forEach((key) => {
    Vue.filter(key, filterHandler[key])
    window[key] = filterHandler[key]
  })
}
