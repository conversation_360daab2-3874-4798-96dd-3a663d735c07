<template>
  <div class="container">
    <!-- 首页列表 -->
    <div class="index-class">
      <!-- 中间数据表格 -->
      <div class="content">
        <div class="content-header">
          <div class="title">输血医嘱</div>
          <div>
            <el-button type="primary" @click="addBlood">新增输血单</el-button>
            <el-button type="primary" @click="stockVisible = true">查看库存</el-button>
          </div>
        </div>
        <div class="table">
          <el-table max-height="428" border :data="bloodInitList">
            <el-table-column align="center" prop="leiBie" width="70" label="来源">
              <template #default="{ row }">
                <el-tag
                  :type="row.leiBie === '住院' ? 'success' : row.leiBie === '急诊' ? 'danger' : ''"
                  effect="dark"
                >
                  {{ row.leiBie }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              prop="shenQingSJ"
              width="150"
              label="申请时间"
            ></el-table-column>
            <el-table-column prop="shenQingYSXM" width="120" label="申请医生"></el-table-column>
            <el-table-column
              label="血液品种"
              :formatter="(row) => `申请:${row.chengFenMC}${row.shenQingSL}${row.jiLiangDW}`"
            ></el-table-column>
            <el-table-column prop="yiZhuLX" width="160" label="输血类型"></el-table-column>
            <el-table-column prop="zt" width="250" label="剩余数量">
              <template #default="{ row }">
                {{ row.yiBeiSL - row.yiYongSL }}{{ row.jiLiangDW }}
              </template>
            </el-table-column>
            <el-table-column prop="zhuangTaiBZMC" width="150" label="状态">
              <template #default="{ row }">
                <el-tag
                  :type="
                    row.zhuangTaiBZMC == '已到期' || row.zhuangTaiBZMC === '已用完'
                      ? 'danger'
                      : row.zhuangTaiBZMC == '取消'
                      ? 'info'
                      : ''
                  "
                  effect="light"
                >
                  {{ row.zhuangTaiBZMC }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              width="160"
              label="到期时间"
              :formatter="(row) => formatDate(row.daoQiSJ)"
            ></el-table-column>
            <el-table-column align="center" prop="caoZuo" width="200" label="操作">
              <template #default="{ row }">
                <el-button
                  type="text"
                  class="button-with-divider"
                  @click="handleClick(row, 'detaile')"
                >
                  详细
                </el-button>
                <el-button
                  type="text"
                  class="button-with-divider"
                  @click="handleClick(row, 'close')"
                >
                  闭环
                </el-button>
                <el-button
                  v-if="row.leiBie == '住院1'"
                  type="text"
                  class="button-with-divider"
                  @click="handleClick(row, 'pause')"
                >
                  暂停
                </el-button>
                <el-button
                  v-else
                  type="text"
                  class="button-with-divider"
                  style="color: #c0c1c3"
                  @click="handleClick(row, 'delete')"
                >
                  取消
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- <el-pagination
            :current-page="tabStates[activeName].currentPage"
            :page-sizes="[10, 20, 50]"
            :page-size="tabStates[activeName].pageSize"
            :total="tabStates[activeName].total"
            layout="total, sizes, prev, pager, next"
            @current-change="(val) => handlePageChange(val, 'currentPage')"
            @size-change="(val) => handlePageChange(val, 'pageSize')"
          ></el-pagination> -->
        </div>
      </div>
      <!-- 底部输血说明 -->
      <div class="bottom">
        <div style="display: flex">
          <i class="el-icon-warning"></i>
          <span class="title-tips">输血说明：</span>
        </div>
        <div class="tips">1.常规输血：因患者病情需些进行治疗输血、手术备血的患者。</div>
        <div class="tips">2.抢救输血：仅限术中抢救，用血（30分钟内）需要输血的患者。</div>
        <div class="tips">3.自体输血：择期手术，预计术中出血盘大，需要输血者先进行储血准备。</div>
        <div class="tips">
          4.输血反应：输血过程中成输血之后，受血者发生了与输血有关的新的异常表现或疾病。
        </div>
        <div class="bottom-tips">
          <!-- <el-tag> -->
          <i class="el-icon-warning"></i>
          <span style="margin-left: 5px">
            血液是一种稀缺的人类资源，珍情自愿无偿献血者的生命礼物-血液！呵护关爱无偿献血者的爱心！在恰当的时候将合适的血液输注给需要的患者。
          </span>
          <!-- </el-tag> -->
        </div>
      </div>
    </div>

    <!-- 新增输血单弹框 -->
    <div class="blood-class stock-class">
      <el-dialog
        :visible.sync="bloodVisible"
        title="输血医嘱"
        pop-type="tip"
        width="1260px"
        :close-on-click-modal="false"
      >
        <!-- 新增输血单详情 -->
        <div class="blood-pop-class">
          <!-- 详情左侧 -->
          <div class="index-box">
            <div class="content">
              <div class="content-header">
                <div class="title">输血医嘱</div>
              </div>
              <div style="padding: 12px !important">
                <table class="table-box left-table">
                  <tr>
                    <td class="info-label">患者姓名:</td>
                    <td class="info-value">{{ patientDetail.bingRenXM }}</td>
                    <td class="info-label">性别:</td>
                    <td class="info-value">{{ patientDetail.xingBie == '1' ? '男' : '女' }}</td>
                    <td class="info-label">年龄:</td>
                    <td class="info-value">{{ patientDetail.nianLing }}</td>
                  </tr>
                  <tr>
                    <td class="info-label">专科:</td>
                    <td class="info-value">{{ patientDetail.zhuanKeMC }}</td>
                    <td class="info-label">病案号:</td>
                    <td class="info-value">{{ patientDetail.bingAnHao }}</td>
                    <td class="info-label">病区-床位:</td>
                    <td class="info-value">
                      {{ patientDetail.bingQuZDYM }}-{{ patientDetail.chuangWeiHao }}
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">临床诊断:</td>
                    <td class="info-value" colspan="5">{{ patientDetail.ruYuanZD }}</td>
                  </tr>
                  <tr>
                    <td colspan="6" style="background: #fafbfc">患者血型</td>
                  </tr>
                  <tr>
                    <td class="info-label">ABO正定型:</td>
                    <td class="info-value">{{ typeAndRHObj && typeAndRHObj.abo }}</td>
                    <td class="info-label">ABO反定型:</td>
                    <td class="info-value">{{ typeAndRHObj && typeAndRHObj.abo_neg }}</td>
                    <td class="info-label">ABO亚型:</td>
                    <td class="info-value">{{ typeAndRHObj && typeAndRHObj.abo_yx }}</td>
                  </tr>
                  <tr>
                    <td class="info-label">RH(D)血型:</td>
                    <td class="info-value">{{ typeAndRHObj && typeAndRHObj.rh }}</td>
                    <td class="info-label multi-td">抗体筛查结果:</td>
                    <td class="info-value" colspan="4">{{ typeAndRHObj && typeAndRHObj.ks }}</td>
                  </tr>
                  <tr>
                    <td class="info-label multi-td">Coomb's试验:</td>
                    <td colspan="5">
                      <span class="td-with-divider">
                        直抗:{{ typeAndRHObj && typeAndRHObj.zhiKang }}
                      </span>
                      <span class="td-with-divider">
                        间抗:{{ typeAndRHObj && typeAndRHObj.jianKang }}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td colspan="6" style="background: #fafbfc">备血申请</td>
                  </tr>
                  <tr class="custom-cell">
                    <td class="info-label">
                      <div class="td-icon-warning-box">
                        <i class="el-icon-warning td-icon-warning"></i>
                        <span>血液品种:</span>
                      </div>
                    </td>
                    <td class="info-value" style="padding: 4px">
                      <el-select
                        v-model="rowObj.xypz"
                        :disabled="editFlag"
                        @change="(item) => handleXypzChange(item)"
                      >
                        <el-option
                          v-for="item in xueYePZList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="info-label" style="padding: 8px 5px">
                      申请数量({{ rowObj.xypzJL }}):
                    </td>
                    <td class="info-value" style="padding: 4px">
                      <el-input v-model="rowObj.sqsl" @input="$forceUpdate()"></el-input>
                    </td>
                    <td class="info-label">血液类型:</td>
                    <td class="info-value" style="padding: 4px">
                      <el-select v-model="rowObj.xylx">
                        <el-option
                          v-for="item in shuXueLXList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                      <el-select v-if="rowObj.xylx == 1" v-model="rowObj.xylxJJ">
                        <el-option
                          v-for="item in jiZhenSXLXMapList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                          placeholder="请选择急救用血"
                        ></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="td-icon-warning-box">
                        <i class="el-icon-warning td-icon-warning"></i>
                        <span>备血:</span>
                      </div>
                    </td>
                    <td class="info-value td-select">
                      <el-checkbox-group
                        v-model="BeiXue"
                        style="display: inline"
                        @change="(item) => handleBeiXueChange(item)"
                      >
                        <el-checkbox
                          v-for="item in xueYeLXList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                        ></el-checkbox>
                      </el-checkbox-group>
                      <!-- 勾选了异性输血 -->
                      <span v-if="BeiXue.indexOf('异型输血') != -1">
                        <span style="color: red; margin-right: 8px">不打勾默认为同型输血</span>
                        <span>输注血型：</span>
                        <el-select v-model="rowObj.yxsx_abo" style="width: 20%; margin: 0px 10px">
                          <el-option
                            v-for="item in xueXingList"
                            :key="item.daiMa"
                            :label="item.mingCheng"
                            :value="item.daiMa"
                          ></el-option>
                        </el-select>
                        <el-select v-model="rowObj.yxsx_rh" style="width: 20%">
                          <el-option
                            v-for="item in rhxueXingList"
                            :key="item.daiMa"
                            :label="item.mingCheng"
                            :value="item.daiMa"
                          ></el-option>
                        </el-select>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td style="background: #fafbfc; border-right: none">临时医嘱</td>
                    <td
                      class="info-label"
                      colspan="5"
                      style="background: #fafbfc; border-left: none; padding: 2px 3px"
                    >
                      <el-button type="primary" @click="addBloodOrder">新增输血医嘱</el-button>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label blood-order-class">
                      <el-table
                        max-height="111"
                        border
                        :data="bloodInitList1"
                        style="width: 100%"
                        align="center"
                        header-align="center"
                        :class="bloodInitList1.length == 0 ? 'hide-empty-text' : ''"
                      >
                        <el-table-column
                          align="center"
                          prop="shenQingYSXM1"
                          width="105"
                          :label="`本次输血数量(${rowObj.xypzJL})`"
                        >
                          <template #default="{ row }">
                            <el-input
                              v-model="row.yz_sxsl"
                              style="width: 70%; margin-right: 5px"
                            ></el-input>
                          </template>
                          <!-- <el-input v-model="rowObj.yz_sxsl" :disabled="editFlag" style="width: 70%;margin-right:5px"></el-input>{{rowObj.xypzJL}} -->
                        </el-table-column>
                        <el-table-column
                          align="center"
                          prop="shenQingYSXM1"
                          width="80"
                          label="是否紧急"
                        >
                          <template #default="{ row }">
                            <el-checkbox-group v-model="row.yz_sfjj">
                              <el-checkbox label="紧急"></el-checkbox>
                            </el-checkbox-group>
                          </template>
                        </el-table-column>
                        <el-table-column prop="shenQingYSXM" width="135" label="病区选择">
                          <template #default="{ row }">
                            <el-select v-model="row.yz_bqxz">
                              <el-option
                                v-for="item in yiZhuQXList"
                                :key="item.buMenID"
                                :label="item.buMenMC"
                                :value="item.buMenID"
                              ></el-option>
                            </el-select>
                          </template>
                        </el-table-column>
                        <el-table-column
                          align="center"
                          prop="shenQingYSXM1"
                          width="105"
                          label="医嘱导入时间"
                        >
                          <template #default="{ row }">
                            {{ row.yz_drsj }}
                          </template>
                        </el-table-column>
                        <el-table-column
                          align="center"
                          prop="shenQingYSXM1"
                          width="95"
                          label="状态"
                        >
                          <template #default="{ row }">
                            {{ row.yz_ztbz }}
                          </template>
                        </el-table-column>
                        <el-table-column
                          align="center"
                          prop="shenQingYSXM1"
                          width="105"
                          label="输血科审核"
                        >
                          <template #default="{ row }">
                            {{ row.yz_sxksp }}
                          </template>
                        </el-table-column>
                        <el-table-column
                          align="center"
                          prop="caoZuo"
                          width="100"
                          fixed="right"
                          label="操作"
                        >
                          <template #default="{ row }">
                            <el-button
                              type="text"
                              class="button-with-divider"
                              @click="delBloodOrder(row)"
                            >
                              删除
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </td>
                  </tr>
                  <tr>
                    <td colspan="6" style="background: #fafbfc">备血申请</td>
                  </tr>
                  <tr>
                    <td class="info-label">已备数量:</td>
                    <td class="info-value">{{ BloodQuantity.ybsl }}</td>
                    <td class="info-label">已记账数量:</td>
                    <td class="info-value">{{ BloodQuantity.yjzsl }}</td>
                    <td class="info-label">已运数量:</td>
                    <td class="info-value">{{ BloodQuantity.yiYunSL }}</td>
                  </tr>
                  <tr>
                    <td class="info-label multi-td">临床已接数量:</td>
                    <td class="info-value">{{ BloodQuantity.yiJieSL }}</td>
                    <td class="info-label">输注确认:</td>
                    <td class="info-value">{{ BloodQuantity.shuZhuQRSL }}</td>
                    <td class="info-label">输注完毕:</td>
                    <td class="info-value">{{ BloodQuantity.shuZhuWBSL }}</td>
                  </tr>
                  <tr>
                    <td colspan="6" style="background: #fafbfc">输血前评估</td>
                  </tr>
                  <tr>
                    <td class="info-label">输血目的:</td>
                    <td class="info-value td-select">
                      <el-checkbox-group v-model="SXMD">
                        <el-checkbox
                          v-for="item in shuXueMDList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                        ></el-checkbox>
                      </el-checkbox-group>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">实验室数据:</td>
                    <td class="info-value" colspan="5" style="padding: 4px">
                      <el-input
                        placeholder="请从右边的检验指标中选择相应指标"
                        v-model="rowObj.syssj"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">临床表现:</td>
                    <td class="info-value" colspan="5" style="padding: 4px">
                      <el-input v-model="rowObj.clbx"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">备注:</td>
                    <td class="info-value" colspan="5" style="padding: 4px">
                      <el-input v-model="rowObj.beizhu"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">申请医师:</td>
                    <td class="info-value">{{ patientInfo.yongHuXM }}</td>
                    <td class="info-label">开单时间:</td>
                    <td class="info-value">{{ rowObj.kdsj }}</td>
                  </tr>
                </table>
              </div>
            </div>
          </div>

          <!-- 详情右侧 -->
          <div class="index-box" style="border: none; width: 40%">
            <div class="content">
              <!-- 详情右侧上面 -->
              <div>
                <div class="content-header extra-header">
                  <div class="title">输血相关实验室指标</div>
                </div>
                <div>
                  <el-descriptions class="margin-top" title="" :column="2" border>
                    <el-descriptions-item
                      v-for="(item, index) in labData"
                      :key="index"
                      :label="item.huaYanXMMC"
                    >
                      {{ item.huaYanJG }}{{ item.danWei }}
                      <span v-if="item.tiShi != ' '" class="import-title" style="color: red">
                        {{ item.tiShi }}
                      </span>
                      <span
                        class="import-title"
                        style="text-decoration: underline"
                        v-if="item.daoRu == 1"
                        @click="laboratoryExport(item)"
                      >
                        导入
                      </span>
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
              <!-- 详情右侧下面 -->
              <div style="margin-top: 16px">
                <div class="survey-box">
                  <div class="content-header survey-header">
                    <div class="title">附加检验医嘱-下达化验医嘱:</div>
                  </div>
                  <div class="survey-describe">
                    (若未选择化验医嘱保存后还可以继续进行保存附加化验医嘱)
                  </div>
                </div>
                <el-table
                  :data="addSurveyData"
                  :show-header="false"
                  stripe
                  size="mini"
                  @selection-change="handleSelectedAddSurvey"
                >
                  <el-table-column
                    type="selection"
                    width="26"
                    header-align="center"
                    align="center"
                    class="no-header"
                  ></el-table-column>
                  <el-table-column prop="huaYanMC"></el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </div>
        <!-- 输血医嘱注意 -->
        <div class="el-icon-warning-box" style="margin-top: 10px">
          <i class="el-icon-warning"></i>
          <span class="title-tips">
            注意：临床医生要正确区分紧急输血和平诊输血，不能滥用紧急输血选项，否则输血病历点评是将判为违规用血。
          </span>
        </div>
        <!-- 底部操作 -->
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="addBloodTransSqd">保存</el-button>
          <el-button @click="bloodVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>

    <!-- 新增实验室弹框 -->
    <div class="laboratory-class stock-class">
      <el-dialog
        :visible.sync="laboratoryVisible"
        title="实验室数据"
        width="380px"
        pop-type="tip"
        :close-on-click-modal="false"
      >
        <el-table
          :data="laboratoryData"
          stripe
          size="mini"
          max-height="480px"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="50"
            header-align="center"
            align="center"
            class="no-header"
          ></el-table-column>
          <el-table-column prop="zbxz" width="290" label="指标选择"></el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="laboratoryVisible = false">确认</el-button>
          <el-button @click="laboratoryVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>

    <!-- 输血血库弹框 -->
    <div class="stock-class">
      <el-dialog
        :visible.sync="stockVisible"
        title="输血血库"
        width="630px"
        pop-type="tip"
        :close-on-click-modal="false"
      >
        <div class="radio-group">
          <el-radio
            v-model="currentHospital"
            label="new"
            class="custom-radio"
            @change="handleHospitalChange"
          >
            血库库存(新院)
          </el-radio>
          <el-radio
            v-model="currentHospital"
            label="old"
            class="custom-radio"
            @change="handleHospitalChange"
          >
            血库库存(老院)
          </el-radio>
        </div>
        <el-table :data="stockData" border stripe size="mini" max-height="480px">
          <el-table-column prop="chengFenMC" width="260" label="类型"></el-table-column>
          <el-table-column prop="abo" label="血型"></el-table-column>
          <el-table-column prop="shenQingZL" label="申请总量"></el-table-column>
          <el-table-column prop="kuCunZL" label="库存"></el-table-column>
          <el-table-column prop="danWei" label="单位"></el-table-column>
        </el-table>
        <el-pagination
          :current-page="currentPagination.currentPage"
          :page-size="currentPagination.pageSize"
          :total="totalPaginatedLength"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        ></el-pagination>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="stockVisible = false">确认</el-button>
          <el-button @click="stockVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>

    <!-- 暂停/删除弹框 -->
    <div class="pop-class">
      <el-dialog :visible.sync="delVisible" width="350px" :close-on-click-modal="false">
        <span slot="title">
          <span style="font-size: 16px">
            <i class="el-icon-warning"></i>
            <span style="font-weight: bold">
              {{ choiceType == 'pause' ? '暂停' : choiceType == 'delete' ? '取消' : '' }}提示
            </span>
          </span>
        </span>
        <div class="delete-component">
          确认 {{ choiceType == 'pause' ? '暂停' : choiceType == 'delete' ? '取消' : '' }}此
          <!-- <a>【{{ rowObj.xypz }}】</a> -->
          <a>申请单</a>
          吗?
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="delBloodTransSqd">确认</el-button>
          <el-button @click="delVisible = false">取消</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  listBloodTransSqd,
  getBaseInfo,
  getEnableAddAssayListByLeiXing,
  getSqdAndAdviceListByShenQingDanID,
  getBloodBankInventory,
  addBloodTransSqd,
  getBloodQuantityBySQDID,
  getInPatientBloodTypeByLB,
  delBloodTransSqd,
  getXueKuBH,
  updateBloodTransSqd,
  getLaboratoryIndicatorsByCFDM,
  getLaboratoryIndicatorsByHYXM
} from '@/api/blood-order'
import { format, differenceInDays } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      bloodDetails: {}, //病人输血单详情
      xueYePZList: [], //血液品种
      shuXueLXList: [], //血液类型
      jiZhenSXLXMapList: [], //选择急救用血
      xueXingList: [], //异性输血的abo
      rhxueXingList: [], //异性输血的rh
      // xueYeLX: '',
      BeiXue: [], //备血
      xueYeLXList: [], //备血选项
      // shuXueMD: '',
      SXMD: [], //输血目的
      shuXueMDList: [], //输血目的选项
      yiZhuQXList: [], //输血医嘱病区选择列表
      bloodInitList: [], //首页列表数据
      bloodInitList1: [
        {
          yz_sxsl: '', //医嘱的输血数量
          yz_sfjj: false, //医嘱的是否紧急
          yz_bqxz: '', //医嘱的病区选择
          yz_drsj: '', //医嘱的导入时间
          yz_ztbz: '', //医嘱的状态标志
          yz_sxksp: '' //医嘱的输血科审批状态
        }
      ], //弹框里的输血医嘱数据
      labData: [], //输血相关实验室指标数据
      laboratoryData: [
        { zbxz: '总蛋白' },
        { zbxz: '白蛋白' },
        { zbxz: '总胆红素' },
        { zbxz: '直接杨红素' },
        { zbxz: '丙谷转氨酶' },
        { zbxz: '纤维蛋白质' },
        { zbxz: 'APTT比值' },
        { zbxz: '凝血酶时间比值' },
        { zbxz: '总蛋白' },
        { zbxz: '白蛋白' },
        { zbxz: '总胆红素' },
        { zbxz: '直接杨红素' },
        { zbxz: '丙谷转氨酶' },
        { zbxz: '纤维蛋白质' },
        { zbxz: 'APTT比值' },
        { zbxz: '凝血酶时间比值' },
        { zbxz: '总蛋白' },
        { zbxz: '白蛋白' },
        { zbxz: '总胆红素' },
        { zbxz: '直接杨红素' },
        { zbxz: '丙谷转氨酶' },
        { zbxz: '纤维蛋白质' },
        { zbxz: 'APTT比值' },
        { zbxz: '凝血酶时间比值' },
        { zbxz: '总蛋白' },
        { zbxz: '白蛋白' },
        { zbxz: '总胆红素' },
        { zbxz: '直接杨红素' },
        { zbxz: '丙谷转氨酶' },
        { zbxz: '纤维蛋白质' },
        { zbxz: 'APTT比值' },
        { zbxz: '凝血酶时间比值' }
      ], //实验室弹框数据
      addSurveyData: [], //附加检验医嘱数据
      addSurveyInitData: [], //附加检验医嘱初始化数据
      selectedAddSurvey: [], //附加检验医嘱勾选数据
      sxkHyjgVoList: [], // 化验结果
      typeAndRHObj: {}, //患者血型数据
      delVisible: false, //删除输血单显示
      stockVisible: false, //查看库存显示
      laboratoryVisible: false, //实验室弹框显示
      bloodVisible: false, //新增输血单显示
      rowObj: {
        xypz: '', //血液品种
        xypzJL: 'ml', //血液品种计量
        xcf: '', //血成分
        sqsl: '', //申请数量
        xylx: '', //血液类型
        xylxJJ: '', //血液类型=急救
        beixue: '', //备血
        yxsx_abo: '', //异形输血abo
        yxsx_rh: '', //异形输血rh
        sxmd: '', //输血目的
        syssj: '', //实验室数据
        clbx: '', //临床表现
        beizhu: '', //备注
        kdsj: '' //开单时间
      }, //新增输血单数据对象
      choiceType: '', //暂停/删除类型
      selectedItems: [], //备选勾选数据
      editFlag: false, //是否编辑判断

      currentHospital: 'new', // 查看库存默认选中新院
      newHospitalData: [], //查看库存新院数据
      oldHospitalData: [], //查看库存老院数据
      newHospitalPagination: {
        currentPage: 1,
        pageSize: 10
      }, // 查看库存新院分页参数
      oldHospitalPagination: {
        currentPage: 1,
        pageSize: 10
      }, // 查看库存老院分页参数
      BloodQuantity: {}, //备血申请血袋数量
      XueKuBH: {} //血库编号
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo,
      yongHuID: ({ user }) => user.yongHuID
    }),
    bingLiID() {
      return this.$route.params.id
    },
    // 查看库存分页判断
    currentPagination() {
      return this.currentHospital === 'new'
        ? this.newHospitalPagination
        : this.oldHospitalPagination
    },
    // 查看库存分页后的数据
    stockData() {
      const data = this.currentHospital === 'new' ? this.newHospitalData : this.oldHospitalData
      const start = (this.currentPagination.currentPage - 1) * this.currentPagination.pageSize
      return data.slice(start, start + this.currentPagination.pageSize)
    },
    // 查看库存数据总数
    totalPaginatedLength() {
      return this.currentHospital === 'new'
        ? this.newHospitalData.length
        : this.oldHospitalData.length
    }
  },
  async mounted() {
    await this.init()
    console.log(this.patientDetail)
    console.log(this.patientInfo)
  },
  methods: {
    // 页面初始化
    async init() {
      this.listBloodTransSqd()
      this.getBloodBankInventory()
    },
    // 实验室弹框处理选择变化
    handleSelectionChange(val) {
      console.log(val)
    },
    // 处理附加检验医嘱选择
    handleSelectedAddSurvey(selectedRows) {
      const selectedOldData = []
      selectedRows.forEach((newRow) => {
        const { lianDongHao, huaYanMC } = newRow
        const huaYanMCList = huaYanMC.split(';').filter((mc) => mc.trim())
        huaYanMCList.forEach((mc) => {
          const matchedItem = this.addSurveyInitData.find(
            (oldItem) => oldItem.lianDongHao === lianDongHao && oldItem.huaYanMC === mc
          )
          if (matchedItem) {
            selectedOldData.push(matchedItem)
          }
        })
      })
      this.selectedAddSurvey = selectedOldData
    },
    // 首页列表操作
    async handleClick(row, type) {
      if (type == 'detaile') {
        this.addType = 'edit'
        this.editFlag = true
        this.rowObj = JSON.parse(JSON.stringify(row))
        this.bloodVisible = true
        this.getBaseInfo()
        this.getBloodQuantityBySQDID({
          shenQingDanID: row.shenQingDanID
        })
        this.getInPatientBloodTypeByLB(1)
        this.getSqdAndAdviceListByShenQingDanID({
          shenQingDanID: row.shenQingDanID
        })
        this.getEnableAddAssayListByLeiXing()
      } else if (type == 'delete') {
        this.delVisible = true
        this.choiceType = type
        this.rowObj = JSON.parse(JSON.stringify(row))
      } else {
        this.rowObj = JSON.parse(JSON.stringify(row))
      }
    },
    // 删除输血申请单
    async delBloodTransSqd() {
      try {
        const res = await delBloodTransSqd({
          bingLiID: this.patientDetail.bingLiID,
          shenQingDanID: this.rowObj.shenQingDanID
        })
        if (res.hasError === 0) {
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.delVisible = false
          this.init()
        }
      } catch (error) {
        console.log(error)
        this.$message({
          message: error.errorMessage,
          type: 'error'
        })
        this.delVisible = false
      }
    },
    // 血液品种选择
    handleXypzChange(val) {
      this.rowObj.xypz = val
      const selectedXypz = this.xueYePZList.find((item) => item.daiMa === val)
      this.rowObj.xypzJL = selectedXypz.jiLiang
      this.rowObj.xcf = selectedXypz.mingCheng
      this.getLaboratoryIndicatorsByCFDM()
    },
    // 根据成分代码获取需要导入的化验指标
    async getLaboratoryIndicatorsByCFDM() {
      try {
        const res = await getLaboratoryIndicatorsByCFDM({
          chengFenDM: this.rowObj.xypz
        })
        if (res.hasError === 0) {
          this.getLaboratoryIndicatorsByHYXM(res.data.beiZhu)
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 根据化验项目获取对应化验指标
    async getLaboratoryIndicatorsByHYXM(data) {
      try {
        const res = await getLaboratoryIndicatorsByHYXM({
          huaYanXMIDList: data
        })
        if (res.hasError === 0) {
          // 1.重置数据
          this.rowObj.syssj = ''
          let text = ''
          this.labData.forEach((item) => (item.daoRu = 0))
          // 2.过滤取出需要手动导入的指标
          let arr = res.data.map((item) => this.labData.find((itm) => item.daiMa == itm.huaYanXMDM))
          // 3.如果条数少则自动导入
          if (arr.length < 5) {
            arr.map((item) => {
              text += `${item.ceShiSJ}  ${item.huaYanXMMC}:${item.huaYanJG}${item.danWei}  ;`
            })
            this.rowObj.syssj = text
          } else {
            //4.条数多怎么赋值导入
            arr.forEach((item) => {
              this.labData.forEach((itm, index) => {
                if (item.huaYanXMDM == itm.huaYanXMDM) {
                  this.$set(this.labData, index, {
                    ...itm,
                    daoRu: 1
                  })
                }
              })
            })
          }
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 备选选择
    handleBeiXueChange(selectedItems) {
      const mutualExclusionGroups = [
        ['一次滤白(新生儿专用)', '二次滤白'], // 滤白互斥组
        ['自体储输血', '异型输血'] // 输血类型互斥组
      ]
      mutualExclusionGroups.forEach((group) => {
        // 找出当前组中被选中的项
        const selectedInGroup = selectedItems.filter((item) => group.includes(item))
        if (selectedInGroup.length > 1) {
          const lastSelected = selectedInGroup[selectedInGroup.length - 1]
          const itemsToRemove = selectedInGroup.filter((item) => item !== lastSelected)
          this.BeiXue = selectedItems.filter((item) => !itemsToRemove.includes(item))
        }
      })
    },
    // 新增输血申请单
    addBlood() {
      this.addType = 'add'
      this.bloodVisible = true
      this.getBaseInfo()
      // this.getBloodQuantityBySQDID()
      this.getXueKuBH({
        bingQuID: this.patientDetail.bingQuID
      })
      this.getInPatientBloodTypeByLB()
      this.getEnableAddAssayListByLeiXing()
      this.rowObj = {
        xypz: '', //血液品种
        xypzJL: 'ml', //血液品种计量
        xcf: '', //血成分
        sqsl: '', //申请数量
        xylx: '', //血液类型
        xylxJJ: '', //血液类型=急救
        beixue: '', //备血
        yxsx_abo: '', //异形输血abo
        yxsx_rh: '', //异形输血rh
        sxmd: '', //输血目的
        syssj: '', //实验室数据
        clbx: '', //临床表现
        beizhu: '', //备注
        kdsj: '' //开单时间
      }
      this.bloodInitList1 = [
        {
          yz_sxsl: '', //医嘱的输血数量
          yz_sfjj: false, //医嘱的是否紧急
          yz_bqxz: '', //医嘱的病区选择
          yz_drsj: '', //医嘱的导入时间
          yz_ztbz: '', //医嘱的状态标志
          yz_sxksp: '' //医嘱的输血科审批状态
        }
      ]
      this.BeiXue = []
      this.SXMD = []
      // this.typeAndRHObj = {}
      this.editFlag = false
    },
    formatDate(dateString) {
      try {
        return format(new Date(dateString), 'yyyy-MM-dd HH:mm')
      } catch (error) {
        return dateString
      }
    },
    // 新增输血医嘱
    addBloodOrder() {
      // this.bloodInitList1 = this.bloodInitList.splice(0, 1)
    },
    // 输血相关实验室指标导入
    laboratoryExport(item) {
      if (!this.rowObj.syssj) {
        this.rowObj.syssj = ''
      }

      if (!this.rowObj.syssj.includes(item.huaYanXMMC)) {
        this.rowObj.syssj += item.huaYanXMMC + item.huaYanJG + item.danWei + '；'
      }
    },
    // 保存输血申请单
    async addBloodTransSqd() {
      console.log(this.rowObj)
      let data = {}
      if (this.addType == 'add') {
        data = {
          abo: this.rowObj.yxsx_abo,
          rh: this.rowObj.yxsx_rh,
          shenQingDanID: '', //申请单ID (新增时可以为空)
          xueKuBH: this.XueKuBH.xueKuBH, //血库编号
          bingRenXM: this.patientDetail.bingRenXM, //病人姓名
          bingRenXB: this.patientDetail.xingBie, //病人性别：1男 2女
          bingRenBH: this.patientDetail.bingRenBH, //病人编号---------
          chuShengRQ: this.patientDetail.chuShengRQ, //出生日期
          bingLiID: this.patientDetail.bingLiID, //病历ID
          shuXueLY: this.SXMD.join(), //输血理由
          yiZhuLX: this.rowObj.xylx, //医嘱类型 1:急救用血、2:当日输血、3:72H内酌情输血
          shenQingSL: this.rowObj.sqsl, //申请数量
          jiLiangDW: this.rowObj.xypzJL, //计量单位
          chengFenDM: this.rowObj.xypz, //成分代码---------血液品种
          xueChengFeng: this.rowObj.xcf, //血成分--------------
          shiFouFZ: this.BeiXue.indexOf('辐照') >= 0 ? 1 : 0, //是否辐照 0否 1是
          ziTiSX: this.BeiXue.indexOf('自体储输') >= 0 ? 1 : 0, //自体输血 0否 1是
          quBaiCS:
            this.BeiXue.indexOf('一次滤白') >= 0 ? 1 : this.BeiXue.indexOf('二次滤白') >= 0 ? 2 : 0, //去白次数 0：未选；1：一次滤白(新生儿专用)；2：二次滤白
          tongXingSX: this.BeiXue.indexOf('异型输血') >= 0 ? 0 : 1, //同型输血 0否 1是
          kaiDanZKID: this.patientDetail.zhuanKeID, //开单专科ID----------
          shenFenZH: '', //身份证----------
          shuXueMD: this.SXMD.join(), //输血目的
          beiZhu: this.rowObj.beizhu, //备注
          zhuanKeID: this.patientDetail.zhuanKeID, //专科ID----------
          shenPiLB: '', //审批类别----------  clbx
          huaYanSJ: this.rowObj.syssj, //实验室数据
          linChuangBX: this.rowObj.clbx, //临床表现
          bloodAdviceList: [
            {
              shuXueYZID: null, //输血医嘱ID（新增时可以null）
              shenQingDanID: '', //申请单ID (新增时可以为空)
              bingQuID: this.bloodInitList1[0].yz_bqxz, //病区ID
              zhuanKeID: this.patientDetail.zhuanKeID, //专科ID
              shuXueSL: this.bloodInitList1[0].yz_sxsl, //输血数量
              jiLiangDW: this.rowObj.xypzJL, //计量单位
              shenQingYSID: this.patientInfo.yiShiYHID, //申请医生id
              chuangWeiHao: this.patientDetail.chuangWeiHao, //床位号
              bingLiID: this.patientDetail.bingLiID, //病历ID
              yiZhuLX: this.bloodInitList1[0].yz_sfjj == true ? 1 : 0,
              zhuangTaiBZ: '1', //状态标志 1 正常 0 暂停 9 自停
              yiZhuZT: '' //医嘱状态
            }
          ], //输血医嘱信息
          attachAssayAdvDtoList: this.selectedAddSurvey, //附加检验医嘱
          sxkHyjgVoList: this.sxkHyjgVoList, //化验结果
          yuanQuDM: this.XueKuBH.yuanQuDM, //院区代码
          zhenLiaoHDID: null, //诊疗活动ID
          xianBuMenID: null, //现部门id
          jiZhenSXLX: this.rowObj.xylxJJ //急诊输血类型
        }
        console.log(data)
        try {
          const res = await addBloodTransSqd(data)
          if (res.hasError === 0) {
            this.$message({
              message: '新增成功',
              type: 'success'
            })
            this.bloodVisible = false
            this.init()
          }
        } catch (error) {
          console.log(error)
        }
      } else if (this.addType == 'edit') {
        let bloodAdviceVo = this.bloodDetails.bloodAdviceVoList[0]
        data = {
          abo: this.rowObj.yxsx_abo,
          rh: this.rowObj.yxsx_rh,
          shenQingDanID: this.rowObj.shenQingDanID, //申请单ID (新增时可以为空)
          xueKuBH: this.XueKuBH.xueKuBH, //血库编号
          bingRenXM: this.patientDetail.bingRenXM, //病人姓名
          bingRenXB: this.patientDetail.xingBie, //病人性别：1男 2女
          bingRenBH: this.patientDetail.bingRenBH, //病人编号---------
          chuShengRQ: this.patientDetail.chuShengRQ, //出生日期
          bingLiID: this.patientDetail.bingLiID, //病历ID
          shuXueLY: this.SXMD.join(), //输血理由
          yiZhuLX: this.rowObj.xylx, //医嘱类型 1:急救用血、2:当日输血、3:72H内酌情输血
          shenQingSL: this.rowObj.sqsl, //申请数量
          jiLiangDW: this.rowObj.xypzJL, //计量单位
          chengFenDM: this.rowObj.xypz, //成分代码---------血液品种
          xueChengFeng: '全血', //血成分--------------
          shiFouFZ: this.BeiXue.indexOf('辐照') >= 0 ? 1 : 0, //是否辐照 0否 1是
          ziTiSX: this.BeiXue.indexOf('自体储输') >= 0 ? 1 : 0, //自体输血 0否 1是
          quBaiCS:
            this.BeiXue.indexOf('一次滤白') >= 0 ? 1 : this.BeiXue.indexOf('二次滤白') >= 0 ? 2 : 0, //去白次数 0：未选；1：一次滤白(新生儿专用)；2：二次滤白
          tongXingSX: this.BeiXue.indexOf('异型输血') >= 0 ? 0 : 1, //同型输血 0否 1是
          kaiDanZKID: this.patientDetail.zhuanKeID, //开单专科ID----------
          shenFenZH: '', //身份证----------
          shuXueMD: this.SXMD.join(), //输血目的
          beiZhu: this.rowObj.beizhu, //备注
          zhuanKeID: this.patientDetail.zhuanKeID, //专科ID----------
          shenPiLB: this.rowObj.shenPiLB, //审批类别----------  clbx
          huaYanSJ: this.rowObj.syssj, //实验室数据
          linChuangBX: this.rowObj.clbx, //临床表现
          bloodAdviceList: [
            {
              shuXueYZID: bloodAdviceVo.shuXueYZID, //输血医嘱ID（新增时可以null）
              shenQingDanID: bloodAdviceVo.shenQingDanID, //申请单ID (新增时可以为空)
              bingQuID: this.bloodInitList1[0].yz_bqxz, //病区ID
              zhuanKeID: bloodAdviceVo.zhuanKeID, //专科ID
              shuXueSL: this.bloodInitList1[0].yz_sxsl, //输血数量
              jiLiangDW: bloodAdviceVo.xypzJL, //计量单位
              shenQingYSID: this.patientInfo.yiShiYHID, //申请医生id
              chuangWeiHao: this.patientDetail.chuangWeiHao, //床位号
              bingLiID: this.patientDetail.bingLiID, //病历ID
              yiZhuLX: this.bloodInitList1[0].yz_sfjj == true ? 1 : 0,
              zhuangTaiBZ: this.bloodInitList1[0].yz_sfjj, //状态标志
              yiZhuZT: '' //医嘱状态
            }
          ], //输血医嘱信息
          attachAssayAdvDtoList: this.selectedAddSurvey, //附加检验医嘱
          sxkHyjgVoList: this.sxkHyjgVoList, //化验结果
          yuanQuDM: this.XueKuBH.yuanQuDM, //院区代码
          zhenLiaoHDID: null, //诊疗活动ID
          xianBuMenID: null, //现部门id
          jiZhenSXLX: this.rowObj.xylxJJ //急诊输血类型
        }
        console.log(data)
        try {
          const res = await updateBloodTransSqd(data)
          if (res.hasError === 0) {
            this.$message({
              message: '修改成功',
              type: 'success'
            })
            this.bloodVisible = false
            this.init()
          }
        } catch (error) {
          console.log(error)
        }
      }
    },
    // 获取输血申请单（住院）列表
    async listBloodTransSqd() {
      try {
        const res = await listBloodTransSqd({
          // bingLiID: Number(this.bingLiID),
          bingLiID: Number(this.patientDetail.bingLiID),
          bingRenBH: this.patientDetail.bingRenBH
        })
        if (res.hasError === 0) {
          this.bloodInitList = res.data
          // this.bloodInitList1 = [{
          //   yz_sxsl:'',//医嘱的输血数量
          //   yz_sfjj:'',//医嘱的是否紧急
          //   yz_bqxz:'',//医嘱的病区选择
          //   yz_drsj:'',//医嘱的导入时间
          //   yz_ztbz:'',//医嘱的状态标志
          //   yz_sxksp:'',//医嘱的输血科审批状态
          // }]
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 获取输血申请单详情
    async getSqdAndAdviceListByShenQingDanID(data) {
      try {
        const res = await getSqdAndAdviceListByShenQingDanID(data)
        if (res.hasError === 0) {
          // 1.获取输入框的数据
          this.bloodDetails = JSON.parse(JSON.stringify(res.data))
          const responseData = JSON.parse(JSON.stringify(res.data))
          this.rowObj = {
            ...this.rowObj,
            xypz: responseData.chengFenDM,
            xcf: responseData.xueChengFeng,
            sqsl: responseData.shenQingSL,
            xylx: responseData.yiZhuLX,
            // xylxJJ: responseData.jiZhenSXLX,
            xypzJL: responseData.jiLiangDW,
            syssj: responseData.huaYanSJ,
            clbx: responseData.linChuangBX,
            kdsj: format(new Date(responseData.shenQingSJ), 'yyyy-MM-dd HH:mm'),
            beizhu: responseData.beiZhu,
            yxsx_abo: responseData.abo,
            yxsx_rh: responseData.rh
          }
          // 2.获取多选框的数据
          const statusMap = {
            1: '医生刚申请',
            2: '护士站导入',
            3: '血库已接收',
            4: '已出库',
            5: '出库后已复核',
            6: '血库已运送',
            7: '辐照已接收',
            8: '已辐照',
            9: '辐照已运送',
            A: '病区已接收'
          }
          const statusMapSxkSp = {
            '01': '同意',
            '02': '不同意',
            '03': '同意部分发血',
            '04': '先发血后补批'
          }
          this.$set(this.bloodInitList1, 0, {
            ...responseData.bloodAdviceVoList[0],
            yz_sxsl: responseData.bloodAdviceVoList[0].shuXueSL,
            yz_sfjj: responseData.bloodAdviceVoList[0].yiZhuLX == 1 ? true : false,
            yz_bqxz: responseData.bloodAdviceVoList[0].bingQuID,
            yz_drsj: responseData.bloodAdviceVoList[0].daoRuSJ,
            yz_ztbz: statusMap[responseData.bloodAdviceVoList[0].zhuangTaiBZMC],
            yz_sxksp: statusMapSxkSp[responseData.bloodAdviceVoList[0].shenPiYJSXK]
          })
          // 3.更新BeiXue
          this.BeiXue = [
            ...(responseData.shiFouFZ == 1 ? ['辐照'] : []),
            ...(responseData.ziTiSX == 1 ? ['自体储输'] : []),
            ...(responseData.quBaiCS == 1
              ? ['一次滤白']
              : responseData.quBaiCS == 2
              ? ['二次滤白']
              : []),
            ...(responseData.tongXingSX == 0 ? ['异型输血'] : [])
          ]
          // 4.更新SXMD
          this.SXMD = responseData.shuXueMD.split(',')
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 输血医嘱基础数据
    async getBaseInfo() {
      try {
        const res = await getBaseInfo()
        if (res.hasError === 0) {
          this.xueYePZList = res.data.xueYePZ
          this.shuXueLXList = res.data.shuXueLX
          this.jiZhenSXLXMapList = Object.entries(res.data.jiZhenSXLXMap).map(([key, value]) => ({
            daiMa: parseInt(key),
            mingCheng: value
          }))
          this.xueXingList = res.data.xueXing
          this.rhxueXingList = res.data.rhxueXing
          this.xueYeLXList = Object.entries(res.data.xueYeLX).map(([key, value]) => ({
            daiMa: parseInt(key),
            mingCheng: value
          }))
          this.shuXueMDList = Object.entries(res.data.shuXueMD).map(([key, value]) => ({
            daiMa: parseInt(key),
            mingCheng: value
          }))
          this.yiZhuQXList = res.data.yiZhuQX
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 输血可开附加化验医嘱列表
    async getEnableAddAssayListByLeiXing(data) {
      try {
        const res = await getEnableAddAssayListByLeiXing({
          leiXing: 2
        })
        if (res.hasError === 0) {
          this.addSurveyInitData = res.data
          this.addSurveyData = Object.values(
            res.data.reduce((acc, item) => {
              const key = item.lianDongHao
              if (!acc[key]) {
                acc[key] = { lianDongHao: key, huaYanMC: [] }
              }
              acc[key].huaYanMC.push(item.huaYanMC)
              return acc
            }, {})
          ).map((g) => ({
            lianDongHao: g.lianDongHao,
            huaYanMC: g.huaYanMC.join(';') + ';'
          }))
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 查询血库血型
    async getBloodBankInventory(data) {
      try {
        const res = await getBloodBankInventory(data)
        if (res.hasError === 0) {
          this.oldHospitalData = res.data.laoYuanList
          this.newHospitalData = res.data.xinYuanList
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 根据申请单ID获取血袋数量
    async getBloodQuantityBySQDID(data) {
      try {
        const res = await getBloodQuantityBySQDID(data)
        if (res.hasError === 0) {
          this.BloodQuantity = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 根据病区ID获取院区和血库编号
    async getXueKuBH(data) {
      try {
        const res = await getXueKuBH(data)
        if (res.hasError === 0) {
          this.XueKuBH = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 获取右侧化验指标
    async getInPatientBloodTypeByLB(data) {
      try {
        const res = await getInPatientBloodTypeByLB({
          bingLiID: this.patientDetail.bingLiID,
          zhuYuanHao: this.patientDetail.zhuYuanHao
        })
        if (res.hasError === 0) {
          let arr1 = res.data.filter((item) => item.neiBuLB == 1)
          let arr2 = res.data.filter((item) => item.neiBuLB != 1)
          const mapping = {
            ABO正定型: 'abo',
            ABO反定型: 'abo_neg',
            ABO亚型: 'abo_yx',
            'RH(D)血型': 'rh',
            抗体筛查: 'ks',
            "Coomb's试验（直抗）": 'zhiKang',
            "Coomb's试验（间抗）": 'jianKang'
          }
          arr1.forEach((item) => {
            const key = mapping[item.huaYanXMMC]
            if (key) {
              this.typeAndRHObj[key] = item.huaYanJG
            }
          })
          this.labData = arr2
          this.sxkHyjgVoList = res.data
            .filter((item) => item.huaYanJG && item.huaYanJG !== '')
            .map((item) => ({
              huaYanDM: item.huaYanXMDM,
              huaYanJG: item.huaYanJG,
              tiaoXingMa: item.shenHeSJ
            }))
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 查看库存处理院区切换（重置分页可选）
    handleHospitalChange() {
      // 如果需要切换后重置到第一页，取消下方注释
      this.currentPagination.currentPage = 1
    },
    // 查看库存分页变更处理
    handleCurrentChange(val) {
      if (this.currentHospital === 'new') {
        this.newHospitalPagination.currentPage = val
      } else {
        this.oldHospitalPagination.currentPage = val
      }
    },
    // 查看库存分页
    handleSizeChange(val) {
      if (this.currentHospital === 'new') {
        this.newHospitalPagination.pageSize = val
      } else {
        this.oldHospitalPagination.pageSize = val
      }
      // 页数变化后重置到第一页
      this.handleCurrentChange(1)
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  // padding: 12px;
  background-color: #fff;
}

// 首页列表
.index-class {
  border: 1px solid #dcdfe6;
  padding: 10px 10px;
  height: calc(100vh - 218px);
  display: flex;
  flex-direction: column;

  .content {
    overflow-y: auto;
    background-color: #eff3fb;
    // height: 730px;
    padding: 10px;

    .content-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 1476px;
      margin-bottom: 8px;

      .title {
        position: relative;
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        margin-left: 9px;
      }

      .title::before {
        position: absolute;
        left: -9px;
        width: 3px;
        height: 14px;
        content: '';
        background-color: #356ac5;
      }

      // .table {
      // ::v-deep .el-button {
      // color: #356ac5;
      // }
      // }
    }

    .button-with-divider {
      position: relative;
      padding-right: 12px;
      margin-right: 2px;
      font-weight: 100;
      // font-size: 12px;
    }

    .button-with-divider::after {
      content: '';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1px;
      height: 12px;
      background-color: #e0e0e0;
    }

    .button-with-divider:last-child::after {
      display: none;
    }
  }

  .bottom {
    width: 86.6%;
    background: #eaf0f9;
    position: fixed;
    bottom: 10px;
    padding: 10px 18px;
    border-radius: 6px;

    .title-tips {
      color: #171c28;
      font-size: 14px;
      font-weight: bold;
      margin-left: 5px;
    }

    .tips {
      color: #171c28;
      line-height: 22px;
      margin-top: 4px;
      font-size: 14px;
    }

    .bottom-tips {
      display: flex;
      color: #ff1e1e;
      font-size: 14px;
      margin-top: 4px;
      padding: 4px 10px;
      background: #fff;
      border: 1px solid #a2bae5;
      background-color: #e9f1ff;
      width: 60%;
    }
  }

  ::v-deep .is-active {
    color: #356ac5 !important;
    background: #eff3fb !important;
    font-weight: bold;
  }

  ::v-deep .el-tabs__item {
    background: #fff;
  }

  ::v-deep .el-radio__label {
    padding-left: 6px;
  }

  ::v-deep .el-pagination {
    margin-top: 8px;
  }

  .el-icon-warning {
    font-size: 16px;
    color: #356ac5;
  }
}

//新增输血单弹框
.blood-class {
  .blood-pop-class {
    display: flex;
    gap: 16px;
    margin: -14px -8px;
    height: calc(110vh - 280px);

    .index-box {
      border: 1px solid #dadee5;
      width: 60%;
      height: auto;
      overflow: visible;
      display: flex;
      flex-direction: column;
      max-height: 100%;
      height: 100%;
      overflow-y: hidden;

      .content {
        background-color: #fff;
        overflow-y: auto;
        flex: 1;
        min-height: 0;

        .extra-header {
          border: 1px solid #dadee5 !important;
          border-bottom: none !important;
        }

        .content-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid #dadee5;
          padding: 8px 16px;
          background: #fafbfc;

          .title {
            position: relative;
            color: #171c28;
            font-size: 14px;
            // line-height: 14px;
            font-weight: bold;
            margin-left: 12px;
          }

          .title::before {
            content: '';
            position: absolute;
            left: -10px;
            top: 4px;
            width: 3px;
            height: 12px;
            background-color: #356ac5;
          }
        }
      }

      .survey-box {
        background: #fafbfc;
        border: 1px solid #dadee5;
        border-bottom: none;
        padding: 8px 0;
      }

      .survey-header {
        border-bottom: none !important;
        padding: 0 16px !important;
      }

      .survey-describe {
        padding-left: 28px;
        font-size: 12px;
      }
    }

    .index-box:last-child {
      width: 40%;
      max-height: 100%;
      height: 100%;
      overflow-y: hidden;

      .content {
        overflow-y: auto;
        flex: 1;
        padding-right: 8px;
      }
    }
  }
}

.el-icon-warning-box {
  position: fixed;
  bottom: 20px;
  margin-top: 15px;
  display: flex;
  align-items: center;

  .el-icon-warning {
    font-size: 20px;
    color: #356ac5;
    margin-right: 4px;
  }
}

// 新增输血库弹框
.stock-class {
  ::v-deep .el-dialog {
    padding: 6px 10px;
  }

  ::v-deep .el-table--mini .el-table__cell {
    padding: 12px 0;
  }

  .dialog-footer {
    padding-bottom: 15px;
  }

  .radio-group {
    margin-top: -20px;
    margin-bottom: 4px;
  }

  ::v-deep .custom-radio.is-checked .el-radio__label {
    color: #171c28;
  }

  ::v-deep .el-dialog__header {
    // border-bottom: 1px solid #dadee6;
    padding: 10px 14px;
    font: var(--font-medium);
    font-size: var(--font-size-medium);
    color: #171c28;
    display: flex;
    align-items: center;
  }

  ::v-deep .el-dialog__header::before {
    content: url('~@/assets/images/info.png');
    // width: 3px;
    // height: 16px;
    // background: #356ac5;
    // margin-right: 6px;
    position: absolute;
    top: 5px;
    left: 6px;
    transform: scale(0.55);
  }

  ::v-deep .el-dialog__title {
    font-weight: bold;
    margin-left: 18px;
    opacity: 0.8;
  }

  ::v-deep .el-pager li {
    margin: 0 4px;
  }
}

// 暂停/删除弹框
.pop-class {
  .el-icon-warning {
    font-size: 20px;
    color: #ed6a0c;
  }

  .delete-component {
    padding-left: 35px;
    color: #96999e;

    a {
      color: #356ac5;
      font-weight: bold;
    }
  }
}

.no-header .cell {
  display: none;
}

// 新增输血单表格左侧
.left-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;

  td:nth-child(odd) {
    width: 14%;
  }

  td:nth-child(even) {
    width: 19.33%;
  }
}

// 新增输血单表格右侧
.right-table {
  td {
    // padding: 8px 14px !important;
    padding: 8px 10px !important;
  }

  td:nth-child(odd) {
    width: 22%;
  }

  td:nth-child(even) {
    width: 28%;
  }
}

// 新增输血单表格
.table-box {
  td {
    border: 1px solid #ddd;
    border-collapse: collapse;
    height: 35px;
    padding: 8px 8px;
  }

  .multi-td {
    height: 38px;
    line-height: 16px;
    width: 100% !important;
    padding: 2px 10px !important;
    // display: flex;
    // align-items: center;
    // text-align: left !important;
  }

  .td-with-divider {
    width: 50%;
    display: inline-block;
    position: relative;
  }

  .td-with-divider::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 24px;
    background-color: #e0e0e0;
    margin: 0 10px;
  }

  .td-with-divider:last-child::after {
    display: none;
  }

  .td-select {
    width: 66.2vh !important;
    display: inline-block;
    height: 100%;
    border: none;
    border-right: 1px solid #ddd;
  }

  ::v-deep .el-checkbox {
    margin-right: 14px;
    margin-bottom: 4px;
  }

  ::v-deep .el-checkbox:last-child {
    margin-right: 10px;
    margin-bottom: 0px;
  }

  .blood-order-class {
    width: 708px !important;
    display: inline-block;
    // max-height: 127px;
    // min-height: 48px;
    height: auto;
    padding: 4px;
    margin-left: -1px;
  }

  .info-label {
    // width: 20vw;
    text-align: right;
    background-color: #eaf0f9;

    .td-icon-warning-box {
      display: flex;
      justify-content: right;

      .td-icon-warning {
        font-size: 16px;
        color: #356ac5;
        margin-right: 4px;
        margin-top: 2px;
      }
    }
  }

  .info-value {
    // width: 14vw;
    background-color: #fff;
  }
}

.blood-class .table-box {
  width: 100%;
  overflow-x: auto;
}

.import-title {
  font-size: 12px;
  color: red;
  margin-left: 5px;
  cursor: pointer;
}

/* 优化滚动条体验 */
::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 0px;
}

::v-deep .el-dialog__body {
  max-height: 80vh;
  overflow-y: auto;
  // min-height: 60vh;
}

::v-deep .el-table .el-table__cell {
  padding: 2px 0;
  // font-size: 12px;
}

::v-deep .el-table th.el-table__cell.is-leaf {
  padding: 7px 0;
}

::v-deep .hide-empty-text .el-table__empty-block {
  display: none !important;
}

::v-deep .el-descriptions-row th {
  width: 22% !important;
  text-align: right !important;
  background: #eaf0f9 !important;
  padding: 8px 10px !important;
  border-color: #ddd !important;
  height: 32px !important;
}
::v-deep .el-descriptions-row td {
  width: 28% !important;
  padding: 8px 10px !important;
  height: 32px !important;
  max-height: 32px !important;
  color: red !important;
}
</style>
