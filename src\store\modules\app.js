import { getLanguage } from '@/lang/index'
import Cookies from 'js-cookie'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  language: getLanguage(),
  size: Cookies.get('size') || 'medium',
  isCurrentDataChange: false, // 页面数据是否变化
  saveCallback: undefined, // 页面数据变化 点保存 执行的回调函数
  isTagClose: false, //是不是tag触发

  attachSidebar: {
    opened: Cookies.get('attachSidebarStatus') ? !!+Cookies.get('attachSidebarStatus') : false,
    withoutAnimation: false
  },
  attachSidebarRoutes: [],
  attachSidebarBasePath: '',
  noNeedVerifyAppList: []
}

const mutations = {
  TOGGLE_SIDEBAR: (state) => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_ATTACHSIDEBAR: (state) => {
    state.attachSidebar.opened = !state.attachSidebar.opened
    state.attachSidebar.withoutAnimation = false
    if (state.attachSidebar.opened) {
      Cookies.set('attachSidebarStatus', 1)
    } else {
      Cookies.set('attachSidebarStatus', 0)
    }
  },
  CHANGE_ATTACHSIDEBAR: (state, bol) => {
    state.attachSidebar.opened = bol
    if (state.attachSidebar.opened) {
      Cookies.set('attachSidebarStatus', 1)
    } else {
      Cookies.set('attachSidebarStatus', 0)
    }
  },
  CLOSE_ATTACHSIDEBAR: (state) => {
    Cookies.set('attachSidebarStatus', 0)
    state.attachSidebar.opened = false
    state.attachSidebar.withoutAnimation = withoutAnimation
  },
  SET_ATTACHSIDEBARROUTES: (state, arr) => {
    state.attachSidebarRoutes = arr
  },
  SET_ATTACHSIDEBARBASEPATH: (state, str) => {
    state.attachSidebarBasePath = str
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_LANGUAGE: (state, language) => {
    state.language = language
    Cookies.set('language', language)
  },
  SET_SIZE: (state, size) => {
    state.size = size
    Cookies.set('size', size)
  },
  SET_CURRENT_DATA_CHANGE: (state, data) => {
    state.isCurrentDataChange = data
  },
  SET_SAVE_CALLBACK: (state, data) => {
    state.saveCallback = data
  },
  TAG_CHANGE(state, data) {
    state.isTagClose = data
  },
  SET_NO_NEED_VERIFY_APP_LIST(state, data) {
    state.noNeedVerifyAppList = data
  }
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleAttachSideBar({ commit }) {
    commit('TOGGLE_ATTACHSIDEBAR')
  },
  changeAttachSideBar({ commit }, bol) {
    commit('CHANGE_ATTACHSIDEBAR', bol)
  },
  closeAttachSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_ATTACHSIDEBAR', withoutAnimation)
  },
  setAttachSidebarRoutes({ commit }, arr) {
    commit('SET_ATTACHSIDEBARROUTES', arr)
  },
  setAttachSidebarBasePath({ commit }, str) {
    commit('SET_ATTACHSIDEBARBASEPATH', str)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setLanguage({ commit }, language) {
    commit('SET_LANGUAGE', language)
  },
  setSize({ commit }, size) {
    commit('SET_SIZE', size)
  },
  setCurrentDataChange({ commit }, { value, callback }) {
    commit('SET_CURRENT_DATA_CHANGE', value)
    // 保存 回调函数
    commit('SET_SAVE_CALLBACK', callback)
  },
  async useCallback({ state }) {
    state.saveCallback && (await state.saveCallback())
  },
  setTagClose({ commit }, data) {
    commit('TAG_CHANGE', data)
  },
  setNoNeedVerifyAppList({ commit }, data) {
    commit('SET_NO_NEED_VERIFY_APP_LIST', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
