<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>医技检查界面</title>
</head>
<body>
  
</body>

</html>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>医技检查界面</title>
  <meta name="description" content="医技检查界面">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <link rel="stylesheet" href="css/e-common.css">
  <!-- 页面CSS -->
  <link rel="stylesheet" href="css/e-style.css">
</head>

<style>
  /* 医技检查界面 */
  .Medical_check {
    position: relative;
    /* margin: 27px 20px 20px 20px; */
    margin-top: 27px;
    margin-left: 20px;
    margin-right: 20px;
  }
  /* 左侧树状结构 */
  .Medical_check_left_tree{
    position: relative;
    margin: 0px;
    padding: 0px;
  }
  .top_search {
    position: relative;
    width: 100%;
    border: 1px solid #707070
  }
  /* 右侧大内容 */
  .Medical_check_right_content {
    position: relative;
    height: 810px;
    border: 1px solid #C6C6C6;
    margin-left: 20px;
    margin-right: 20px;
    /* 可能刪 */
    margin-bottom: 20px; 
  }
  /* 右侧填写 */
  .right_content_left_fillout{
    position: relative;
    height: 810px;
    overflow-y:auto;
    /* overflow:auto */
  }
  .form_fillout_content{
    position: relative;
    margin: auto;
    margin-top: 20px;
  }
  .form_fillout_content>textarea{
    position: relative;
    vertical-align: top;
    width: 90.15%;
    margin-left: 12px;
    margin-right: 20px;
    border: 1px solid #BFBFBF;
    margin-bottom: 24px;
  }
  .form_fillout_content>input{
    position: relative;
    vertical-align: top;
    width: 90.15%;
    height: 40px;
    margin-left: 12px;
    margin-right: 20px;
    border: 1px solid #BFBFBF;
    margin-bottom: 24px;
  }
  /* 右侧列表 */
  .right_content_right_list{
    position: relative;
    /* 当内容超过这个高度就会显示滚轮 */
    height: 810px;
    overflow-y:auto;
    /* overflow:auto */
  }
  </style>

<body>
  <div class="Medical_check">
    <div class="Medical_check_left_tree col-md-2">
      <input class="top_search" type="text"/>
      <div class="below_ztree">
        222
      </div>
    </div>
    <div class="col-md-10">
      <div class="Medical_check_right_content">
        <div class="right_content_left_fillout col-md-8">
          <form class="form_fillout_content">
              <label>体检数据：</label>
              <textarea name="examination_data" id="examination_data" rows="4%"></textarea>
              

            
              <!-- <label>化验特检：</label>
              <input type="text">
              <label>检查项目：
                <div>
                  <div>
                    <label class="col=md=6"><input name="Fruit" type="checkbox" value="" />苹果 &npbs;123 </label> 
                    <label class="col=md=6"><input name="Fruit" type="checkbox" value="" />桃子 </label>
                  </div> 
                  <div class="col=md=6">
                    <label><input name="Fruit" type="checkbox" value="" />苹果 &npbs;123 </label> 
                    <label><input name="Fruit" type="checkbox" value="" />桃子 </label> 
                  </div> 
                </div>
              </label> -->
          </form>
        </div>
  
        <div class="right_content_right_list col-md-4">
          111
        </div>
      </div>
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <script src="js/e-common.js"></script>
  <!-- 登录页js文件 -->
  <!-- <script src="js/e-blnrcx.js"></script> -->
</body>
</html>
