/* stylelint-disable no-descending-specificity */

.el-container {
  height: 100%;

  .el-aside {
    width: var(--side-bar-width) !important;
    transition: transform 0.28s;

    &__collapse {
      width: 54px !important;
    }
  }

  .sidebar-mobile {
    .el-drawer {
      width: var(--side-bar-width) !important;
    }
  }
}

.side-container {
  height: 100%;
  overflow: hidden;
  background: $menuBg;

  .sidebar-title {
    color: $menuText;
  }

  .el-submenu__icon-arrow {
    color: $menuText;
  }

  .el-scrollbar {
    //height: calc(100% - 46px);
    height: 100%;
    overflow: hidden;

    .el-scrollbar__wrap {
      margin-right: -7px !important;
    }

    .change-icon {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: 40px;
      margin-left: 25px;
      font-size: 14px;
      line-height: 100%;
      color: $menuActive;
      text-align: center;
      cursor: pointer;

      .icon_box {
        width: 14px;
        height: 14px;
        font-size: 14px;
      }

    }

    .el-menu {
      background: $menuBg;
      border-right: 0;

      .el-menu-item {
        display: flex;
        gap: 10px;
        align-items: center;
        justify-content: flex-start;
        width: var(--side-bar-width - 6px);
        height: 40px;
        margin-left: 6px;
        line-height: 100%;
        transition: none;

        .el-img-box {
          display: flex;
          align-items: center;
          height: 100%;
          color: $menuText;

          .el-img {
            width: 14px;

            &::before {
              display: inline-block;
              width: 14px;
            }
          }
        }

        .menu-item-title {
          color: $menuText;
        }
        &:focus:not(.is-active){
          background-color: transparent;
        }
        &.is-attachSidebar-parent-click:not(.is-active){
          background: $menuHover;
          border-radius: 10px 0 0 10px !important;
        }
        &.is-attachSidebar-parent {
          &::before {
            display: none;
          }

          &::after {
            display: none;
          }
          &.is-attachSidebar-parent-active {
            background: $menuActive;
            border-radius: 10px 0 0 10px !important;
            &::before {
              position: absolute;
              top: -8px;
              right: 0;
              z-index: 1;
              display: block;
              width: 8px;
              height: 8px;
              content: '';
              background: radial-gradient(
                circle at 0% 0%,
                rgb(255 255 255 / 0%) 0%,
                rgb(255 255 255 / 0%) 70%,
                $menuActive 71%,
                $menuActive 0%
              );
            }

            &::after {
              position: absolute;
              right: 0;
              bottom: -8px;
              z-index: 1;
              display: block;
              width: 8px;
              height: 8px;
              content: '';
              background: radial-gradient(
                circle at 0% 100%,
                rgb(255 255 255 / 0%) 0%,
                rgb(255 255 255 / 0%) 70%,
                $menuActive 71%,
                $menuActive 0%
              );
            }
          }
        }
        &.is-active {
          position: relative;
          background: $menuActive;
          border-radius: 10px 0 0 10px !important;

          .el-img-box {
            color: $menuActiveText;
          }

          &::before {
            position: absolute;
            top: -8px;
            right: 0;
            z-index: 1;
            width: 8px;
            height: 8px;
            content: '';
            background: radial-gradient(circle at 0% 0%,
              rgb(255 255 255 / 0%) 0%,
              rgb(255 255 255 / 0%) 70%,
              $menuActive 71%,
              $menuActive 0%);
          }

          &::after {
            position: absolute;
            right: 0;
            bottom: -8px;
            z-index: 1;
            width: 8px;
            height: 8px;
            content: '';
            background: radial-gradient(circle at 0% 100%,
              rgb(255 255 255 / 0%) 0%,
              rgb(255 255 255 / 0%) 70%,
              $menuActive 71%,
              $menuActive 0%);
          }

          .menu-item-title {
            color: $menuActiveText;
          }
        }

        &:hover:not(.is-active) {
          background: $menuHover;
          border-radius: 10px 0 0 10px !important;
        }

        * {
          vertical-align: bottom;
        }
      }

      .el-submenu {
        background: $menuBg;

        .el-submenu__title {
          display: flex;
          gap: 10px;
          align-items: center;
          width: var(--side-bar-width - 6px);
          height: 40px;
          margin-left: 6px;
          line-height: 100%;
          transition: none;

          .el-img-box {
            display: flex;
            align-items: center;
            height: 100%;
            color: $menuText;

            .el-img {
              width: 14px;

              &::before {
                display: inline-block;
                width: 14px;
              }
            }
          }

          .menu-item-title {
            color: $menuText;
          }

          &:hover {
            background: $subMenuHover;
            border-radius: 10px 0 0 10px !important;
          }
        }

        .el-menu {
          padding: 6px 0;
          background: $subMenuBg;

          .el-menu-item {
            min-width: auto;
            padding: 0 20px !important;
          }

        }

        &.is-active:not(.is-opened) {
          .el-submenu__title {
            background: $menuActive;
            border-radius: 10px 0 0 10px !important;

            .el-submenu__icon-arrow {
              color: $menuBg;
            }


            .menu-item-title {
              color: $menuActiveText;
            }

            .el-img-box {
              color: $menuActiveText;
            }

            &::before {
              position: absolute;
              top: -8px;
              right: 0;
              z-index: 1;
              width: 8px;
              height: 8px;
              content: '';
              background: radial-gradient(circle at 0% 0%,
                rgb(255 255 255 / 0%) 0%,
                rgb(255 255 255 / 0%) 70%,
                $menuActive 71%,
                $menuActive 0%);
            }

            &::after {
              position: absolute;
              right: 0;
              bottom: -8px;
              z-index: 1;
              width: 8px;
              height: 8px;
              content: '';
              background: radial-gradient(circle at 0% 100%,
                rgb(255 255 255 / 0%) 0%,
                rgb(255 255 255 / 0%) 70%,
                $menuActive 71%,
                $menuActive 0%);
            }
          }
        }
      }

      &--collapse {
        width: 100%;

        .el-menu-item {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 !important;
          margin-left: 0;

          .el-tooltip {
            display: flex !important;
            align-items: center;
          }
        }

        .el-submenu {
          .el-submenu__title {
            margin-left: 0;

            .menu-item-title {
              display: none;
            }
          }
        }
      }
    }
  }
}

.side-container__collapse {
  .el-submenu__icon-arrow {
    display: none;
  }

  .el-scrollbar {
    .change-icon {
      justify-content: center;
      margin-left: auto;
    }
  }
}

.el-menu--vertical {
  .el-menu {
    background: $menuBg;

    .el-menu-item {
      height: 40px;
      line-height: 40px;

      .menu-item-title {
        color: $menuText;
      }

      &:hover:not(.is-active) {
        background: $menuHover;
      }

      &.is-active {
        background: $menuActive;

        .menu-item-title {
          color: $menuActiveText;
        }
      }
    }
  }

  &__nightMode {
    .el-menu {
      background: $menuBgNight;

      .el-menu-item {
        .menu-item-title {
          color: $menuTextNight;
        }

        &:hover:not(.is-active) {
          background: $menuHoverNight;
        }

        &.is-active {
          background: $menuActiveNight;

          .menu-item-title {
            color: $menuActiveTextNight;
          }
        }
      }
    }
  }
}

.popper-submenu > .el-menu--popup {
  display: grid;
  grid-template-columns: repeat(3, 1fr);

  &::before,
  &::after {
    display: none;
  }

  li.el-menu-item,
  .el-submenu .el-submenu__title {
    color: rgba(23, 28, 40, 0.65);
    font-size: 14px;
    &:hover {
      color: #356ac5;
    }
  }
}


// /* 定义动画 */
@keyframes change-icon-arrow {
  to {
    opacity: 1;
  }
}

@mixin changewidth($navFontSize) {

  /* stylelint-disable-next-line rule-empty-line-before */
  li.el-menu-item,
  .el-submenu .el-submenu__title {
    font-size: $navFontSize;
  }
}

// @media screen and (width <=480px) {
//   @include changewidth(10px);

//   :root {
//     --side-bar-width: 144px;
//   }
// }

// @media screen and (width <=1279px) and (width >=480px) {
//   @include changewidth(10px);

//   :root {
//     --side-bar-width: 144px;
//   }
// }

// @media screen and (width <=1439px) and (width >=1280px) {
//   @include changewidth(12px);

//   :root {
//     --side-bar-width: 192px; // 144.64
//   }
// }

/* stylelint-disable-next-line media-feature-range-notation */
@media screen and (max-width: 1919px) {
  @include changewidth(14px);

  :root {
    --side-bar-width: 220px;
  }
}

/* stylelint-disable-next-line media-feature-range-notation */
@media screen and (min-width: 1920px) {
  @include changewidth(16px);

  :root {
    --side-bar-width: 235px;
  }
}
