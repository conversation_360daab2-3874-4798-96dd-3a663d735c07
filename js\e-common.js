//插件引用
document.write("<script src='./lib/jquery/jquery.min.js'></script>"); //JQuery
document.write("<script src='./lib/jquery/jquery.easyui_ehrsz.min.js'></script>"); //easyui_ehrsz
document.write("<script src='./lib/jquery/easyui-lang-zh_CN.js'></script>"); //easyui_ehrsz
document.write("<script src='./lib/My97DatePicker/WdatePicker.js'></script>"); //easyui_ehrsz
document.write("<script src='./lib/bootstrap/js/bootstrap.min.js'></script>"); //Bootstrap
document.write("<script src='./lib/bootstrap/js/bootstrap-hover-dropdown.js'></script>"); //Bootstrap
document.write("<script src='./lib/bootstrap/js/BootstrapMenu.min.js'></script>"); //右键菜单
document.write("<script src='./lib/underscore/underscore-min.js'></script>"); //underscore
document.write("<script src='./lib/iziModal/js/iziModal.min.js'></script>"); //对话框
document.write("<script src='./lib/toastr/toastr.min.js'></script>"); //提示框
document.write("<script src='./js/e-common.view.js'></script>"); //view视图插件
document.write("<script src='./js/e-interface.au.js?v=" + Date.now() + "'></script>"); //au接口
document.write("<script src='./js/e-interface.ehrsz.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.ehrTx.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.BrMainLeft.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.blnrcx.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.blcxgj.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.lclj.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.yz_sz.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.brtwdlb.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.blwslist.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.bqxz.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script type='text/javascript' src='js/e-interface.ycwsp.js?v=" + Date.now() + "'><\/script>"); //接口
document.write("<script src='./js/e-interface.zqjllistall.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.zytjsqdyzmain.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.hfltzb.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.nutriOrderList.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.pnOrderList.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.yytb.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.blnrjghcxNew.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.kyzbcx.js?v=" + Date.now() + "'></script>"); //接口
document.write("<script src='./js/e-interface.cwYyyzNew.js?v=" + Date.now() + "'></script>"); //接口
// document.write("<script src='./js/datajson.js'></script>") //测试数据
WRT_e.ui = {
  modelEls:[],
  message: function (o) { //对话确认框
    let {
      type, //类型info success error confirm warning
      title, //标题
      content, //内容
      onOk, //确认回调函数
      onCancel, //取消回调函数
      okText, //取消回调函数
      cancelText, //取消回调函数
      width,
      closeOnEscape
    } = o
    //新建
    $("body").append('<div class="e_message"></div>') //添加modal DOM
    var model = $('body>.e_message:not(.iziModal)').iziModal({ //初始化modal
      width: (width || 400), //宽度
      overlayClose: false, //点击遮罩关闭框
      transitionIn: 'fadeInDown',
      transitionOut: 'fadeOutUp',
      closeOnEscape:closeOnEscape
    })
    //type类型
    var type_icon = 'glyphicon-info-sign'
    switch (type) {
      case 'info':
        var type_icon = 'glyphicon-info-sign'
        break;
      case 'success':
        type_icon = 'glyphicon-info-ok-sign'
        break;
      case 'error':
        type_icon = 'glyphicon-remove-sign'
        break;
      case 'confirm':
        type_icon = 'glyphicon-question-sign'
        break;
      case 'warning':
        type_icon = 'glyphicon-warning-sign'
        break;
    }
    //配置modal
    model.iziModal('setContent',
      `<div class="izi_message">
          <div class="izi_message_body">
            <i class="glyphicon ${type_icon}"></i>
            <span class="izi_message_body_title">${title}</span>
            <div class="izi_message_body_content">${content}</div>
          </div>
          <div class="izi_message_btn text-right">
            <button type="button" class="btn btn-primary" name="confirm" autofocus>${okText||'确认'}</button>
            ${onCancel?`<button type="button" class="btn btn-default" name="close">${cancelText||'取消'}</button>`:""}
          </div>
        </div>`);
    model.iziModal('open')
    this.modelEls.push(model.find(".btn-primary"));
    model.find(".btn-primary").focus();
    $(".izi_message_btn").keyup(function(event){
      switch(event.keyCode){
        case 37://左
        model.find(".btn-default").focus();
        break;
        case 37://左
        model.find(".btn-primary").focus();
      }
    })
    //绑定事件
    model.find('.izi_message_btn>button').click(e => {
      this.modelEls.splice(this.modelEls.length-1,1);
      if(this.modelEls.length!=0)
      this.modelEls[this.modelEls.length-1].focus();
      if (e.target.name == 'close') {
        if (onCancel) onCancel()
      } else {
        if (onOk) onOk()
        $(".iziModal-overlay").css("display","none")
      }
      model.iziModal('destroy')
      setTimeout(() => model.remove(), 100)
    })
  },
  hint: function (o) { //信息提示框 type包括：info warning success error
    let {
      type = 'info',
        msg
    } = o
    toastr[type](msg)
  },
  model: function (o) { //弹出模拟框
    let {
      id, //modal的ID
      title, //标题
      width, //宽
      iframe, //是否为iframe
      iframeHeight, //高
      iframeURL, //iframe链接
      content,
      onOk, //确认回调
      onCancel, //取消回调
      closeButton,
      closeOnEscape,
      // onClosing,
      // onClosed,
      onOpened,
    } = o
    if ($(`#${id}`).length == 0) {
      $("body").append(`<div id="${id}"></div>`) //添加modal DOM
    }
    var model = $(`#${id}`).iziModal({ //初始化modal
      overlayClose: false, //点击遮罩关闭框
      title: title,
      width: width || 520,
      iframe: iframe || false, //iframe嵌套
      iframeHeight: iframeHeight || 400,
      iframeURL: iframeURL,
      closeButton: closeButton,
      closeOnEscape:closeOnEscape,
      onOpened:onOpened
    })
    //配置modal
    model.iziModal('setContent',
      `<div class="izi_modal">
        <div class="izi_modal_content">
          ${content}
        </div>
        ${onCancel?`<div class="izi_modal_btn text-right"><button type="button" class="btn btn-default btn-sm" name="close">取消</button></div>`:''}
        ${onOk?`<div class="izi_modal_btn text-right"><button type="button" class="btn btn-primary btn-sm" name="confirm">确认</button></div>`:''}
      </div>`);
    model.iziModal('open')
    //绑定事件
    model.find('.izi_modal_btn>button').click(e => {
      if (e.target.name == 'close') {
        if (onCancel) onCancel()
        model.iziModal('destroy')
      } else {
        //点击确认把model对象传出去由外进行关闭
        if (onOk) onOk(model)
      }
    })
  },
}
function result_token(){
  'https://wsyy.wzhospital.cn/web/auth/#/resetToken?app_key=&app_key=3d6yugnzd8xyf9fqe0oh4gyjuyj3ced46yamxh3ix3cyx0ftyybdvuj4737qz475'
}

//格式化日期
Date.prototype.Format = function (fmt) { //author: meizz
  var o = {
    "M+": this.getMonth() + 1, //月份
    "d+": this.getDate(), //日
    "H+": this.getHours(), //小时
    "m+": this.getMinutes(), //分
    "s+": this.getSeconds(), //秒
    "q+": Math.floor((this.getMonth() + 3) / 3), //季度
    "S": this.getMilliseconds() //毫秒
  };
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  for (var k in o)
    if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
  return fmt;
}


