<template>
  <a :href="url" target="_blank">{{ column.label }}</a>
</template>

<script>
export default {
  name: 'TableLink',
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    column: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      url: ''
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      if (this.column.value === 'biHuanDZ') {
        this.url = this.row.biHuanDZ
      }
      if (this.column.value === 'buLiangFYSB') {
        this.url = this.row.buLiangFYSB
      }
    }
  }
}
</script>

<style lang="scss" scoped>
a {
  color: #409eff;
}
</style>
