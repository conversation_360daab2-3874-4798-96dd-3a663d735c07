<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>儿科营养医嘱单</title>
  <meta name="description" content="儿科营养医嘱单">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
  #nbPNCalculator, .nbPNCalculatorMain, .nbTBinfoR, .nbPNCTCAll {
    text-align: center;
    margin: auto;
  }
  .nbPNCalculatorMain {
    padding-top: 50px;
  }
  /* 基础信息 */
  .nbTBinfoR {
    font-family: 'Microsoft YaHei';
    width: 96%;
    /* width: 99%; */
    height: 60px;
    /* line-height: 60px; */
    padding: 0 20px;
    border: 1px solid #E6E6E6;
    border-radius: 8px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
  }
  .nbTBinfoR > span {
    padding-right: 20px;
  }
  .tBinfoBold {
    font-weight: bolder;
  }
  .tBinfoBcNum {
    font-family: 'Microsoft YaHei';
    font-style: normal;
    font-weight: 400;
    /* line-height: 22px; */
    color: #215FEB;
    text-align: center;
    background: #C4D2F2;
    border-radius: 4px;
  }
  /* 第2部分外框 */
  .nbPNCTCAll {
    font-family: 'Microsoft YaHei';
    width: 96%;
    /* width: 99%; */
    padding: 1%;
    border: 1px solid #E6E6E6;
    border-radius: 8px;
    margin-bottom: 10px;
    /* display: flex;
    flex-direction: column; */
    /* display: flex;
    justify-content: space-between;
    align-items: center; */

  }
  .nbPNC {
    /* display: flex;
    flex-direction: column; */
    /* flex-wrap: wrap; */
    padding: 10px 0 20px;
  }
  /* 计算器标题 */
  .nbPNCTitle {
    font-weight: 700;
    font-size: 15px;
    text-align: left;
    padding-bottom: 5px;
  }
  /* 计算器列表 */
  .nbPNCList {
    position: relative;
    width: 100%;
    display: flex;
    /* border: 1px solid #E6E6E6;
    border-radius: 8px;
    display: flex;
    justify-content: space-evenly; */
  }
  .list1, .list2, .list3 {
    padding: 15px 0px;
    width: 33.33%;
  }
  .list1 {
    position: relative;
    /* width: 33%; */
    /* border-right: 1px solid #E6E6E6; */
    border: 1px solid #E6E6E6;
    border-radius: 8px 0 0 8px;
  }
  .list2 {
    position: relative;
    /* width: 33%; */
    /* border-right: 1px solid #E6E6E6; */
    border: 1px solid #E6E6E6;
  }
  .list3 {
    position: relative;
    /* width: 33%; */
    border: 1px solid #E6E6E6;
    border-radius: 0 8px 8px 0;
  }
  .form-group {
    display: flex;
    /* align-items: center; */
    align-items: baseline;
    justify-content: flex-start;
    margin-bottom: 10px;
    padding: 0 15px;
    /* font-size: 12px; */
  }
  .form-group >label {
    /* padding-right: 10px; */
    width: 225px;
    /* padding-right: 4px;
    font-size: 12px; */

    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  /* 表单输入框 */
  .form-control {    
    width: 64px;
    /* width: 40px !important; */
    /* height: 26px;
    padding: 3px 5px; */
    height: 22px;
    padding: 5px;
    border: 1px solid #767676;
    /* border-radius: 4px; */
    cursor: none !important;
  }
  .inputLine {
    display: flex; 
    align-items: center; 
    flex-direction: row;
  }
  
  .btn_save {
    
    border: 1px solid #fff;
    background: #489CFF;
    color: #fff;
    padding: 2px 15px;
    border-radius: 4px;
    cursor: pointer;
    /* height: 32px;
    line-height: 32px;
    padding: 0 25px; */
    /* height: 26px;
    line-height: 24px;
    padding: 0 20px;
    border: 1px solid  #489CFF;
    box-sizing: border-box;
    border-radius: 4px;
    background:  #489CFF;
    color: #fff;
    cursor: pointer;
    transition: 0.2s; */
  }
  
  .btn_save:hover {
    color: #fff;
  }
  
  /* .btn:hover {
    color: #fff;
  } */
  /* .btn {
    color: #fff;
  } */

  .Btn_nrInput {
    border: 1px solid #767676;
    border-radius: 4px;
  }
  .tipIcon {
    color: #A3BEFF;
    float: right;
    font-size: 20px;
    padding: 0 10px;
    cursor: pointer;
  }
  /* 相关检查项（表格+ 图片） */
  .nbPNTableAndChart {
    /* background: #F9F9FB; */
    background: #f4f4f4;
    /* background: #818181; */
    border-radius: 8px;
  }
  .nbPNTableContect {
    padding-bottom: 15px;
  }
  /* 表格 */
  .nbPNTable, .nbPNChart {
    padding: 10px 20px;
  }
  .nbPNTableTitle, .nbPNChartTitle {
    text-align: left;
    font-weight: 700;
    font-size: 15px;
  }
  .show_Table {
    width: 100%;
    margin: auto;
    border: 1px solid #767676;
    /* border: 1px solid #EBEBEB; */
  }
  .show_Table .table-nr{
    padding: 2px 5px;
  }
  /* 图 */
  .nbPNChart {
    background: #fff;
    /* background: red; */
    border-radius: 8px;
  }
</style>
<body id="nbPNCalculator">
  <div class="nbPNCalculatorMain">
    <!-- 基础信息 -->
    <div class="nbPNBaseInfo"></div>
    <!-- 计算器 + 相关检查项（表格 + 图片） -->
    <div class="nbPNCTCAll">
      <!-- 计算器 -->
      <div class="nbPNC">
        <div class="nbPNCTitle">计算器</div>
        <div class="nbPNCList">
          
        </div>
      </div>
      <!-- 相关检查项（表格+ 图片） -->
      <div class="nbPNTableAndChart">
        <!-- 相关检验项目 + 表格 -->
        <!-- <div class="nbPNTable">
          <div class="nbPNTableTitle">相关检验项目</div>
          <div class="nbPNTableContect"></div>
        </div> -->
        <!-- 血糖标准天 + 图片 -->
        <!-- <div class="nbPNChart"></div> -->

        <div class="nbPNTable">
          <div class="nbPNTableTitle">相关检验项目</div>
          <div class="nbPNTableContect"></div>
          <div class="nbPNChart">
            <div class="nbPNChartContect" id="nbPNChartContect" style="height:300px;width: 100%;">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <script src="./lib/echarts/echarts.min.js"></script>
  <script src="./lib/charts/echarts.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面JS -->
  <!-- <script src='js/e-EhrSz.js'></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-nbPNCalculator.js'><\/script>");
  </script>
</body>
</html>