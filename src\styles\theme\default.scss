/* 公共样式参数
-------------------------- */
// 元素之间公共间距
$--common-margin: 16px !default;
// 元素公共内边距
$--common-padding: 8px !default;

// 表单名称与值之间的间距
$--label-value-margin: 6px !default;
// 主色调
$--color-primary: #409EFF !default;
// 成功颜色
$--color-success: #67C23A !default;
/// 警告颜色
$--color-warning: #E6A23C !default;
// 错误颜色
$--color-danger: #F56C6C !default;
/// 通知颜色
$--color-info: #909399 !default;
// 基础字体颜色
$--font-color-base: #171C28;
// disabled 颜色
$--disabled-color: rgba($--font-color-base, 0.05) !default;
/// placeholder 字体颜色
$--color-text-placeholder: rgba($--font-color-base, 0.45) !default;
// 控件内部字体颜色
$--font-color-inner: #fff;
// 正文字体颜色
$--font-color-regular: rgba($--font-color-base, 0.85) !default;
// 正文字体大小
$--font-size-regular: 14px !default;
// 正文字体
$--font-family-regular: sourcehansanscn-regular,
sourcehansanscn !default;
// 正文字体粗细
$--font-weight-regular: 400 !default;
// 加粗字体颜色
$--font-color-medium: rgb(0 0 0 / 85%) !default;
// 加粗字体大小
$--font-size-medium: 16px !default;
// 加粗字体
$--font-family-medium: sourcehansanscn-medium,
sourcehansanscn !default;
// 加粗字体粗细
$--font-weight-medium: 500 !default;
// 默认边框宽度
$--border-width-base: 1px !default;
// 默认边框颜色
$--border-color-base: #DADEE5 !default;
// 公共圆角大小
$--border-radius-base: 4px !default;

// 表头背景色
$--table-header-background-color: rgb(46 85 237 / 5%) !default;
// 表格当前行颜色
$--table-current-row-background-color: rgb(46 85 237 / 5%) !default;
// 表格单元格上下内边距
$--table-cell-padding-vertical: 6px;
// 表格单元格左右内边距
$--table-cell-padding-horizontal: 10px;
// 表格斑马纹颜色
$--table-stripe-color: #EFF3FB;

// 表单label字体颜色
$--form-label-text-color: rgba($--font-color-base, 0.65) !default;
// 表单输入元素宽度
$--form-item-content-width: 284px !default;
// 表单输入框高度
$--form-item-content-height: 32px !default;
// 小宽度弹窗
$--dialog-width-mini: 350px !default;
// 小宽度弹窗
$--dialog-width-small: 480px !default;
// 中等宽度弹窗
$--dialog-width-medium: 850px !default;
// 大宽度弹窗
$--dialog-width-large: 1000px !default;

// 内容区域固定高度
$--content-header-height: 179px !default;

@import "../element-variable";
@import "../index";
