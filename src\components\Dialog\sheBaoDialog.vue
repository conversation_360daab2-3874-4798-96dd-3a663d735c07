<template>
  <div>
    <el-dialog
      :title="dialogType === 'new' ? '医保限制支付范围提示' : '审批提示'"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
      custom-class="shen-pi-dialog"
    >
      <div class="carousel-content">
        <el-descriptions class="custom-descriptions" :column="1" border>
          <el-descriptions-item label="药品名称：">
            <div class="name-box">
              <span>{{ item.mingCheng }}</span>
              <el-button v-if="dialogType === 'new'" @click="openHistoryXDFW">
                历史限定范围查询
              </el-button>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="医保使用限制范围：">
            <el-radio-group
              v-if="dialogType === 'new'"
              v-model="radioValue"
              size="mini"
              class="xian-ding-item"
            >
              <el-radio v-for="(i, index) in item.xiangDingFW" :key="index" :label="i">
                <span :style="{ color: index === item.xiangDingFW.length - 1 ? 'red' : '' }">
                  {{ i }}
                </span>
              </el-radio>
            </el-radio-group>
            <div v-else class="limit-box">{{ item.limit }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="病人诊断：">
            <div class="diagnose-box">{{ item.diagnose }}</div>
          </el-descriptions-item>
        </el-descriptions>
        <div v-if="dialogType === 'new'">
          <el-row class="content-row">
            <el-col class="error-text">注：请根据患者病情如实勾选并在病历中体现！</el-col>
            <el-col>
              <div class="button-container">
                <el-button type="primary" @click="handleSubmit">确 定</el-button>
                <el-button @click="closeDialog">取 消</el-button>
              </div>
            </el-col>
          </el-row>
        </div>
        <div v-else>
          <!-- 类型1：符合条件/自费选择 -->
          <el-row v-if="item.type === '1'" class="content-row">
            <el-col class="center-content margin-top-16">
              <div>
                <el-button :type="ziFei === '0' ? 'primary' : ''" @click="handleSubmit('0')">
                  我知道了，符合条件
                </el-button>
                <el-button
                  :type="ziFei === '1' ? 'primary' : ''"
                  style="margin-left: 32px"
                  @click="handleSubmit('1')"
                >
                  不符合条件，自费
                </el-button>
              </div>
            </el-col>
            <el-col>
              <div class="error-text">
                务必与医保使用范围相符，如选择自费，请务必做好患者自费知情同意工作。
              </div>
            </el-col>
          </el-row>
          <!-- 类型2：审批记账/自费选择 -->
          <el-row v-if="item.type === '2'" class="content-row">
            <el-col class="notice-text">
              该项目须审批后才能记账。选中后该项目会自动插入审批队列。等待【社保窗口】审批。
            </el-col>
            <el-col class="center-content">
              <el-radio-group v-model="ziFei">
                <el-radio label="0">审批记账</el-radio>
                <el-radio label="1">自费</el-radio>
              </el-radio-group>
            </el-col>
            <el-col>
              <div class="button-container">
                <el-button type="primary" @click="handleSubmit">确 定</el-button>
                <el-button @click="closeDialog">取 消</el-button>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getHistoryXDFW } from '@/api/inpatient-order'
export default {
  name: 'SheBaoDialog',
  props: {
    // 药品审批只有一条
    displayList: {
      type: Array,
      default: () => []
    },
    visible: {
      type: Boolean,
      default: false
    },
    // 弹窗类型，默认老弹窗
    dialogType: {
      type: String,
      default: 'old'
    },
    // 选中的药品
    sheBaoYaoPinData: {
      type: Object,
      default: () => {}
    },
    inpatientInit: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      ziFei: '',
      radioValue: '',
      // 限定范围弹窗
      historyXDFWDialog: false
    }
  },
  computed: {
    item() {
      return this.displayList[0] || {}
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.ziFei = '0'
      }
    }
  },
  methods: {
    openHistoryXDFW() {
      this.historyXDFWDialog = true
    },
    getHistoryXDFW(kaiShiSJ, jieShuSJ) {
      getHistoryXDFW({
        bingAnHao: this.inpatientInit.inPatientVo?.bingAnHao,
        jieShuSJ: jieShuSJ,
        kaiShiSJ: kaiShiSJ,
        waiGouYP: '',
        yaoPinID: this.sheBaoYaoPinData.daiMa
      }).then((res) => {
        if (res.hasError === 0) {
          console.log(res)
        }
      })
    },
    handleSubmit(value) {
      if (this.dialogType === 'old') {
        this.ziFei = value
      } else {
        if (!this.radioValue) {
          this.$message.error('请选择医保使用限制范围')
          return
        }
        if (this.radioValue === '均不符合，需自费，请告知患者') {
          this.ziFei = '1'
        } else {
          this.ziFei = '0'
        }
      }
      this.$emit('confirm', this.ziFei)
      this.dialogVisible = false
    },
    closeDialog() {
      this.dialogVisible = false
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss">
.shen-pi-dialog {
  .el-icon-close.el-icon-close {
    display: none;
  }

  .el-dialog {
    &__header {
      border-bottom: 1px solid #dadee6;
      padding: 10px 14px;
      font: var(--font-medium);
      font-size: var(--font-size-medium);
      color: #171c28;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        width: 3px;
        height: 16px;
        background: #356ac5;
        margin-right: 6px;
      }

      .el-dialog__headerbtn {
        font-size: 19px;
        right: 14px;
        top: auto;

        .el-dialog__close {
          color: #8590b3;
        }
      }
    }
  }

  .el-carousel__container {
    height: 100%;
  }

  .carousel-content {
    position: relative;
    margin-right: 8px;
    border: 1px solid #dadee6;
    padding: 10px 10px 0;
    height: 100%;

    .custom-descriptions {
      .el-descriptions-item__label {
        width: 160px;
        text-align: right;
        font-weight: 600;
        padding-right: 0;
      }
      .name-box {
        height: 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .xian-ding-item {
        display: flex;
        flex-direction: column;
        height: 90px;
        overflow-y: auto;

        .el-radio {
          margin: 0;
        }
      }

      .limit-box {
        height: 90px;
        overflow-y: auto;
      }

      .diagnose-box {
        height: 100px;
        overflow-y: auto;
      }
    }
  }

  .content-row {
    margin-bottom: 5px;
  }

  .center-content {
    display: flex;
    justify-content: center;

    &.margin-top-16 {
      margin-top: 16px;
    }
    .el-button {
      width: 145px;
    }
  }

  .error-text {
    color: red;
    padding-top: 16px;
  }

  .notice-text {
    text-align: center;
    color: #f1924e;
  }

  .button-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
    .el-button {
      width: 120px;
    }
  }
}
</style>
