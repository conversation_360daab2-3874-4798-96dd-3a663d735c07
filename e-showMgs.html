<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title></title>
  <meta name="description" content="">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">

</head>
<style>

</style>
<body>
  <div id="title_text">
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
<script>
  WRT_e.api.ehrSz.GetSysMgs({
    params:{},
    success(data){
      // console.log(data.Result)
      if(data.Code==1){
        function escape2Html(str) {
          var arrEntities={'lt':'<','gt':'>','nbsp':' ','amp':'&','quot':'"'};
            return str.replace(/&(lt|gt|nbsp|amp|quot);/ig,function(all,t){
            return arrEntities[t];
          });
        }
        let html=escape2Html(data.Result)
        $("#title_text").html(html)
        
      }
    }
  })
</script>
</body>
</html>