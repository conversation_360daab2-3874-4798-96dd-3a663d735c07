<!-- 手术新技术新项目维护 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="table-background">
        <div class="table-title">
          <div>
            <span />
            手术新技术新项目维护
          </div>
          <el-button type="primary" @click="openAddDrawer">新增</el-button>
        </div>
        <div class="table-component">
          <table>
            <thead>
              <tr style="text-align: left">
                <th style="width: 50px"></th>
                <th>代码</th>
                <th>名称</th>
                <th>专科</th>
                <th>状态</th>
                <th>备注</th>
                <th>修改人员</th>
                <th>修改日期</th>
                <th style="width: 70px; text-align: center">操作</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in dataList">
                <tr :key="index" :class="[index % 2 == 1 ? 'tr-two' : 'tr-one']">
                  <td>
                    {{ index + 1 }}
                  </td>
                  <td>
                    {{ item.field1 }}
                  </td>
                  <td>
                    {{ item.field2 }}
                  </td>
                  <td>
                    {{ item.field3 }}
                  </td>
                  <td>
                    <el-tag :type="item.field4 === '启用' ? null : 'danger'">
                      {{ item.field4 }}
                    </el-tag>
                  </td>
                  <td>
                    {{ item.field5 }}
                  </td>
                  <td>
                    {{ item.field6 }}
                  </td>
                  <td>
                    {{ item.field7 }}
                  </td>
                  <td style="text-align: center">
                    <div style="display: flex; align-items: center">
                      <el-button type="text" @click="openEditDrawer(item)">编辑</el-button>
                      <el-divider direction="vertical" />
                      <el-button type="text" @click="deleteItem(item)">删除</el-button>
                    </div>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <edit-drawer :visible.sync="editVisible" :data.sync="selectItem" :type="drawerType" />

    <el-dialog :visible.sync="deleteVisible" width="350px">
      <span slot="title">
        <span style="font-size: 16px">
          <i class="el-icon-warning"></i>
          删除提示
        </span>
      </span>
      <div class="delete-component">
        确认删除
        <a>【{{ selectItem?.field2 }}】</a>
        吗?
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="deleteVisible = false">保 存</el-button>
        <el-button @click="deleteVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import EditDrawer from './components/EditDrawer.vue'
// import { getShouShuLieBiao } from '@/api/system-maintenance'
export default {
  name: 'SystemOperationNewProject',
  components: {
    EditDrawer
  },
  data() {
    return {
      dataList: [],
      selectItem: null,
      editVisible: false,
      deleteVisible: false,
      drawerType: 'edit'
    }
  },
  mounted() {
    this.initSystemIcd()
  },
  methods: {
    initSystemIcd() {
      this.dataList = [
        {
          field1: 'xjs2021014',
          field2: 'CO2激光治疗女性下生殖道病变',
          field3: '',
          field4: '启用',
          field5: '妇科',
          field6: '黄可慧',
          field7: '2021-12-22 14:36:30'
        },
        {
          field1: 'xjs2022018',
          field2: 'CT引导下胸交感神经调制术',
          field3: '呼吸内科',
          field4: '启用',
          field5: '疼痛科 林海',
          field6: '朱坚磊',
          field7: '2021-10-20 15:07:03'
        },
        {
          field1: 'xjs202203',
          field2: 'EMS碎石清石系统在复杂肝内胆管结石中的应用',
          field3: '血液内科',
          field4: '启用',
          field5: '肝胆胰外科',
          field6: '朱坚磊',
          field7: '2022-10-20 15:39:52'
        },
        {
          field1: 'xjs2022013',
          field2: 'HFNC在非插管单孔胸腔镜手术中的应用',
          field3: '',
          field4: '停用',
          field5: '麻醉科',
          field6: '黄可慧',
          field7: '2023-04-12 10:54:52'
        },
        {
          field1: 'xjs2022035',
          field2: '保存生育功能的宫颈癌根治术(广泛性宫颈切除术)',
          field3: '',
          field4: '启用',
          field5: '妇科 张玉阳',
          field6: '黄可慧',
          field7: '2023-04-17 16:32:35'
        },
        {
          field1: 'xjs2019008',
          field2: '超声导向下经皮经肝门静脉穿刺直接测压术',
          field3: '',
          field4: '启用',
          field5: '消化内科',
          field6: '黄可慧',
          field7: '2023-11-27 16:35:52'
        }
      ]
      // getShouShuLieBiao({}).then((res) => {
      //   if (res.hasError === 0) {
      //     this.dataList = res.data
      //     console.log('手术项目icd res:', res)
      //   }
      // })
    },
    openAddDrawer() {
      this.selectItem = {
        field1: '',
        field2: '',
        field3: '',
        field4: '启用',
        field5: '',
        field6: '',
        field7: ''
      }
      this.drawerType = 'add'
      this.editVisible = true
    },
    openEditDrawer(item) {
      this.selectItem = item
      this.drawerType = 'edit'
      this.editVisible = true
    },
    deleteItem(item) {
      this.selectItem = item
      this.deleteVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .head-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      .search-label {
        margin: 0 10px;
        font-weight: 600;
      }
      button.search-button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover).search-button {
        background: #a66dd4;
        border-color: #a66dd4;
      }
      :deep(.el-button--primary:focus).search-button {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        font-weight: 600;
        width: 1080px;
        span {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        margin-top: 10px;
        width: 1080px;
        table {
          margin-top: 10px;
          width: 100%;
        }
        th,
        td {
          border: 1px solid #ddd;
          border-collapse: collapse; /* 移除表格内边框间的间隙 */
          height: 40px;
          padding: 5px 8px;
        }
        .tr-one {
          background-color: #f6f6f6;
        }
        th,
        .tr-two {
          background-color: #eff3fb;
        }
        .el-tag {
          border-radius: 2px;
        }
      }
    }
  }
  :deep(.el-dialog__footer) {
    border-top: none;
  }

  .el-icon-warning {
    font-size: 20px;
    color: #ed6a0c;
  }
  .delete-component {
    padding-left: 30px;
    color: #96999e;
    a {
      color: #356ac5;
    }
  }
}
</style>
