<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <el-radio v-model="chaXunSJRadio" label="1">查询时间:</el-radio>
        <el-date-picker
          v-model="chaXunSJ"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </div>
      <div class="header-item">
        <el-radio v-model="chaXunSJRadio" label="2">查询病案号:</el-radio>
        <div class="header-item-input">
          <el-input v-model="selectContent" placeholder="请输入内容"></el-input>
        </div>
        <div class="header-item-button"><el-button>查询</el-button></div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">无纸化试用病人</div>
        <div class="button"><el-button type="primary" @click="handleAdd">新增</el-button></div>
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData">
          <el-table-column prop="bingAnHao" width="119" label="病案号"></el-table-column>
          <el-table-column prop="bingRenMC" width="119" label="病人名称"></el-table-column>
          <el-table-column prop="zhuanKe" width="119" label="专科"></el-table-column>
          <el-table-column prop="bingQu" width="160" label="病区"></el-table-column>
          <el-table-column prop="bingChuangHao" width="88" label="病床号"></el-table-column>
          <el-table-column prop="ruYuanSJ" width="160" label="入院时间"></el-table-column>
          <el-table-column prop="chuYuanSJ" width="160" label="出院时间"></el-table-column>
          <el-table-column prop="tiJiaoYS" width="119" label="提交医生"></el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">
                查看病人
              </el-button>
              <el-divider direction="vertical"></el-divider>
              <el-button type="text" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible" width="40%" :before-close="handleClose">
      <div slot="title" class="dialog-title">
        无纸化病人-{{ handleMode == 'update' ? '查看' : '新增' }}
      </div>
      <table>
        <tbody>
          <tr>
            <td class="info-label">病案号:</td>
            <td class="info-value">
              <el-input v-model="bingAnHao"></el-input>
            </td>
            <td class="info-label">病人名称:</td>
            <td class="info-value">
              <el-input v-model="bingRenMC"></el-input>
            </td>
          </tr>
          <tr>
            <td class="info-label">科室:</td>
            <td class="info-value">
              <el-input v-model="zhuanKe"></el-input>
            </td>
            <td class="info-label">入院时间:</td>
            <td class="info-value">
              <el-input v-model="ruYuanSJ"></el-input>
            </td>
          </tr>
          <tr>
            <td class="info-label">出院时间:</td>
            <td class="info-value">
              <el-input v-model="chuYuanSJ"></el-input>
            </td>
            <td class="info-label">操作:</td>
            <td class="info-value">
              <!-- <div class="flex">
                <el-radio v-model="zhuangTai" label="0">启用</el-radio>
                <el-radio v-model="zhuangTai" label="1">停用</el-radio>
              </div> -->
            </td>
          </tr>
        </tbody>
      </table>
      <div class="bottom-btn-group">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      chaXunSJRadio: '',
      chaXunSJ: '',
      selectContent: '',
      tableData: [
        {
          bingAnHao: '0014342728',
          bingRenMC: '金城',
          zhuanKe: 'ICU',
          bingQu: 'A330病区',
          bingChuangHao: '302',
          ruYuanSJ: '2022-01-24',
          chuYuanSJ: '2023-01-19',
          tiJiaoYS: '胡和'
        }
      ],
      dialogVisible: false,
      handleMode: '',
      bingAnHao: '',
      bingRenMC: '',
      zhuanKe: '',
      bingQu: '',
      bingChuangHao: '',
      ruYuanSJ: '',
      chuYuanSJ: '',
      tiJiaoYS: ''
    }
  },
  methods: {
    handleClick(row) {
      this.dialogVisible = true
      this.handleMode = 'update'
      this.bingAnHao = row.bingAnHao
      this.bingRenMC = row.bingRenMC
      this.zhuanKe = row.zhuanKe
      this.ruYuanSJ = row.ruYuanSJ
      this.chuYuanSJ = row.chuYuanSJ
      console.log(row)
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleAdd() {
      this.handleMode = 'add'
      this.dialogVisible = true
      this.bingAnHao = ''
      this.bingRenMC = ''
      this.zhuanKe = ''
      this.ruYuanSJ = ''
      this.chuYuanSJ = ''
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
  .table {
    min-height: 650px;
    width: 77%;
  }
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 77%;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}

::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
td {
  border: 1px solid #ddd;
  border-collapse: collapse; /* 移除表格内边框间的间隙 */
  height: 35px;
  padding: 10px;
}
.info-label {
  text-align: right;
  width: 10vw;
  background-color: #eaf0f9;
}
.info-value {
  width: 14vw;
  height: 30px;
  background-color: #f7f9fd;
}
.flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.no-padding {
  padding: 0;
  .no-padding-label {
    padding: 10px;
  }
  ::v-deep .el-button {
    flex: 1;
    height: 100%;
  }
}
.bottom-btn-group {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 30px;
  ::v-deep .el-button {
    margin-left: 10px;
  }
}
</style>
