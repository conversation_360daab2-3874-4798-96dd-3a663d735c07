WRT_e.api = WRT_e.api || {}

WRT_e.api.zkcyzqlist = {
  //获取全部文书列表
  getAllWenShu: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":portal
      })
    }else{
      $.ajax({
        url: WRT_config.server2+'/diagnoses-v2/v2/zhuanKeGeShiDZ/getAllWenShu',
        type: 'get',
        data:'',
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        // headers: {
        //   Accept: "application/json; charset=utf-8",
        //   token:sessionStorage.getItem("token")
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  //根据专科查询
  getListByZkid: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":portal
      })
    }else{
      $.ajax({
        url: WRT_config.server2+'/diagnoses-v2/v2/zhuanKeGeShiDZ/getListByZkid?zhuanKeID='+o.zhuanKeID,
        type: 'get',
        data:'',
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        // headers: {
        //   Accept: "application/json; charset=utf-8",
        //   userId: o.j_yhid,
        //   ms_key: md5Str
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  //新增
  addSingle: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":portal
      })
    }else{
      $.ajax({
        url: WRT_config.server2+'/diagnoses-v2/v2/zhuanKeGeShiDZ/addSingle',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        // headers: {
        //   Accept: "application/json; charset=utf-8",
        //   userId: o.j_yhid,
        //   ms_key: md5Str
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  //修改/替换
  updateSingle: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":portal
      })
    }else{
      $.ajax({
        url: WRT_config.server2+'/diagnoses-v2/v2/zhuanKeGeShiDZ/updateSingle',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        // headers: {
        //   Accept: "application/json; charset=utf-8",
        //   userId: o.j_yhid,
        //   ms_key: md5Str
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  //删除
  deleteSingle: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":portal
      })
    }else{
      $.ajax({
        url: WRT_config.server2+'/diagnoses-v2/v2/zhuanKeGeShiDZ/delete'+o.params,
        type: 'get',
        data:"",
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
        },
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        // headers: {
        //   Accept: "application/json; charset=utf-8",
        //   userId: o.j_yhid,
        //   ms_key: md5Str
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
}