<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>新营养医嘱</title>
  <meta name="description" content="新营养医嘱">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
  #nbPNCalculator, .nbPNCalculatorMain, .nbTBinfoR, .nbPNCTCAll {
    text-align: center;
    margin: auto;
  }
  .nbPNCalculatorMain {
    padding-top: 50px;
  }
  /* 标题 + 新增按钮 */
  .yyyztitleAbtn {
    display: flex;
    align-items: baseline;
    justify-content: center;
  }
  .addBtn {
    position: relative;
    padding-left: 20px;
    text-decoration: none;
  }
  /* 外部表格 */
	.overall_table_frame {
		position: relative;
		/* width: calc(100%); */
		/* border: 1px solid #B7C3DA; */
		margin: 0 auto;
    padding: 20px;
		/* margin-bottom: 10px; */
	}

	.overall_table_frame>table {
		position: relative;
		/* width: 50%; */
	}

	#yyyz_table {
		position: relative;
		border: 1px solid #B7C3DA;
    margin: 0 auto;
	}

	.row_head>th {
		background: #85A2DB;
		/* color: #000; */
		/* background: #6699FB; */
		color: #FFFFFF;
		text-align: center;
		font-family: Microsoft YaHei;
		font-size: 16px;
		font-style: normal;
		font-weight: 400;
		line-height: 30px;
		letter-spacing: 0em;
	}

	.row_nr>td {
		text-align: center;
		font-family: Microsoft YaHei;
		color: #000;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 26px;
		letter-spacing: 0em;
		padding: 4px 8px;
	}

  /* 弹窗 */
  /* 外部整体弹窗 */
  .addModelW {}
  /* 病人基本信息 */
  .brBaseInfo {
		position: relative;
    float: left;
  }
  .brInfoForm {
    display: flex;
    flex-direction: row;
    /* flex-wrap: wrap; */
    justify-content: flex-start;
    align-items: center;
    font-size: 16px;
  }
	.Ltitle {
		position: relative;
		padding: 0 16px;
		font-size: 16px;
		border-left: 3px solid #155bd4;
		height: 26px;
		line-height: 26px;
	}
	.allInput {
		border-radius: 4px;
		color: rgba(0, 0, 0, .65);
		background-color: #fff;
		border: 1px solid #d9d9d9;
    /* color: red; */
	}
  .inputNowRed input:-webkit-autofill,
  .inputNowRed input:-webkit-autofill:hover,
  .inputNowRed input:-webkit-autofill:focus,
  .inputNowRed input:-webkit-autofill:active {
      /* 修改自动填充时的背景颜色 */
      -webkit-text-fill-color: red; /* 修改字体颜色 */
      transition: background-color 5000s ease-in-out 0s;  /* 防止背景颜色改变 */
  }
  
  .inputNowRed input:-webkit-autofill {
      -webkit-box-shadow: 0 0 0px 1000px white inset; /* 修改背景颜色 */
  }
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  } 
  .radio-inline+.radio-inline {
		margin-left: 0px;
	}
	.radio-inline {
		display: flex;
		/* padding-left: 15px; */
		/* align-items: center; */
		align-items: baseline;
		padding-top: 2px;
    padding-bottom: 2px;
	}


  /*第一部分输入Form表单 */
  .yyyzFormBD {
    position: relative;
    float: left;
  }
  .yzdForm {
    position: relative;
		/* border: 1px solid #d9d9d9; */
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding-top: 10px;
  }
  
	.radio-inline1 {
    display: contents;
	}
  .formLName {
    position: relative;
		border-top: 1px solid #d9d9d9;
		border-bottom: 1px solid #d9d9d9;
		border-left: 1px solid #d9d9d9;
    padding: 4px 10px;
    /* line-height: 50px; */
    /* height: 36px;
    line-height: 28px; */
  }


  /* 第二部分表格表单 （新单上的2+3模块拼接）*/
  .yyyzFormTb {
    position: relative;
    float: left;
    width: 100%;

    padding-top: 10px;
  }
  .sjhzTb {
    padding-top: 10px;
  }
  /* 第三部分 相关检验项目表 */
  .yyyzXGJYXM {
    position: relative;
    float: left;
    width: 100%;
    padding-top: 10px;
  }
  .XGJYXMTb {
		position: relative;
		text-align: center;
		margin: 0 auto;
    border-radius: 8px;
		padding: 10px 0 30px 0;
  }
  .show_Table {
    width: 100%;
    margin: auto;
    border: 1px solid #767676;
  }
  .show_Table .table-nr{
    padding: 2px 5px;
  }

  .subClick[disabled]:hover, .subClick[disabled] {
    color: rgba(0, 0, 0, .25);
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    text-shadow: none;
    box-shadow: none;
  }
  .saveClick, .subClick {
		position: relative;
		float: right;
		right: 0;
    margin-left: 15px;
  }
  .text-right{
    position: relative;
    padding: 0 25px 15px;
  }

</style>
<body id="nbPNCalculator">
  <div class="nbPNCalculatorMain">
    <!-- 标题 + 新增按钮 -->
    <div class="yyyztitleAbtn">
      <h2><string>营养医嘱单</string></h2>
      <a href="javascript:void(0)" class="addBtn" onclick="addYyyz(0)">新增</a>
    </div>
    <!--营养医嘱单展示列表 -->
    <div class="yyyzdTB"></div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面JS -->
  <!-- <script src='js/e-EhrSz.js'></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-cwYyyzNew.js?v=" + Date.now() + "'><\/script>");
  </script>
</body>
</html>