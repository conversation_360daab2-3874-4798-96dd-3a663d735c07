<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>华法令调整表</title>
  <meta name="description" content="华法令调整表">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
  <script src="./lib/echarts/echarts.min.js"></script>
</head>
<style>
    #hfltzb_left{
        height: 300px;
        width: 943px;
        overflow-y: auto;
        overflow-x: hidden;
        border: 1px solid #95B8E7;
        margin-top: 10px;
        }
    #hfltzb_right{
        height: 300px;
        width: 743px;
        overflow-y: auto;
        overflow-x: hidden;
        border: 1px solid #95B8E7;
        float: left;
    }
    .hf_header{
        background: linear-gradient(to bottom,#EFF5FF 0,#E0ECFF 100%);
        background-repeat: repeat-x;
        border-color: #95B8E7;
        width: 941px;
        padding: 3px;
    }
    .left_head{
        width: 941px;
    }
    .right_head{
        width: 741px;
    }
    .header_title{
        font-size: 12px;
        font-weight: bold;
        color: #0E2D5F;
        height: 16px;
        line-height: 16px;
    }
    .hf_tbody{
        border-color: #dddddd;
        background: linear-gradient(to bottom,#F9F9F9 0,#efefef 100%);
        background-repeat: repeat-x;
        line-height: 24px;
    }
    .hf_tbody th{
        white-space: nowrap;
        word-wrap: normal;
        font-size: 12px;
        font-weight: normal;
        padding-left: 3px;
        cursor:context-menu
    }
    #hfltzb_left table td,#hfltzb_right table td{
        padding-left: 3px;
        cursor:context-menu
    }
    .btn_show:hover{
        color: blueviolet;
        cursor: pointer;
    }
</style>
<body style="padding:10px">
    <div style="height: 310px;">
        <div id="hfltzb_right">
            
        </div>
        <div id="main" style="width: 600px;height:300px;float: right;"></div>
    </div>
    <div id="hfltzb_left">

    </div>
    <!-- 配置文件 -->
    <script src="./e-config.js"></script>
    <!-- 公用模块JS -->
    <!-- <script src="js/e-common.js"></script> -->
    <script type="text/javascript">
        document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
    </script>
    <!-- 登录页js文件 -->
    <script type="text/javascript">
        document.write("<script type='text/javascript' src='js/e-hfltzb.js?v=" + Date.now() + "'><\/script>");
    </script>
</body>

</html>