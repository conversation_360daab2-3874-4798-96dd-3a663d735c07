<template>
  <div>
    <el-dialog :visible="visible" width="80vw" @close="updateVisible(false)">
      <span slot="title">
        <span class="drawer-title">
          <i class="el-icon-menu"></i>
          新生儿静脉营养(PN)计算器
        </span>
      </span>
      <div>
        <div class="drawer-component">
          <div v-if="formData?.ylEkpnyyzdVo" style="display: flex; width: 100%">
            <div class="label-frame">
              <table>
                <tbody>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">姓名/床号:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        {{ patientInit.bingRenXM }}/{{ patientInit.chuangWeiHao }}
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">注入方式:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-select v-model="sel_method" placeholder="" style="width: 100%">
                          <el-option
                            v-for="item in [
                              { label: '中心静脉', value: '0' },
                              { label: '外周静脉', value: '1' }
                            ]"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          ></el-option>
                        </el-select>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">体重:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input v-model="formData.ylEkpnyyzdVo.tiZhong" />
                        kg
                        <template v-if="vitalSign.tiZhong">
                          最近有效值：{{ vitalSign.tiZhong }}kg
                        </template>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label" rowspan="2">
                      <div class="info-content">
                        <el-select
                          v-model="formData.ylEkpnyyzdVo.naiLeiXing1"
                          placeholder=""
                          style="width: 100%"
                        >
                          <el-option
                            v-for="item in naiLeiXing1Option"
                            :key="item.beiZhu"
                            :label="item.mingCheng"
                            :value="item.beiZhu"
                          ></el-option>
                        </el-select>
                      </div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input v-model="formData.ylEkpnyyzdVo.naiDeCS" />
                        ml/次
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-value">
                      <div class="info-content">
                        <el-select
                          v-model="qhValue"
                          placeholder=""
                          style="margin: 0 5px; width: 40%"
                        >
                          <el-option
                            v-for="item in qhOption"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          ></el-option>
                        </el-select>
                        <el-select
                          v-model="formData.ylEkpnyyzdVo.diYiGeNDS"
                          placeholder=""
                          style="margin: 0 5px; width: 40%"
                        >
                          <el-option
                            v-for="item in dunOption"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          ></el-option>
                        </el-select>
                        顿
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label" rowspan="2">
                      <div class="info-content">
                        <el-select
                          v-model="formData.ylEkpnyyzdVo.naiLeiXing2"
                          placeholder=""
                          style="width: 100%"
                        >
                          <el-option
                            v-for="(item, index) in naiLeiXing2Option"
                            :key="index"
                            :label="item.mingCheng"
                            :value="item.beiZhu"
                          ></el-option>
                        </el-select>
                      </div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="naiDeCS2" disabled />
                        ml/次
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-value">
                      <div class="info-content">
                        <el-select :value="qhValue" disabled placeholder="" style="width: 40%">
                          <el-option
                            v-for="item in qhOption"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          ></el-option>
                        </el-select>
                        <el-input :value="diErGeNDS" disabled style="margin: 0 10px" />
                        顿
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">全天每kg总液量:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input
                          v-model="formData.ylEkpnyyzdVo.quanTianMKGZYL"
                          @change="allDayFluidVChange"
                        />
                        ml/kg
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">治疗液体量:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input v-model="formData.ylEkpnyyzdVo.zhiLiaoYTL" />
                        ml
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">氨基酸:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-select
                          v-model="formData.ylEkpnyyzdVo.anJiSuan"
                          placeholder=""
                          style="width: 70%"
                        >
                          <el-option
                            v-for="item in kgDOption"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          ></el-option>
                        </el-select>
                        g/(kg*d)
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">脂肪乳:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-select
                          v-model="formData.ylEkpnyyzdVo.zhiFangRu"
                          placeholder=""
                          style="width: 70%"
                        >
                          <el-option
                            v-for="item in [{ label: 0, value: 0 }, ...kgDOption]"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          ></el-option>
                        </el-select>
                        g/(kg*d)
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN糖速:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input v-model="formData.ylEkpnyyzdVo.pnTS" @change="TSOfPNChange" />
                        mg/(kg*min)
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN中钠量:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input v-model="NaInPN" @change="NaOfPNChange" />
                        mmol/(kg*d)
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN中钾量:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input v-model="KInPN" @change="KOfPNChange" />
                        mmol/(kg*d)
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN中磷量:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input v-model="PInPN" @change="POfPNChange" />
                        mmol/(kg*d)
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN中镁量:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input v-model="MgInPN" @change="MgOfPNChange" />
                        mmol/(kg*d)
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN时间:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-select
                          v-model="formData.ylEkpnyyzdVo.pnSJ"
                          placeholder=""
                          style="width: 70%"
                        >
                          <el-option
                            v-for="item in [
                              { label: 24, value: 24 },
                              { label: 20, value: 20 },
                              { label: 16, value: 16 },
                              { label: 12, value: 12 }
                            ]"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          ></el-option>
                        </el-select>
                        小时
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN泵值:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input disabled :value="pNpumpSpeed" />
                        ml/h
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="action-button">
                <el-button type="primary" @click="savePdnYlEkpnyyzd">保存</el-button>
                <el-button type="primary" @click="calculate">计算</el-button>
              </div>
            </div>
            <div class="label-frame" style="margin-left: 7px">
              <table class="drug-table">
                <tbody>
                  <template v-for="(item, index) in drugInfoList">
                    <tr
                      v-if="
                        !(
                          item.zongLiang === '3' &&
                          index !== drugInfoList.findIndex((fItem) => fItem.zongLiang === '3')
                        )
                      "
                      :key="'tr' + index"
                    >
                      <td class="info-label">
                        <div v-if="item.zongLiang !== '3'" class="info-content">
                          <el-select
                            v-if="item.zongLiang === '1'"
                            v-model="selected_WSS[item.yaoPinID]"
                            placeholder=""
                            style="width: 30%"
                            @change="calculate"
                          >
                            <el-option
                              v-for="opItem in [
                                { label: '有', value: 1 },
                                { label: '无', value: 0 }
                              ]"
                              :key="opItem.value"
                              :label="opItem.label"
                              :value="opItem.value"
                            ></el-option>
                          </el-select>
                          <el-select
                            v-else-if="item.zongLiang === '7'"
                            v-model="selected_ZKND[item.yaoPinID]"
                            placeholder=""
                            style="width: 30%"
                            @change="calculate"
                          >
                            <el-option
                              v-for="opItem in [
                                { label: '有', value: 1 },
                                { label: '无', value: 0 }
                              ]"
                              :key="opItem.value"
                              :label="opItem.label"
                              :value="opItem.value"
                            ></el-option>
                          </el-select>
                          {{ item.mingCheng }}:
                        </div>
                        <div v-else class="info-content">
                          <el-select v-model="select_ZFYP" placeholder="" style="width: 100%">
                            <el-option
                              v-for="opItem in drugInfoList.filter((o) => {
                                return o.zongLiang === '3'
                              })"
                              :key="opItem.yaoPinID"
                              :label="opItem.mingCheng"
                              :value="opItem.yaoPinID"
                            ></el-option>
                          </el-select>
                        </div>
                      </td>
                      <td class="info-value">
                        <div class="info-content">
                          <el-input :value="yaoPinXXForm[item.yaoPinID]" disabled />
                          {{ item.danWei }}
                        </div>
                      </td>
                    </tr>
                  </template>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">容量系数:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-select
                          v-model="formData.ylEkpnyyzdVo.rongLiangXS"
                          placeholder=""
                          style="width: 70%"
                        >
                          <el-option
                            v-for="item in [1.1, 1.2, 1.3, 1.4]"
                            :key="'coe-' + item"
                            :label="item"
                            :value="item"
                          ></el-option>
                        </el-select>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>

              <table style="margin-top: 10px">
                <tr>
                  <td class="info-label">
                    <div class="info-content">医嘱开始时间:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      <el-date-picker
                        v-model="formData.ylEkpnyyzdVo.yiZhuSJ"
                        type="datetime"
                        placeholder="选择日期"
                        value-format="yyyy-MM-dd hh:mm:ss"
                        style="width: 100%"
                      />
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <div class="info-content">持续输液:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      <el-input v-model="chiXuTS" />
                      天
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <div class="info-content">用药频率:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      <el-select disabled :value="'st'" placeholder="" style="width: 70%">
                        <el-option
                          v-for="item in ['st']"
                          :key="item"
                          :label="item"
                          :value="item"
                        ></el-option>
                      </el-select>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <div class="info-content">用法:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">营养静脉滴注</div>
                  </td>
                </tr>
              </table>
              <div class="action-button">
                <el-button type="primary" @click="uploadFormSubmit">提交医嘱</el-button>
              </div>
            </div>
            <div class="label-frame" style="margin-left: 7px">
              <table>
                <tbody>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">REE:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="REE" disabled />
                        kcal/d
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">总能量:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="allEnergy" disabled />
                        kcal/d
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">奶能量:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="milkEnergy" disabled />
                        kcal/d
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN能量:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="pnEnergy" disabled />
                        kcal/d
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">每kg能量:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="totalEnergy" disabled />
                        kcal/kg
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN氨基酸:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="aaEnergy" disabled />
                        g/(kg*d)
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN糖脂比:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="sugarAndfat" disabled />
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN热氮比:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="pnRDB" disabled />
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN渗透压:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="osmolality" disabled />
                        mOsm/L
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN糖浓度:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="text_tnd" disabled />
                        %
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN氨基酸浓度:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="AAND" disabled />
                        %
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN脂肪浓度:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="FATND" disabled />
                        %
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN一价离子浓度:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="monovalent" disabled />
                        mmol/L
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN二价离子浓度:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="divalent" disabled />
                        mmol/L
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">PN液量:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="pnFluidV" disabled />
                        mL
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="action-button">
                <el-button type="text">
                  <i
                    class="el-icon-question"
                    style="font-size: 25px"
                    @click="manualVisible = true"
                  />
                </el-button>
              </div>
            </div>
          </div>

          <div class="inspect-table" style="margin-top: 10px">
            <table>
              <tbody>
                <tr>
                  <th colspan="8">
                    <div class="table-title">
                      <div>
                        <span class="bar" />
                        相关检验项目
                      </div>
                    </div>
                  </th>
                </tr>
                <tr>
                  <td class="info-label">
                    <div class="info-content">总胆红素:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0001'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">直接胆红素:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0002'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">白蛋白:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0004'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">丙氨酸氨基转移酶:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0011'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <div class="info-content">天冬酸氨基氨基:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0012'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">碱性磷酸酶:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0013'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">γ谷氨酰基转移酶:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0020'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">肌酐:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0008'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                </tr>

                <tr>
                  <td class="info-label">
                    <div class="info-content">尿素氮:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0007'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">甘油三酯:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0010'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">血清钾:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0014'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">血清钠:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0015'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                </tr>

                <tr>
                  <td class="info-label">
                    <div class="info-content">血清氯:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0016'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">血清钙:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0017'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">血清磷:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0018'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">血清镁:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        huaYanJgAll.find((item) => {
                          return item.huaYanDM === '0019'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="inspect-table" style="margin-top: 10px">
            <table>
              <tbody>
                <tr>
                  <th>
                    <div class="table-title">
                      <div>
                        <span class="bar" />
                        血糖标准天
                      </div>
                    </div>
                  </th>
                </tr>
                <tr>
                  <td>
                    <div ref="chart" style="width: 100%; height: 300px" />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="uploadFormSave()">保 存</el-button>
        <el-button type="primary" @click="uploadFormSubmit()">提 交</el-button> -->
        <el-button @click="updateVisible(false)">取 消</el-button>
      </span>
    </el-dialog>
    <action-manual :visible.sync="manualVisible" />
  </div>
</template>

<script>
import { deepClone } from '@/utils'
import * as echarts from 'echarts'
import {
  getPdnTypeOfMilk,
  getVitalSignList,
  getHuaYanJgAll,
  getBloodgluseStandardDays,
  savePdnYlEkpnyyzd,
  savePdnYPYZ
} from '@/api/nutrition-advice'
import ActionManual from './actionManual.vue'

import { mapState } from 'vuex'

let stringISEmpty = (str) => {
  if (null == str || undefined == str || str == '') {
    return true
  }
  return false
}

export default {
  name: 'EditDialog',
  components: {
    ActionManual
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    yaoFangDM: {
      type: String,
      default: ''
    },

    //药品详情列表
    drugInfoList: {
      type: Array,
      default: null
    },
    nutritionData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      yiZhuDanID: 0, //医嘱单id 新建时为0
      vitalSign: {}, // 生命体征
      displayList: [],
      sheBaoDialogType: '',
      sheBaoYaoPinData: null,
      huaYanJgAll: [], //化验结果

      sel_method: '0', //静脉类型
      naiLeiXing1Option: [], //奶类型1
      naiLeiXing2Option: [], //奶类型2
      kgDOption: [],
      qhOption: [
        { label: 'q2h', value: 2 },
        { label: 'q3h', value: 3 },
        { label: 'q4h', value: 4 },
        { label: 'q6h', value: 6 },
        { label: 'q8h', value: 8 },
        { label: 'q12h', value: 12 }
      ], //q*h选项
      qhValue: 2,
      NaInPN: null,
      KInPN: null,
      PInPN: null,
      MgInPN: null,

      selected_WSS: {},
      selected_ZKND: {},
      select_ZFYP: 0,
      chiXuTS: null, //持续天数

      pnEnergy: null, //pn能量
      milkEnergy: null, //奶能量
      totalEnergy: null, //每千克能量
      monovalent: null, //一价离子浓度
      divalent: null, //二价离子浓度
      text_tnd: null, //糖浓度
      allEnergy: null, //总能量
      REE: null,
      AAND: null, //氨基酸浓度
      FATND: null, //脂肪浓度

      sugarEnergy: null, //糖能量
      sugarAndfat: null, //糖脂比
      aaEnergy: null, //氨基酸
      fatEnergy: null, //FAT能量
      pnRDB: null, //热氮比
      osmolality: null, //渗透压

      manualVisible: false, //操作指南
      notes: '',
      formData: null,
      // bingLiID: '2616474', //当前病人
      // patientInit: {
      //   bingAnHao: '**********',
      //   bingRenBH: '33030099000000004200854644',
      //   jieSuanDM: 'Y09',
      //   chuShengRQ: '1962-07-26',
      //   chuangWeiHao: '001',
      //   lianXiDH: '15957707284',
      //   lianXiDZ: '龙港市凰浦北路25号',
      //   xingBie: '1',
      //   bingRenXM: '潘时章', //姓名
      //   zhuYuanHao: '2057930',
      //   zhuYuanID: 2616474,
      //   shouShuTZD: {
      //     hunYinZK: '2'
      //   }
      // },
      yingYangYZD: {},
      yaoPinXXForm: {}
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    ...mapState({
      initInfo: ({ patient }) => patient.initInfo,
      zhiLiaoZuID: ({ patient }) => patient.zhiLiaoZuID,
      userInfo: (state) => state.user.userInfo,

      patientInit: ({ patient }) => patient.patientInit
    }),

    //顿选项1
    dunOption() {
      const option = []
      for (let i = 0; i <= 24 / this.qhValue; i++) {
        option.push({ label: i, value: i })
      }
      return option
    },
    //第二个奶顿数
    diErGeNDS() {
      if (this.formData.ylEkpnyyzdVo?.naiDeCS && this.formData.ylEkpnyyzdVo?.diYiGeNDS) {
        return 24 / this.qhValue - this.formData.ylEkpnyyzdVo?.diYiGeNDS
      }
      return null
    },
    //第二个奶次数
    naiDeCS2() {
      if (this.formData.ylEkpnyyzdVo?.naiDeCS && this.formData.ylEkpnyyzdVo?.diYiGeNDS) {
        return this.formData.ylEkpnyyzdVo?.naiDeCS
      }
      return null
    },

    //设置Pn液量
    pnFluidV() {
      //体重X全天总液量-治疗液量-两种奶液体总量
      if (
        this.formData.ylEkpnyyzdVo.zhiLiaoYTL &&
        this.formData.ylEkpnyyzdVo.quanTianMKGZYL &&
        this.formData.ylEkpnyyzdVo.tiZhong &&
        this.formData.ylEkpnyyzdVo.naiDeCS &&
        this.formData.ylEkpnyyzdVo.diYiGeNDS
      ) {
        var weight = this.formData.ylEkpnyyzdVo.tiZhong
        var allDay = this.formData.ylEkpnyyzdVo.quanTianMKGZYL
        var treatFluid = this.formData.ylEkpnyyzdVo.zhiLiaoYTL
        var milk =
          this.formData.ylEkpnyyzdVo.naiDeCS * this.formData.ylEkpnyyzdVo.diYiGeNDS +
          this.formData.ylEkpnyyzdVo.naiDeCS * this.diErGeNDS

        var sum = weight * allDay - treatFluid - milk
        sum = Math.round(sum)
        return sum
      }
      return null
    },

    //设置泵速
    pNpumpSpeed() {
      //PN液量÷PN时间
      if (this.pnFluidV && this.formData.ylEkpnyyzdVo.pnSJ) {
        return this.pnFluidV / this.formData.ylEkpnyyzdVo.pnSJ
      }
      return null
    },
    //实际提交的液量
    realFluidV() {
      if (this.pnFluidV) {
        return (sum * parseInt(this.formData.ylEkpnyyzdVo.rongLiangXS * 10)) / 10
      }
      return null
    }
  },
  async mounted() {
    for (let i = 1; i < 15; i++) {
      this.kgDOption.push({ label: i * 0.25, value: i * 0.25 })
    }

    const res2 = await getPdnTypeOfMilk()
    console.log('儿科静脉营养医嘱_获取奶类型和医嘱频率', res2)
    if (res2.hasError === 0) {
      res2.data.forEach((item) => {
        const option = { mingCheng: item.mingCheng, beiZhu: Number(item.beiZhu) }
        if (item.mingCheng.indexOf('母乳') > -1) {
          this.naiLeiXing1Option.push(option)
        } else {
          this.naiLeiXing2Option.push(option)
        }
      })
    }

    const res3 = await getHuaYanJgAll({ bingLiID: this.bingLiID })
    console.log('根据病例ID获取全部化验结果', res3)
    if (res3.hasError === 0) {
      this.huaYanJgAll = res3.data
    }

    const res4 = await getVitalSignList({ bingLiID: this.bingLiID })

    console.log('获取病人生命体征', res4)
    if (res4.hasError === 0 && res4.data.length > 0) {
      this.vitalSign = {
        shenGao: res4.data[0].shenGao,
        tiZhong: res4.data[0].tiZhong
      }
    }

    const res5 = await getBloodgluseStandardDays({
      bingLiID: this.bingLiID,
      operateStartDateStr: '2025-01-01 00:00:00',
      operateEndDateStr: '2025-05-29 00:00:00'
    })

    console.log('护士站_血糖_根据病历id和时间查询标准天血糖数据', res5)
    if (res5.hasError === 0 && res5.data.length > 0) {
    }
  },
  methods: {
    inputChange(value, yaoPinID) {
      console.log(value, Number(value))
      if (!Number(value)) {
        this.yaoPinXXForm[yaoPinID] = ''
      }
      this.computedYingYangYZD()
    },

    calculate() {
      console.log('药品数量', this.yaoPinXXForm)
      // this.yaoPinXXForm[0] = 123
      // return

      if (!this.checkInputVal()) {
        return
      }
      const yaoPinXXForm = {} //药品表单
      //是复合磷酸氢钾体积
      let isKP = false
      let KPV = 0
      let KCapacity = 0
      //甘油磷酸钠体积
      let NaPV = 0
      let NaCapacity = 0

      let fluid = this.pnFluidV
      let fluidSum = 0
      let hasNagetive = false
      let value = 0
      let weight = this.formData.ylEkpnyyzdVo?.tiZhong || 10
      let condition = 0
      let pnTS = this.formData.ylEkpnyyzdVo?.pnTS

      let P = parseFloat(this.PInPN || 0)
      let Mg = parseFloat(this.MgInPN || 0)
      let NaValue = parseFloat(this.NaInPN || 0)
      // 体重XPN中钠量X58.5÷1000÷PN液量
      condition = (weight * NaValue * 58.5) / 1000 / fluid
      if (condition >= 0.003) {
        this.NaInPN = ''
        this.$message({
          message: '钠量占比超过0.3%，请重新填写计算!',
          type: 'error',
          duration: 1500
        })
        return false
      }
      let KValue = parseFloat(this.KInPN || 0)
      // 体重XPN中钾量X74.55÷1000÷PN液量
      condition = (weight * KValue * 74.55) / 1000 / fluid
      if (condition >= 0.003) {
        this.KInPN = ''
        this.$message({
          message: '钾量占比超过0.3%，请重新填写计算!',
          type: 'error',
          duration: 1500
        })
        return false
      }

      this.drugInfoList.forEach((item) => {
        //0 糖1 维生素2氨基酸AJS3脂肪4甘油磷酸钠5复合磷酸钾6水7左卡尼汀  Na K Ca P Mg
        switch (item.zongLiang) {
          case '0':
            //求糖
            // pIndex = parseInt(input_index.eq(i).val());
            //糖速X体重X60X24÷1000÷可维护浓度
            /* value = pnTS * weight * 60 * 24 / 1000 / parseFloat(ypInfo[pIndex].nongDu) * 100;
                 value = Math.round(value);
                 if (value < 0) {
                     hasNagetive = true;
                 }
                 input_num.eq(i).val(value);
                 fluidSum += value;*/
            break
          case '1': //求维生素
          case '8': //微量营养素
            if (this.selected_WSS[item.yaoPinID] === 0) {
              yaoPinXXForm[item.yaoPinID] = ''
              break
            }
            let sum = 0

            if (item.mingCheng.indexOf('水溶性') > -1) {
              //水溶性维生素
              //体重X0.1 上限为1
              sum = weight * 0.1
              if (sum > 1) {
                this.$message({
                  message: '水溶性维生素要小于1支',
                  type: 'error',
                  duration: 1500
                })
                yaoPinXXForm[item.yaoPinID] = ''
                return false
              }
            } else {
              //脂溶性维生素
              //体重X0.2 上限为2
              sum = weight * 0.2
              if (sum > 2) {
                this.$message({
                  message: '脂溶性维生素要小于2支',
                  type: 'error',
                  duration: 1500
                })
                yaoPinXXForm[item.yaoPinID] = ''
                return false
              }
            }
            value = Math.round(sum * 10) / 10
            if (value < 0) {
              hasNagetive = true
            }
            yaoPinXXForm[item.yaoPinID] = value
            break
          case '2': //求氨基酸
            let aminoAcid = parseFloat(this.formData.ylEkpnyyzdVo.anJiSuan)
            //氨基酸X体重÷可维护浓度
            value = Math.round((aminoAcid * weight * 100) / parseFloat(item.nongDu))
            if (value < 0) {
              hasNagetive = true
            }
            yaoPinXXForm[item.yaoPinID] = value
            fluidSum += value
            break
          case '3':
            //求脂肪
            //脂肪乳x体重÷可维护浓度
            let le = parseFloat(this.formData.ylEkpnyyzdVo.zhiFangRu)
            value = Math.round((weight * le * 100) / parseFloat(item.nongDu))
            fluidSum += value
            if (value < 0) {
              hasNagetive = true
            }
            yaoPinXXForm[item.yaoPinID] = value
            break
          case '4':
            //甘油磷酸钠
            //体重XPN中磷量÷20X可维护甘油磷酸钠容量
            NaCapacity = parseFloat(item.rongLiang)
            value = (weight * P * NaCapacity) / 20
            value = Math.round(value * 100) / 100
            NaPV = value

            fluidSum += value
            if (value < 0) {
              hasNagetive = true
            }
            yaoPinXXForm[item.yaoPinID] = value
            break
          case '5':
            //求复合磷酸氢钾
            //体重XPN中磷量÷复合磷酸氢钾mmolX复合磷酸氢钾容量
            isKP = true
            KCapacity = parseFloat(item.rongLiang)
            value = (weight * P * KCapacity) / parseFloat(item.mmol)
            value = Math.round(value * 100) / 100
            KPV = value
            fluidSum += value
            if (value < 0) {
              hasNagetive = true
            }
            yaoPinXXForm[item.yaoPinID] = value
            break

          case '7':
            //左卡尼汀
            //体重X25÷1000÷可维护浓度
            if (this.selected_ZKND[item.yaoPinID] === 0) {
              yaoPinXXForm[item.yaoPinID] = ''
              break
            }
            value = (weight * 25) / 1000 / (parseFloat(item.nongDu) / 100)
            yaoPinXXForm[item.yaoPinID] = value
            if (value < 0) {
              hasNagetive = true
            }
            fluidSum += value

            break
          /*   case 'Ca':
                       //求Ca
                       //体重XPN中钙量X448.4÷1000÷可维护葡萄糖酸钙浓度
                     
                       pIndex = parseInt(input_index.eq(i).val());
                       value = weight * Ca * 448.4 / 1000 / (parseFloat(ypInfo[pIndex].nongDu) / 100);
                      
                       let Mg = 0;
                       if (!stringISEmpty($("#MgInPN").val())) {
                           Mg = parseFloat($("#MgInPN").val());
                       }
                      
        
                       value = Math.round(value * 100) / 100;
                       fluidSum += value;
                       if (value < 0) {
                           hasNagetive = true;
                       }
                       input_num.eq(i).val(value);
                       break;
        */
          case 'Mg':
            //求含Mg的溶液
            //体重XPN中镁量X246.48÷1000÷可维护硫酸镁浓度
            value = (weight * Mg * 246.48) / 1000 / (parseFloat(item.nongDu) / 100)
            value = Math.round(value * 100) / 100
            fluidSum += value
            if (value < 0) {
              hasNagetive = true
            }
            yaoPinXXForm[item.yaoPinID] = value
            break

          default:
            break
        }
      })
      //葡萄糖浓度
      var pptND = new Array(2)
      var pptNDIndex = 0
      this.drugInfoList.forEach((item) => {
        //0 糖1 维生素2氨基酸AJS3脂肪4Na5K6Ca7P8Mg
        switch (item.zongLiang) {
          case '0':
            pptND[pptNDIndex] = parseInt(item.yaoPinID)
            pptNDIndex++
            break
          case 'Na': //求Na
            if (!isKP) {
              value =
                ((weight * NaValue - (NaPV * 20) / NaCapacity) * 58.5) /
                1000 /
                (parseFloat(item.nongDu) / 100)
            } else {
              value = (weight * NaValue * 58.5) / 1000 / (parseFloat(item.nongDu) / 100)
            }

            value = Math.round(value * 10) / 10
            fluidSum += value
            if (value < 0) {
              hasNagetive = true
            }
            yaoPinXXForm[item.yaoPinID] = value
            break
          case 'K': //求K
            if (isKP) {
              value =
                ((weight * KValue - (KPV / KCapacity) * 8.8) * 74.55) /
                1000 /
                (parseFloat(item.nongDu) / 100)
            } else {
              value = (weight * KValue * 74.55) / 1000 / (parseFloat(item.nongDu) / 100)
            }
            value = Math.round(value * 10) / 10
            fluidSum += value
            if (value < 0) {
              hasNagetive = true
            }
            yaoPinXXForm[item.yaoPinID] = value
            break
          default:
            break
        }
      })
      if (
        this.drugInfoList.find((item) => item.yaoPinID === pptND[0])?.nongDu <
        this.drugInfoList.find((item) => item.yaoPinID === pptND[1])?.nongDu
      ) {
        var t = pptND[0]
        pptND[0] = pptND[1]
        pptND[1] = t
      }

      //葡萄糖计算
      pptNDIndex = -1
      this.drugInfoList.forEach((item) => {
        if (item.zongLiang == '0') {
          pptNDIndex++

          switch (pptNDIndex) {
            case 1:
              value = fluid - fluidSum
              break
            default:
              if (pptND[pptNDIndex + 1]) {
                var a = this.drugInfoList.find((dItem) => dItem.yaoPinID === pptND[pptNDIndex + 1])
                var b = this.drugInfoList.find((dItem) => dItem.yaoPinID === pptND[pptNDIndex])
                value =
                  ((pnTS * weight * 60 * 24) / 1000 -
                    ((fluid - fluidSum) * parseFloat(a.nongDu)) / 100) /
                  (parseFloat(b.nongDu) / 100 - parseFloat(a.nongDu) / 100)
              }

              break
          }
          value = Math.round(value)
          fluidSum += value
          if (value < 0) {
            hasNagetive = true
          }
          yaoPinXXForm[item.yaoPinID] = value
        }
      })
      this.yaoPinXXForm = yaoPinXXForm

      this.setPnEnergy()

      if (!this.setTotalEnergy()) {
        return false
      }

      if (!this.setMonovalent()) {
        return false
      }

      if (!this.setDivalent()) {
        return false
      }

      if (!this.setText_TND()) {
        return false
      }

      this.setAllEnergy()
      this.setREE()

      if (!this.setSAFEnergyAndND()) {
        return false
      }

      if (!this.setOsmolality()) {
        return false
      }

      if (hasNagetive) {
        this.$message({
          message: '计算出来的结果有负数!',
          type: 'error',
          duration: 1500
        })
        return false
      }
      return true
    },
    //设置pn能量
    setPnEnergy() {
      var sum = 0
      var weight = this.formData.ylEkpnyyzdVo.tiZhong

      sum += ((parseFloat(this.formData.ylEkpnyyzdVo.pnTS) * weight * 60 * 24) / 1000) * 4
      sum += parseFloat(this.formData.ylEkpnyyzdVo.anJiSuan) * weight * 4
      sum += parseFloat(this.formData.ylEkpnyyzdVo.zhiFangRu) * weight * 9

      this.pnEnergy = Math.round(sum * 10) / 10
    },

    //计算奶能量
    setMilkEnergy() {
      //奶品种相应单位能量X每次用量X0.01X顿数
      //混合喂养，两者相加
      if (
        stringISEmpty(this.formData.ylEkpnyyzdVo.naiLeiXing1) ||
        stringISEmpty(this.formData.ylEkpnyyzdVo?.diYiGeNDS) ||
        stringISEmpty(this.formData.ylEkpnyyzdVo?.naiDeCS)
      ) {
        this.milkEnergy = 0
        return 0
      }
      var value
      var value1
      value =
        parseFloat(this.formData.ylEkpnyyzdVo.naiLeiXing1) *
        parseFloat(this.formData.ylEkpnyyzdVo?.naiDeCS) *
        0.01 *
        parseFloat(this.formData.ylEkpnyyzdVo?.diYiGeNDS)
      value1 = 0
      if (!stringISEmpty(this.formData.ylEkpnyyzdVo.naiLeiXing2)) {
        value1 =
          parseFloat(this.formData.ylEkpnyyzdVo.naiLeiXing2) *
          parseFloat(this.naiDeCS2) *
          0.01 *
          parseFloat(this.diErGeNDS)
      }

      value = value + value1
      this.milkEnergy = Math.round(value * 10) / 10
      return value
    },

    //设置每kg能量
    setTotalEnergy() {
      var millEnergy = this.setMilkEnergy()
      var pnEnergy = this.pnEnergy
      if (parseFloat(this.formData.ylEkpnyyzdVo.tiZhong) == 0) {
        this.totalEnergy = ''
        return false
      }

      var value =
        (parseFloat(millEnergy) + parseFloat(pnEnergy)) /
        parseFloat(this.formData.ylEkpnyyzdVo.tiZhong)
      this.totalEnergy = Math.round(value * 10) / 10
      return true
    },

    //设置一价离子浓度
    setMonovalent() {
      //[(体重XPN钠量)+(体重XPN钾量)]÷ PN液量X1000
      var Na = 0
      if (!stringISEmpty(this.NaInPN)) {
        Na = parseFloat(this.NaInPN)
      }
      var K = 0
      if (!stringISEmpty(this.KInPN)) {
        K = parseFloat(this.KInPN)
      }
      var weight = parseFloat(this.formData.ylEkpnyyzdVo.tiZhong)
      var pnFluid = parseFloat(this.pnFluidV)
      var value = ((weight * (Na + K)) / pnFluid) * 1000
      if (value >= 150) {
        this.$message({
          message: '一价离子浓度必须小于150',
          type: 'error',
          duration: 1500
        })
        return false
      }

      this.monovalent = Math.round(value * 10) / 10
      return true
    },

    //设置二价离子浓度
    setDivalent() {
      //[(体重XPN钙量)+(体重XPN镁量)]÷ PN液量X1000
      var Mg = 0
      if (!stringISEmpty(this.MgInPN)) {
        Mg = parseFloat(this.MgInPN)
      }
      var pnFluid = parseFloat(this.pnFluidV)
      var weight = parseFloat(this.formData.ylEkpnyyzdVo.tiZhong)
      var value = ((weight * Mg) / pnFluid) * 1000
      if (value >= 10) {
        this.$message({
          message: '二价离子浓度必须小于10',
          type: 'error',
          duration: 1500
        })
        return false
      }

      this.divalent = Math.round(value * 10) / 10
      return true
    },

    //设置糖浓度
    setText_TND() {
      //PN糖速X体重X60X24÷1000÷PN液量X100
      var sum =
        ((parseFloat(this.formData.ylEkpnyyzdVo.pnTS) *
          parseFloat(this.formData.ylEkpnyyzdVo.tiZhong) *
          60 *
          24) /
          1000 /
          parseFloat(this.pnFluidV)) *
        100
      if (sum >= 25) {
        this.$message({
          message: '糖浓度要小于25%',
          type: 'error',
          duration: 1500
        })
        this.text_tnd = ''
        return false
      }
      this.text_tnd = Math.round(sum * 10) / 10
      return true
    },

    //总能量
    setAllEnergy() {
      var millEnergy = this.milkEnergy
      var pnEnergy = this.pnEnergy
      var value = parseFloat(millEnergy) + parseFloat(pnEnergy)
      this.allEnergy = Math.round(value * 10) / 10
    },

    setREE() {
      var weight = parseFloat(this.formData.ylEkpnyyzdVo.tiZhong)
      if (this.patientInit.xingBie == '2') {
        this.REE = (58.3 * weight - 31).toFixed(1)
      } else {
        this.REE = (59.5 * weight - 30).toFixed(1)
      }
    },

    //求糖能量   AA能量 Fat能量 氨基酸浓度 脂肪乳浓度 PN热氮比
    setSAFEnergyAndND() {
      var SugarValue = 0
      var AAValue = 0
      var FATValue = 0
      var pnTS = parseFloat(this.formData.ylEkpnyyzdVo.pnTS)

      if (parseFloat(this.pnFluidV) == 0) {
        this.AAND = ''
        this.FATND = ''
      } else {
        var weight = parseFloat(this.formData.ylEkpnyyzdVo.tiZhong)
        var PnFluidV = parseFloat(this.pnFluidV)
        var aand = 0
        //氨基酸量
        var ajsl = parseFloat(this.formData.ylEkpnyyzdVo.anJiSuan)
        //脂肪乳
        var zfrl = parseFloat(this.formData.ylEkpnyyzdVo.zhiFangRu)
        //氨基酸量X体重÷PN液量X100%
        aand = Math.round(((ajsl * weight) / PnFluidV) * 1000) / 10
        //脂肪乳量X体重÷PN液量X100%
        var fatnd = Math.round(((zfrl * weight) / PnFluidV) * 1000) / 10
        if (zfrl > 0) {
          if (aand >= 2) {
            this.AAND = aand
          } else {
            this.$message({
              message: '氨基酸浓度要大于2%!',
              type: 'error',
              duration: 1500
            })
            this.AAND = ''
            return false
          }
          if (fatnd >= 1) $('#FATND').val(fatnd)
          else {
            this.$message({
              message: '脂肪浓度要大于1%!',
              type: 'error',
              duration: 1500
            })
            this.FATND = ''
            return false
          }
        } else {
          this.AAND = aand
          this.FATND = fatnd
        }
      }

      if (parseFloat(this.pnEnergy) == 0) {
        this.sugarEnergy = '0%'
        this.aaEnergy = '0%'
        this.fatEnergy = '0%'
      } else {
        var pnEnergy = parseFloat(this.pnEnergy)
        SugarValue = ((pnTS * weight * 60 * 24) / 1000) * 4
        FATValue = parseFloat(this.formData.ylEkpnyyzdVo.zhiFangRu) * weight * 9
        AAValue = parseFloat(ajsl) * weight
        var pnRDB = Math.floor(((SugarValue + FATValue) / AAValue) * 6.25)
        SugarValue = SugarValue / pnEnergy
        //糖质量X4÷PN能量*100%
        SugarValue = Math.round(SugarValue * 100)
        this.sugarEnergy = SugarValue + '%'

        this.aaEnergy = ajsl
        FATValue = FATValue / pnEnergy
        //脂肪质量X9÷PN能量*100%
        FATValue = Math.round(FATValue * 100)
        this.fatEnergy = FATValue + '%'
        this.sugarAndfat =
          Math.round((100 * SugarValue) / (SugarValue + FATValue)) +
          ':' +
          Math.round((100 * FATValue) / (SugarValue + FATValue))

        this.pnRDB = pnRDB.toString() + ':1'
      }
      return true
    },

    //求渗透压
    setOsmolality() {
      var sum = 0
      this.drugInfoList.forEach((item) => {
        //0糖1维生素2氨基酸3脂肪4Na5K6Ca7P8Mg
        switch (item.zongLiang) {
          case '3':
            //求脂肪
            if (this.select_ZFYP === item.yaoPinID) {
              sum += parseFloat(this.yaoPinXXForm[item.yaoPinID]) * parseFloat(item.shenTouYa)
            }
            break

          default:
            if (parseFloat(this.yaoPinXXForm[item.yaoPinID]) >= 0) {
              sum += parseFloat(this.yaoPinXXForm[item.yaoPinID]) * parseFloat(item.shenTouYa)
            }
            break
        }
      })

      var pnFluidV = this.pnFluidV
      sum = sum / pnFluidV
      sum = Math.round(sum)
      if (sum > 300) {
        if (this.sel_method == '1') {
          if (sum > 850) {
            this.$message({
              message: '外周静脉的渗透压不能超过850!',
              type: 'error',
              duration: 1500
            })
            return false
          }
        }
      } else {
        this.$message({
          message: '渗透压不能小于300!',
          type: 'error',
          duration: 1500
        })
        $.messager.alert('提示', '渗透压不能小于300!', 'info')
        return false
      }

      this.osmolality = sum
      return true
    },

    //全天总液量变化
    allDayFluidVChange(value) {
      if (value > 200) {
        this.formData.ylEkpnyyzdVo.quanTianMKGZYL = ''
        this.$message({
          message: '全天总液量不能超过200!',
          type: 'error',
          duration: 1500
        })
      }
    },
    //PN中钠量
    NaOfPNChange(value) {
      if (value > 5) {
        this.NaInPN = ''
        this.$message({
          message: 'PN中钠量不能超过5!',
          type: 'error',
          duration: 1500
        })
      }
    },
    //PN中钾量
    KOfPNChange(value) {
      if (value > 3) {
        this.KInPN = ''
        this.$message({
          message: 'PN中钾量不能超过3!',
          type: 'error',
          duration: 1500
        })
      }
    },
    //PN中磷量
    POfPNChange(value) {
      if (value > 3.5) {
        this.PInPN = ''
        this.$message({
          message: 'PN中磷量不能超过3.5!',
          type: 'error',
          duration: 1500
        })
      }
    },
    //PN中镁量
    MgOfPNChange(value) {
      if (value > 0.3) {
        this.MgInPN = ''
        this.$message({
          message: 'PN中镁量不能超过0.3!',
          type: 'error',
          duration: 1500
        })
      }
    },
    //PN糖速
    TSOfPNChange(value) {
      if (value > 12) {
        this.formData.ylEkpnyyzdVo.pnTS = ''
        this.$message({
          message: 'PN糖速不能超过12!',
          type: 'error',
          duration: 1500
        })
      }
    },
    //点击计算按钮，检查输入的项是否输入
    checkInputVal() {
      if (stringISEmpty(this.formData.ylEkpnyyzdVo.tiZhong)) {
        this.$message({
          message: '体重不能为空!',
          type: 'error',
          duration: 1500
        })
        return false
      }

      if (!stringISEmpty(this.formData.ylEkpnyyzdVo.naiLeiXing1)) {
        if (stringISEmpty(this.formData.ylEkpnyyzdVo.naiDeCS)) {
          this.$message({
            message: '请填写奶的量!',
            type: 'error',
            duration: 1500
          })
          return false
        }

        if (stringISEmpty(this.formData.ylEkpnyyzdVo.diYiGeNDS)) {
          this.$message({
            message: '请填写每天的顿数!',
            type: 'error',
            duration: 1500
          })
          return false
        }
      }

      if (stringISEmpty(this.formData.ylEkpnyyzdVo.quanTianMKGZYL)) {
        this.$message({
          message: '全天总液量不能为空!',
          type: 'error',
          duration: 1500
        })
        return false
      }

      if (stringISEmpty(this.formData.ylEkpnyyzdVo.zhiLiaoYTL)) {
        this.$message({
          message: '治疗液体不能为空!',
          type: 'error',
          duration: 1500
        })
        return false
      }

      if (stringISEmpty(this.formData.ylEkpnyyzdVo.pnTS)) {
        this.$message({
          message: 'PN糖速不能为空!',
          type: 'error',
          duration: 1500
        })
        return false
      }

      return true
    },
    //儿科静脉营养医嘱_保存医嘱单
    async savePdnYlEkpnyyzd() {
      this.checkInputVal()
      let futureDate = new Date()
      const year = futureDate.getFullYear()
      const month = futureDate.getMonth() + 1
      const day = futureDate.getDate()
      const hours = futureDate.getHours()
      const minutes = futureDate.getMinutes()
      const seconds = futureDate.getSeconds()
      let xiuGaiSJ =
        year +
        '-' +
        (month < 10 ? '0' + month : month) +
        '-' +
        (day < 10 ? '0' + day : day) +
        ' ' +
        (hours < 10 ? '0' + hours : hours) +
        ':' +
        (minutes < 10 ? '0' + minutes : minutes) +
        ':' +
        (seconds < 10 ? '0' + seconds : seconds)

      const formData = {
        ylEkpnyyzdVo: {
          ...this.formData.ylEkpnyyzdVo,
          yiZhuDanID: this.yiZhuDanID,
          bingLiID: this.bingLiID,
          naiZongLiang:
            this.formData.ylEkpnyyzdVo.naiDeCS * this.formData.ylEkpnyyzdVo.diYiGeNDS +
            this.formData.ylEkpnyyzdVo.naiDeCS * this.diErGeNDS,
          yiZhuYSYHID: this.userInfo.yongHuID,
          caoZuoZheID: this.userInfo.yongHuID,
          zhuangTaiBZ: '1',
          xiuGaiSJ
        },
        ylEkpnyyzdYpVoList: this.drugInfoList
          .filter((item) => {
            return (
              !stringISEmpty(this.yaoPinXXForm[item.yaoPinID]) &&
              this.yaoPinXXForm[item.yaoPinID] > 0
            )
          })
          .map((item) => {
            return {
              yiZhuDanID: this.yiZhuDanID, //医嘱单ID
              yaoPinID: item.yaoPinID, //药品ID
              yaoPinYL: this.yaoPinXXForm[item.yaoPinID] //药品用量
            }
          })
      }
      console.log(formData)

      const res = await savePdnYlEkpnyyzd(formData)
      console.log('儿科静脉营养医嘱_保存医嘱单', res)
      if (res.hasError === 0) {
        this.yiZhuDanID = res.data
        this.$message({
          message: '保存成功',
          type: 'success',
          duration: 1500
        })
        this.$emit('upload-success')
      }
    },

    //提交
    async uploadFormSubmit() {
      if (stringISEmpty(this.chiXuTS)) {
        this.$message({
          message: '持续输液不能为空!',
          type: 'error',
          duration: 1500
        })
        return false
      }
      const data = {
        bingLiID: this.bingLiID,
        bingRenXM: this.patientInit.bingRenXM,
        chiXuTS: this.chiXuTS, //持续天数
        // yaoFangDM: this.yaoFangDM,
        yaoFangDM: '2A3',
        yiZhuDanID: this.yiZhuDanID, //医嘱单ID
        zhiXingPL: 'st',
        zhuYuanID: this.patientInit.zhuYuanID, //住院ID
        zhuanKeID: this.initInfo.zhuanKeID, //专科ID
        zhiLiaoZuID: this.zhiLiaoZuID
      }
      const res = await savePdnYPYZ(data)

      console.log('儿科静脉营养医嘱_保存医嘱药品(提交)', res)
      if (res.hasError === 0) {
        this.$message({
          message: '提交成功',
          type: 'success',
          duration: 1500
        })

        this.$emit('upload-success')
        // this.$emit('update:visible', false)
      }
    },

    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    initChart() {
      const chart = echarts.init(this.$refs.chart)
      const red = 7.8
      const yellow = 3.9
      const dataList = [
        '02-08',
        '02-09',
        '02-10',
        '02-11',
        '02-12',
        '02-13',
        '02-14',
        '02-15',
        '02-16',
        '02-17',
        '02-18'
      ]
      const option = {
        tooltip: {},
        legend: {
          data: ['血糖值', String(red), String(yellow)]
        },
        xAxis: {
          data: dataList
        },
        yAxis: {},
        series: [
          {
            name: '血糖值',
            type: 'line',
            data: [3.1, 16.9, 42, 16.8, 17, 33, 30.5, 33, 20.5, 1, 0.1]
          },
          {
            name: red,
            type: 'line',
            data: dataList.map((item) => {
              return red
            })
          },
          {
            name: yellow,
            type: 'line',
            data: dataList.map((item) => {
              return yellow
            })
          }
        ]
      }
      chart.setOption(option)
    },

    async initFormData() {
      // const res = await getPdnPreviousYZDByBlid({
      //   bingLiID: this.bingLiID
      // })
      // console.log('儿科静脉营养医嘱_根据病历ID获取上一次医嘱单', res)
      // if (res.hasError === 0) {
      // }
      this.initChart()
      console.log(this.nutritionData)
      this.formData = this.nutritionData
        ? deepClone(this.nutritionData)
        : deepClone({
            ylEkpnyyzdVo: {
              naiLeiXing1: this.naiLeiXing1Option[0].beiZhu,
              naiLeiXing2: this.naiLeiXing2Option[0].beiZhu,
              rongLiangXS: 1.1,
              anJiSuan: 0.25,
              zhiFangRu: 0,
              pnSJ: 24
            }
          })
      this.yiZhuDanID = this.nutritionData ? this.nutritionData.ylEkpnyyzdVo.yiZhuDanID : 0
      this.chiXuTS = null

      this.select_ZFYP = this.drugInfoList.find((item) => {
        return item.zongLiang === '3'
      })?.yaoPinID

      let WSS = {}
      let ZKND = {}
      this.drugInfoList.forEach((item) => {
        if (item.zongLiang === '1') {
          WSS[item.yaoPinID] = 1
        } else if (item.zongLiang === '7') {
          ZKND[item.yaoPinID] = 0
        }
      })
      this.selected_WSS = WSS
      this.selected_ZKND = ZKND
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-title {
  font-size: 16px;
}

.drawer-component {
  font-size: 13px;
  height: 80vh;
  overflow-y: auto;
  padding: 0px 16px;

  .table-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-left: 10px;
    font-size: 15px;
    font-weight: 600;
    .bar {
      border-left: 3px solid #356ac5;
      padding-left: 5px;
    }
  }
  .label-frame {
    width: 33%;
    padding: 7px;
    border: 1px solid #ddd;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    flex-direction: column;

    .action-button {
      margin-top: 10px;
    }
  }

  table {
    width: 100%;
  }

  th {
    border: 1px solid #ddd;
    border-collapse: collapse;
    /* 移除表格内边框间的间隙 */
    height: 35px;
    background-color: #fafbfc;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse;
    /* 移除表格内边框间的间隙 */
    height: 35px;
  }

  .info-label {
    text-align: right;
    background-color: #eaf0f9;
    .info-content {
      width: 9vw;
      padding: 5px;
    }

    span {
      color: #f35656;
      position: relative;
      top: 3px;
      right: 3px;
    }
  }

  .info-value {
    background-color: #ffffff;
    text-align: left;
    .info-content {
      width: 14vw;
      padding: 5px;
    }

    .el-input {
      width: 100px;
    }
  }

  .drug-table {
    .info-value {
      text-align: left;

      .info-content {
        width: 9vw;
      }
    }

    .info-label {
      .info-content {
        width: 14vw;
      }
    }
  }

  .inspect-table {
    .info-value {
      .info-content {
        width: 9vw;
      }
    }

    .info-label {
      .info-content {
        width: 9vw;
      }
    }
  }

  .hint-info {
    margin-top: 10px;
    display: flex;

    .el-icon-warning {
      margin: 3px 3px 0 0;
      color: #356ac5;
    }
  }
}

:deep(.el-dialog__footer) {
  border-top: none;
}

:deep(.el-radio) input[aria-hidden='true'] {
  display: none !important;
}
</style>
