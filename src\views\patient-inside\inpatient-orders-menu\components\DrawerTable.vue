<template>
  <el-drawer
    :visible="visible"
    destroy-on-close
    :wrapper-closable="false"
    :append-to-body="false"
    :modal="false"
    :show-close="false"
    :size="drawerFullScreen ? '100%' : '50%'"
    direction="btt"
  >
    <template #title>
      <div class="drawer-header">
        <span class="full-button" @click="drawerFullScreen = !drawerFullScreen">
          <i :class="drawerFullScreen ? 'el-icon-caret-bottom' : 'el-icon-caret-top'"></i>
        </span>
        <div class="title">新增医嘱</div>
        <span>药品检索方式：</span>
        <el-radio-group v-model="yaoPinJianSuo" size="small">
          <el-radio label="0">精确</el-radio>
          <el-radio label="1">模糊</el-radio>
        </el-radio-group>
        <div style="margin-left: 8px">
          <span>草药总价:</span>
          <span>0元</span>
        </div>
      </div>
    </template>
    <el-table
      ref="drawerTable"
      :data="drawerYiZhuList"
      border
      stripe
      height="100%"
      size="mini"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="40"></el-table-column>
      <el-table-column
        v-for="column in drawerColumns"
        :key="column.value"
        :prop="column.value"
        :label="column.label"
        v-bind="column.props"
      >
        <template v-if="column.component" #default="{ row }">
          <!-- 动态加载组件 -->
          <component
            :is="column.component"
            :row="row"
            :column="column"
            :tab-active="tabActive"
            :inpatient-init="inpatientInit"
            @updateRow="({ prop, updateValue }) => handleUpdateRow(row, prop, updateValue)"
            @updateColumns="handleUpdateColumns"
            @inputBlur="({ prop, inputValue }) => handleInputBlur(row, prop, inputValue)"
            @inputFocus="({ prop, inputValue }) => handleInputFocus(row, prop, inputValue)"
          />
        </template>
      </el-table-column>
    </el-table>
  </el-drawer>
</template>
<script>
import { checkZhongYaoYiCiYL, getYYTS } from '@/api/inpatient-order'

export default {
  name: 'DrawerYiZhuList',
  components: {
    DrawerTableDatePicker: () => import('./DrawerTableDatePicker.vue'),
    DrawerTableSelect: () => import('./DrawerTableSelect.vue'),
    DrawerTableDrugList: () => import('./DrawerTableDrugList.vue'),
    DrawerTableInput: () => import('./DrawerTableInput.vue'),
    DrawerTableCheckbox: () => import('./DrawerTableCheckbox.vue')
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default: () => []
    },
    inpatientInit: {
      type: Object,
      default: () => {}
    },
    tabActive: {
      type: String,
      default: 'cq'
    }
  },
  data() {
    return {
      drawerFullScreen: false,
      yaoPinJianSuo: '0',
      drawerColumns: [
        { value: 'xuNiZH', label: '组号', props: { fixed: true } },
        {
          value: 'yiZhuLX',
          label: '医嘱类型',
          props: { fixed: true, width: '135' },
          component: 'DrawerTableSelect',
          options: [{ value: 'cq', label: '长期医嘱' }]
        },
        {
          value: 'kaiShiSJ',
          label: '计划开始日期',
          props: { fixed: true, width: '175' },
          component: 'DrawerTableDatePicker'
        },
        {
          value: 'mingCheng',
          label: '医嘱名称',
          props: { fixed: true, width: '250' },
          component: 'DrawerTableDrugList',
          moHuSS: this.yaoPinJianSuo,
          inpatientInit: this.inpatientInit
        },
        { value: 'zuHaoTXT', label: '组', props: { fixed: true, width: '30', align: 'center' } },
        { value: 'yiCiYL', label: '一次用量', component: 'DrawerTableInput' },
        {
          value: 'jiLiangDW',
          label: '剂量单位',
          props: { width: '130' },
          component: 'DrawerTableSelect',
          options: []
        },
        {
          value: 'zhiXingFF',
          label: '用法',
          props: { width: '130' },
          component: 'DrawerTableSelect',
          options: []
        },
        {
          value: 'zhiXingPL',
          label: '频率',
          props: { width: '130' },
          component: 'DrawerTableSelect',
          options: []
        },
        {
          value: 'geiYaoSJ',
          label: '用药/执行时间',
          props: { width: '130' },
          component: 'DrawerTableSelect',
          options: []
        },
        {
          value: 'guiGe',
          label: '规格/单价',
          props: { width: '100' },
          component: 'DrawerTableInput'
        },
        { value: 'yiShengXM', label: '医师姓名' },
        {
          value: 'leiBie',
          label: '类别',
          props: { width: '130' },
          component: 'DrawerTableSelect',
          options: [
            { value: '3', label: '药品' },
            { value: '1', label: '治疗' },
            { value: '2', label: '饮食' },
            { value: '4', label: '嘱托' }
          ]
        },
        { value: 'feiYongLXMC', label: '费用类型' },
        {
          value: 'shiFouZB',
          label: '自备',
          component: 'DrawerTableCheckbox',
          props: { width: '50' }
        },
        {
          value: 'jinRiLT',
          label: '今日临停',
          component: 'DrawerTableCheckbox',
          props: { width: '50' }
        },
        {
          value: 'teShuYF',
          label: '特殊用法/备注说明',
          props: { width: '150' },
          component: 'DrawerTableInput'
        },
        {
          value: 'bingQuID',
          label: '执行病区',
          props: { width: '130' },
          component: 'DrawerTableSelect',
          options: []
        },
        { value: 'shouFeiSL', label: '数量/剂数', component: 'DrawerTableInput' }, // 收费数量
        { value: 'chiXuTS', label: '持续天数', component: 'DrawerTableInput' },
        { value: 'yongYaoTS', label: '用药天数', component: 'DrawerTableInput' },
        { value: 'danWei', label: '单位' },
        { value: 'shouFeiCS', label: '收费次数' },
        { value: 'jinRiCY', label: '预计出院' }, // 今日出院
        { value: 'zanShiBQMC', label: '是否代煎' }, // 是否自取 3-代煎 4-自煎
        { value: 'yaoPinDSBZ', label: '滴速' }, // 药品滴速备注
        {
          value: 'jieShuSJ',
          label: '结束时间',
          props: { width: '185' },
          component: 'DrawerTableDatePicker'
        },
        {
          value: 'tongZhiDanID',
          label: '手术通知单',
          props: { width: '130' },
          component: 'DrawerTableSelect',
          options: []
        },
        { value: 'luRuSJ', label: '医嘱时间', props: { width: '90' } }, // 录入时间
        { value: 'daoRuRYXM', label: '导入人员' },
        { value: 'daoRuSJ', label: '导入时间', props: { width: '90' } }
      ],
      checkYiCiYLTimer: null
    }
  },
  computed: {
    drawerYiZhuList: {
      get() {
        return this.value
      },
      set(newValue) {
        this.$emit('input', [...newValue])
      }
    }
  },
  watch: {
    inpatientInit: {
      handler(newValue) {
        const mapOptions = (data, valueKey, labelKey) => {
          return (
            data?.map((item) => ({
              value: item[valueKey],
              label: item[labelKey]
            })) || []
          )
        }
        this.drawerColumns.forEach((item) => {
          switch (item.value) {
            case 'zhiXingFF':
              item.options = mapOptions(newValue?.feiCaoYaoYYFF, 'fangFaDM', 'fangFaMC')
              break
            case 'zhiXingPL':
              item.options = mapOptions(newValue?.yongYaoPL, 'pinLuDM', 'pinLuMC')
              break
            case 'geiYaoSJ':
              item.options = mapOptions(newValue?.geiYaoSJ, 'geiYaoSJDM', 'geiYaoSJMC')
              break
            case 'bingQuID':
              item.options = mapOptions(newValue?.zhiXingBQs, 'daiMa', 'mingCheng')
              break
            case 'tongZhiDanID':
              item.options = mapOptions(newValue?.shouShuTZDs, 'daiMa', 'mingCheng')
              break
            default:
              break
          }
        })
      },
      immediate: true
    },
    yaoPinJianSuo(newValue) {
      this.drawerColumns.forEach((item) => {
        if (item.value === 'mingCheng') {
          item.moHuSS = newValue
        }
      })
    },
    tabActive: {
      handler(newValue) {
        this.drawerColumns.forEach((item) => {
          if (item.value === 'yiZhuLX') {
            item.options =
              newValue === 'cq'
                ? [{ value: 'cq', label: '长期医嘱' }]
                : [
                    { value: 'ls', label: '临时医嘱' },
                    { value: 'cydy', label: '出院带药' },
                    { value: 'cy', label: '草药医嘱' },
                    { value: 'zcydy', label: '草药带药' }
                  ]
          }
        })
      },
      immediate: true
    }
  },
  methods: {
    async handleUpdateRow(row, prop, updateValue) {
      if (typeof updateValue === 'object' && prop === 'mingCheng') {
        // 更新多个字段
        Object.keys(updateValue).forEach((key) => {
          this.$set(row, key, updateValue[key])
          // 处理医嘱类型、开始时间、执行频率、执行方法、给药时间、结束时间的全组同步
          if (
            key === 'yiZhuLX' ||
            key === 'kaiShiSJ' ||
            key === 'zhiXingPL' ||
            key === 'zhiXingFF' ||
            key === 'geiYaoSJ' ||
            key === 'jieShuSJ'
          ) {
            const currentPrefix = row.xuNiZH.split('-')[0]
            this.drawerYiZhuList.forEach((item) => {
              const itemPrefix = item.xuNiZH.split('-')[0]
              if (itemPrefix === currentPrefix) {
                this.$set(item, key, updateValue[key])
                this.$set(item, 'leiBie', updateValue['leiBie'] || '3')
                // 如果是更新医嘱类型，且是草药或草药带药
                if (prop === 'yiZhuLX') {
                  if (updateValue['yiZhuLX'] === 'cy' || updateValue['yiZhuLX'] === 'zcydy') {
                    this.$set(item, 'zhiXingFF', '煎服')
                    this.$set(item, 'zhiXingPL', 'bid')
                    this.$set(item, 'geiYaoSJ', 'pc')
                    this.$set(item, 'shouFeiSL', '7')
                    let count = 1
                    this.drawerYiZhuList.forEach((groupItem) => {
                      const groupItemPrefix = groupItem.xuNiZH.split('-')[0]
                      if (groupItemPrefix === currentPrefix) {
                        this.$set(groupItem, 'xuNiZH', `${currentPrefix}-${count}`)
                        count++
                      }
                    })
                  } else {
                    this.$set(item, 'xuNiZH', currentPrefix)
                  }
                }
              }
            })
          }
        })
      } else {
        // 更新单个字段
        this.$set(row, prop, updateValue)
        // 处理全组同步
        if (
          prop === 'yiZhuLX' ||
          prop === 'kaiShiSJ' ||
          prop === 'zhiXingPL' ||
          prop === 'zhiXingFF' ||
          prop === 'geiYaoSJ' ||
          prop === 'jieShuSJ'
        ) {
          const currentPrefix = row.xuNiZH.split('-')[0]
          this.drawerYiZhuList.forEach((item) => {
            const itemPrefix = item.xuNiZH.split('-')[0]
            if (itemPrefix === currentPrefix) {
              this.$set(item, prop, updateValue)
              this.$set(item, 'leiBie', updateValue['leiBie'] || '3')
              // 如果是更新医嘱类型，且是草药或草药带药
              if (prop === 'yiZhuLX') {
                if (updateValue === 'cy' || updateValue === 'zcydy') {
                  this.$set(item, 'zhiXingFF', '煎服')
                  this.$set(item, 'zhiXingPL', 'bid')
                  this.$set(item, 'geiYaoSJ', 'pc')
                  this.$set(item, 'shouFeiSL', '7')
                  let count = 1
                  this.drawerYiZhuList.forEach((groupItem) => {
                    const groupItemPrefix = groupItem.xuNiZH.split('-')[0]
                    if (groupItemPrefix === currentPrefix) {
                      this.$set(groupItem, 'xuNiZH', `${currentPrefix}-${count}`)
                      count++
                    }
                  })
                } else {
                  this.$set(item, 'xuNiZH', currentPrefix)
                }
              }
            }
          })
        }
      }

      // 切换类别拉下框选项
      let options = []
      switch (row.yiZhuLX) {
        case 'cq':
          options = [
            { value: '3', label: '药品' },
            { value: '1', label: '治疗' },
            { value: '2', label: '饮食' },
            { value: '4', label: '嘱托' }
          ]
          break
        case 'ls':
          options = [
            { value: '3', label: '药品' },
            { value: '1', label: '治疗' },
            { value: '4', label: '嘱托' }
          ]
          break
        default:
          options = [{ value: '3', label: '药品' }]
          break
      }
      this.drawerColumns.forEach((item) => {
        if (item.value === 'leiBie') {
          item.options = options
        }
      })
    },
    handleUpdateColumns({ column, options }) {
      this.drawerColumns.forEach((item) => {
        if (item.value === column) {
          item.options = options
        }
      })
    },
    async handleInputBlur(row, prop, inputValue) {
      switch (prop) {
        case 'yiCiYL':
          // 一次用量校验
          if (row.zhiXingFF && row.zhiXingPL) {
            if (row.changYongLiang && row.yiCiYL > Number(row.changYongLiang) * 2) {
              await this.$alert(
                `【${row.mingCheng}】您的一次用量超过常用量的2倍，请注意！`,
                '提示',
                {
                  confirmButtonText: '确定',
                  type: 'info'
                }
              )
            }

            // 草药一次用量校验
            try {
              if (
                row.yiCiYL &&
                row.yaoPinData.jiXing === 'Ypj' &&
                (row.yiZhuLX === 'cy' || row.yiZhuLX === 'zcydy')
              ) {
                await checkZhongYaoYiCiYL({
                  jiLiangDW: row.jiLiangDW,
                  yaoFangDM: row.yaoPinData.yaoFangDM,
                  yaoPinID: row.yaoPinData.yaoPinID,
                  yaoPinMC: encodeURIComponent(row.mingCheng),
                  yiCiYL: row.yiCiYL
                })
              }
            } catch (e) {
              await this.$alert(e.errorMessage, '提示', {
                confirmButtonText: '确定',
                type: 'info'
              })
            }
          }
          break
        case 'shouFeiSL':
          if (row.yiZhuLX === 'cydy') {
            if (
              row.yiCiYL &&
              row.jiLiangDW &&
              row.zhiXingFF &&
              row.zhiXingPL &&
              row.shouFeiSL &&
              row?.yaoPinData?.yaoPinID
            ) {
              const res = await getYYTS({
                yiCiYL: row.yiCiYL,
                jiLiangDW: row.jiLiangDW,
                yongYaoFF: row.zhiXingFF,
                yongYaoPL: row.zhiXingPL,
                shuLiang: row.shouFeiSL,
                yaoFangDM: row.yaoPinData.yaoFangDM,
                yaoPinID: row.yaoPinData.yaoPinID
              })
              if (res.hasError === 0) {
                this.$set(row, 'yongYaoTS', res.data)
              }
            }
          }
          await this.validateShouFeiSL(row, prop, inputValue)
          break
      }
    },
    async handleInputFocus(row, prop, inputValue) {
      switch (prop) {
        case 'shouFeiSL':
          await this.validateShouFeiSL(row, prop, inputValue)
          break
      }
    },
    async handleRowClick(row, column, event) {
      this.$emit('row-click', { row, column, event })
    },
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection)
    },
    // 处理收费数量的校验
    async validateShouFeiSL(row, prop, inputValue) {
      if (row.yiZhuLX === 'cydy') {
        // 出院带药的收费数量校验
        if (row?.yaoPinData?.keShouFeiYL) {
          const keShouFeiYL = row.yaoPinData.keShouFeiYL
          if (inputValue && Number(inputValue) > Number(keShouFeiYL)) {
            await this.$confirm(
              `【${row.mingCheng}】的可收费用量为${keShouFeiYL}， 您所开数量超过限量，系统将默认将其修改为合法值！`,
              '提示',
              {
                showCancelButton: false,
                type: 'info'
              }
            )
            this.$set(row, 'shouFeiSL', keShouFeiYL)
          }
        }
      } else if (row.yiZhuLX === 'cy' || row.yiZhuLX === 'zcydy') {
        // 草药或草药带药的收费数量校验
        if (inputValue && Number(inputValue) > 30) {
          await this.$confirm(`帖数不得超过30帖！`, '提示', {
            confirmButtonText: '确定',
            type: 'info'
          })
          this.$set(row, 'shouFeiSL', '30')
        }
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
