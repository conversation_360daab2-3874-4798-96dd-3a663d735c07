<template>
  <!--出院记录-->
  <div class="discharge-summary-container">
    <!-- 评分表弹窗 -->
    <default-dialog
      :title="scoreFormDialog.title"
      :visible.sync="scoreFormDialog.visible"
      :width="scoreFormDialog.width"
      pop-type="custom"
      append-to-body
      destroy-on-close
      @cancel="closeScoreFormDialog"
    >
      <div :style="{ height: scoreFormDialog.height, position: 'relative' }">
        <div v-if="scoreFormDialog.loading" class="score-form-loading">
          <div class="loading-spinner">
            <i class="el-icon-loading"></i>
            <p>加载中...</p>
          </div>
        </div>
        <iframe
          v-if="scoreFormDialog.url"
          :src="scoreFormDialog.url"
          frameborder="0"
          style="width: 100%; height: 100%"
          @load="handleScoreFormIframeLoad"
        ></iframe>
      </div>
    </default-dialog>

    <div class="discharge-summary-content">
      <!-- 头部标题和按钮 -->
      <div class="header">
        <div class="title">出院记录</div>
        <div class="actions">
          <el-button type="primary" size="mini" @click="handleSaveClick">保存</el-button>
          <el-button type="danger" size="mini" @click="handleDeleteClick">删除</el-button>
          <el-button type="info" size="mini" @click="handlePrintClick">打印</el-button>
        </div>
      </div>

      <!-- 内容区域 -->
      <div v-loading="loading" class="content" element-loading-text="加载中...">
        <iframe
          v-if="iframeUrl"
          ref="contentIframe"
          :src="iframeUrl"
          frameborder="0"
          @load="handleIframeLoad"
        ></iframe>
        <div v-else class="no-content">
          <i class="el-icon-document"></i>
          <p>暂无内容</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getWenShuListByWslx,
  checkRankinStatus,
  checkICUStatus,
  checkChuYuanJL,
  getChuYuanJLGSDM
} from '@/api/discharge-summary'
import { mapState } from 'vuex'
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'

export default {
  name: 'DischargeSummary',
  components: {
    DefaultDialog
  },
  data() {
    return {
      // 文书列表
      wenShuList: [],
      // 当前文书
      currentWenShu: null,
      // iframe URL
      iframeUrl: '',
      // 加载状态
      loading: false,
      // 基础URL
      baseUrl: 'http://************/ehr',
      // 评分表弹窗
      scoreFormDialog: {
        visible: false,
        title: '',
        url: '',
        loading: true,
        width: '800px',
        height: '80vh'
      }
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientInfo: ({ patient }) => patient.patientInit,
      gongZhongDM: ({ patient }) => patient.doctorInfo.gongZhongDM_DZ, // 工种代码对照
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  mounted() {
    // 初始化数据
    this.fetchInitData()
  },
  methods: {
    // 获取初始化数据
    async fetchInitData() {
      this.loading = true
      try {
        // 获取文书列表
        const res = await getWenShuListByWslx({
          bingLiID: this.bingLiID,
          wenShuLX: '1E' // 出院记录的文书类型
        })

        if (res.hasError === 0) {
          this.wenShuList = res.data || []

          if (this.wenShuList.length > 0) {
            // 取第一条记录
            this.currentWenShu = this.wenShuList[0]
            // 设置iframe URL
            this.setIframeUrl(this.currentWenShu)
          } else {
            // 如果列表为空，则新增一份
            await this.handleAddRecord()
          }
        } else {
          this.$message.error(res.errorMsg || '获取文书列表失败')
        }
      } catch (error) {
        console.error('初始化失败', error)
        this.$message.error('初始化失败')
      } finally {
        this.loading = false
      }
    },

    // 设置iframe URL
    setIframeUrl(record) {
      if (!record) {
        this.iframeUrl = ''
        return
      }

      // 构建编辑URL
      this.iframeUrl = this.getRecordEditUrl(record)
    },

    // 获取记录编辑URL
    getRecordEditUrl(record) {
      if (!record) return ''

      // 构建URL参数
      let params = {
        as_blid: record.bingliID || this.bingLiID,
        as_zyid: this.patientInfo.zhuYuanID,
        as_zkid: this.zhuanKeID,
        as_openmode: 'ehr3',
        tmpid: Math.random()
      }

      // 如果是新增记录
      if (!record.id || record.id.toString().startsWith('t')) {
        params = {
          ...params,
          as_gsdm: record.geShiDM,
          as_wsid: 0,
          as_wslx: record.wenShuLX || '1E',
          as_yhid: this.yongHuID,
          as_tmpid: 't1'
        }
      }

      // 将参数转换为URL查询字符串
      const queryString = Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join('&')

      // 返回完整URL
      return `${this.baseUrl}/zyblws/cyjl.aspx?${queryString}`
    },

    // 处理iframe加载完成
    handleIframeLoad() {
      console.log('iframe加载完成')
      this.loading = false
    },

    // 处理新增记录
    async handleAddRecord() {
      try {
        this.loading = true

        // 1. 检查用户是否有权限书写文书
        if (this.gongZhongDM === '0000') {
          await this.$confirm('非医生账号无法书写文书，请联系医务处或信息处进行确认修改', '提示', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'warning'
          })
          return
        }

        // 2. 检查mRS评分情况
        const mrsRes = await checkRankinStatus({
          bingLiID: this.bingLiID,
          wenShuLX: '1E'
        })

        if (mrsRes.hasError !== 0) {
          this.$message.error(mrsRes.errorMsg || '检查mRS评分状态失败')
          return
        }

        if (mrsRes.data.status === '1') {
          await this.$confirm(
            mrsRes.data.message || '完成改良Rankin量表(mRS)评分后才可书写出院记录',
            '提示',
            {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            }
          ).then(() => {
            // 打开mRS评分表页面
            this.openScoreForm('改良Rankin量表(mRS)评分', 72)
          })
          return
        }

        // 3. 转科记录(14)和24小时入出院记录(08)的特殊检查
        // if (selectedWenShu.wenShuLX === '14' || selectedWenShu.wenShuLX === '08') {
        //   const icuRes = await checkICUStatus({
        //     bingLiID: this.bingLiID,
        //     wenShuLX: selectedWenShu.wenShuLX
        //   })

        //   if (icuRes.hasError !== 0) {
        //     this.$message.error(icuRes.errorMsg || '检查ICU状态失败')
        //     return
        //   }

        //   if (icuRes.data.status === '1') {
        //     await this.$confirm(
        //       icuRes.data.message || 'ICU患者填写出院记录需填ICU质量控制登记表！',
        //       '提示',
        //       {
        //         confirmButtonText: '确定',
        //         showCancelButton: false,
        //         type: 'warning'
        //       }
        //     ).then(() => {
        //       // 打开ICU质量控制登记表页面
        //       this.openScoreForm('ICU质量控制登记表', 71)
        //     })
        //     return
        //   }
        // }

        // 4. 出院记录常规判断
        const chuYuanRes = await checkChuYuanJL({
          bingLiID: this.bingLiID,
          wenShuLX: '1E'
        })

        if (chuYuanRes.hasError !== 0) {
          this.$message.error(chuYuanRes.errorMsg || '检查出院记录状态失败')
          return
        }

        if (chuYuanRes.data.status === '1') {
          await this.$confirm(
            chuYuanRes.data.message || '该患者已存在出院记录，不能重复添加',
            '提示',
            {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            }
          )
          return
        }

        // 5. 根据专科ID获取出院记录格式代码
        const chuYuanJLRes = await getChuYuanJLGSDM({
          zhuanKeID: this.zhuanKeID
        })

        if (chuYuanJLRes.hasError !== 0) {
          this.$message.error(chuYuanJLRes.errorMsg || '获取出院记录格式代码失败')
          return
        }

        // 6. 创建新记录对象
        const newRecord = {
          id: 't' + new Date().getTime(),
          wenShuMC: '出院记录',
          geShiDM: chuYuanJLRes.data.geShiDM,
          wenShuLX: '1E'
        }

        // 7. 设置当前文书和iframe URL
        this.currentWenShu = newRecord
        this.setIframeUrl(newRecord)
      } catch (error) {
        this.$message.error('新增记录失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },

    // 处理保存按钮点击
    async handleSaveClick() {
      if (!this.currentWenShu) {
        this.$message.error('没有可保存的文书')
        return
      }

      // 获取iframe元素
      const iframe = this.$refs.contentIframe
      if (!iframe || !iframe.contentWindow) {
        this.$message.error('无法访问iframe内容')
        return
      }

      try {
        // 调用iframe内部的保存方法
        if (typeof iframe.contentWindow.ehr_save === 'function') {
          iframe.contentWindow.ehr_save()

          // 重新获取文书列表
          await this.refreshWenShuList()
          this.$message.success('保存成功')
        } else {
          this.$message.error('iframe中没有保存方法')
        }
      } catch (error) {
        console.error('保存失败', error)
        this.$message.error('保存失败: ' + (error.message || '未知错误'))
      }
    },

    // 刷新文书列表
    async refreshWenShuList() {
      try {
        // 获取文书列表
        const res = await getWenShuListByWslx({
          bingLiID: this.bingLiID,
          wenShuLX: '1E' // 出院记录的文书类型
        })

        if (res.hasError === 0) {
          this.wenShuList = res.data || []

          if (this.wenShuList.length > 0) {
            // 更新当前文书
            this.currentWenShu = this.wenShuList[0]
            // 设置iframe URL
            this.setIframeUrl(this.currentWenShu)
          }
        } else {
          this.$message.error(res.errorMsg || '获取文书列表失败')
        }
      } catch (error) {
        console.error('刷新文书列表失败', error)
        this.$message.error('刷新文书列表失败')
      }
    },

    // 处理删除按钮点击
    handleDeleteClick() {
      if (!this.currentWenShu) {
        this.$message.error('没有可删除的文书')
        return
      }

      this.$confirm('确定要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          // 获取iframe元素
          const iframe = this.$refs.contentIframe
          if (!iframe || !iframe.contentWindow) {
            this.$message.error('无法访问iframe内容')
            return
          }

          try {
            // 调用iframe内部的删除方法
            if (typeof iframe.contentWindow.ehr_delete === 'function') {
              iframe.contentWindow.ehr_delete()

              // 临时记录是否为新增的记录（ID以't'开头）
              const isTemporaryRecord =
                this.currentWenShu.id && this.currentWenShu.id.toString().startsWith('t')

              if (isTemporaryRecord) {
                // 如果是临时记录（新增但未保存），清空当前文书
                this.currentWenShu = null
                this.iframeUrl = ''
              } else {
                // 如果是已保存的记录，重新获取文书列表
                await this.refreshWenShuList()
              }

              this.$message.success('删除成功')
            } else {
              this.$message.error('iframe中没有删除方法')
            }
          } catch (error) {
            console.error('删除失败', error)
            this.$message.error('删除失败: ' + (error.message || '未知错误'))
          }
        })
        .catch(() => {
          // 取消删除，不做任何操作
        })
    },

    // 处理打印按钮点击
    handlePrintClick() {
      if (!this.currentWenShu) {
        this.$message.error('没有可打印的文书')
        return
      }

      // 获取iframe元素
      const iframe = this.$refs.contentIframe
      if (!iframe || !iframe.contentWindow) {
        this.$message.error('无法访问iframe内容')
        return
      }

      try {
        // 调用iframe内部的打印方法
        if (typeof iframe.contentWindow.print === 'function') {
          iframe.contentWindow.ehr_print()
        } else {
          this.$message.error('iframe中没有打印方法')
        }
      } catch (error) {
        console.error('打印失败', error)
        this.$message.error('打印失败: ' + (error.message || '未知错误'))
      }
    },

    // 打开评分表页面（使用弹窗）
    openScoreForm(title, pfid) {
      // 设置弹窗属性
      this.scoreFormDialog.title = title
      this.scoreFormDialog.url = `${this.baseUrl}/pfbgl/pfblist.aspx?as_blid=${this.bingLiID}&as_pfid=${pfid}&as_pfzhid=0`
      this.scoreFormDialog.loading = true
      this.scoreFormDialog.visible = true
    },

    // 关闭评分表弹窗
    closeScoreFormDialog() {
      this.scoreFormDialog.visible = false
      this.scoreFormDialog.url = ''
      this.scoreFormDialog.title = ''
    },

    // 处理评分表iframe加载完成
    handleScoreFormIframeLoad() {
      this.scoreFormDialog.loading = false
    }
  }
}
</script>

<style scoped lang="scss">
.discharge-summary-container {
  height: 100%;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  background-color: #f6f6f6;
  position: relative;
}

.discharge-summary-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 20px;
  background-color: #eaf0f9;
  border-bottom: 1px solid #dcdfe6;
}

.title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  display: inline-block;
  border-left: 4px solid #356ac5;
  padding-left: 5px;
}

.actions {
  display: flex;
  gap: 10px;
}

.content {
  flex: 1;
  position: relative;
  overflow: hidden;

  iframe {
    width: 100%;
    height: 100%;
    border: none;
    display: block;
  }

  .no-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 10px;
    }

    p {
      font-size: 16px;
    }
  }
}

// 评分表弹窗样式
.score-form-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  .loading-spinner {
    text-align: center;

    i {
      font-size: 32px;
      color: #409eff;
    }

    p {
      margin-top: 10px;
      color: #606266;
    }
  }
}
</style>
