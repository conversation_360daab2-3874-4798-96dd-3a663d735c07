// 长时间不操作默认锁屏时间
export const initTime = 60 * 60

const state = {
  isLock: false, // 是否锁屏
  lockTime: 3600 // 锁屏时间
}

const mutations = {
  SET_LOCK(state, data) {
    state.isLock = data
  },
  SET_LOCK_TIME(state, data) {
    state.lockTime = data
  }
}

const actions = {
  setLock({ commit }, data) {
    commit('SET_LOCK', data)
  },
  setLockTime({ commit }, data = initTime) {
    commit('SET_LOCK_TIME', data)
  }
}

const getters = {
  lockTime(state) {
    return state.lockTime
  },
  isLock(state) {
    return state.isLock
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
