<template>
  <el-dialog
    :class="{ 'scroll-body': scrollBody }"
    :title="title"
    :visible="visible"
    :width="width"
    v-bind="$attrs"
    :close-on-click-modal="false"
    :custom-class="`dialog dialog-${size}`"
    @close="handleCancel"
    @open="$emit('open')"
  >
    <template #title>
      <slot name="title">{{ title }}</slot>
    </template>
    <slot></slot>
    <template>
      <!-- 提示模式 关闭 -->
      <span v-if="popType === 'tip'" slot="footer" class="dialog-footer">
        <el-button v-if="showCloseButton" @click="handleCancel">{{ closeButtonText }}</el-button>
      </span>
      <!-- 正常模式 确认 取消 -->
      <span v-if="popType === ''" slot="footer" class="dialog-footer">
        <el-button v-if="showConfirmButton" type="primary" @click="handleSubmit">
          {{ confirmButtonText }}
        </el-button>
        <slot name="footer"></slot>
        <el-button v-if="showCancelButton" @click="handleCancel">{{ cancelButtonText }}</el-button>
      </span>
      <!-- 保存模式 是 否 取消-->
      <span v-if="popType === 'save'" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">是</el-button>
        <el-button @click="closeDialog">否</el-button>
        <el-button v-if="showCancelButton" @click="handleCancel">{{ cancelButtonText }}</el-button>
      </span>
      <!-- 自定义模式 -->
      <span v-if="popType === 'custom'" slot="footer" class="dialog-footer">
        <slot name="footer"></slot>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'DefaultDialog',
  props: {
    scrollBody: {
      type: Boolean,
      default: false
    },
    fixedHeight: {
      type: Boolean,
      default: false
    },
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '提示'
    },
    size: {
      type: String,
      default: 'small'
    },
    width: {
      type: String,
      default: '60%'
    },
    // 数据是否变化
    isDataChange: {
      type: Boolean,
      default: false
    },
    confirmButtonText: {
      type: String,
      default: '确 定'
    },
    cancelButtonText: {
      type: String,
      default: '取 消'
    },
    closeButtonText: {
      type: String,
      default: '关 闭'
    },
    showConfirmButton: {
      type: Boolean,
      default: true
    },
    showCancelButton: {
      type: Boolean,
      default: true
    },
    showCloseButton: {
      type: Boolean,
      default: true
    },
    // 数据变化提示
    dataChangeTip: {
      type: String,
      default: '数据未保存,保存已修改的数据吗?'
    },
    // '' 正常模式  save提示框 保存模式 tip
    popType: {
      type: String,
      default: ''
    }
  },
  // export type PopType = '' | 'save' | 'tip'
  // export type DialogSize = 'large' | 'medium' | 'small' | 'mini'
  emits: ['confirm', 'close', 'update:visible', 'cancel', 'confirm'],
  data() {
    return {}
  },
  mounted() {},
  methods: {
    // 关闭前 数据变化弹窗判断
    handleClose() {
      if (this.isDataChange) {
        this.$showConfirm(
          this.dataChangeTip,
          () => {
            // 成功回调  是
            this.$emit('confirm', 'save')
          },
          {
            title: '信息提示',
            width: '340px',
            popType: 'save',
            // 否 回调
            closeCallback: () => {
              this.closeDialog()
            }
          }
        )
      } else {
        this.cancelDialog()
      }
    },
    // 否
    closeDialog() {
      this.$emit('close')
      this.$emit('update:visible', false)
    },
    // 取消 X
    cancelDialog() {
      this.$emit('update:visible', false)
      this.$emit('cancel')
    },
    // 确认
    handleSubmit() {
      this.$emit('confirm')
    },
    // 关闭 提示
    closeTip() {
      this.$emit('closeTip')
      this.$emit('update:visible', false)
    },
    // 关闭和取消
    handleCancel() {
      if (this.visible) {
        if (this.popType === 'tip') {
          // 提示模式
          this.closeTip()
        } else if (this.popType === 'save') {
          // 保存模式
          this.cancelDialog()
        } else {
          // 正常模式
          this.handleClose()
        }
      }
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-dialog {
    &.dialog {
      &-medium {
        width: var(--dialog-width-medium);
      }

      &-small {
        width: var(--dialog-width-small);
      }

      &-large {
        width: var(--dialog-width-large);
      }

      &-mini {
        width: var(--dialog-width-mini);
      }
    }

    &__header {
      border-bottom: 1px solid #dadee6;
      padding: 10px 14px;
      font: var(--font-medium);
      font-size: var(--font-size-medium);
      color: #171c28;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        width: 3px;
        height: 16px;
        background: #356ac5;
        margin-right: 6px;
      }

      .el-dialog__headerbtn {
        font-size: 19px;
        right: 14px;
        top: auto;

        .el-dialog__close {
          color: #8590b3;
        }
      }
    }
  }
}
.scroll-body {
  ::v-deep .el-dialog {
    &__body {
      max-height: calc(var(--content-fixed-height) * 0.9);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
}
</style>
