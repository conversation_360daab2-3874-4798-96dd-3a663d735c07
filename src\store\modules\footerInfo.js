/**
 * <AUTHOR>
 * @Date 2024-02-18
 * @LastEditors Raven
 * @LastEditTime 2024-03-07
 * @FilePath /base-wyyy-template/src/store/modules/footerInfo.js
 * @Description
 * @
 * @Copyright (c) 2024 by 附一医信息处, All Rights Reserved.
 */
import { getVerList } from '@/api/basic'
import { getDefaultState } from '@/store/modules/user'

/** 授权模块 对user的补充*/
const state = {
  caoZuoRenXM: '', //姓名
  caoZuoRenKS: '', //科室
  dengLuSJ: '', //登录时间
  ip: '', //ip
  jiShuZC: '', //技术支持
  banBenH: '' //版本号
}
const mutations = {
  SET_CAOZUORENXM(state, data) {
    state.caoZuoRenXM = data
  },
  SET_CAOZUORENKS(state, data) {
    state.caoZuoRenKS = data
  },
  SET_DENGLUSJ(state, data) {
    state.dengLuSJ = data
  },
  SET_IP(state, data) {
    state.ip = data
  },
  SET_JISHUZC(state, data) {
    state.jiShuZC = data
  },
  SET_BANBENH(state, data) {
    state.banBenH = data
  }
}

const actions = {
  getFooterInfo({ coomit, state, rootState, dispatch, rootGetters }, data) {
    dispatch('setbanBenH')
  },
  setcaoZuoRenXM({ commit }, data) {
    commit('SET_CAOZUORENXM', data)
  },
  setcaoZuoRenKS({ commit }, data) {
    commit('SET_CAOZUORENKS', data)
  },
  setdengLuSJ({ commit }, data) {
    commit('SET_DENGLUSJ', data)
  },
  setip({ commit }, data) {
    commit('SET_IP', data)
  },
  setjiShuZC({ commit }, data) {
    commit('SET_JISHUZC', data)
  },
  setbanBenH({ commit, state }, data) {
    getVerList({ fenYe: false, yingYongDM: getDefaultState().systemID }).then((rsp) => {
      try {
        commit('SET_BANBENH', rsp.data.jiLuLB[0].banBenHao)
      } catch {
        console.log('获取版本信息失败或者版本信息为空')
      }
    })
  },
  setFooterInfo({ commit }, { caoZuoRenXM, caoZuoRenKS, dengLuSJ, ip, jiShuZC, banBenH }) {
    commit('SET_CAOZUORENXM', caoZuoRenXM)
    commit('SET_CAOZUORENKS', caoZuoRenKS)
    commit('SET_DENGLUSJ', dengLuSJ)
    commit('SET_IP', ip)
    commit('SET_JISHUZC', jiShuZC)
    commit('SET_BANBENH', banBenH)
  }
}

const getters = {
  caoZuoRenXM(state) {
    return state.caoZuoRenXM
  },
  caoZuoRenKS(state) {
    return state.caoZuoRenKS
  },
  dengLuSJ(state) {
    return state.dengLuSJ
  },
  ip(state) {
    return state.ip
  },
  jiShuZC(state) {
    return state.jiShuZC
  },
  banBenH(state) {
    return state.banBenH
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
