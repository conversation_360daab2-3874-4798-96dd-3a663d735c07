<template>
  <div class="container">
    <div class="left">
      <div class="left-header">专科治疗组列表</div>
      <div class="left-content">
        <div class="left-content-title">专科治疗组选择</div>
        <div class="left-content-item">消化内科吴伟组</div>
        <div class="left-content-item">消化内科金抒清组</div>
      </div>
    </div>
    <div class="right">
      <div class="right-header">
        <div class="right-header-title">数据列表</div>
        <div>
          <el-button class="right-header-btn">新增</el-button>
        </div>
      </div>
      <div class="right-content">
        <el-table max-height="648" border :data="tableData">
          <el-table-column prop="kaiShiSJ" label="开始时间" width="160"></el-table-column>
          <el-table-column prop="jieShuSJ" label="结束时间" width="160"></el-table-column>
          <el-table-column prop="guDingCWS" label="固定床位数" width="110"></el-table-column>
          <el-table-column prop="buGuDingCWS" label="不固定床位数" width="110"></el-table-column>
          <el-table-column prop="teShuSM" label="特殊说明" width="170"></el-table-column>
          <el-table-column prop="zhuangTai" label="状态" width="70"></el-table-column>
          <el-table-column prop="xiuGaiSJ" label="修改时间" width="160"></el-table-column>
          <el-table-column prop="shenPiSJ" label="审批时间" width="160"></el-table-column>
          <el-table-column prop="caoZuo" label="操作"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tableData: [
        {
          kaiShiSJ: '2022-09-01',
          jieShuSJ: '2022-09-30',
          guDingCWS: '18',
          buGuDingCWS: '0',
          teShuSM: '无',
          zhuangTai: '已审批',
          xiuGaiSJ: '',
          shenPiSJ: '2022-09-21 11:24:36',
          caoZuo: ''
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 10px 80px;
  display: flex;
  background-color: #fff;
  .left {
    margin-right: 20px;
    width: 20%;
    border-radius: 4px;
    background: #eaf0f9;
    padding: 5px 16px;
    .left-header {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
      margin-top: 10px;
    }
    .left-header::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .left-content {
      margin-top: 16px;
      font-size: 14px;
      line-height: 14px;
      color: #171c28;
      .left-content-title {
        padding: 9px 12px;
        background-color: #eaf0f9;
        border: 1px solid #dcdfe6;
        color: rgba($color: #171c28, $alpha: 0.85);
        font-weight: 600;
      }
      .left-content-item {
        padding: 9px 12px;
        border: 1px solid #dcdfe6;
      }
      .left-content-item:nth-child(odd) {
        background-color: #eff3fb;
      }
      .left-content-item:nth-child(even) {
        background-color: #f6f6f6;
      }
      .left-content-item:hover {
        background-color: #6787cc;
        color: #fff;
      }
    }
  }
  .right {
    background-color: #eff3fb;
    padding: 10px 12px;
    width: 80%;
    .right-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .right-header-title {
        position: relative;
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        margin-left: 9px;
      }
      .right-header-title::before {
        position: absolute;
        left: -9px;
        width: 3px;
        height: 14px;
        content: '';
        background-color: #356ac5;
      }
      ::v-deep .el-button {
        background-color: rgba($color: #171c28, $alpha: 0.05);
        color: rgba($color: #171c28, $alpha: 0.45);
      }
    }
    .right-content {
      // width: 80%;
      margin-top: 20px;
    }
  }
}
</style>
