<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>医技检查申请单</title>
  <meta name="description" content="医技检查申请单">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
  .container_zytjsqdtx {
    overflow: hidden;
    padding: 16px;
  }

  .container_zytjsqdtx .form-horizontal .group {
    margin-bottom: 15px;
  }

  .container_zytjsqdtx .form-horizontal .control-label {
    flex: 0 0 160px;
    padding-right: 20px;
    padding-top: 7px;
    color: #333;
    text-align: right;
  }

  .container_zytjsqdtx .form-horizontal .control-inner {
    color: #707070;
  }

  .container_zytjsqdtx .title {
    position: relative;
  }

  .container_zytjsqdtx .title_text {
    text-align: center;
    font-size: 18px;
    font-weight: 700;
    color: #333;
    padding: 24px 0;
  }

  .container_zytjsqdtx .title_left {
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
  }


  .container_zytjsqdtx .title_left .glyphicon-question-sign {
    width: 22px;
    height: 22px;
    line-height: 20px;
    font-size: 18px;
    color: #3E6DC5;
    margin-right: 8px;
    cursor: pointer;
  }

  .container_zytjsqdtx .title_right {
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    align-items: center;
  }

  .container_zytjsqdtx .title_right .input-xs {
    height: 24px;
    line-height: 24px;
    padding: 2px 5px;
  }

  .yzmlmx_list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
  }

  .yzmlmx_list>.item {
    flex: 0 0 50%;
    display: flex;
    justify-content: space-between;
    margin: 0;
    padding: 2px 10px;
  }

  .yzmlmx_list>.item .radio-inline {
    padding: 0;
  }

  .yzmlmx_list>.item input[type=checkbox] {
    margin: -2px 6px 0 0;
  }

  .combo {
    width: 100%;
    height: 34px;
    padding: 5px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%);
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%);
    -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  }

  .combo-text {
    font-size: 14px;
  }

  .panel {
    margin-bottom: 0;
    background-color: transparent;
    border: none;
    border-radius: 0;
    border: 1px solid #99BBE8;
  }

  .panel-body {
    padding: 0;
    border: none;
  }

  .calendar {
    padding: 0;
  }

  .e-btn-list {
    color: #3D6CC8;
    background-color: #fff;
  }
</style>

<body>
  <div id="container_zytjsqdtx"></div>
  <OBJECT id="YbPost" align="CENTER" WIDTH=0 HEIGHT=0 classid="CLSID:BFD6D70C-BF75-488E-B1AF-1021477CDE32"></OBJECT>
  <div id="divMaskAll"
    style=" position:absolute; top:0px; left:0px; z-index:8888; filter:alpha(opacity=30); background-color:#777; display:none;">
  </div>
  <div id="divMaskAllContent"
    style=" position:absolute; top:50%; left:50%; margin-left:-100px; margin-right:-100px; z-index:8889; display:none;">
    <table style="width:400px; background-color:White; border:1px dashed black;">
      <tr>
        <td style="height:40px;">&nbsp;&nbsp;请选择影像存储方式：</td>
      </tr>
      <tr>
        <td style="height:40px; text-align:center;">
          <span style="display:none;"><input type="checkbox" id="chkYXpop" value="yx" /><label for="chkYXpop">数字影像<span
                style=" color:Red; font-size:12px;">(推荐勾选)</span></label>&nbsp;&nbsp;
            <input type="checkbox" id="chkJPpop" value="jp" /><label for="chkJPpop">胶片</label></span>
          <span>
            <label class="radio-inline">
              <input type="radio" name="radioPop" id="radioPopYX" onclick="f_radioPopClick(this);" /><label
                for="radioPopYX">数字影像</label></label>
            <label class="radio-inline">
              <input type="radio" name="radioPop" id="radioPopAll" onclick="f_radioPopClick(this);" /><label
                for="radioPopAll">数字影像+胶片</label></label>
            <label class="radio-inline">
              <input type="radio" name="radioPop" id="radioPopJP" onclick="f_radioPopClick(this);" /><label
                for="radioPopJP">胶片</label></label>
          </span>
        </td>
      </tr>
      <tr>
        <td style="height:40px; text-align:center;"><input class="btn btn-default" type="button" value="确定"
            onclick="f_SaveSure()" /></td>
      </tr>
    </table>
  </div>
  <div id="divMaskAllSGN"
    style=" position:absolute; top:0px; left:0px; z-index:8888; filter:alpha(opacity=30); background-color:#777; display:none;">
  </div>
  <div id="divMaskAllSGNContent"
    style=" position:absolute; top:120px; left:calc(50% - 235px); z-index:8889; display:none;">
    <table id="tblMaskAllSGNContent" style="width:470px; background-color:White; border:1px dashed black;">
      <tr>
        <td style="height:40px; padding-left:10px; font-size:18px; color:blue;">
          提示：<br />
          1、严重肾功能不全患者慎做MRI增强。<br />
          2、该患者三个月内在本院的肾功能检验情况：<br /><span id="span_CheckSGNResult" style="padding-left:26px;"></span>。
        </td>
      </tr>
      <tr>
        <td style="height:150px; padding-left:10px;">
          请选择以下按钮中的一项：<br /><br />
          <input class="btn btn-default" type="button" value="→ 已评估，继续开增强MRI" id="btn_sgn_1" onclick="f_btn_sgn_1()"
            style="margin-bottom:10px;" /><br />
          <input class="btn btn-default" type="button" value="→ 已评估，不宜做增强MRI" id="btn_sgn_2" onclick="f_btn_sgn_2()"
            style="margin-bottom:10px;" /><br />
          <input class="btn btn-default" type="button" value="→ 开肾功能检验" id="btn_sgn_3" onclick="f_btn_sgn_3()"
            style="margin-bottom:10px;" />
        </td>
      </tr>
      <tr>
        <td><iframe id="iframesgn" frameborder="0" scrolling="auto"
            style="width:100%;height:100%; display:none;"></iframe></td>
      </tr>
      <tr>
        <td style="height:20px; text-align:right;"><input class="btn btn-default" type="button" value="关闭"
            onclick="f_closesgn()" /></td>
      </tr>
    </table>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <script src="js/e-common.js"></script>
  <!-- js文件 -->
  <script>
    /******************公共对象 ******************/
    var params = {} //url带参
    var e_init = {} //初始化
    var e_yzmlkzmx = [] //医嘱项目组合
    var e_yzmlmx = [] //医嘱项目
    var e_sfxmzh = [] //收费项目组合
    var e_qcbw = [] //取材部位内容
    var sfdyJson
    /******************统一页面启动 ******************/
    $(document).ready(() => {
      let url = window.location.href.split("?") || []
      let text = url[1].split("&")
      for (let i of text) {
        let fd = i.split("=")
        params[fd[0]] = fd[1]
      }
      WRT_e.api.zytjsqdyzmain.getZytjsqdtx({
        params: {
          ll_yzmlid: params['av_yzmlid'],
          ls_zllx: 11,
          ls_zyid: params['av_blid'],
          ll_blid: params['av_blid'],
          ll_zkid: params['av_zkid'],
          ll_bqid: params['av_bqid'],
          ll_bmid: params['av_bmid'],
          ll_sqdsjid: params['av_sqdsjid'] || 0, //没有0 历史的就填id
          ls_sqdstate: params['av_sqdstate'] //“0“新增 “1”修改 ‘2’不可修改
        },
        success(data) {
          if (data.Code == 1) {
            let _data = JSON.parse(data.Result)
            //新冠测试
            $.ajax({
              url: WRT_config.server + "/zyyz/TjsqdHandler.ashx?Method=GetFyct",
              type: 'post',
              success: function (datas) {
                let title = '是否用于新冠肺炎检查'
                if (_data.yzml == "1009") {
                  title = "是否用于手术、门诊输液等新冠肺炎排查"
                }
                if (datas.indexOf(_data.yzml) != -1) {
                  WRT_e.ui.message({
                    title,
                    content: ``,
                    okText: '是',
                    width: 280,
                    cancelText: '否',
                    onOk() {
                      _data.xgbz = "1"
                    },
                    onCancel() {
                      _data.xgbz = null
                    }
                  })
                }
              }
            });
            //全局储存
            e_yzmlkzmx = JSON.parse(_data.yzmlkzmx)
            e_yzmlmx = JSON.parse(_data.yzmlmx).map(el => {
              //储存控制关系和控制组合
              let yzmlmx_obj = {
                kzgx: [],
                kzz: [],
              }
              for (let item of e_yzmlkzmx.filter(e => e.YZMLMXID == el.YZMLMXID)) {
                yzmlmx_obj.kzgx.push(item.KZ)
                yzmlmx_obj.kzz.push(item.ZH)
              }
              //已添加检查项目
              let sqdxmsj_obj = {}
              if (_data.list_sqdxmsj) {
                let yzmlmx_find = JSON.parse(_data.list_sqdxmsj).find(e => e.YZMLMXID == el.YZMLMXID)
                if (yzmlmx_find) {
                  sqdxmsj_obj.CHECKED = 1
                  sqdxmsj_obj.SL = yzmlmx_find.SL
                }
              }
              return {
                ...el,
                ...yzmlmx_obj,
                ...sqdxmsj_obj
              }
            })
            e_sfxmzh = JSON.parse(_data.sfxmzh).filter(e => e.LB != '0').map(e => ({
              ...e,
              ...{
                SL: (function () {
                  if (_data.list_zysqdyz) {
                    let obj = JSON.parse(_data.list_zysqdyz).find(el => el.SFXMID == e.SFXMID)
                    if (obj) {
                      return obj.SL
                    } else {
                      return 0
                    }
                  }
                })()
              }
            }))

            if (_data.qcbw == "True") {
              e_qcbw = JSON.parse(_data.list_sqdxmsj || '[]').filter(e => e.YZMLMXID < 0).reverse().map((e,
                i) => ({
                  bwmc: e.XMMC,
                  xh: i + 1
                }))
            }
            e_init = _data
            // console.log(e_yzmlmx, e_yzmlkzmx, e_sfxmzh, e_init)
            //渲染申请单
            $("#container_zytjsqdtx").html(
              new zytjsqdtx_init().init({
                data: _data
              }).render().$el
            )
            $('#jcmdsssj').datetimebox();
            $('#zzkssj').datetimebox();
            $('#zzjssj').datetimebox();
            $('#qwsj').datetimebox();
            $('#bbltsj').datetimebox();
            $('#bbgdsj').datetimebox();
            //页面初始化方法
            checkIsRepeat(params['av_yzmlid'], e_init.brbh, e_init.ysyhid, params['av_zkid'],);
            f_chkYX_pc($("#chkYX"));
            f_chkJP_pc($("#chkJP"));
            f_checkfjxx()
          }
        }
      })
      //是否打印，可以判断是否部位，1为部位
      getSfdyJson(params['av_yzmlid']);
      //MRI肾功能
      enhancedMRHint(params['av_yzmlid']);
    })

    /********************视图********************/
    //设置历史申请单
    var zytjsqdtx_init = WRT_e.view.extend({
      render: function () {
        let _data = this.data
        var html = `
          <div class="container_zytjsqdtx">
            <div id="div_fjxsnr" style="z-index: 100; position: absolute; top: 10px; left: calc(50% - 240px);font-family: 宋体, Arial, Helvetica, sans-serif;border-radius: 4px;overflow: hidden;background: #ddd;color: red;${_data.zjnr ? '' : 'display:none;'}">${_data.zjnr}</div>
            <div class="title">
              <div class="title_left"><span title="帮助" class="glyphicon glyphicon-question-sign"></span>
                ${params['av_sqdsjid'] ? `<span class="e-tag e-tag-red">${params['av_sqdsjid']}</span>` : ''}
                ${this.getztbz()}
              </div>
              <div class="title_text">${_data.sqdmc}</div>
              <p class="bg-danger" style="padding: 0 6px;${_data.zysx ? '' : 'display:none;'}">${_data.zysx}</p>
              <div class="title_right">
                <div class="flex-row align-items-center">
                  <label for="jjjb" class="control-label" style="margin-bottom: 0;">级别：</label>
                  <select class="form-control input-xs" name="jjjb" style="width:80px;"> 
                    ${this.getjjjb()}
                  </select>
                </div>
              </div>
            </div>
            <div class="form-horizontal" role="form">
              <div class="flex-row group">
                ${this.getzxbq()}
              </div>
              <div class="flex-row group" style="${_data.IsYxJpShow == 1 ? '' : 'display:none;'}">
                <label for="ccfs" class="control-label">存储方式：</label>
                <div class="control-inner flex-fill">
                  ${this.getccfs()}
                </div>
              </div>
              <div class="flex-row group">
                <div class="control-inner" style="padding-left: 160px;">
                  <button id="btn_mzsj" type="submit" class="e_btn_primary e-btn-list" ${params['av_sqdstate'] == 2 ? 'disabled' : ''}>导入末次门诊特检申请单数据</button>
                </div>
              </div>
              <div class="flex-row group">
                <label for="lczd" class="control-label">临床诊断：</label>
                <div class="control-inner flex-fill">
                  <input type="text" class="form-control" name="lczd" value="${this.data.lczd}">
                </div>
              </div>
              <div class="flex-row group">
                <label for="grbs" class="control-label">个人病史：</label>
                <div class="control-inner flex-fill">
                  <textarea class="form-control" rows="3" name="grbs"  style="resize:none" >${this.data.grbs}</textarea>
                </div>
              </div>
              <div class="flex-row group">
                <label for="tjsj" class="control-label">体检数据：</label>
                <div class="control-inner flex-fill">
                  <textarea class="form-control" rows="3" name="tjsj"  style="resize:none" >${this.data.tjsj}</textarea>
                </div>
              </div>
              <div class="flex-row group">
                <label for="hytj" class="control-label">化验特检：</label>
                <div class="control-inner flex-fill">
                  <textarea class="form-control" rows="3" name="hytj"  style="resize:none" >${this.data.hytj}</textarea>
                </div>
              </div>
              ${this.simSqd()}
              <div class="flex-row group">
                <label class="control-label">检查项目：</label>
                <div class="control-inner flex-fill yzmlmx_list" id="yzmlmx_list">
                  ${this.getyzmlmx()}
                </div>
              </div>
              ${this.crxbb()}
              ${this.xynk()}
              <hr />
              <div class="flex-row group">
                <label for="jcmd" class="control-label">检查目的：</label>
                <div class="control-inner flex-fill">
                  <select class="form-control" name="jcmd" style="width:186px;">
                    ${this.getjcmd()}
                  </select>
                </div>
              </div>
              <div class="flex-row group">
                <label for="jcmdsssj" class="control-label">手术时间：</label>
                <div class="control-inner flex-fill">
                  <input type="text" class="form-control" name="jcmdsssj" id="jcmdsssj" style="width:160px">
                  <span style="color:#3E6DC5;">*可填写手术时间以方便预约</span>
                </div>
              </div>
              <div class="flex-row group">
                <label for="tssm" class="control-label">备注说明：</label>
                <div class="control-inner flex-fill">
                  <input type="text" class="form-control" name="tssm" value="${this.data.tssm || ''}">
                </div>
              </div>
              <div class="flex-row group">
                <div class="control-inner" style="padding-left: 160px;">
                  <select class="form-control input-xs" name="rqlx" style="width: 140px;display: inline-block;margin-right: 8px;"> 
                    <option selected="selected" value=""></option>
                    <option value="00">期望检查日期</option>
                    <option value="01">手术日期</option>
                    <option value="02">讨论日期</option>
                    <option value="03">出院日期</option>
                  </select>
                  <span id="qwsjSpan" style="display:none;"><input type="text" class="form-control" name="qwsj" id="qwsj" style="width:160px;"></span>
                  <button id="btn_save" type="submit" class="e_btn_primary ghost" style="margin:0 8px;" ${params['av_sqdstate'] == 2 ? 'disabled' : ''}>保存</button>
                  <button id="btn_reload" type="submit" class="e_btn_primary ghost" style="margin-right: 8px;">刷新</button>
                  <span>参考总价：<u style="color:#3E6DC5;">￥<span id="lbl_sum">00.00</span></u>元</span>
                </div>
              </div>
              <hr />
              <div class="flex-row group">
                <label class="control-label">症状开始-结束时间：<br />
                  <span style="color:#3E6DC5;">（选填）</span></label>
                <div class="control-inner flex-fill">
                  <input type="text" class="form-control" name="zzkssj" id="zzkssj" style="display:inline-block;width:160px"> ~
                  <input type="text" class="form-control" name="zzjssj" id="zzjssj" style="display:inline-block;width:160px">
                </div>
              </div>
              <div class="flex-row group">
                <label for="firstname" class="control-label">症状描述：<br />
                  <span style="color:#3E6DC5;">（选填）</span></label>
                <div class="control-inner flex-fill">
                  <input type="text" class="form-control" id="firstname">
                </div>
              </div>
              <div class="flex-row group">
                <div class="control-inner" style="padding-left: 160px;">
                  <button type="submit" class="e_btn_primary e-btn-list"  onclick="showAllTjsqdToday();">查看该患者今日所开特检单</button>
                  <span id="spanSgnMark" style="border:2px solid blue; color:blue; font-weight:bold; padding:1px; letter-spacing:2px; display:none;">肾功能</span>
                </div>
              </div>
            </div>
          </div>`
        this.$el.html(html)
        return this;
      },
      events: {
        "click .container_zytjsqdtx": "clickDiv",
        "click #btn_mzsj": "btn_mzsj",
        "click #btn_save": "save",
        "click #btn_reload": "reload",
        "change input[name='ccfs']": "changeCcfs",
        "click input[name='yzmlmx']": 'clickYzmlmx',
        "change select[name='rqlx']": 'changeRqlx'
      },
      clickDiv() {
        if ($('#div_fjxsnr')) {
          div_fjxsnr.style.display = "none";
        }
      },
      btn_mzsj() { //导入末次门诊特检申请单数据
        WRT_e.api.zytjsqdyzmain.getMzmcsj({
          params: {
            ll_blid: params['av_blid']
          },
          success(data) {
            if (data.Code == 1) {
              $('.container_zytjsqdtx textarea[name="grbs"]').val(data.Result.ls_grbs)
              $('.container_zytjsqdtx textarea[name="hytj"]').val(data.Result.ls_hytj)
              $('.container_zytjsqdtx textarea[name="tjsj"]').val(data.Result.ls_tjsj)
            } else if (data.Code == -1) {
              WRT_e.ui.hint({
                type: 'error',
                msg: data.CodeMsg
              })
            }
          }
        })
      },
      reload() { //刷新
        window.location.href = window.location.href;
      },
      save(event) { //保存
        if (this.data.IsYxJpShow == "0") {
          if (!checksubmit()) {
            return false;
          }
        } else {
          if (!checksubmit()) {
            return false;
          } else {
            if (getChkJpyxstr() == "") {
              showMask();
              return false;
            } else {
              f_UncheckYXPD();
            }
          }
        }
        let form = $('.container_zytjsqdtx')
        let yzmlmx = []
        form.find('[name="yzmlmx"]:checked').each(function () {
          yzmlmx.push({
            ...e_yzmlmx.find(e => e.YZMLMXID == $(this).val()),
            ...{
              SL: $(`#sl_${$(this).val()}`).text()
            }
          })
        })
        let obj = {
          ll_yzmlid: params['av_yzmlid'], //医嘱目录id
          ls_zllx: 11, //诊疗类型
          ls_zyid: params['av_blid'], //住院id
          ll_blid: params['av_blid'], //病例id
          ll_zkid: params['av_zkid'], //专科id
          ll_bqid: params['av_bqid'], //病区id
          ll_bmid: params['av_bmid'], //部门id
          ll_sqdsjid: params['av_sqdsjid'] || 0, //申请单数据id 没有0 历史的就填id
          ll_zlzid: this.data.brzlzid, //病人治疗组id
          ll_sqdid: params['av_sqdsjid'] || 0, //申请单数据id 没有0 历史的就填id
          ls_sqdstate: params['av_sqdstate'], //申请单状态 “0“新增 “1”修改 ‘2’不可修改
          ls_csyt: form.find('[name="csyt"]:checked').val() || '', //ct-sim用途
          ls_csfzjc: '', //ct-sim辅助检查
          ls_cssmfwsj: form.find('[name="cssmfwsj"]').val() || '', //ct-sim扫描范围上界
          ls_cssmfwxj: form.find('[name="cssmfwxj"]').val() || '', //ct-sim扫描范围下界
          ls_cssmfwzx: form.find('[name="cssmfwzx"]').val() || '', //ct-sim扫描范围中心
          ls_csqtfzjc: form.find('[name="csqtfzjc"]').val() || '', //ct-sim其他辅助检查
          ls_csflqjlxhm: form.find('[name="csflqjlxhm"]').val() || '', //ct-sim放疗期间联系号码
          ls_yblx: form.find('[name="xyyblx"]').val() || '', //血液样本类型
          ls_xysglx: form.find('[name="xysglx"]').val() || '', //血液试管类型:
          ls_sgn_mr: this.data.sgn_mr || '', //肾功能默认
          ls_xgbz: this.data.xgbz || null, //新冠标志
          ls_zzkssj: form.find('[name="zzkssj"]').val() || '', //症状开始时间
          ls_zzjssj: form.find('[name="zzjssj"]').val() || '', //症状结束时间
          ls_zzms: this.data.zzms || '', //症状描述
          ls_sfcrxbb: form.find('[name="sfcrxbb"]:checked').val() || '', //是否传染性标本
          ls_crxbbnr: form.find('[name="sfcrxbb"]:checked').val() == 0 ? '' : form.find(
            "input[name='rad_crnr']:checked").val() == "其他" ? form.find("[name='tx_crnr']").val() || '' : form
              .find("[name='rad_crnr']:checked").val() || '', //传染性标本内容
          ls_jcmdsssj: form.find('[name="jcmdsssj"]').val() || '', //检查目的手术时间
          ls_bbltsj: form.find('[name="bbltsj"]').val() || '', //标本离体时间
          ls_bbgdsj: form.find('[name="bbgdsj"]').val() || '', //标本固定时间
          ls_rqlx: form.find('[name="rqlx"]:checked').val() || '', //日期类型
          ls_rqlx_name: form.find('[name="rqlx"]:checked').text() || '', //日期类型名称
          ls_qwsj: form.find('[name="qwsj"]').val() || '', //期望时间
          chkYX: $('#chkYX').is(':checked') ? 'True' : 'False', //是否数字胶片
          chkJP: $('#chkJP').is(':checked') ? 'True' : 'False', //是否普通胶片
          isblsqd: this.data.ISBLSQD == '0' ? 'False' : 'True', //是否病理申请单
          ls_zxbq: form.find('[name="bmdms"]:checked').val() || '', //执行病区
          ls_zxbq1: form.find('[name="bmdms1"]:checked').val() || '', //执行病区（手术室）
          ls_zjnr: this.data.zjnr || '', //自检内容
          ls_grbs: form.find('[name="grbs"]').val() || '', //个人病史
          ls_hytj: form.find('[name="hytj"]').val() || '', //化验特检
          ls_jcmd: form.find('[name="jcmd"] option:selected').val() || '', //检查目的
          ls_jjjb: form.find('[name="jjjb"] option:selected').val() || '', //紧急级别
          ls_lczd: form.find('[name="lczd"]').val() || '', //临床诊断
          ls_tjsj: form.find('[name="tjsj"]').val() || '', //体检数据
          ls_tssm: form.find('[name="tssm"]').val() || '', //添加备注（新冠）
          ls_yzlbdm: this.data.yzlbdm || '', //医嘱类别代码
          ls_hyzk: form.find('[name="hyzk"]').val() || '', //婚姻状况
          ls_lxdh: form.find('[name="lxdh"]').val() || '', //联系电话
          ls_lxdz: form.find('[name="lxdz"]').val() || '', //联系地址
          ls_tssm2: form.find('[name="tssm2"]').val() || '', //原切片号
          ls_ygyb: this.data.ygyb || '', //阳光医保开关??????????????????????????
          ls_ygybzkid: this.data.ygybzkid || '', //阳光医保专科id??????????????????????????
          json_yzxm: JSON.stringify(yzmlmx), //医嘱项目列表
          json_sfzh: JSON.stringify(e_sfxmzh), //收费组合列表
          json_qcbw: (function () {
            let arr = []
            form.find('[name="qcbw"]').each((index, el) => {
              if ($(el).val().trim() != '') {
                arr.push({
                  bwmc: $(el).val(),
                  xh: index + 1
                })
              }
            })
            return JSON.stringify(arr)
          })(), //取材部位列表
        }
        // console.log(obj, e_sfxmzh)
        WRT_e.api.zytjsqdyzmain.getsaveYz({
          params: obj,
          success(data) {
            if (data.Code == 1) {
              parent.f_jumpok()
            } else if (data.Code == -1) {
              WRT_e.ui.hint({
                type: 'error',
                msg: data.CodeMsg
              })
            }
          }
        })
      },

      getztbz() {
        let ztbz = ""
        switch (this.data.sqdstate) {
          case '0':
            ztbz = '新增'
            break;
          case '1':
            ztbz = '正常'
            break;
          case '2':
            ztbz = _.find([{
              value: 0,
              label: '已暂停'
            }, {
              value: 1,
              label: '已收费'
            }, {
              value: 2,
              label: '部分收费'
            }, {
              value: 3,
              label: '已执行'
            }, {
              value: 9,
              label: '已退费'
            }], (e) => e.value == this.data.sqdztbz).label
            break;
        }
        return (`<span class="e-tag e-tag-blue">${ztbz}</span>`)
      },
      getzxbq() {
        let list = (obj, name) => {
          return _.map([...[{
            BMID: params['av_bqid'],
            BMMC: `病人当前病区(${this.data.bq_bmmc})`,
            checked: true,
          }], ...JSON.parse(obj)], e => {
            return `
          <label class="radio-inline">
            <input type="radio" name="${name}" value="${e.BMID}" ${(this.data.zxbq == e.BMID || e.checked) ? 'checked' : ''}>${e.BMMC}
          </label>`
          }).join('')
        }
        //判断麻醉医师
        if (this.data.zxbq_visible == 'True') {
          return `<label for="bmdms" class="control-label">开具申请单到：</label>
         <div class="control-inner flex-fill">${list(this.data.list_bmdms, 'bmdms')}</div>`
        } else {
          return `<label for="bmdms1" class="control-label">执行病区：</label>
         <div class="control-inner flex-fill">${list(this.data.list_bmdms1, 'bmdms1')}</div>`
        }
      },
      getjjjb() {
        return _.map([{
          value: 0,
          label: '普通'
        }, {
          value: 1,
          label: '紧急'
        }], e => {
          return `<option value="${e.value}" ${this.data.jjjb == e.value ? 'selected' : ''}>${e.label}</option>`
        }).join('')
      },
      simSqd() { //放疗申请单
        if (['28670', '28611'].indexOf(params['av_yzmlid']) > -1) {
          let csyt = _.map([{
            value: '0001',
            label: '第一次定位'
          }, {
            value: '0002',
            label: '改野'
          }, {
            value: '0003',
            label: '数据采集'
          }], e => {
            return `
            <label class="radio-inline">
              <input type="radio" name="csyt" value="${e.value}" ${this.data.csyt == e.value ? 'checked' : ''}>${e.label}
            </label>`
          }).join('')
          let lcsqtfzjc = _.map([{
            value: '0001',
            label: 'MRI'
          }, {
            value: '0002',
            label: 'PET'
          }, {
            value: '0003',
            label: 'ECT'
          }], e => {
            return `
            <label class="radio-inline">
              <input type="radio" name="lcsqtfzjc" value="${e.value}" ${this.data.lcsqtfzjc == e.value ? 'checked' : ''}>${e.label}
            </label>`
          }).join('')
          return `<hr />
          <div class="flex-row group">
            <label for="csyt" class="control-label">用途：</label>
            <div class="control-inner flex-fill">
              ${csyt}
            </div>
          </div>
          <div class="flex-row group">
            <label class="control-label">扫描范围：</label>
            <div class="control-inner flex-fill">
              <div class="row">
                <div class="col-xs-4 col-sm-4">
                  上界:<input type="text" class="form-control" name="cssmfwsj" value="${this.data.cssmfwsj || ''}" style="width:80px;display:inline-block;">
                </div>
                <div class="col-xs-4 col-sm-4">
                  下界:<input type="text" class="form-control" name="cssmfwxj" value="${this.data.cssmfwxj || ''}" style="width:80px;display:inline-block;">
                </div>
                <div class="col-xs-4 col-sm-4">
                  中心:<input type="text" class="form-control" name="cssmfwzx" value="${this.data.cssmfwzx || ''}" style="width:80px;display:inline-block;">
                </div>
              </div>
            </div>
          </div>
          <div class="flex-row group">
            <label for="lcsqtfzjc" class="control-label">其他辅助检查：</label>
            <div class="control-inner flex-fill">
              ${lcsqtfzjc}
            </div>
          </div>
          <div class="flex-row group">
            <label for="flqjlxhm" class="control-label">放疗期间联系号码：</label>
            <div class="control-inner flex-fill">
              <input type="text" class="form-control" name="flqjlxhm" value="${this.data.flqjlxhm || ''}" style="width:160px;">
            </div>
          </div><hr />
          `
        } else {
          return ''
        }
      },
      crxbb() { //传染性标本
        if (this.data.yzlbdm.substring(0, 4) == "0203") {
          return `<hr />
          <div class="row">
            <div class="col-xs-6 col-sm-6">
              <div class="flex-row group">
                <label class="control-label">是否传染性标本：</label>
                <div class="control-inner flex-fill">
                  <label class="radio-inline"><input value="0" name="sfcrxbb" type="radio" onclick="getRadio()" checked>否</label>
                  <label class="radio-inline"><input value="1" name="sfcrxbb" type="radio" onclick="getRadio()">是</label>
                </div>
              </div> 
            </div>
            <div class="col-xs-6 col-sm-6">
              <div class="flex-row group" id="rad_crnr" style="display:none">
                <label class="control-label">标本内容：</label>
                <div class="control-inner flex-fill">
                  <label class="radio-inline"><input value="结核" name="rad_crnr" type="radio" onclick="f_ssnr_Click()">结核</label>
                  <label class="radio-inline"><input value="乙肝" name="rad_crnr" type="radio" onclick="f_ssnr_Click()">乙肝</label>
                  <label class="radio-inline"><input value="HIV" name="rad_crnr" type="radio" onclick="f_ssnr_Click()">HIV</label>
                  <label class="radio-inline"><input value="梅毒" name="rad_crnr" type="radio" onclick="f_ssnr_Click()">梅毒</label>
                  <label class="radio-inline"><input value="其他" name="rad_crnr" type="radio" onclick="f_ssnr_Click()">其他</label>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-xs-6 col-sm-6"></div>
            <div class="col-xs-6 col-sm-6">
              <div class="flex-row group" id="tx_crnr" style="display:none">
                <label class="control-label">请填写标本内容：</label>
                <div class="control-inner flex-fill">
                  <input class="form-control" type="text" name="tx_crnr"/>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-xs-6 col-sm-6">
              <div class="flex-row group">
                <label for="bbltsj" class="control-label">标本离体时间：</label>
                <div class="control-inner flex-fill">
                  <input type="text" class="form-control" name="bbltsj" id="bbltsj" style="width:160px">
                </div>
              </div>  
            </div>
            <div class="col-xs-6 col-sm-6">
              <div class="flex-row group">
                <label for="bbgdsj" class="control-label">标本固定时间：</label>
                <div class="control-inner flex-fill">
                  <input type="text" class="form-control" name="bbgdsj" id="bbgdsj" style="width:160px">
                </div>
              </div>
            </div>
          </div><hr />
          <div class="row">
            <div class="col-xs-6 col-sm-6">
              <div class="flex-row group">
                <label class="control-label">取材部位：</label>
                <div class="control-inner flex-fill">
                  <div style="border:2px solid #333;border-radius:4px;height: 180px;overflow: auto;">
                    <table class="table table-condensed table-hover" style="margin-bottom:0;">
                      <tr style="background:#EFEFEF;">
                        <th width="50">序号</th>
                        <th>标本名称及取材部位</th>
                      </tr>
                      ${(function () {
              let text = ''
              for (let i = 0; i < 12; i++) {
                text += `<tr>
                            <td><strong>${i + 1}</strong></td>
                            <td><input type="text" name="qcbw" value="${e_qcbw[i] ? e_qcbw[i].bwmc : ''}" style="width:200px;border-radius: 3px;border: 1px solid #ccc;box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%);"></td>
                          </tr>`
              }
              return text
            })()
            }
                    </table>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xs-6 col-sm-6">
              <div class="flex-row group">
                <label for="hyzk" class="control-label">婚姻：</label>
                <div class="control-inner flex-fill">
                  <select class="form-control input-xs" name="hyzk" style="width: 140px;"> 
                    <option value="1">未婚</option>
                    <option selected="selected" value="2">已婚</option>
                    <option value="3">离婚</option>
                    <option value="4">再婚</option>
                    <option value="5">丧偶</option>
                    <option value="6">其他</option>
                  </select>
                </div>
              </div>
              <div class="flex-row group">
                <label for="lxdh" class="control-label">联系电话：</label>
                <div class="control-inner flex-fill">
                  <input type="text" class="form-control" name="lxdh" value="${this.data.lxdh}">
                </div>
              </div>
              <div class="flex-row group">
                <label for="lxdz" class="control-label">联系地址：</label>
                <div class="control-inner flex-fill">
                  <input type="text" class="form-control" name="lxdz" value="${this.data.lxdz}">
                </div>
              </div>
              <div class="flex-row group">
                <label for="tssm2" class="control-label">原切片号：</label>
                <div class="control-inner flex-fill">
                  <input type="text" class="form-control" name="tssm2" value="">
                </div>
              </div>
            </div>
          </div>
          `
        } else {
          return ''
        }
      },
      xynk() { //血液内科
        if (this.data.yzlbdm.substring(0, 4) == "0218") {
          let xyyblx = _.map([{
            value: 'N',
            label: '骨髓'
          }, {
            value: 'C',
            label: '血液'
          }, {
            value: 'G',
            label: '脑脊液'
          }, {
            value: 'W',
            label: '穿刺液'
          }, {
            value: '97',
            label: '组织'
          }, {
            value: 'QT',
            label: '其他'
          }], e => {
            return `<option value="${e.value}" ${this.data.xyyblx == e.value ? 'selected' : ''}>${e.label}</option>`
          }).join('')
          let xysglx = _.map([{
            value: '001', 
            label: '紫盖管'
          }, {
            value: '002',
            label: '长紫盖管'
          }, {
            value: '003',
            label: '黄盖管'
          }, {
            value: '101',
            label: '玻璃片'
          }, {
            value: '109',
            label: '无菌管'
          }, {
            value: '112',
            label: '专用管'
          }, {
            value: '0000',
            label: '其他'
          }], e => {
            return `<option value="${e.value}" ${this.data.xysglx == e.value ? 'selected' : ''}>${e.label}</option>`
          }).join('')
          return `<hr />
          <div class="row">
            <div class="col-xs-6 col-sm-6">
              <div class="flex-row group">
                <label for="xyyblx" class="control-label">血液样本类型：</label>
                <div class="control-inner flex-fill">
                  <select class="form-control input-xs" name="xyyblx" style="width: 140px;"> 
                    ${xyyblx}
                  </select>
                </div>
              </div>
            </div>
            <div class="col-xs-6 col-sm-6">
              <div class="flex-row group">
                <label for="xysglx" class="control-label">试管类型：</label>
                <div class="control-inner flex-fill">
                  <select class="form-control input-xs" name="xysglx" style="width: 140px;"> 
                    ${xysglx}
                  </select>
                </div>
              </div>
            </div>
          </div>
          `
        } else {
          return ''
        }
      },
      getccfs() {
        let arr = []
        if (('chkYX' in this.data) && ('chkJP' in this.data)) {
          arr = [{
            value: 'radioOneYX',
            label: '数字影像',
            checked: this.data.chkYX && !this.data.chkJP
          }, {
            value: 'radioOneAll',
            label: '数字影像+胶片',
            checked: this.data.chkYX && this.data.chkJP
          }, {
            value: 'radioOneJP',
            label: '胶片',
            checked: !this.data.chkYX && this.data.chkJP
          }]
        } else {
          arr = [{
            value: 'radioOneYX',
            label: '数字影像',
            checked: this.data.YxJpDefault == 'yx'
          }, {
            value: 'radioOneAll',
            label: '数字影像+胶片',
            checked: this.data.YxJpDefault == 'yxjp'
          }, {
            value: 'radioOneJP',
            label: '胶片',
            checked: this.data.YxJpDefault == 'jp'
          }]
        }
        let list1 = _.map(arr, e => {
          return `<label class="radio-inline"><input id="${e.value}" type="radio" name="ccfs" ${e.checked ? 'checked' : ''}>${e.label}</label>`
        }).join('')
        let list2 = ''
        switch (arr.find(e => e.checked == true) ? arr.find(e => e.checked == true).value : '') {
          case 'radioOneYX':
            list2 =
              `<span style="display:none;"><input name="chkYX" type="checkbox" id="chkYX" value="yx" checked/><input name="chkJP" type="checkbox" id="chkJP" value="jp"/></span>`
            break
          case 'radioOneAll':
            list2 =
              `<span style="display:none;"><input name="chkYX" type="checkbox" id="chkYX" value="yx" checked/><input name="chkJP" type="checkbox" id="chkJP" value="jp" checked/></span>`
            break
          case 'radioOneJP':
            list2 =
              `<span style="display:none;"><input name="chkYX" type="checkbox" id="chkYX" value="yx"/><input name="chkJP" type="checkbox" id="chkJP" value="jp" checked/></span>`
            break
          default:
            list2 =
              `<span style="display:none;"><input name="chkYX" type="checkbox" id="chkYX" value="yx"/><input name="chkJP" type="checkbox" id="chkJP" value="jp"/></span>`
            break
        }

        return list1 + list2
      },
      changeCcfs(e) {
        let value = $(e.target).attr("id")
        if (value == "radioOneAll") {
          $("#chkYX").prop("checked", true)
          $("#chkJP").prop("checked", true)
          f_chkYX_pc($("#chkYX"));
          f_chkJP_pc($("#chkJP"));
        } else if (value == "radioOneJP") {
          $("#chkYX").prop("checked", false)
          $("#chkJP").prop("checked", true)
          f_chkYX_pc($("#chkYX"));
          f_chkJP_pc($("#chkJP"));
        } else if (value == "radioOneYX") {
          $("#chkYX").prop("checked", true)
          $("#chkJP").prop("checked", false)
          f_chkYX_pc($("#chkYX"));
          f_chkJP_pc($("#chkJP"));
        }
      },
      getjcmd() {
        return _.map(JSON.parse(this.data.jcmd), e => {
          return `<option value="${e.YZMDDM}" ${this.data.jcmd_chosen == e.YZMDDM ? 'selected' : ''}>${e.YZMDMC}</option>`
        }).join('')
      },
      getyzmlmx() {
        let list = ''
        //隐藏影像项目
        let HideJp = function (e) {
          if (e.MC == "影像片袋") {
            return true
          } else if (e.YZXMLB == '1' || e.YZXMLB == '2') {
            return true
          }
          return false
        }

        _.each(e_yzmlmx, e => {
          list += `
          <div class="item" style="${HideJp(e) ? 'display:none;' : ''}">
            <label class="radio-inline"><input type="checkbox" yzxmlb="${e.YZXMLB}" name="yzmlmx" value="${e.YZMLMXID}" ${e.CHECKED ? 'checked' : ''}>${e.MC}</label>
            <div class="item_dj" style="flex: 0 0 80px;text-align: right;">￥${(e.DJ || 0).toString().indexOf('.') < 0 ? `${e.DJ || 0}.00` : e.DJ || 0}*<span id="sl_${e.YZMLMXID}" style="display:inline-block;width:16px;text-align: left;">${e.SL || e.MRSL}</span></div>
          </div>`
        })
        return list
      },
      clickYzmlmx(e) { //点击医嘱项目
        let _target = $(e.target)
        // console.log($(e.target).is(":checked"))//是否点击状态
        let obj = e_yzmlmx.find(e => e.YZMLMXID == _target.val())
        Setgljcxm(obj, _target)
        f_cdssCall()
        Setzfbz(obj, _target)
      },
      changeRqlx(e) { //切换日期类型
        let value = $(e.target).val()
        if (value) {
          $("#qwsjSpan").show()
        } else {
          $("#qwsjSpan").hide()
        }
      },
    })
    /*************************************方法************************************/
    function Setgljcxm(obj, target) {
      SetKZ(obj, target) //设置控制
      sumje = SetZje();
      if (sumje.toString().indexOf('.', 0) < 0) {
        sumje = sumje + ".00";
      }
      $("#lbl_sum").text(sumje)

      //阳光医保开关打开，结算类型为社保病人，项目打勾时，上传数据
      if (e_init.ygyb == "1" && e_init.jslx == "01" && target.is(':checked') && (e_init.ygybzkid ==
        "all" || e_init.ygybzkid.indexOf(params['av_zkid']) > -1)) {
        f_ygybSendWhenCheck(obj);
      }

      if (sfdyJson) {
        if (e_init.yzlbdm.indexOf("020202") == 0 || e_init.yzlbdm.indexOf("020203") == 0) {
          if (getSfdyByYzmlmxid(obj.YZMLMXID) == "1" && target.is(':checked')) {
            if (getNumberOfBwChecked() > 1) {
              if (e_init.yzlbdm.indexOf("020202") == 0) {
                alert("该病人检查已超过一个部位，为了不过度检查，减少对病人的辐射伤害，请慎重考虑！");
              } else if (e_init.yzlbdm.indexOf("020203") == 0) {
                alert("该病人检查已超过一个部位，为了不过度检查，减少对病人的伤害，请慎重考虑！");
              } else {

              }
            }
          }
        }

      }
      if (e_init.jcgxSwitch == "1") {
        var eRadOCXobj = document.getElementById("eRadOCX");
        if (sfdyJson) {
          if (getSfdyByYzmlmxid(obj.YZMLMXID) == "1" && target.is(':checked')) {
            if (eRadOCXobj) {
              var ls_result = eRadOCXobj.SetPatInfo(100, getXmlOfBwChecked());
              //alert(getXmlOfBwChecked());
              //alert(ls_result);
            }
          }
        }
      }
    }

    function f_cdssCall() {
      if ($("#hdf_cdssSwitch", parent.document).val() == "1") {
        var yzmlid = $("#hdf_yzml").val();
        var chk = $("input:checkbox:checked");
        var czbw = "";
        for (var i = 0; i < chk.length; i++) {
          if (chk[i].parentNode.customyzmlmxid != undefined) {
            czbw += chk[i].parentNode.customyzmlmxid + ",";
          }
        }
        var jcmc = document.getElementById("dlbl_yzml").innerText;
        if (params['ly'] == "tjyz")
          parent.f_cdss1(yzmlid, jcmc, czbw);
      }
    }

    function Setzfbz(obj, target) {
      var spjg
      var controlsl = parseInt($(`#sl_${obj.YZMLMXID}`).text()) //数量
      if (obj.XMZHZH == "" || obj.XMZHZH == null) {
        if (target.is(':checked')) {
          if (obj.SPLB == "1" || obj.SPLB == "2") {
            spjg = f_zlsbsp(obj.SFXMID, obj.SFZHID, obj.SFXMMC, obj.ZFBZ, obj.XZFW || null, obj.SPLB, e_init.lczd);
            if (spjg != "") //审批正常成功
            {
              obj.ZFBZ = spjg;
              // Setxgxmzfbz(itemcount,sfxmid.value,returnvalue);
            } else {
              target.prop("checked", false);
              //  objcontrol.onclick(); 
            }
          }
        }
      }
      //改变的项目需要转换 sfxmzhnews
      else {
        firstindex = f_getzhitemindex(obj.XMZHZH)
        if (target.is(':checked')) //选中
        {
          for (i = firstindex; i < e_sfxmzh.length; i++) {
            if (controlsl <= 0) return;
            if (parseInt(e_sfxmzh[i].SL) == 0) {
              if (e_sfxmzh[i].SPLB != "1" && e_sfxmzh[i].SPLB != "2") //不需要审批项目
              {
                e_sfxmzh[i].SL = controlsl
              } else {
                spjg = f_zlsbsp(e_sfxmzh[i].SFXMID, e_sfxmzh[i].SFZHID, e_sfxmzh[i].SFXMMC, e_sfxmzh[i].ZFBZ,
                  e_sfxmzh[i].XZFW, e_sfxmzh[i].SPLB, e_init.lczd)
                if (spjg != "") {
                  e_sfxmzh[i].ZFBZ = spjg
                  e_sfxmzh[i].SL = controlsl
                } else {
                  target.prop("checked", false);
                  target.click();
                }
              }
              return;
            } else {
              if (parseInt(e_sfxmzh[i].SL) < e_sfxmzh[i].ZDSL) {
                if (parseInt(e_sfxmzh[i].SL) + controlsl <= e_sfxmzh[i].ZDSL) {
                  e_sfxmzh[i].SL = parseInt(e_sfxmzh[i].SL) + controlsl
                  controlsl = 0;
                  return;
                } else {
                  controlsl = controlsl + parseInt(e_sfxmzh[i].SL) - e_sfxmzh[i].ZDSL
                  e_sfxmzh[i].SL = e_sfxmzh[i].ZDSL
                }
              }
            }
          }
        } else {
          sumzdsl = f_getmaxsum();
          sumxzsl = f_getitemcount(obj.XMZHZH)
          if (sumxzsl >= sumzdsl) return;
          for (j = e_sfxmzh.length - 1; j >= firstindex; j--) {
            if (controlsl == 0) return;
            if (parseInt(e_sfxmzh[j].SL) == 0) continue;
            if (controlsl <= parseInt(e_sfxmzh[j].SL)) {
              e_sfxmzh[j].SL = parseInt(e_sfxmzh[j].SL) - controlsl;
              controlsl = 0;
              return;
            }
            e_sfxmzh[j].SL = 0;
            controlsl = controlsl - parseInt(e_sfxmzh[j].SL)
          }
        }
      }
    }

    // function f_zlsbsp(sfxmid, sfzhid, sfxmmc, zfbz, xzfw, splb, lczd) {
    //   var url = "zlsbsp.aspx?av_xzfw=" + xzfw + "&av_sfxmid=" + sfxmid + "&av_sfzhid=" + sfzhid + "&av_sfxmmc=" +
    //     sfxmmc + "&av_zfbz=" + zfbz + "&av_zllx=11&av_splb=" + splb + "&av_lczd=" + lczd + "&tempid=" + Math.random();
    //   // var returnvalue = window.showModalDialog(encodeURI(url), window,
    //   //   "dialogHeight=400px;dialogWidth=520px;status=no;scroll=yes;resizable=no");
    //   var returnvalue = window.open(`${WRT_config.server}/zyyz/${encodeURI(url)}`, window,
    //     "height=400;width=520;status=no;scrollbars=yes;resizable=no");
    //   // var returnvalue = WRT_e.ui.model({
    //   //   id: '社保审批',
    //   //   title: '社保审批',
    //   //   width: 520,
    //   //   iframeHeight: 400,
    //   //   iframe: true,
    //   //   iframeURL: `${WRT_config.server}/zyyz/${url}`
    //   // })
    //   if (returnvalue != null && returnvalue != "") {
    //     return returnvalue
    //   }
    //   return ""
    // }

    function f_zlsbsp(sfxmid, sfzhid, sfxmmc, zfbz, xzfw, splb, lczd, sfxmzhnews_index, objcontrol) {
      var returnvalue = "";
      var url = `${WRT_config.server}/zyyz/zlsbsp.aspx?av_xzfw=` + xzfw + "&av_sfxmid=" + sfxmid + "&av_sfzhid=" + sfzhid + "&av_sfxmmc=" + sfxmmc + "&av_zfbz=" + zfbz + "&av_zllx=11&av_splb=" + splb + "&av_lczd=" + lczd + "&tempid=" + Math.random();
      myShowModalDialog(encodeURI(url), 520, 400, function (retval) {
        returnvalue = retval;

        if (returnvalue != null && returnvalue != "") {
          objcontrol.checked = true;
          if (sfxmzhnews_index != -1) {
            sfxmzhnews[sfxmzhnews_index].zfbz.value = returnvalue;
          } else {
            var zfbz = getobject(objcontrol.id.replace("cb_xz", "hdf_zfbz"));
            zfbz.value = returnvalue;
          }
        } else {
          objcontrol.checked = false;
        }
      });
      return returnvalue;
    }
    function myShowModalDialog(url, width, height, callback) {
      if (window.showModalDialog) {
        if (callback) {
          var rlt = showModalDialog(url, window, 'resizable:no;scroll:auto;status:no;center:yes;help:no;dialogWidth:' + width + ' px;dialogHeight:' + height + ' px');
          if (rlt)
            return callback(rlt);
          else {
            callback(window.returnValue);
          }
        }
        else
          showModalDialog(url, window, 'resizable:no;scroll:auto;status:no;center:yes;help:no;dialogWidth:' + width + ' px;dialogHeight:' + height + ' px');
      }
      else {
        if (callback)
          window.showModalDialogCallback = callback;
        var top = (window.screen.availHeight - 30 - height) / 2; //获得窗口的垂直位置;
        var left = (window.screen.availWidth - 10 - width) / 2; //获得窗口的水平位置;
        var winOption = "top=" + top + ",left=" + left + ",height=" + height + ",width=" + width + ",resizable=no,scrollbars=auto,status=no,toolbar=no,location=no,directories=no,menubar=no,help=no";
        window.open(url, "", winOption);
      }
    }
    function f_getzhitemindex(xmzhzh) {
      //    lczd=getobject("tb_lczd").value; 
      //    consl=parseInt(getobject(objcontrol.id.replace("cb_xz","tb_sl")).value)
      //    
      for (i = 0; i < e_sfxmzh.length; i++) {
        if (e_sfxmzh[i].ZH == xmzhzh) //说明未选择过，选择前则需要审批
        {
          return i;
        }
      }
      return -1
    }

    function f_getmaxsum() {
      //    lczd=getobject("tb_lczd").value; 
      //    consl=parseInt(getobject(objcontrol.id.replace("cb_xz","tb_sl")).value)
      var maxsum = 0
      for (i = 0; i < e_sfxmzh.length; i++) {
        maxsum = maxsum + e_sfxmzh[i].ZDSL
      }
      return maxsum;
    }

    function f_getitemcount(xmzhzh) {
      var xzsumsl = 0;
      for (var i = 0; i < e_yzmlmx.length; i++) {
        if (e_yzmlmx[i].XMZHZH == xmzhzh && $(`#yzmlmx_list input[value=${e_yzmlmx[i].YZMLMXID}]`).is(':checked') ==
          true) {
          xzsumsl = xzsumsl + parseInt($(`#sl_${e_yzmlmx[i].YZMLMXID}`).text());
        }
      }
      return xzsumsl;
    }

    function SetKZ(obj, target) {
      //当前项作为依赖项的所有组控制关系
      let obj_zkz = []
      for (let item of e_yzmlkzmx) {
        if (item.YZMLMXID == obj.YZMLMXID && (item.KZ == "8" || item.KZ == "9")) {
          obj_zkz = [...obj_zkz, ...e_yzmlkzmx.filter(e => e.ZH == item.ZH)]
        }
      }

      //里面的组控制主项没有一项被选中
      for (let item of obj_zkz) {
        if (item.KZ == "2") {
          if (!$(`#yzmlmx_list input[value=${item.YZMLMXID}]`).is(':checked')) {
            WRT_e.ui.hint({
              type: 'info',
              msg: '该项不能独立选择'
            })
            target.prop("checked", false);
            return
          }
        }
      }
      //当前元素的所有组控制关系
      SetZkz(obj, target.is(':checked'), -1);
    }

    function SetZkz(myitem, checked, zh) {
      //vb_obj=GetZKZByYzmlmxid2(myitem);//当前元素的所有组控制关系
      for (var rownum = 0; rownum < myitem.kzz.length; rownum++) {
        //   //非主项，互斥项，全选项不影响其他项不做判断
        if (myitem.kzgx[rownum] != "0" && myitem.kzgx[rownum] != "1" && myitem.kzgx[rownum] != "2") continue;
        var vb_obj = e_yzmlkzmx.filter(e => e.ZH == myitem.kzz[rownum]); //返回指定组的控制关系
        for (var i = 0; i < vb_obj.length; i++) {
          if (vb_obj[i].ZH == zh || vb_obj[i].YZMLMXID == myitem.YZMLMXID) {
            continue;
          }
          var zngxitem = e_yzmlmx.find(e => e.YZMLMXID == vb_obj[i].YZMLMXID); //组内关系元素
          var tempcheck = $(`#yzmlmx_list input[value=${zngxitem.YZMLMXID}]`).is(':checked')
          switch (vb_obj[i].KZ) {
            case "0": //互斥,
              if (checked == true && tempcheck == true) {
                // zngxitem.checked = false;
                $(`#yzmlmx_list input[value=${zngxitem.YZMLMXID}]`).prop("checked", false)
                SetZkz(zngxitem, false, vb_obj[i].ZH);
              }
              break;
            case "1": //全选
              if (tempcheck != checked) {
                // zngxitem.checked = checked;
                $(`#yzmlmx_list input[value=${zngxitem.YZMLMXID}]`).prop("checked", true)
                SetZkz(zngxitem, checked, vb_obj[i].ZH);
              }
              break;
            case "2": //主项
              //不处理
              break;
            case "5": //默认次项
              if (checked == true) //主项选中,次项默认选中
              {
                f_additemsl(zngxitem, myitem.kzz[rownum], myitem.YZMLMXID, vb_obj);
                SetZkz(zngxitem, checked, vb_obj[i].ZH);
              } else { //做去除操作
                //如果该项的所有主项都去除，则该项去除
                if (f_getitem_zx_selectcount(zngxitem, "5") <= 0) {
                  $(`#yzmlmx_list input[value=${zngxitem.YZMLMXID}]`).prop("checked", checked)
                  // zngxitem.zsslid.value = 0;
                  $(`#sl_${zngxitem.YZMLMXID}`).text(zngxitem.MRSL)
                }
                // else {
                f_subitemsl(zngxitem, myitem.kzz[rownum], myitem.YZMLMXID, vb_obj);
                // }
                if (tempcheck != $(`#yzmlmx_list input[value=${zngxitem.YZMLMXID}]`).is(':checked')) { //次项有发生改变
                  SetZkz(zngxitem, checked, vb_obj[i].ZH);
                }
              }
              break;
            case "8": //依赖项,如果所有主项都去除，则该项去除
              if (checked == false) {
                if (f_getitem_zx_selectcount(zngxitem, "8") <= 0) {
                  // zngxitem.checked = checked;
                  $(`#yzmlmx_list input[value=${zngxitem.YZMLMXID}]`).prop("checked", checked)
                  // zngxitem.sl = zngxitem.mrsl;
                  $(`#sl_${zngxitem.YZMLMXID}`).text(zngxitem.MRSL)
                }
              }
              break;
            case "9": //依赖项,如果所有主项都去除，则该项去除
              if (checked == false) {
                if (f_getitem_zx_selectcount(zngxitem, "9") <= 0) {
                  // zngxitem.checked = checked;
                  $(`#yzmlmx_list input[value=${zngxitem.YZMLMXID}]`).prop("checked", checked)
                }
              }
              if (checked == true) //主项选中,默认依赖项默认选中
              {
                // zngxitem.checked = checked;
                $(`#yzmlmx_list input[value=${zngxitem.YZMLMXID}]`).prop("checked", checked)
              }
              break;
          }
          if (tempcheck != $(`#yzmlmx_list input[value=${zngxitem.YZMLMXID}]`).is(':checked')) {
            SetZkz(zngxitem, $(`#yzmlmx_list input[value=${zngxitem.YZMLMXID}]`).is(':checked'), vb_obj[i].ZH);
            Setzfbz(zngxitem, $(`#yzmlmx_list input[value=${zngxitem.YZMLMXID}]`))
          }
        }
      }
    }

    function f_checkhc(yzmlmxid1, yzmlmxid2) //判断yzmlmixid1，yzmlmxid2 是否存在互斥关系
    {
      var obj1 = e_yzmlmx.find(e => e.YZMLMXID == yzmlmxid1)
      var obj2 = e_yzmlmx.find(e => e.YZMLMXID == yzmlmxid2)
      for (var i = 0; i < obj1.kzgx.length; i++) {
        if (obj1.kzgx[i] != "0") continue;

        for (var j = 0; j < obj2.kzgx.length; j++) {
          if (obj2.kzgx[j] == "0" && obj2.kzz[j] == obj1.kzz[i])
            return true;
        }
      }
      return false;
    }

    function f_additemsl(zngxitem, zh, yzmlmxid, temp) {
      var haschecked = false;
      for (var i = 0; i < temp.length; i++) {
        if (temp[i].KZ == "2" && temp[i].YZMLMXID != yzmlmxid) { //判断同一组里面有没有其他主项
          if ($(`#yzmlmx_list input[value=${temp[i].YZMLMXID}]`).is(':checked')) { //同组其他主项已经有打钩
            haschecked = !f_checkhc(yzmlmxid, temp[i].YZMLMXID);
            break;
          }
        }
      }
      for (var j = 0; j < zngxitem.kzgx.length; j++) //zngxitem元素是不是存在关系为主项的组，存在则数量不增加
      {
        if (zngxitem.kzgx[j] == "2") {
          haschecked = true;
          break;
        }

      }
      if (!haschecked) {
        // zngxitem.zsslid.value = parseInt(zngxitem.zsslid.value) + zngxitem.mrsl;
        if ($(`#yzmlmx_list input[value=${zngxitem.YZMLMXID}]`).is(':checked')) {
          $(`#sl_${zngxitem.YZMLMXID}`).text(parseInt($(`#sl_${zngxitem.YZMLMXID}`).text()) + zngxitem.MRSL)
        }
      }
      $(`#yzmlmx_list input[value=${zngxitem.YZMLMXID}]`).prop("checked", true)
    }

    function f_subitemsl(zngxitem, zh, yzmlmxid, temp) {
      var haschecked = false;
      for (var i = 0; i < temp.length; i++) {
        if ((temp[i].KZ == "2") && temp[i].YZMLMXID != yzmlmxid) { //判断同一组里面有没有其他主项
          // var index = findbyyzmlmxid(temp[i].YZMLMXID);
          if ($(`#yzmlmx_list input[value=${temp[i].YZMLMXID}]`).is(':checked')) { //同组其他主项已经有打钩
            haschecked = !f_checkhc(yzmlmxid, temp[i].YZMLMXID);
            break;
          }
        }
      }

      if (!haschecked) {
        // zngxitem.zsslid.value = parseInt(zngxitem.zsslid.value) - zngxitem.mrsl;
        $(`#sl_${zngxitem.YZMLMXID}`).text(parseInt($(`#sl_${zngxitem.YZMLMXID}`).text()) - zngxitem.MRSL)
        if (parseInt($(`#sl_${zngxitem.YZMLMXID}`).text()) <= 0) {
          $(`#yzmlmx_list input[value=${zngxitem.YZMLMXID}]`).prop("checked", false)
          // zngxitem.zsslid.value = 0;
          $(`#sl_${zngxitem.YZMLMXID}`).text(zngxitem.MRSL)
        }
      }
    }

    function f_getitem_zx_selectcount(myitem, kz) {
      let vb_obj = []
      var rtn = 0;
      for (var i = 0; i < myitem.kzz.length; i++) {
        if (myitem.kzgx[i] == kz) {
          vb_obj = [...vb_obj, ...e_yzmlkzmx.filter(e => e.ZH == myitem.kzz[i])]
        }
      }
      for (var j = 0; j < vb_obj.length; j++) {
        if (vb_obj[j].YZMLMXID != myitem.YZMLMXID && vb_obj[j].KZ == "2") {
          if ($(`#yzmlmx_list input[value=${vb_obj[j].YZMLMXID}]`).is(':checked')) rtn += 1;
        }
      }
      return rtn;
    }

    function getChkJpyxstr() {
      var str = "";
      if ($("#chkYX").is(':checked')) {
        str += "yx";
      }
      if ($("#chkJP").is(':checked')) {
        str += "jp";
      }
      return str;
    }

    // 执行是否将影像片袋项目去掉勾选。（仅在保存按钮前使用）
    function f_UncheckYXPD() {
      if (!$("#chkJP").is(':checked')) {
        $("#yzmlmx_list input[name='yzmlmx']").each(function (i, item) {
          if ($(item).parent().text() == "影像片袋") {
            item.checked = false;
          }
        });
      } else { // 修改的时候用到。当用户第一次选择了影像。第二次修改时勾选胶片。
        //先判断一下是否有胶片勾着。（防止有哪套是仅有个主项啥都不带的）
        var checkFlag = false;
        $("#yzmlmx_list input[name='yzmlmx']").each(function (i, item) {
          if ($(item).attr("yzxmlb") == "1" && item.checked == true) {
            checkFlag = true;
          }
        });
        $("#yzmlmx_list input[name='yzmlmx']").each(function (i, item) {
          if ($(item).parent().text() == "影像片袋" && item.checked == false && checkFlag == true) {
            item.checked = true;
          }
        });
      }
    }

    function f_chkYX_pc(obj) {
      if (obj.is(':checked')) {
        $(`#yzmlmx_list input[yzxmlb=2]`).prop("checked", true)
      } else {
        $(`#yzmlmx_list input[yzxmlb=2]`).prop("checked", false)
      }
      var temp_sumje = SetZje();
      if (temp_sumje.toString().indexOf('.', 0) < 0) {
        temp_sumje = temp_sumje + ".00";
      }
      $("#lbl_sum").text(temp_sumje)
    }

    function f_chkJP_pc(obj) {
      var temp_sumje = SetZje();
      if (temp_sumje.toString().indexOf('.', 0) < 0) {
        temp_sumje = temp_sumje + ".00";
      }
      $("#lbl_sum").text(temp_sumje)
    }

    function f_radioPopClick(obj) {
      if (obj.id == "radioPopAll") {
        $("#radioOneAll").click();
      } else if (obj.id == "radioPopJP") {
        $("#radioOneJP").click();
      } else if (obj.id == "radioPopYX") {
        $("#radioOneYX").click();
      } else { }
    }

    function f_SaveSure() {
      if ($("#chkJP").is(':checked') == false && $("#chkYX").is(':checked') == false) {
        alert("请选择一种影像存储方式");
      } else {
        $("#btn_save").click();
      }
    }

    function SetZje() {
      var sumje = 0.00;
      // var newstr = "";
      if ($("#chkJP").is(':checked')) {
        for (var i = 0; i < e_yzmlmx.length; i++) {
          if ($(`#yzmlmx_list input[value=${e_yzmlmx[i].YZMLMXID}]`).is(':checked') == true) {
            sumje = ((sumje + parseFloat(e_yzmlmx[i].DJ) * parseInt($(`#sl_${e_yzmlmx[i].YZMLMXID}`).text())) * 100) /
              100;
            // newstr = newstr + "#" + i + "*" + parseInt(e_yzmlmx[i].MRSL) + "#";
          }
        }
      } else {
        for (var i = 0; i < e_yzmlmx.length; i++) {
          if ($(`#yzmlmx_list input[value=${e_yzmlmx[i].YZMLMXID}]`).is(':checked') == true && e_yzmlmx[i].YZXMLB !=
            "1" && e_yzmlmx[i].SFXMMC != "影像片袋") {
            sumje = ((sumje + parseFloat(e_yzmlmx[i].DJ) * parseInt($(`#sl_${e_yzmlmx[i].YZMLMXID}`).text())) * 100) /
              100;
            // newstr = newstr + "#" + i + "*" + parseInt(e_yzmlmx[i].MRSL) + "#";
          }
        }
      }
      // newstr += getobject("tb_lczd").value + "#" + getobject("tb_grbs").value + "#" + getobject("tb_tjsj").value + "#" +
      //   getobject("tb_hytj").value + "#" + getobject("tb_tssm").value + "#";
      // if (newstr != oldstr) {
      //   getobject("hdf_ischanged").value = "1";
      // } else {
      //   getobject("hdf_ischanged").value = "0";
      // }
      return sumje;
    }

    function getSfdyJson(yzmlid) {
      $.ajax({
        url: WRT_config.server + "/zyyz/TjsqdHandler.ashx?Method=getSfdyJson",
        data: {
          yzmlid: yzmlid
        },
        dataType: 'json',
        success: function (data) {
          sfdyJson = data;
        }
      });
    }

    function getSfdyByYzmlmxid(yzmlmxid) {
      for (var i = 0; i < sfdyJson.rows.length; i++) {
        if (yzmlmxid == sfdyJson.rows[i].YZMLMXID) {
          return sfdyJson.rows[i].SFDY;
        }
      }
    }

    function getNumberOfBwChecked() {
      var count = 0;
      $("#yzmlmx_list input[name='yzmlmx']").each(function (i, item) {
        if (getSfdyByYzmlmxid($(item).val()) == "1" && $(item).is(':checked')) {
          count++;
        }
      });
      return count;
    }

    //浙江省检查共享用
    function getXmlOfBwChecked() {
      var str1 = "";
      var str2 = "";
      var str3 = "";
      var xml = "";
      $("#yzmlmx_list input[name='yzmlmx']").each(function (i, item) {
        if (getSfdyByYzmlmxid($(item).val()) == "1" && $(item).is(':checked')) {
          str1 += $(item).parent().attr("title") + "|";
          str2 += $("#hdf_yzlbdm_zxks").val() + "|";
          str3 += $("#hdf_yzlbdm_jclx").val() + "|";
        }
      });
      str1 = str1.substring(0, str1.length - 1);
      str2 = str2.substring(0, str2.length - 1);
      str3 = str3.substring(0, str3.length - 1);
      xml = "<eRadParameters>";
      xml += "<StudiesExamineAlias>" + str1 + "</StudiesExamineAlias>";
      xml += "<StudiesModalities>" + str3 + "</StudiesModalities>";
      xml += "<ExecutiveDepartment>" + str2 + "</ExecutiveDepartment>";
      xml += "</eRadParameters>";
      return xml;
    }

    //阳光医保
    function f_ygybSendWhenCheck(jqSpan) {
      $.ajax({
        url: WRT_config.server + "/zyyz/TjsqdHandler.ashx?Method=f_ygybSendWhenCheck",
        data: {
          blid: params['av_blid'],
          yzyhid: e_init.ysyhid,
          kfzkid: params['av_zkid'],
          xm_sfxmid: obj.SFXMID,
          xm_dj: obj.DJ,
          xm_sl: obj.MRSL,
          xm_yzmlmxid: obj.YZMLMXID,
          xm_sfxmmc: obj.SFXMMC
        },
        success: function (data) {
          //alert(data);
          YbPost.ybpost(data);
        }
      });
    }

    function enhancedMRHint(yzmlid) {
      if (e_init.sqdstate == "1") {
        if (e_init.sgn_mr.length > 1) {
          $("#spanSgnMark").show();
        }
      } else {
        $.ajax({
          url: WRT_config.server + "/zyyz/TjsqdHandler.ashx?Method=enhancedMRHint",
          data: {
            yzmlid: yzmlid
          },
          success: function (data) {
            if (data == "1") {
              showMaskSGN();
              $.ajax({
                url: WRT_config.server + "/zyyz/TjsqdHandler.ashx?Method=CheckSGNResult",
                data: {
                  as_blid: params['av_blid']
                },
                dataType: "json",
                success: function (data) {
                  $("#span_CheckSGNResult").text(data.msg);
                },
                error: function (xhr, textStatus, errorThrown) {
                  alert("网络错误，请联系信息科");
                  return;
                }
              });
            }
          },
          error: function (xhr, textStatus, errorThrown) {
            alert("网络错误，请联系信息科");
            return;
          }
        });
      }
    }

    function showMask() {
      $("#divMaskAll").css("height", $(document).height()).css("width", $(document).width()).show();
      $("#divMaskAllContent").show();
    }

    function showMaskSGN() {
      $("#divMaskAllSGN").css("height", $(document).height()).css("width", $(document).width()).show();
      $("#divMaskAllSGNContent").show();
    }

    function hideMaskSGN() {
      $("#divMaskAllSGN").hide();
      $("#divMaskAllSGNContent").hide();
    }

    function f_closesgn() {
      hideMaskSGN();
      window.location.href = WRT_config.server + "/zyyz/BlankPage.aspx?as_type=1";
    }

    function f_btn_sgn_1() {
      hideMaskSGN();
    }

    function f_btn_sgn_2() {
      hideMaskSGN();
      window.location.href = WRT_config.server + "/zyyz/BlankPage.aspx?as_type=1";
    }

    function f_btn_sgn_3() {
      $.ajax({
        url: WRT_config.server + "/zyyz/TjsqdHandler.ashx?Method=CheckSGN24H",
        data: {
          as_blid: params['av_blid']
        },
        dataType: "json",
        success: function (data) {
          if (data.code == "0") {
            $("#iframesgn").attr("src", "../newhyyz/addhymbxm.aspx?blid=" + params['av_blid'] +
              "&mbid=28404&tempid=" + Math.random());
            $("#iframesgn").show();
          } else {
            alert("检测到24小时内已有肾功能检验医嘱，将直接进入MRI开单");
            hideMaskSGN();
          }
        },
        error: function (xhr, textStatus, errorThrown) {
          alert("网络错误，请联系信息科");
          return;
        }
      });
    }
    //是否传染性单选
    function getRadio() {
      var sfcr = $("input[name='sfcrxbb']:checked").val();
      var value = $("input[name='rad_crnr']:checked").val();
      if (sfcr == "1") {
        document.getElementById("rad_crnr").style.display = "";
        if (value == "其他") {
          document.getElementById("tx_crnr").style.display = "";
        }
      } else if (sfcr == "0") {
        document.getElementById("rad_crnr").style.display = "none";
        document.getElementById("tx_crnr").style.display = "none";
      }
    }
    //单选传染性
    function f_ssnr_Click() {
      var value = $("input[name='rad_crnr']:checked").val();
      if (value == "其他") {
        document.getElementById("tx_crnr").style.display = "";
      } else {
        document.getElementById("tx_crnr").style.display = "none";
      }
    }

    function checksubmit() {
      if (checkBC() == false) {
        return false;
      }
      if (!confirm('确定保存该检查医嘱吗？')) return false;
      var jcmd = $("select[name=jcmd]")
      if (jcmd.find("option").length > 1 && jcmd.val() == "") {
        alert("检查目的必须选取!")
        return false;
      }
      if ($("input[name=lczd]").val() == "") {
        alert("临床诊断必须输入!")
        return false;
      }
      if ($("input[name=lczd]").val().length > 50) {
        alert("临床诊断不能超过50字!")
        return false;
      }
      if ($("textarea[name=grbs]").val().length > 250) {
        alert("个人病史不能超过250字!")
        return false;
      }
      if ($("textarea[name=tjsj]").val().length > 250) {
        alert("体检数据不能超过250字!")
        return false;
      }
      if ($("textarea[name=hytj]").val().length > 250) {
        alert("化验特检不能超过250字!")
        return false;
      }
      if (e_init.yzlbdm.substring(0, 4) == "0203") {
        if ($("input[name='sfcrxbb']:checked").val() == 1) {
          if ($("input[name='rad_crnr']:checked").val() == undefined || ($("input[name='tx_crnr']").val() == "" &&
            $("input[name='rad_crnr']:checked").val() == "其他")) {
            alert("传染性标本内容必填;");
            return;
          }
        }
      }
      if ($("#lbl_sum").text() == "0.00" || $("#lbl_sum").text() == "0.0" || $("#lbl_sum").text() == "0") {
        alert("总金额不能为0元!");
        return false;
      }
      if ($("textarea[name=grbs]").val() == "") {
        alert("病史摘要必须输入！")
        return false;
      }
      if ($("textarea[name=tjsj]").val() == "") {
        alert("体检数据必须输入！")
        return false;
      }
      if (f_IsYxOnly()) {
        alert("不能只选择数字影像服务费！");
        return false;
      }
      if ($("textarea[name=hytj]").val() == "") {
        if (e_init.sqdmc.indexOf('支气管镜检查') >= 0) {
          alert("化验特检栏中必须填写：BRT，HBsAg,凝血时间，心电图等项目的结果信息！");
        } else {
          alert("化验特检数据必须输入！");
        }
        return false;
      }
      if (e_init.zjlb == '1') {
        // var pl_zjnr = getobject('pl_zjnr');
        // var hdf_zjnr = getobject("hdf_zjnr");
        // var hdf_zjnrvalue = getobject("hdf_zjnrvalue");
        // if (pl_zjnr != null && pl_zjnr != undefined && pl_zjnr.innerHTML != '') {
        //   if (hdf_zjnr.value == pl_zjnr.innerHTML.toLowerCase()) {
        //     alert('红色方框中的内容必须填写！');
        //     return false;
        //   }
        //   hdf_zjnrvalue.value = pl_zjnr.innerHTML;
        // }
      }
      if ($(`#yzmlmx_list input[name=yzmlmx]:checked`).length == 0) {
        alert("检查项目必须至少选择1项");
        return false;
      }
      return true;
    }
    //彩色多普勒&浅表器官彩色多普勒一天最多2个。(与专科和医生无关)
    function checkBC() {
      var ls_cchint = "彩超常规检查除肿瘤病人外，一天每人次不能超过2个部位。保存已取消。";
      var ls_qbcchint = "浅表器官彩超检查除肿瘤病人外，一天每人次不能超过2个部位。保存已取消。";
      if (params.av_yzmlid == 25696) { //肿瘤病人多部位单子 不限制
        return true;
      }
      var ls_rtn = true; //true通过检查  false不通过检查
      if (params['av_sqdstate'] == "0") { //新增（先判断外围多少个，再判断本新单有多少个）
        $.ajax({
          url: WRT_config.server + "/zyyz/TjsqdHandler.ashx?Method=checkBC_New",
          async: false,
          data: {
            yzmlid: params['av_yzmlid'],
            brbh: e_init.brbh
          },
          success: function (data) {
            if (data != "no_need_check") {
              var li_cc = parseInt(data.split("^")[0]); //彩超
              var li_qbcc = parseInt(data.split("^")[1]); //浅表彩超
              $("#yzmlmx_list input[name=yzmlmx]:checked").each(function (i, item) {
                if (e_yzmlmx.find(e => e.YZMLMXID == $(item).val()).SFXMMC.indexOf(
                  "彩超常规检查(") == 0) {
                  li_cc++;
                }
                if (e_yzmlmx.find(e => e.YZMLMXID == $(item).val()).SFXMMC.indexOf(
                  "浅表器官彩色多普勒超声") == 0) {
                  li_qbcc++;
                }
              });
              if (li_cc > 2) {
                WRT_e.ui.message({
                  title: '提示',
                  content: ls_cchint,
                })
                ls_rtn = false;
              }
              if (li_qbcc > 2) {
                WRT_e.ui.message({
                  title: '提示',
                  content: ls_qbcchint,
                })
                ls_rtn = false;
              }
            }
          },
          error: function (xhr, textStatus, errorThrown) {
            alert("网络错误，请联系信息科");
            ls_rtn = false;
          }
        });
      } else { //编辑（先判断外围多少个，再判断本单编辑后有多少个）
        $.ajax({
          url: WRT_config.server + "/zyyz/TjsqdHandler.ashx?Method=checkBC_Modify",
          async: false,
          data: {
            yzmlid: params['av_yzmlid'],
            brbh: e_init.brbh,
            sqdid: params['av_sqdsjid']
          },
          success: function (data) {
            if (data != "no_need_check") {
              var li_cc = parseInt(data.split("^")[0]); //彩超
              var li_qbcc = parseInt(data.split("^")[1]); //浅表彩超
              $("#yzmlmx_list input[name=yzmlmx]:checked").each(function (i, item) {
                if (e_yzmlmx.find(e => e.YZXMID == $(item).val()).SFXMMC.indexOf(
                  "彩超常规检查(") == 0) {
                  li_cc++;
                }
                if (e_yzmlmx.find(e => e.YZXMID == $(item).val()).SFXMMC.indexOf(
                  "浅表器官彩色多普勒超声") == 0) {
                  li_qbcc++;
                }
              });
              if (li_cc > 2) {
                WRT_e.ui.message({
                  title: '提示',
                  content: ls_cchint,
                })
                ls_rtn = false;
              }
              if (li_qbcc > 2) {
                WRT_e.ui.message({
                  title: '提示',
                  content: ls_qbcchint,
                })
                ls_rtn = false;
              }
            }
          },
          error: function (xhr, textStatus, errorThrown) {
            alert("网络错误，请联系信息科");
            ls_rtn = false;
          }
        });
      }
      return ls_rtn;
    }

    function f_IsYxOnly() {
      var tempstr = "";
      $("#yzmlmx_list input[name='yzmlmx']").each(function (i, item) {
        if ($(item).is(':checked')) {
          if ($(item).attr("yzxmlb") == "") {
            tempstr += "x";
          } else {
            tempstr += $(item).attr("yzxmlb");
          }
        }
      });
      if (tempstr == "2") {
        return true;
      } else {
        return false;
      }
    }

    function checkIsRepeat(yzmlid, brbh, ysyhid, zkid) {
      if (yzmlid == 17241 || yzmlid == 23695) {
        return;
      }
      if (e_init.sqdstate == "0") {
        $.ajax({
          url: WRT_config.server + "/zyyz/TjsqdHandler.ashx?Method=checkIsRepeat",
          data: {
            yzmlid: yzmlid,
            brbh: brbh,
            ysyhid: ysyhid,
            zkid: zkid
          },
          success: function (data) {
            if (data == "-1") {
              $("#btn_save").attr("disabled", true); //由于使用刷新式进入，所以无需在其他地方显式再enable
              WRT_e.ui.message({
                title: '提示信息',
                content: "该患者已有一张同类申请单，请合理检查，规范开单！",
                onOk() { }
              })
            }
            if (data == "-2") {
              $("#btn_save").attr("disabled", true); //由于使用刷新式进入，所以无需在其他地方显式再enable
              WRT_e.ui.message({
                title: '提示信息',
                content: "该患者已有一张同类申请单，请合理检查，规范开单！",
                onOk() { }
              })
            }
          },
          error: function (xhr, textStatus, errorThrown) {
            alert("网络错误，请联系信息科");
            return;
          }
        });
      }
    }

    function showAllTjsqdToday() {
      $.ajax({
        type: 'post',
        url: WRT_config.server + "/zyyz/TjsqdHandler.ashx?Method=GetAllTjsqdToday",
        data: {
          av_blid: params['av_blid']
        },
        success: function (data) {
          if (data.length > 0) {
            alert(data);
          } else {
            alert("无");
          }
        },
        error: function (xhr, textStatus, errorThrown) {
          alert("加载出错(网络错误)");
        }
      });
    }

    function f_checkfjxx() {
      if (e_init.fjxx == "0" && e_init.sqdstate == "0") {
        if (!confirm('请确认：【无性生活史不能检查】')) {
          document.getElementById('container_zytjsqdtx').style.display = 'none';
        }
      }
    }
  </script>
</body>

</html>