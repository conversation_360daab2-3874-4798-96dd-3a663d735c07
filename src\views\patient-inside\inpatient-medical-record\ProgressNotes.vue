<template>
  <!--病程记录-->
  <div class="progress-note-container">
    <!-- 评分表弹窗 -->
    <default-dialog
      :title="scoreFormDialog.title"
      :visible.sync="scoreFormDialog.visible"
      :width="scoreFormDialog.width"
      pop-type="custom"
      append-to-body
      destroy-on-close
      @cancel="closeScoreFormDialog"
    >
      <div :style="{ height: scoreFormDialog.height, position: 'relative' }">
        <div v-if="scoreFormDialog.loading" class="score-form-loading">
          <div class="loading-spinner">
            <i class="el-icon-loading"></i>
            <p>加载中...</p>
          </div>
        </div>
        <iframe
          v-if="scoreFormDialog.url"
          :src="scoreFormDialog.url"
          frameborder="0"
          style="width: 100%; height: 100%; margin: 0 auto"
          @load="handleScoreFormIframeLoad"
        ></iframe>
      </div>
    </default-dialog>

    <!-- 左右布局 -->
    <div class="progress-note-layout">
      <!-- 左侧列表 -->
      <div class="left-panel">
        <div class="left-header">
          <div class="title-area">
            <span>病程记录列表</span>
            <el-select
              v-model="selectedRecordType"
              placeholder="请选择"
              size="mini"
              class="record-type-select"
            >
              <el-option
                v-for="item in keShuXieWenShuList"
                :key="item.geShiDM"
                :label="item.wenShuMC"
                :value="item.geShiDM"
              ></el-option>
            </el-select>
            <el-button
              type="primary"
              size="mini"
              :loading="addingRecord"
              :disabled="addingRecord"
              @click="handleAddRecord"
            >
              新增
            </el-button>
          </div>
        </div>
        <div class="record-list">
          <el-table
            v-loading="loading"
            :data="yiShuXieWenShuList"
            highlight-current-row
            :height="tableHeight"
            element-loading-text="加载中..."
            border
            stripe
            size="mini"
            @row-click="handleRecordClick"
          >
            <el-table-column prop="jilluSJ" label="记录时间" width="150"></el-table-column>
            <el-table-column prop="wenShuMC" label="记录名称">
              <template #default="{ row }">
                <span class="record-name">{{ row.wenShuMC }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 右侧详情 -->
      <div class="right-panel">
        <div class="right-header">
          <div class="button-group">
            <el-button type="primary" size="mini" :loading="loading" @click="refreshPage">
              刷新本页
            </el-button>
            <el-button
              type="primary"
              size="mini"
              :disabled="loading"
              @click="toggleAllRecords(false)"
            >
              折叠
            </el-button>
            <el-button
              type="primary"
              size="mini"
              :disabled="loading"
              @click="toggleAllRecords(true)"
            >
              展开
            </el-button>
          </div>
        </div>
        <div
          ref="recordDetails"
          v-loading="loading"
          class="record-details"
          element-loading-text="加载中..."
        >
          <!-- 危急值记录提醒 -->
          <div v-if="weiJiZhiList.length > 0" class="record-detail-item">
            <div class="record-header" @click="toggleWeiJiZhi">
              <div class="record-title">
                <span>危急值记录提醒</span>
              </div>
              <i :class="weiJiZhiExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            </div>
            <div v-show="weiJiZhiExpanded" class="record-content">
              <div v-loading="weiJiZhiLoading">
                <el-table :data="weiJiZhiList" border stripe size="mini">
                  <el-table-column label="检查时间" width="150">
                    <template slot-scope="scope">{{ scope.row.jianChaSJ }}</template>
                  </el-table-column>
                  <el-table-column label="检查项目">
                    <template slot-scope="scope">{{ scope.row.jianChaXM }}</template>
                  </el-table-column>
                  <el-table-column label="检查结果">
                    <template slot-scope="scope">
                      <span
                        :style="{
                          color:
                            scope.row.jianChaJG && scope.row.jianChaJG.includes('危') ? 'red' : ''
                        }"
                      >
                        {{ scope.row.jianChaJG }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column label="检查备注">
                    <template slot-scope="scope">{{ scope.row.jianChaBZ }}</template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
          <!-- 其余列表 -->
          <div
            v-for="(record, index) in recordDetailList"
            :id="'panel_' + record.id"
            :key="record.id"
            class="record-detail-item"
          >
            <div class="record-header">
              <div class="record-title" @click="toggleRecord(index)">
                <span>{{ record.wenShuMC }}</span>
              </div>
              <div class="record-actions">
                <template v-if="record.isEditing">
                  <el-button
                    type="success"
                    size="mini"
                    class="action-button save-button"
                    @click="handleSaveClick(record.id)"
                  >
                    保存
                  </el-button>
                  <el-button
                    type="danger"
                    size="mini"
                    class="action-button delete-button"
                    @click="handleDeleteClick(record.id)"
                  >
                    删除
                  </el-button>
                  <el-button
                    type="info"
                    size="mini"
                    class="action-button print-button"
                    @click="handlePrintClick(record.id)"
                  >
                    打印
                  </el-button>
                </template>
                <el-button
                  type="primary"
                  size="mini"
                  class="action-button edit-button"
                  @click="handleEditClick(record.id)"
                >
                  {{ record.isEditing ? '取消' : '编辑' }}
                </el-button>
                <i
                  :class="record.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                  @click="toggleRecord(index)"
                ></i>
              </div>
            </div>
            <div v-show="record.expanded" class="record-content">
              <iframe
                v-if="record.url"
                :id="'iframe_' + record.id"
                :src="record.url"
                frameborder="0"
                @load="handleIframeLoad($event, record.id)"
              ></iframe>
              <div v-else class="no-content">暂无内容</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getProgressNoteInit,
  getCriticalValueByBingLiID,
  getGeShiDMFromRedis,
  checkVTEStatus,
  checkICUStatus,
  checkFJZT,
  checkRankinStatus,
  checkPHQStatus,
  checkNIHSSStatus,
  checkPregnancyVTEStatus
} from '@/api/progress-note'
import { mapState } from 'vuex'
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'

export default {
  name: 'ProgressNotes',
  components: {
    DefaultDialog
  },
  data() {
    return {
      // 记录类型选择
      selectedRecordType: '',
      // 可书写文书列表（下拉框选项）
      keShuXieWenShuList: [],
      // 已书写文书列表（记录列表）
      yiShuXieWenShuList: [],
      // 危机值展开状态
      weiJiZhiExpanded: true,
      // 危急值列表数据
      weiJiZhiList: [],
      // 危急值加载状态
      weiJiZhiLoading: false,
      // 详情列表
      recordDetailList: [],
      // 全部展开状态
      allExpanded: true,
      // 表格高度
      tableHeight: '100%',
      // 加载状态
      loading: false,
      // 基础URL
      baseUrl: 'http://10.41.220.39/ehr',
      // 添加记录状态
      addingRecord: false,
      // 评分表弹窗
      scoreFormDialog: {
        visible: false,
        title: '',
        url: '',
        loading: true,
        width: '800px',
        height: '80vh'
      }
    }
  },
  computed: {
    bingLiID() {
      // return '2618572'
      return this.$route.params.id
    },
    ...mapState({
      // zhuanKeID: () => '42',
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientInfo: ({ patient }) => patient.patientInit,
      gongZhongDM: ({ patient }) => patient.doctorInfo.gongZhongDM_DZ, // 工种代码对照
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  created() {
    // 初始化数据
    this.fetchInitData()
  },
  methods: {
    // 获取初始化数据
    async fetchInitData() {
      this.loading = true
      try {
        // 获取初始化数据
        const initRes = await getProgressNoteInit({
          bingLiID: this.bingLiID,
          zhuanKeID: this.zhuanKeID
        })

        if (initRes.hasError === 0) {
          const data = initRes.data

          // 处理可书写文书列表（下拉框选项）
          if (data && data.keShuXieWenShuList) {
            this.keShuXieWenShuList = data.keShuXieWenShuList

            // 默认选择第一个选项
            if (this.keShuXieWenShuList.length > 0) {
              this.selectedRecordType = this.keShuXieWenShuList[0].geShiDM
            }
          }

          // 处理已书写文书列表（记录列表）
          if (data && data.yiShuXieWenShuList) {
            this.yiShuXieWenShuList = data.yiShuXieWenShuList

            // 初始化详情列表
            this.recordDetailList = this.yiShuXieWenShuList.map((item) => ({
              ...item,
              expanded: true,
              isEditing: false,
              url: item.wenShuYLURL
            }))
          }
        }
      } catch (error) {
        console.error('初始化失败', error)
        this.$message.error('初始化失败')
      } finally {
        this.loading = false
      }

      // 获取危急值列表
      await this.fetchCriticalValueList()
    },

    // 获取危急值列表
    async fetchCriticalValueList() {
      this.weiJiZhiLoading = true
      try {
        const res = await getCriticalValueByBingLiID({
          bingLiID: this.bingLiID
        })

        if (res.hasError === 0) {
          this.weiJiZhiList = res.data || []
        }
      } catch (error) {
        console.error('获取危急值列表失败', error)
      } finally {
        this.weiJiZhiLoading = false
      }
    },

    // 处理记录点击
    handleRecordClick(row) {
      // 滚动到对应的详情
      const detailElement = document.getElementById(`panel_${row.id}`)
      if (detailElement) {
        this.$refs.recordDetails.scrollTop =
          detailElement.offsetTop - this.$refs.recordDetails.offsetTop
      }
    },
    // 切换危急值列表展开
    toggleWeiJiZhi() {
      this.weiJiZhiExpanded = !this.weiJiZhiExpanded
    },

    // 切换单个记录的展开状态
    toggleRecord(index) {
      this.recordDetailList[index].expanded = !this.recordDetailList[index].expanded
    },

    // 切换所有记录的展开状态
    toggleAllRecords(expanded) {
      this.allExpanded = expanded
      this.recordDetailList.forEach((item) => {
        item.expanded = this.allExpanded
      })
    },

    // 刷新页面
    refreshPage() {
      // 保存当前展开状态
      const expandedStates = {}
      this.recordDetailList.forEach((record) => {
        expandedStates[record.id] = record.expanded
      })

      // 重新获取数据
      this.fetchInitData().then(() => {
        // 恢复展开状态
        this.recordDetailList.forEach((record) => {
          if (expandedStates[record.id] !== undefined) {
            record.expanded = expandedStates[record.id]
          }
        })
      })
    },

    // 处理新增记录
    async handleAddRecord() {
      if (!this.selectedRecordType) {
        this.$confirm('请选择记录类型', '提示', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'warning'
        })
        return
      }

      // 获取选中的文书类型
      const selectedWenShu = this.keShuXieWenShuList.find(
        (item) => item.geShiDM === this.selectedRecordType
      )

      if (!selectedWenShu) {
        this.$confirm('未找到选中的文书类型', '提示', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'warning'
        })
        return
      }

      try {
        this.loading = true
        this.addingRecord = true

        // 1. 检查用户是否有权限书写文书
        if (this.gongZhongDM === '0000') {
          await this.$confirm('非医生账号无法书写文书，请联系医务处或信息处进行确认修改', '提示', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'warning'
          })
          return
        }

        // 1.1 检查文书是否可以多份
        const geShiInfoRes = await getGeShiDMFromRedis({
          daiMa: selectedWenShu.geShiDM
        })

        if (geShiInfoRes.hasError !== 0) {
          this.$message.error(geShiInfoRes.errorMsg || '获取文书格式信息失败')
          return
        }

        // 如果返回值中的duoFenBZ为0，表示该文书只能有一份
        if (geShiInfoRes.data && geShiInfoRes.data.duoFenBZ === '0') {
          await this.$confirm('该记录一份病历中只允许存在一份，如页面上未显示，请刷新！', '提示', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'warning'
          })
          return
        }

        // 1.2 检查病历状态
        if (this.patientInfo.bingLiZT === '1') {
          // 病历已封存，只有特定类型的文书可以添加
          const specialTypes = ['1Z', '1I'] // 辅检记录和危急值记录

          // 如果不是特殊类型，则不允许添加
          if (!specialTypes.includes(selectedWenShu.wenShuLX)) {
            this.$message({
              type: 'warning',
              title: '提示信息',
              message: '已封存病历不能进行该操作!'
            })
            return
          }

          // 对于辅检记录(1Z)，需要检查辅助状态
          if (selectedWenShu.wenShuLX === '1Z') {
            const fjztRes = await checkFJZT({
              bingLiID: this.bingLiID,
              wenShuLX: selectedWenShu.wenShuLX
            })

            if (fjztRes.hasError !== 0) {
              this.$message.error(fjztRes.errorMsg || '检查辅检状态失败')
              return
            }

            if (fjztRes.data.status === '1') {
              this.$confirm(fjztRes.data.message, '提示', {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              })
              return
            }
          }
        }

        // 2. 根据文书类型进行特殊检查

        // 2.1 首次病程记录(11)的特殊检查
        if (selectedWenShu.wenShuLX === '11') {
          // 2.1.1 检查NIHSS评分情况
          const nihssRes = await checkNIHSSStatus({
            bingLiID: this.bingLiID,
            wenShuLX: selectedWenShu.wenShuLX
          })

          if (nihssRes.hasError !== 0) {
            this.$message.error(nihssRes.errorMsg || '检查NIHSS评分状态失败')
            return
          }

          if (nihssRes.data.status === '1') {
            await this.$confirm(
              nihssRes.data.message ||
                '完成美国国立卫生院神经功能缺损评分表(NIHSS)评分后才可书写首次病程记录',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开NIHSS评分表页面
              this.openScoreForm('NIHSS评分表', 17)
            })
            return
          }

          // 2.1.2 检查mRS评分情况
          const mrsRes = await checkRankinStatus({
            bingLiID: this.bingLiID,
            wenShuLX: selectedWenShu.wenShuLX
          })

          if (mrsRes.hasError !== 0) {
            this.$message.error(mrsRes.errorMsg || '检查mRS评分状态失败')
            return
          }

          if (mrsRes.data.status === '1') {
            await this.$confirm(
              mrsRes.data.message || '完成改良Rankin量表(mRS)评分后才可书写首次病程记录',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开mRS评分表页面
              this.openScoreForm('改良Rankin量表(mRS)评分', 72)
            })
            return
          }

          // 2.1.3 检查PHQ量表状态
          const phqRes = await checkPHQStatus({
            bingLiID: this.bingLiID
          })

          if (phqRes.hasError !== 0) {
            this.$message.error(phqRes.errorMsg || '检查PHQ量表状态失败')
            return
          }

          if (phqRes.data.status === '1') {
            let message = phqRes.data.message || '完成PHQ-4量表后才可书写首次病程记录'

            await this.$confirm(message, '提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            }).then(() => {
              // 打开PHQ量表页面（使用不同的URL格式）
              const pfid = message.includes('PHQ-9') ? 274 : 148
              const title = message.includes('PHQ-9') ? 'PHQ-9量表' : 'PHQ-4量表'

              // PHQ量表使用pfbym.aspx而不是pfblist.aspx
              this.scoreFormDialog.title = title
              this.scoreFormDialog.url = `${this.baseUrl}/pfbgl/pfbym.aspx?as_blid=${this.bingLiID}&as_pfid=${pfid}&as_pfzhid=0`
              this.scoreFormDialog.loading = true
              this.scoreFormDialog.visible = true
            })
            return
          }

          // 2.1.4 检查产科VTE状态（如果年龄大于14岁）
          const nianLing = Number(this.patientInfo.nianLing.split('岁')[0])
          if (nianLing > 14) {
            const ckVteRes = await checkPregnancyVTEStatus({
              bingLiID: this.bingLiID,
              yongHuID: this.yongHuID
            })

            if (ckVteRes.hasError !== 0) {
              this.$message.error(ckVteRes.errorMsg || '检查产科VTE状态失败')
              return
            }

            if (ckVteRes.data.status === '1') {
              await this.$confirm(
                ckVteRes.data.message ||
                  '完成妊娠期及产褥期静脉血栓血塞证(VTE)的危险因素评分后才可书写首次病程记录',
                '提示',
                {
                  confirmButtonText: '确定',
                  showCancelButton: false,
                  type: 'warning'
                }
              ).then(() => {
                // 打开VTE评分表页面
                this.openScoreForm('妊娠期及产褥期VTE评分', 71)
              })
              return
            }
          }

          // 2.1.5 检查VTE状态
          const vteRes = await checkVTEStatus({
            bingLiID: this.bingLiID,
            yongHuID: this.yongHuID
          })

          if (vteRes.hasError !== 0) {
            this.$message.error(vteRes.errorMsg || '检查VTE状态失败')
            return
          }

          if (vteRes.data.status === '1') {
            await this.$confirm(
              vteRes.data.message || '患者需要填写VTE评估表，请先完成评估',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开VTE评分表页面
              this.openScoreForm('VTE评估表', 71)
            })
            return
          }
        }

        // 2.2 上级医师查房记录(13)的特殊检查
        else if (selectedWenShu.wenShuLX === '13') {
          // 检查NIHSS评分情况
          const nihssRes = await checkNIHSSStatus({
            bingLiID: this.bingLiID,
            wenShuLX: selectedWenShu.wenShuLX
          })

          if (nihssRes.hasError !== 0) {
            this.$message.error(nihssRes.errorMsg || '检查NIHSS评分状态失败')
            return
          }

          if (nihssRes.data.status === '1') {
            await this.$confirm(
              nihssRes.data.message ||
                '该患者住院第五天需要完成美国国立卫生院神经功能缺损评分表(NIHSS)评分',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开NIHSS评分表页面
              this.openScoreForm('NIHSS评分表', 17)
            })
            return
          }
        }

        // 2.3 出院记录(1E)的特殊检查
        else if (selectedWenShu.wenShuLX === '1E') {
          // 检查mRS评分情况
          const mrsRes = await checkRankinStatus({
            bingLiID: this.bingLiID,
            wenShuLX: selectedWenShu.wenShuLX
          })

          if (mrsRes.hasError !== 0) {
            this.$message.error(mrsRes.errorMsg || '检查mRS评分状态失败')
            return
          }

          if (mrsRes.data.status === '1') {
            await this.$confirm(
              mrsRes.data.message || '完成改良Rankin量表(mRS)评分后才可书写出院记录',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开mRS评分表页面
              this.openScoreForm('改良Rankin量表(mRS)评分', 72)
            })
            return
          }
        }

        // 2.4 其他文书类型的VTE检查
        else if (selectedWenShu.wenShuLX === '15') {
          // 检查VTE状态
          const vteRes = await checkVTEStatus({
            bingLiID: this.bingLiID,
            yongHuID: this.yongHuID
          })

          if (vteRes.hasError !== 0) {
            this.$message.error(vteRes.errorMsg || '检查VTE状态失败')
            return
          }

          if (vteRes.data.status === '1') {
            await this.$confirm(
              vteRes.data.message || '患者需要填写VTE评估表，请先完成评估',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开VTE评分表页面
              this.openScoreForm('VTE评估表', 71)
            })
            return
          }
          // 检查ICU状态
          const icuRes = await checkICUStatus({
            bingLiID: this.bingLiID,
            wenShuLX: selectedWenShu.wenShuLX
          })

          if (icuRes.hasError !== 0) {
            this.$message.error(icuRes.errorMsg || '检查ICU状态失败')
            return
          }

          if (icuRes.data.status === '1') {
            await this.$confirm(
              icuRes.data.message || 'ICU患者填写出院记录需填ICU质量控制登记表！',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开ICU质量控制登记表页面
              this.openScoreForm('ICU质量控制登记表', 71)
            })
            return
          }
        }

        // 2.5 转科记录(14)和24小时入出院记录(08)的特殊检查
        if (selectedWenShu.wenShuLX === '14' || selectedWenShu.wenShuLX === '08') {
          const icuRes = await checkICUStatus({
            bingLiID: this.bingLiID,
            wenShuLX: selectedWenShu.wenShuLX
          })

          if (icuRes.hasError !== 0) {
            this.$message.error(icuRes.errorMsg || '检查ICU状态失败')
            return
          }

          if (icuRes.data.status === '1') {
            await this.$confirm(
              icuRes.data.message || 'ICU患者填写出院记录需填ICU质量控制登记表！',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开ICU质量控制登记表页面
              this.openScoreForm('ICU质量控制登记表', 71)
            })
            return
          }
        }

        // 3. 创建新记录对象
        const newRecord = {
          id: 't' + new Date().getTime(),
          wenShuMC: selectedWenShu.wenShuMC,
          geShiDM: selectedWenShu.geShiDM,
          wenShuLX: selectedWenShu.wenShuLX,
          expanded: true,
          isEditing: true // 新增记录默认为编辑状态
        }

        // 设置URL
        newRecord.url = this.getRecordEditUrl(newRecord)

        // 5. 添加到详情列表
        this.recordDetailList.push(newRecord)
      } catch (error) {
        this.$message.error('新增记录失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
        this.addingRecord = false
      }
    },

    // 获取记录编辑URL
    getRecordEditUrl(record) {
      if (!record || !record.id) return ''

      // 构建URL参数
      const params = {
        as_blid: record.bingliID || this.bingLiID,
        as_gsdm: record.geshiDM || record.geShiDM,
        as_zyid: this.patientInfo.zhuYuanID,
        as_yhid: record.yishiYHID || this.yongHuID,
        as_wsid: 0, // 新文书ID为0
        as_wslx: record.wenshuLX || record.wenShuLX,
        as_tmpid: 't1',
        tmpid: Math.random()
      }

      // 将参数转换为URL查询字符串
      const queryString = Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join('&')

      // 返回完整URL
      return `${this.baseUrl}/zyblws/blwsdetail.aspx?${queryString}`
    },

    // 处理iframe加载完成
    handleIframeLoad(_, recordId) {
      console.log('iframe加载完成', recordId)
    },

    // 处理编辑按钮点击
    handleEditClick(recordId) {
      // 检查病历状态，如果已封存则不允许编辑
      if (this.patientInfo.bingLiZT === '1') {
        this.$message({
          type: 'warning',
          message: '已封存病历不能进行该操作!'
        })
        return
      }

      // 查找对应的记录
      const record = this.recordDetailList.find((item) => item.id === recordId)
      if (!record) {
        this.$message.error('未找到对应的记录')
        return
      }
      // 切换编辑状态
      if (record.isEditing) {
        record.isEditing = false
        record.url = record.wenShuYLURL
      } else {
        record.isEditing = true
        record.url = this.getRecordEditUrl(record)
        console.log(record.url)
      }
    },

    // 处理保存按钮点击
    async handleSaveClick(recordId) {
      // 查找对应的记录
      const record = this.recordDetailList.find((item) => item.id === recordId)
      if (!record) {
        this.$message.error('未找到对应的记录')
        return
      }

      // 获取iframe元素
      const iframe = document.getElementById(`iframe_${recordId}`)
      if (!iframe || !iframe.contentWindow) {
        this.$message.error('无法访问iframe内容')
        return
      }

      try {
        // 调用iframe内部的保存方法
        if (typeof iframe.contentWindow.ehr_save === 'function') {
          iframe.contentWindow.ehr_save()

          // 重新获取已书写文书列表，以更新yiShuXieWenShuList
          await this.refreshYiShuXieWenShuList()
          record.isEditing = false
          this.$message.success('保存成功')
        } else {
          this.$message.error('iframe中没有保存方法')
        }
      } catch (error) {
        console.error('保存失败', error)
        this.$message.error('保存失败: ' + (error.message || '未知错误'))
      }
    },

    // 刷新已书写文书列表
    async refreshYiShuXieWenShuList() {
      try {
        // 获取初始化数据
        const initRes = await getProgressNoteInit({
          bingLiID: this.bingLiID,
          zhuanKeID: this.zhuanKeID
        })

        if (initRes.hasError === 0) {
          const data = initRes.data

          // 更新已书写文书列表
          if (data && data.yiShuXieWenShuList) {
            this.yiShuXieWenShuList = data.yiShuXieWenShuList

            // 更新recordDetailList中的记录，保持展开状态和编辑状态
            const updatedRecordDetailList = []

            // 遍历新获取的文书列表
            for (const item of this.yiShuXieWenShuList) {
              // 查找recordDetailList中是否已存在该记录
              const existingRecord = this.recordDetailList.find((record) => record.id === item.id)

              if (existingRecord) {
                // 如果已存在，保持其展开状态和编辑状态
                updatedRecordDetailList.push({
                  ...item,
                  expanded: existingRecord.expanded,
                  isEditing: existingRecord.isEditing,
                  url: existingRecord.isEditing ? this.getRecordEditUrl(item) : item.wenShuYLURL
                })
              } else {
                // 如果不存在，添加新记录
                updatedRecordDetailList.push({
                  ...item,
                  expanded: true,
                  isEditing: false,
                  url: item.wenShuYLURL
                })
              }
            }

            // 更新recordDetailList
            this.recordDetailList = updatedRecordDetailList
          }
        }
      } catch (error) {
        console.error('刷新已书写文书列表失败', error)
        this.$message.error('刷新已书写文书列表失败')
      }
    },

    // 处理删除按钮点击
    handleDeleteClick(recordId) {
      this.$confirm('确定要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          // 查找对应的记录
          const record = this.recordDetailList.find((item) => item.id === recordId)
          if (!record) {
            this.$message.error('未找到对应的记录')
            return
          }

          // 获取iframe元素
          const iframe = document.getElementById(`iframe_${recordId}`)
          if (!iframe || !iframe.contentWindow) {
            this.$message.error('无法访问iframe内容')
            return
          }

          try {
            // 调用iframe内部的删除方法
            if (typeof iframe.contentWindow.ehr_delete === 'function') {
              iframe.contentWindow.ehr_delete()

              // 临时记录是否为新增的记录（ID以't'开头）
              const isTemporaryRecord = recordId.toString().startsWith('t')

              if (isTemporaryRecord) {
                // 如果是临时记录（新增但未保存），直接从列表中移除
                const index = this.recordDetailList.findIndex((item) => item.id === recordId)
                if (index !== -1) {
                  this.recordDetailList.splice(index, 1)
                }
              } else {
                // 如果是已保存的记录，重新获取已书写文书列表
                await this.refreshYiShuXieWenShuList()
              }

              this.$message.success('删除成功')
            } else {
              this.$message.error('iframe中没有删除方法')
            }
          } catch (error) {
            console.error('删除失败', error)
            this.$message.error('删除失败: ' + (error.message || '未知错误'))
          }
        })
        .catch(() => {
          // 取消删除，不做任何操作
        })
    },

    // 处理打印按钮点击
    handlePrintClick(recordId) {
      // 查找对应的记录
      const record = this.recordDetailList.find((item) => item.id === recordId)
      if (!record) {
        this.$message.error('未找到对应的记录')
        return
      }

      // 获取iframe元素
      const iframe = document.getElementById(`iframe_${recordId}`)
      if (!iframe || !iframe.contentWindow) {
        this.$message.error('无法访问iframe内容')
        return
      }

      try {
        // 调用iframe内部的打印方法
        if (typeof iframe.contentWindow.print === 'function') {
          iframe.contentWindow.ehr_print()
        } else {
          this.$message.error('iframe中没有打印方法')
        }
      } catch (error) {
        console.error('打印失败', error)
        this.$message.error('打印失败: ' + (error.message || '未知错误'))
      }
    },

    // 打开评分表页面（使用弹窗）
    openScoreForm(title, pfid) {
      // 设置弹窗属性
      this.scoreFormDialog.title = title
      this.scoreFormDialog.url = `${this.baseUrl}/pfbgl/pfblist.aspx?as_blid=${this.bingLiID}&as_pfid=${pfid}&as_pfzhid=0`
      this.scoreFormDialog.loading = true
      this.scoreFormDialog.visible = true
    },

    // 关闭评分表弹窗
    closeScoreFormDialog() {
      this.scoreFormDialog.visible = false
      this.scoreFormDialog.url = ''
      this.scoreFormDialog.title = ''
    },

    // 处理评分表iframe加载完成
    handleScoreFormIframeLoad() {
      this.scoreFormDialog.loading = false
    }
  }
}
</script>

<style scoped lang="scss">
.progress-note-container {
  height: 100%;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  background-color: #f6f6f6;
  position: relative;
}

.progress-note-layout {
  display: flex;
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.left-panel {
  width: 380px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #eff3fb;
  border-radius: 4px;
}

.left-header {
  padding: 10px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #eff3fb;
}

.title-area {
  display: flex;
  align-items: center;

  span {
    font-weight: bold;
    margin-right: 10px;
    display: inline-block;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    vertical-align: middle;
  }

  .record-type-select {
    margin-left: 0;
    width: 180px;
  }
}

.record-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;

  ::v-deep .el-table {
    .record-name {
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    .record-time {
      font-size: 14px;
    }
  }
}

.right-panel {
  flex: 1;
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  background-color: #eff3fb;
  padding: 10px;
  border-radius: 4px;
}

.right-header {
  padding: 0 12px;
  height: 58px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #eff3fb;
  display: flex;
  align-items: center;
}

.button-group {
  display: flex;
}

.record-details {
  flex: 1;
  overflow: auto;
  padding-top: 10px;
  background-color: #eff3fb;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 10px;
  }

  p {
    font-size: 16px;
  }
}

.record-detail-item {
  margin-bottom: 10px;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s;

  &:hover {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 50px;
  background-color: #eaf0f9;
  padding: 8px 12px;
  border-bottom: 1px solid #dcdfe6;
  transition: background-color 0.3s;

  &:hover {
    background-color: #ecf5ff;
  }

  .record-actions {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-right: 10px;

    .action-button {
      margin-left: 0;
      margin-right: 0;
      padding: 5px 10px;
    }

    i {
      margin-left: 5px;
      cursor: pointer;
    }
  }
}

.record-title {
  display: flex;
  align-items: center;
  padding-left: 8px;
  border-left: 4px solid #356ac5;
  cursor: pointer;
  flex: 1;

  span {
    font-weight: bold;
    color: #303133;
    font-size: 15px;
  }
}

.record-content {
  padding: 0;
  background-color: #fff;
  transition: height 0.3s;

  iframe {
    width: 100%;
    height: 540px;
    border: none;
    display: block;
  }

  .no-content {
    padding: 30px;
    text-align: center;
    color: #909399;
    font-size: 14px;
    background-color: #fafafa;
  }
}

// 评分表弹窗样式
.score-form-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  .loading-spinner {
    text-align: center;

    i {
      font-size: 32px;
      color: #409eff;
    }

    p {
      margin-top: 10px;
      color: #606266;
    }
  }
}

// 响应式调整
@media screen and (max-width: 768px) {
  .left-panel {
    width: 220px;
  }

  .title-area {
    flex-wrap: wrap;

    .record-type-select {
      margin-top: 5px;
      margin-left: 0;
    }
  }
}
</style>
