<template>
  <el-dialog :visible="dialogRef.visible" width="600px" :show-close="false" @open="initFormData()">
    <span slot="title">
      <span class="dialog-title">
        <i class="el-icon-menu"></i>
        化验备注
      </span>
    </span>
    <div v-if="formData" class="dialog-component">
      需要以下参数,请填写!
      <div class="dialog-input">
        <el-form ref="formData" :model="formData" :rules="rules">
          <el-form-item label="身高(cm):" prop="SG">
            <el-input v-model="formData.SG" />
          </el-form-item>
          <el-form-item label="体重(Kg):" prop="TZ">
            <el-input v-model="formData.TZ" />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm()">确 认</el-button>
      <el-button v-if="dialogRef.data !== ''" @click="dialogRef.resolve(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { deepClone } from '@/utils'

export default {
  name: 'RemarksDialog',
  props: {
    dialogRef: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: null,
      rules: {
        SG: [{ required: true, message: '请输入身高', trigger: 'blur' }],
        TZ: [{ required: true, message: '请输入体重', trigger: 'blur' }]
      }
    }
  },
  methods: {
    initFormData() {
      this.formData = {
        SG: '', //身高
        TZ: '' //体重
      }
      if (this.dialogRef.data) {
        let yzbzAll = this.dialogRef.data.split(' ')
        let fjbz = []
        yzbzAll.map((e) => {
          const item = e.split(':')
          if (item[0] === '身高(cm)') {
            this.formData.SG = item[1]
          } else if (item[0] === '体重(Kg)') {
            this.formData.TZ = item[1]
          }
        })
      }
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.dialogRef.resolve(`身高(cm):${this.formData.SG} 体重(Kg):${this.formData.TZ}`)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-title {
  font-size: 16px;
}
.dialog-component {
  padding: 0 30px;
}
.dialog-input {
  margin-top: 10px;
  .el-input {
    width: 65%;
    margin-left: 10px;
  }
}

:deep(.el-dialog__footer) {
  border-top: none;
}
</style>
