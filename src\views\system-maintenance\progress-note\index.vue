<!-- 病程记录 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="table-background" style="width: 400px">
        <div class="table-title">
          <div>
            <span class="bar" />
            病程记录列表
          </div>
          <div>
            <el-select v-model="selectItem" style="width: 170px" placeholder="请选择类别">
              <el-option
                v-for="item in selectList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-button type="primary">新增</el-button>
          </div>
        </div>
        <div class="table-component">
          <table>
            <thead>
              <tr style="text-align: left">
                <th style="width: 150px">记录时间</th>
                <th>记录名称</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in data2List">
                <tr
                  :key="index"
                  :class="[index % 2 == 1 ? 'tr-two' : 'tr-one']"
                  :style="{ color: index === 2 ? '#F35656' : '#000' }"
                >
                  <td>
                    {{ item.field1 }}
                  </td>
                  <td>
                    {{ item.field2 }}
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
      <div class="table-background" style="margin-left: 12px; flex-grow: 1">
        <div class="head-search">
          <el-button type="primary">刷新本页</el-button>
          <el-button
            type="primary"
            @click="
              foldState1 = false
              foldState2 = false
            "
          >
            折叠
          </el-button>
          <el-button
            type="primary"
            @click="
              foldState1 = true
              foldState2 = true
            "
          >
            展开
          </el-button>
        </div>
        <div class="table-component">
          <div class="table-title fold-title" :style="foldState1 ? { borderBottom: 'none' } : {}">
            <div>
              <span class="bar" />
              危机值记录提醒
            </div>
            <button type="text" @click="foldState1 = !foldState1">
              <i
                :class="[foldState1 ? 'el-icon-arrow-up' : 'el-icon-arrow-right']"
                class="fold-icon"
              />
            </button>
          </div>
          <div v-if="foldState1" style="display: flex">
            <table v-for="(table, index) in [0, 1]" :key="index">
              <thead>
                <tr style="text-align: left">
                  <th>检查事件</th>
                  <th>检查项目</th>
                  <th>检查结果</th>
                  <th>处理意见</th>
                </tr>
              </thead>
              <tbody>
                <template v-for="(item, index) in data1List">
                  <tr :key="index" :class="[index % 2 == 1 ? 'tr-two' : 'tr-one']">
                    <td>
                      {{ item.field1 }}
                    </td>
                    <td>
                      {{ item.field2 }}
                    </td>
                    <td style="color: #f35656">
                      {{ item.field3 }}
                    </td>
                    <td>
                      {{ item.field4 }}
                    </td>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>
        </div>

        <div class="table-component">
          <div class="table-title fold-title" :style="foldState2 ? { borderBottom: 'none' } : {}">
            <div>
              <span class="bar" />
              危机值处理记录(新)
              <span class="sub-title">徐可 2024-03-05 09:09</span>
            </div>
            <div>
              <template v-if="editState">
                <el-button class="fold-button" type="primary">保存</el-button>
                <el-button class="fold-button" type="primary">删除</el-button>
                <el-button class="fold-button" type="primary">打印</el-button>
              </template>
              <el-button class="fold-button" type="primary" @click="editState = !editState">
                编辑
              </el-button>
              <el-button class="fold-button" type="primary">图片管理</el-button>
              <button type="text" @click="foldState2 = !foldState2">
                <i
                  :class="[foldState2 ? 'el-icon-arrow-up' : 'el-icon-arrow-right']"
                  class="fold-icon"
                />
              </button>
            </div>
          </div>
          <div
            v-if="foldState2"
            style="border: 1px solid #ddd; height: 350px; background-color: #fff"
          >
            测试文书
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProgressNote',
  data() {
    return {
      searchValue: '',
      data1List: [],
      data2List: [],
      foldState1: true,
      foldState2: true,
      selectList: [
        { label: '首次病程记录', value: 1 },
        { label: '上级医师查房记录', value: 2 },
        { label: '多学科联合查房记录', value: 3 },
        { label: '日常病程记录', value: 4 },
        { label: '转科记录', value: 5 },
        { label: '交班记录', value: 6 },
        { label: '接班记录', value: 7 },
        { label: '有创诊疗操作记录', value: 8 },
        { label: '科室大查房记录', value: 9 }
      ],
      selectItem: 9,
      editState: false
    }
  },
  mounted() {
    this.initSystemIcd()
  },
  methods: {
    initSystemIcd() {
      this.data1List = [
        {
          field1: '2023-09-05 17:04:14',
          field2: '#钾 (急诊)',
          field3: '2.68mmol/L予补钾',
          field4: '复查电解质'
        },
        {
          field1: '2023-05-06 09:45:09',
          field2: '出现3次或3次以上≥3.0...',
          field3: '出现3次或3次以上≥3.0...',
          field4: ''
        },
        {
          field1: '2023-05-05 14:33:03',
          field2: '心房颤动时R-R期间≥5.0s',
          field3: '心房颤动时R-R期间≥5.0s',
          field4: ''
        },
        {
          field1: '2022-08-09 16:19:54',
          field2: '#血小板',
          field3: '2.68mmol/L',
          field4: '予补钾'
        },
        {
          field1: '2021-06-21 16:04:38',
          field2: '护士站末梢血糖',
          field3: '33',
          field4: ''
        }
      ]
      this.data2List = [
        {
          field1: '2022-09-01 16:24',
          field2: '会诊记录(许可)'
        },
        {
          field1: '2022-12-01 14:50',
          field2: '会诊记录(许可)'
        },
        {
          field1: '2023-04-04 21:24',
          field2: '日常病程记录(徐可)'
        },
        {
          field1: '2024-01-22 16:53',
          field2: '日常病程记录(徐可)'
        },
        {
          field1: '2024-01-22 16:56',
          field2: '朱坚磊主任医师携陈成宇住院医师查房记录(徐可)'
        },
        {
          field1: '2024-03-05 09:09',
          field2: '危急值处理记录(新)(徐可)'
        }
      ]
      // getYlSsicdByPage({
      //   pageIndex: 1,
      //   pageSize: 10
      // }).then((res) => {
      //   if (res.hasError === 0) {
      //     // this.data1List = res.data;
      //     console.log('手术icd res:', res)
      //   }
      // })
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    padding: 10px;
    height: 100%;
    .head-search {
      padding: 16px;
      background-color: #eaf0f9;
      border-radius: 4px;
      border: 1px solid #ddd;
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button.search-button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover).search-button {
        background: #a66dd4;
        border-color: #a66dd4;
      }
      :deep(.el-button--primary:focus).search-button {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        .bar {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
        .sub-title {
          font-weight: 400;
        }
      }
      .table-component {
        margin-top: 10px;
        .fold-title {
          padding: 10px;
          background-color: #eaf0f9;
          border: 1px solid #ddd;
        }
        .fold-icon {
          margin-right: 3px;
          color: #8590b3;
          font-size: 12px;
        }
        .fold-button {
          margin: -10px 10px -10px 0;
        }
        table {
          width: 100%;
        }
        th,
        td {
          border: 1px solid #ddd;
          border-collapse: collapse; /* 移除表格内边框间的间隙 */
          height: 35px;
          padding: 5px 8px;
        }
        .tr-one {
          background-color: #f6f6f6;
        }
        th,
        .tr-two {
          background-color: #eaf0f9;
        }
      }
    }
  }
}
</style>
