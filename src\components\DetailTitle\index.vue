<script setup>
const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})
</script>

<template>
  <div class="detail-title">
    <span class="detail-title-line"></span>
    <span class="detail-title-text">
      {{ props.title }}
      <slot></slot>
    </span>
    <span class="detail-title-extra">
      <slot name="extra"></slot>
    </span>
  </div>
</template>

<style lang="scss" scoped>
$detail-title-height: 14px;

.detail-title {
  display: flex;
  align-items: center;

  .detail-title-line {
    width: 3px;
    height: $detail-title-height;
    background: #356ac5;
    margin-right: 7px;
    // vertical-align: middle;
  }

  .detail-title-text {
    display: inline-block;
    font-family: 'Source Han Sans', sans-serif;
    font-size: $detail-title-height;
    font-weight: bold;
    letter-spacing: 0;
    font-feature-settings: 'kern' on;
    /* 颜色/文字/高强调 */
    color: #171c28;
    margin-right: auto;
  }

  .detail-title-extra {
  }
}
</style>
