import request from '@/utils/request'

// 根据病例ID获取YlYyyzd(e_GetYyyzdList)
export function getYyyzdByBlid(params) {
  return request({
    url: '/medicaladvice/v1/Nutrition/getYyyzdByBlid',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取营养医嘱药品信息(e_GetYyypList)
export function getYyypByYuanQuDM(params) {
  return request({
    url: '/medicaladvice/v1/Nutrition/getYyypByYuanQuDM',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据病历ID查询化验结果_初始化调用
export function getTestResultsForCrrt(params) {
  return request({
    url: '/medicalrecord/v1/Crrt/getTestResultsForCrrt',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取药品营养信息
export function getDrugNutritionData(params) {
  return request({
    url: '/medicaladvice/v1/Nutrition/getDrugNutritionData',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 计算营养医嘱单药品信息
export function calculateNutritionDrugData(data) {
  return request({
    url: '/medicaladvice/v1/Nutrition/calculateNutritionDrugData',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 判断用户是否有保存权限
export function checkNewNutritionDocAdvicePrivi() {
  return request({
    url: '/medicaladvice/v1/Nutrition/checkNewNutritionDocAdvicePrivi',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}

// 判断是否有新肠外营养医嘱资质(提交药品医嘱用)(e_GetYhTjQx)
export function checkNewNutritionDrugPrivi(params) {
  return request({
    url: '/medicaladvice/v1/Nutrition/checkNewNutritionDrugPrivi',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 通用_查询_根据用户ID获取医疗人院信息
export function getDoctorInfoByUserID(params) {
  return request({
    url: '/staff/v1/doctor/getDoctorInfoByUserID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取基础能量消耗(e_GetBEE)
export function getBEE(params) {
  return request({
    url: '/medicaladvice/v1/Nutrition/getBEE',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 计算BMI结果(e_GetBMI)
export function getBMI(params) {
  return request({
    url: '/medicaladvice/v1/Nutrition/getBMI',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//获取病人生命体征
export function getVitalSignList(params) {
  return request({
    url: '/nursingservice/v1/ShengMingTZ/getVitalSignList',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//初始化获取营养医嘱单药房代码
export function getYaoFangDmInit(params) {
  return request({
    url: '/medicaladvice/v1/Nutrition/getYaoFangDmInit',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//根据ID获取医嘱单药品信息(e_GetYyyzd_yp)
export function getYlYyyzdYpByYzdId(params) {
  return request({
    url: '/medicaladvice/v1/Nutrition/getYlYyyzdYpByYzdId',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 保存营养医嘱单
export function saveNutritionOrder(data) {
  return request({
    url: '/medicaladvice/v1/Nutrition/saveNutritionOrder',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 提交营养医嘱单
export function saveNutritionDrugAdvice(data) {
  return request({
    url: '/medicaladvice/v1/Nutrition/saveNutritionDrugAdvice',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 停止营养医嘱单
export function stopYlyyyzd(params) {
  return request({
    url: '/medicaladvice/v1/Nutrition/stopYlyyyzd',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取药品限定范围
export function getInpatientItemLimits(data) {
  return request({
    url: '/finance/v1/SocialApproval/getInpatientItemLimits',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 儿科静脉营养医嘱_获取营养药品
export function getPdnDrugList(params) {
  return request({
    url: '/medicaladvice/v1/Nutrition/getPdnDrugList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 儿科静脉营养医嘱_获取奶类型和医嘱频率
export function getPdnTypeOfMilk(params) {
  return request({
    url: '/medicaladvice/v1/Nutrition/getPdnTypeOfMilk',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据病例ID获取全部化验结果
export function getHuaYanJgAll(params) {
  return request({
    url: '/medicaladvice/v1/Nutrition/getHuaYanJgAll',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 护士站_血糖_根据病历id和时间查询标准天血糖数据
export function getBloodgluseStandardDays(params) {
  return request({
    url: '/nursingservice/v1/bloodglucose/getBloodgluseStandardDays',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 儿科静脉营养医嘱_保存医嘱单
export function savePdnYlEkpnyyzd(data) {
  return request({
    url: '/medicaladvice/v1/Nutrition/savePdnYlEkpnyyzd',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 儿科静脉营养医嘱_保存医嘱药品(提交)
export function savePdnYPYZ(data) {
  return request({
    url: '/medicaladvice/v1/Nutrition/savePdnYPYZ',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 儿科静脉营养医嘱_根据病历ID获取上一次医嘱单
export function getPdnPreviousYZDByBlid(params) {
  return request({
    url: '/medicaladvice/v1/Nutrition/getPdnPreviousYZDByBlid',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 儿科静脉营养医嘱_根据病案号查询列表
export function getPdnListByBingAnHao(params) {
  return request({
    url: '/medicaladvice/v1/Nutrition/getPdnListByBingAnHao',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
