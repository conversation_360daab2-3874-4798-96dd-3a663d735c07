<template>
  <div v-bind="linkProps(to)" @click="handleLinkClick(to)">
    <slot />
  </div>
</template>

<script>
import { isExternal } from '@/utils/validate'
import { refreshLinkToken } from '@/utils/tools'
import { getYiLiaoJGDM } from '@/utils/auth'
import { getDefaultState } from '@/store/modules/user'

export default {
  props: {
    to: {
      type: String,
      required: true
    }
  },
  computed: {
    // 如果是外链
    isExternal() {
      return isExternal(this.to)
    }
  },
  methods: {
    linkProps(to) {
      if (this.isExternal) {
        return {
          href: to,
          target: '_blank',
          rel: 'noopener'
        }
      }
    },
    async handleOpenWindow(to) {
      const _href = await refreshLinkToken(to, getDefaultState()['systemID'], getYiLiaoJGDM)
      window.open(_href)
    },
    handleLinkClick() {
      this.$emit('otherClick')
      if (this.isExternal) {
        this.handleOpenWindow(this.to)
      }
    }
  }
}
</script>
