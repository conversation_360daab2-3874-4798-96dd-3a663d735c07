
# 温州医科大学附属第一医院电子病历前端UI页面改造项目

<p>
<a  href="#">
<img  src="https://img.shields.io/badge/Jquery-1.9.1-brightgreen.svg"  alt="Jquery">
</a>
<a  href="http://getbootstrap.com">
<img  src="https://img.shields.io/badge/Bootstrap-3.3.7-blue.svg"  alt="Bootstrap">
</a>
<a  href=" https://underscorejs.org">
<img  src="https://img.shields.io/badge/underscore-1.12.0-brightgreen.svg"  alt="underscore">
</a>
<a  href="http://izimodal.marcelodolce.com">
<img  src="https://img.shields.io/badge/iziModal-1.6.0-brightgreen.svg"  alt="iziModal">
</a>
<a  href="#">
<img  src="https://img.shields.io/badge/toastr-3-brightgreen.svg"  alt="toastr">
</a>
<a  href="#">
<img  src="https://img.shields.io/badge/language-javascript%20%2F%20jquery-blue.svg">
</a>
</p>
<span>简体中文<span>

---
## 项目介绍
>第一医院电子病历前端UI页面改造项目，是根据电子病历优化需要，主要是对电子病历前端UI页面进行改造，为解决现有系统前台处理速度、界面响应速度慢、分辨率过小时不能精简显示等问题而开发的页面改版。
>

### 开发环境
| 项目 | 名称 |
| --| -- |
| Js 架构 | Jquery 1.9.1|
| 配套扩展模块| Jquery.easyui等|
| Css 架构| Bootstrap v3|
| 前端模板工具| UnderScore |
| 通用界面工具| 自行开发 |
| 模块化封装| 立即执行函数 IIFE |
| 数据交换方式| Ajax JSON格式|
| 打包工具| 无 |
| 代码管理| GitLab|
| IDE工具| VS Code|
### 内置功能
1.登录：用户可以通过账号登录，验证码登录两种方式进行登录。

2.病人一览表
&nbsp;&nbsp;&nbsp;&nbsp;1） 病人管理：界面加载完成后获取相关信息提示，并根据不同组显示不同筛选条件及类型，进行条件筛选，查询病人展示；点击相应病人，进入显示病人基本信息界面。
&nbsp;&nbsp;&nbsp;&nbsp;2）专科日志：该页面可进行条件筛选，查询各类专科信息及数量；点击相应专科下拉按钮，展开显示对应详情信息。

3.病人主页：显示菜单栏相关信息会诊单信息，转科病历修正通知单。

4.长期、临时医嘱界面：显示医嘱相关信息获取执行方法，用药频率，药房代码等信息。可对药物过敏，手术通知单等信息进行筛选，可查询医嘱相关信息展示；可新增医嘱，进行保存或审批。

5.医技检查界面：该页面可输入关键字或点击树状结构后，展示相应技术；选中对应技术获取详情内容，查看今日所开特检单。

6.化验医嘱界面：该页面在点击树状结构后，展示检查项目；通过对展示详情内容进行勾选，验证该医嘱是否已选中，并进行展示；可进行导出保存

7.病例过程：该页面可选择病程记录类型，查询病历记录，展示病历记录详情，可进行图片管理，编辑操作；点击增加按钮，判断文书是否只存在一份，是否完成评估，再填写新的病历记录

8.病例知情记录汇总：可选择记录类型查询知情记录，点击记录，获取记录详情；可进行保存或删除

9.按照病人基本信息查询病例：点击设置查询条件；新增或删除查询条件，点击确定查询病人

10.按照病例内容查询病例：输入相关条件，点击查询按钮获取病人信息

### 使用介绍

  

## 演示图

<table>
<tr>
<td>图片1[账号登录](http://gitlab.wzzh.info/Zhengyate/wzfyy/blob/master/docs/ui%E8%AE%BE%E8%AE%A1%E7%A8%BF/%E7%99%BB%E5%BD%95%E7%95%8C%E9%9D%A2.png)</td>
<td>图片2（验证码登录）</td>
</tr>
<tr>
<td>图片3![病人管理 页内首页](http://gitlab.wzzh.info/Zhengyate/wzfyy/blob/master/docs/ui%E8%AE%BE%E8%AE%A1%E7%A8%BF/%E5%86%85%E9%A1%B5%E9%A6%96%E9%A1%B5.png)</td>
<td>图片4（专科日志）</td>
</tr>
<tr>
<td>图片5（病人主页）</td>
<td>图片6（长期、临时医嘱界面）</td>
</tr>
<tr>
<td>图片7（医技检查界面）</td>
<td>图片8（化验医嘱界面）</td>
</tr>
<tr>
<td>图片9（按照病人基本信息查询病例）</td>
<td>图片10（按照病例内容查询病例）</td>
</tr>
</table>

## 支持浏览器
| [<img src="https://img.onlinedown.net/download/202103/111411-603c5c0349d27.jpg" alt="IE / Edge" width="24px" height="24px" />](https://godban.github.io/browsers-support-badges/)</br>IE / Edge | [<img src="https://bizaladdin-image.baidu.com/0/pic/46bba687d1310065d6501e0c5d71334e.jpg" alt="Chrome" width="24px" height="24px" />](https://godban.github.io/browsers-support-badges/)</br>Chrome |
| --------- | --------- |
| 版本号：| 96以上|