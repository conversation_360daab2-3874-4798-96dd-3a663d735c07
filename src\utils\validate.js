/**
 * Created by PanJiaChen on 16/11/18.
 */

import { isIdCardNo } from '@/utils/IDCardNoVeri'

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL(url) {
  const reg =
    /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return reg.test(url)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(str) {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(str) {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail(email) {
  const reg = // eslint-disable-next-line no-useless-escape
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(email)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(str) {
  return typeof str === 'string' || str instanceof String
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]'
  }
  return Array.isArray(arg)
}

/**
 * 校验字符串实际占用的字符数
 * @typedef {import('@/utils/mixins/form').Validator<any>} AnyValidator
 * @param {number} maxLen 最大字符数
 * @returns {AnyValidator} 校验函数
 */
// 用法：el-form的rules属性中的validator属性
export function getDatabaseLengthValidator(maxLen) {
  return (rule, value, callback) => {
    if (getGBKStringLength(String(value ?? '')) <= maxLen) {
      callback()
      return
    }
    callback('超过限制长度')
  }
}

/**
 * @summary 获取包含中文字符的字符串的编码长度
 * @param {string} str 字符串
 * @returns number 字符串的长度
 *
 * - 如果是英文字符长度为1
 * - 如果是中文字符长度为2
 */
export function getGBKStringLength(str) {
  let realLength = 0
  const len = str.length
  let charCode = -1
  for (let i = 0; i < len; i++) {
    charCode = str.charCodeAt(i)
    if (charCode >= 0 && charCode <= 128) {
      realLength += 1
    } else {
      realLength += 2
    }
  }
  return realLength
}

/**
 *
 * @desc   判断是否为手机号
 * @param  {String|Number} str
 * @return {Boolean}
 */
export function isPhoneNum(str) {
  return /^(0|86|17951)?(1[3-9][0-9])[0-9]{8}$/.test(str)
}

/**
 *
 * @desc  判断是否为身份证号,包括正确性
 * @param  {String|Number} str
 * @return {Boolean}
 */
export function isRightIdCard(str) {
  return isIdCardNo(str).success !== 0
}

/**
 *
 * @desc  判断是否为身份证号
 * @param  {String|Number} str
 * @return {Boolean}
 */
export function isIdCard(str) {
  return /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/.test(
    str
  )
}

/**
 * @Description: 验证港澳回乡身份证 (来源于安卓)
 * @param {string} str
 * @return {boolean}
 */
export function isHKOrMOIdCard(str) {
  return /(^[HM]\\d{10}$)|(^[HM]\\d{8}$)/.test(str)
}

/**
 * @Description:  验证香港身份证 (来源于安卓)
 * @param {string} str
 * @return {boolean}
 */
export function isHKIdCard(str) {
  return /(^((\\s?[A-Za-z])|([A-Za-z]{2}))\\d{6}(([0−9aA])|([0-9aA]))$)/.test(str)
}
