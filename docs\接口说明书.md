## 附一医接口说明:

| 版本号 | 变更点说明 | 变更日期 | 变更人 | 审批人 |
| ------ | ------ | ------ | ------ | ------ |
| 0.1 | 创建 | 2021/02/25 | 林贤思 |  |
| 0.2 | 修改 | 2021/03/12 | 林贤思 |  |
| 0.3 | 新增病人一览表 | 2021/03/18 | 林贤思 |  |
| 0.4 | 新增病人详情信息 | 2021/03/22 | 林贤思 |  |
| 0.5 | 新增病人查询 | 2021/03/26 | 林贤思 |  |

### 设计说明


### 获取项目信息
```
POST /ehr/login.aspx/e_init
```

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string | 1-正常  0/其他-异常或其他错误 |
| CodeMsg | string |  |
| Result | {json} | 界面信息 |

#### json
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| ipAddress | string | IP地址 |
| pageTitle | string | 标题 |


### 登录
```
POST /ehr/login.aspx/e_login
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| au_username |  string | 用户名 |
| au_password |  string | 密码 |
| au_code |  string | 验证码 |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string | 1-正常  0/其他-异常或其他错误 |
| CodeMsg | string | 指定跳转 |
| Result | 登录人员信息 | json |


### 获取验证码
```
POST /ehr/login.aspx/e_vcodes
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| au_username |  string | 用户名 |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | null | null |


### 获取主页内页数据
```
GET /ehr/EhrSz.aspx/e_init
```

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详情请看病人一览表xlsx |


### 电子病历调阅申请待处理弹框提醒
```
GET /ehr/EhrSz.aspx/e_PopTip
```

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | string | 返回数量，为0不需要提醒 |


### 根据菜单ID获取URL
```
POST /ehr/EhrSz.aspx/e_GetMenuUrl
```
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_mid |  string | 菜单ID |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | string | 返回菜单url，有子菜单的不存url |


### 1治疗组病人,2专科病区病人,3专科治疗组获取病人信息
```
POST /ehr/EhrSz.aspx/e_GetPatientList
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| as_lb |  string | 1治疗组 2专科病区病人 3专科治疗 |
| as_lbid |  string | 2专科病区病人传入病区ID,3专科治疗组传入治疗组ID |
| as_new |  string | 默认为0 |
| as_pathpatient |  string | 默认为0 |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 返回病人信息字段 |
详情请看病人一览表xlsx


### 2专科病区病人，切换病区获取病人信息
```
POST /ehr/EhrSz.aspx/e_GetPatientList
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_zkid |  string | 专科id |
| al_bqid |  string | 病区id |
| as_new |  string | 默认为0 |
| as_pathpatient |  string | 默认为0 |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 返回病人信息字段 |
详情请看病人一览表xlsx


### 切换专科，获取病区信息
```
POST /ehr/EhrSz.aspx/e_GetZkdyBq
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_zkid |  string | 专科id |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详情请看病人一览表xlsx |



### 判断当前选择病区与当前显示病人信息中第一个病人病区是否同一个病区
```
POST /ehr/EhrSz.aspx/e_IsSameBQ
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_zkid |  string | 专科id |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详情请看病人一览表xlsx |



### 判断当前选择病区与当前显示病人信息中第一个病人病区是否同一个病区
```
POST /ehr/EhrSz.aspx/e_GetPaQcCnt
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_blid |  string | 病历id |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详情请看病人一览表xlsx |



### 得到医生是否存在未CA签章文书
```
POST /ehr/EhrSz.aspx/e_GetDocUnsign
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| openid |  string | openid |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详情请看病人一览表xlsx |



### 登出操作
```
GET /ehr/EhrSz.aspx/e_relogin_Click
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| openid |  string | openid |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详情请看病人一览表xlsx |



### 得到会诊单信息数量
```
POST /ehr/EhrSz.aspx/e_GetHzdCount
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_zkid |  string | 专科ID |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详情请看病人一览表xlsx |


### 大数据预测
```
POST /ehr/EhrSz.aspx/e_GetDSJYC
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_zkid |  string | 专科ID |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 0或1 |


### 会诊单详细信息
```
POST /ehr/EhrSz.aspx/e_GetHzdInfo
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_zkid |  string | 专科ID |
| al_ysyhid |  string | 用户id |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 会诊单信息 |


### 需要医务科参加的会诊通知
```
POST /ehr/EhrSz.aspx/e_GetHZDNeedYWCJ
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_ysyhid |  string | 用户id |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 相关信息 |


### 特殊抗菌药物会诊单、三联抗菌药物会诊单
```
POST /ehr/EhrSz.aspx/e_GetKjywhzd
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_ysyhid |  string | 用户id |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 相关信息 |


### 获取病人检查提醒的数量
```
POST /ehr/EhrSz.aspx/e_GetBlbgCount
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_zkid |  string | 专科id |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 数量 |


### 辅助登录接口
```
POST /ehr/EhrSz.aspx/e_GetFZDLParms
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| ls_sqyydm |  string | 申请要去访问的应用代码 |
| ls_iplb |  string | 申请要去访问的地址存储 |
| ls_url |  string | 网址 |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详情看病人一览表xlsx |


### 清空我的病人
```
POST /ehr/EhrSz.aspx/e_clearAllMyPat
```

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] |  |


### 根据类型得到我的病人 2按病人 1按床位
```
POST /ehr/EhrSz.aspx/e_GetMyPatByLX
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| as_lx |  string | 类型 |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详情看病人一览表xlsx |


### 保存个性化记录
```
POST /ehr/EhrSz.aspx/e_SaveGXHJL
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| gxhnr |  string | 个性内容 |
| gxhjlid |  string | 个性化id |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] |  |


### 得到某个病人的备注
```
POST /ehr/EhrSz.aspx/e_getBz
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_blid |  string | 病历id |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 备注 |


### 得到某个病人检查检验的地址
```
POST /ehr/EhrSz.aspx/e_getJyJcUrl
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_blid |  string | 病历id |
| ai_lb |  string | 类别 |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | url |


### 获取专科日志
```
POST /ehr/ehrTX.aspx/e_GetPortal
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| as_lb |  string |  |
| al_glid |  string |  |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详情看病人一览表xlsx |


### 获取病人检查提醒的数量
```
POST /ehr/ehrTX.aspx/e_GetBlbgCount
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_zkid |  string | 专科id |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 数量 |


### 获取超2天未归档的病人（原来为上缴病人）
```
POST /ehr/ehrTX.aspx/e_GetUndocumentedPatInfo
```

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详情看病人一览表xlsx |



### 病人详细界面初始化
```
POST /ehr/BrMainLeft.aspx.aspx/e_init
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详情看病人详情信息xlsx |


### 更新病区入院时间

```
POST /ehr/BrMainLeft.aspx/e_UpdateBqrysj
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| as_bqrysj | String |  |
| al_blid | int |  |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | String | 返回提示信息 |


### 康复病人短信提醒

```
POST /ehr/BrMainLeft.aspx/e_setKfbrtx
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| ll_zyid | int |  |
| ll_yhid | int |  |
| ll_bmid | int |  |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详情看病人详情信息xlsx |


### 获取病人的在院状态

```
POST /ehr/BrMainLeft.aspx/e_GetBrzt
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_zyid | int |  |
#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详情看病人详情信息xlsx |


### 获取接口url

```
POST /ehr/BrMainLeft.aspx/e_GetJkurl
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_blid | int |  |
| al_yhid | int |  |
| as_yhxm | string |  |
| al_uzkid | int |  |
| as_jk | string |  |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | url |


### 加入我的病人

```
POST /ehr/BrMainLeft.aspx/e_AddMyPatient
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_blid | int |  |
| al_yhid | int |  |
| as_lb | string | 1添加、0删除 |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string | 提示信息 |


### 设置日间病人

```
POST /ehr/BrMainLeft.aspx/e_SetRjss
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_blid | int |  |
| as_bqrysj | string |  |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string | 提示信息 |


### 获取转科病历修正通知单

```
POST /ehr/BrMainLeft.aspx/e_SetZgys
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_zyid | int |  |
| al_zgysid | int |  |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string | 提示信息 |


### 更新主管医生

```
POST /ehr/BrMainLeft.aspx/e_GetlxztzdHtml
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_blid | int |  |
| al_zkid | int |  |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 相关信息 |


### 按病人基本信息查询病历(高级)初始化

```
POST /ehr/blcx/blcxgj.aspx/e_init
```

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详细请看按病人基本信息查询病历(高级).xlsx |


### 按病人基本信息查询病历(高级)根据条件查询病人信息

```
POST /ehr/blcx/blcxgj.aspx/e_Search
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| as_tjyj | string |  |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详细请看按病人基本信息查询病历(高级).xlsx |


### 按病历内容查询病历初始化

```
POST /ehr/blnrcx.aspx/e_init
```

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详细请看按病历内容查询病历.xlsx |


### 按病历内容查询病历根据条件查询病人信息

```
POST /ehr/blcx/blcxgj.aspx/e_Search
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| cxtj | string | json字符串  |
| as_kssj | string | 查询开始时间  |
| as_jssj | string | 查询结束时间  |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Code | string |  |
| CodeMsg | string |  |
| Result | [] | 详细请看按病历内容查询病历.xlsx |

-----------------------------------------(待确认)

### 危机值
```
POST /ehr/wjz/wjzlist.aspx/getWjzxxCount
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| ll_yhid | int |  |
{ll_yhid:13131}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | int | 返回参数 |


### 会诊单管理
```
POST /ehr/zyblhzd/hzdlist.aspx/GetHzdList_Html
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_blid | int |  |
| al_zkid | String |  |
| as_hzdlb | String |  |
{'al_blid':1492785,'al_zkid':'88', 'as_hzdlb':''}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |



### 获取执行方法，用药频率等数据

```
POST /ehr/yz_sz/ypfjxx.aspx/GetYpyfpl
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| as_lb | String | 1用法 2频率 3给药方法 xtpl葡萄糖（血糖）频率 4中药特殊用法 5草药用法 |
| as_yzlb | String |  |
{'as_lb':'1','as_yzlb':'cq'}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 获取药房代码

```
POST /ehr/yz_sz/yz_sz.aspx/GetZkYfdms
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_zkid | int |  |
| as_yqdm | String |  |
{'al_zkid':88,'as_yqdm':'02'}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 病人药物过敏

```
POST /ehr/yz_sz/yz_sz.aspx/GetYwgm
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_blid | int |  |
{'al_blid':1492785}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 手术通知单

```
POST /ehr/yz_sz/yz_sz.aspx/getSstzd
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_blid | int |  |
{al_blid: 1492785}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 获取社保限量支付药品

```
POST /ehr/yz_sz/yz_sz.aspx/f_getSbXlzfYp
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| al_zyid | int |  |
{'al_zyid':1492785}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 医嘱名称检索

```
POST /ehr/yz_sz/ypfjxx.aspx/GetItemListHTML
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| as_key | String |  |
{'as_type':'1011','as_key':'1','al_zkid':88,'as_mhss':'0','as_yfdms':'2A3^2A2^2A2','as_jx':'','as_yzlb':'','as_yzlx':'cq'}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 获取时间

```
POST /ehr/yz_sz/yz_sz.aspx/GetDateString
```

#### 参数
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 停止
```
POST /ehr/yz_sz/yz_sz.aspx/StopYz
```

{'as_yzlst':'yp25586384,','as_lb':'1','al_blid':1746080, 'al_yhid':13131,'as_tzsj':'','as_sfxt':''}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d |  | 返回参数 |


### 历史用药
```
POST /ehr/yz_sz/yzmb.aspx/GetYpMbByLb
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| as_type | | 4在用口服药 zyzj在用针剂 wgyp外购药品 zsyp赠送药品 zcyfc中成药分餐 |

{'as_type':'zyzj','as_bz':'1746080','al_blid':1746080,'al_yhid':13131,'al_zkid':88,'as_yqdm':'02','as_yzlx':'cq'}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d |  | 返回参数 |


### 获取治疗医嘱模板
```
POST /ehr/yz_sz/yzmb.aspx/GetYpMbByLb
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d |  | 返回参数 |


### 保存
```
POST /ehr/yz_sz/yz_sz.aspx/SaveYz
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{'ao_yzarr':[{"mc":"急诊护士","id":"z23529","lb":"zl","yzlx":"cq","yzlb":"","sfzb":"0","gg":"","zxff":"","zxpl":"","jx":"","cxts":"999","dw":"","tsyf":"","jrcy":"","zsbq":"","yzid":"0","gysj":"","kssj":"2021-03-01 16:09:00","jssj":"0000-00-00 00:00:00","bqid":"3040","sl":"1","cs":"","gjcg":"","zhtxt":"","bmlx":"","xmbm":"","sfbl":"","mps":"","sqdsjid":"","yplb":"0","jbdm":"","jbmc":"","sqyy":"","yysq":""}],'as_delid':'','al_blid':1492785,'al_ysid':13131,'as_kzkblyz':'','as_kzkblyz_ybqzk':'','as_grzd':'','ao_tsywba':[],'as_yzlbtype':'cq'}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d |  | 返回参数 |


### 审核
```
POST /ehr/yz_sz/hlyy/hlyy.aspx/hlyyCheck2
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d |  | 返回参数 |


### 提交
```
POST /ehr/yz_sz/yz_sz.aspx/SubmitYz
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{'as_yzlst':'1574,','al_blid':1492785, 'al_ysid':13131, 'al_cy':9,'as_kzkblyz':''}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d |  | 返回参数 |


### 已导出的医嘱
```
POST /ehr/yz_sz/yz_sz.aspx/GetCurrYz
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{'al_blid':1492785,'as_yzlb':'1','as_zy':'1','as_tz':'true','as_sfxt':'','as_lb':'','as_fylx':''}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d |  | 返回参数 |


### 综合模板
```
POST /ehr/yz_sz/zhmb.ashx
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| Method | |  |
| as_mblb | |  |
| al_glid | |  |

Method=ajax_getZhmbByLb&as_mblb=4&al_glid=13131

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d |  | 返回参数 |


### 保存模板
```
POST /ehr/yz_sz/yzmb.aspx/GetYpByTime
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
{'al_blid':1492686,'al_zkid':88}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 获取化验医嘱
```
post /ehr/newhyyz/hyyz.aspx
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
/ehr/newhyyz/hyyz.aspx?as_blid=1492686&as_bqid=3040&tmpid=9%3a37%3a06&as_zlzid=0

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | HTML | 返回参数 |


### 选中化验医嘱
```
post /ehr/ajaxpro/zy_HyyzControl,App_Web_hyyzcontrol.ascx.85154d5c.ashx
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | HTML | 返回参数 |


### 验证化验项目
```
get /ehr/newhyyz/Handler.ashx
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
/ehr/newhyyz/Handler.ashx?op=JYKZ

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
|  | [] | 返回参数 |


### 导出化验项目
```
POST /ehr/newhyyz/hyyz.aspx/YdcHyyz
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{'val':'[{"blid":"1492686","bqid":"3040","zlzid":"0"},{"yzid":0,"kssj":"2021-03-02 09:37","yzbz":"","hyxmid":"30520","mlid":"403","mlxh":"23","sftc":"0","sbsp":"","mbid":"-1","ztbz":"1"},{"yzid":0,"kssj":"2021-03-02 09:37","yzbz":"","hyxmid":"30522","mlid":"403","mlxh":"23","sftc":"0","sbsp":"","mbid":"-1","ztbz":"1"}]','dcsj':''}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | {} | 返回参数 |


### 保存化验项目
```
POST /ehr/newhyyz/hyyz.aspx/SaveHyyz
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{'val':'[{"blid":"1492686","bqid":"3040","zlzid":"0"},{"yzid":0,"kssj":"2021-03-02 09:37","yzbz":"","hyxmid":"30520","mlid":"403","mlxh":"23","sftc":"0","sbsp":"","mbid":"-1","ztbz":"1"},{"yzid":0,"kssj":"2021-03-02 09:37","yzbz":"","hyxmid":"30522","mlid":"403","mlxh":"23","sftc":"0","sbsp":"","mbid":"-1","ztbz":"1"}]','dcsj':''}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | {} | 返回参数 |


### 获取医技项目
```
GET /ehr/zyyz/zytjsqdyzmain.aspx
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

av_zyid=0&av_blid=1492686&av_bqid=3040&av_zllx=11&tmpid=9:58:41&as_zlzid=0&av_zkid=88

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | HTML | 返回参数 |


### 医技关键字搜索
```
GET /ehr/zyyz/TjsqdHandler.ashx
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

Method=GetKeySearch&kw=1&zllx=11&_=1614650357087

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | [] | 返回参数 |


### 获取医技申请单
```
GET /ehr/zyyz/zytjsqdtx.aspx
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

ly=tjyz&av_yzmlid=24776&av_blid=1492686&av_bmid=2475&av_zkid=88&av_zyid=0&av_zllx=11&av_sqdstate=0&av_bqid=3040&tempmath=0.8593347604951518

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | HTML | 返回参数 |


### 申请单详情操作
```
GET /ehr/zyyz/TjsqdHandler.ashx
```

是否已有同类型申请单： Method=checkIsRepeat&yzmlid=24776&brbh=00011410956108&ysyhid=13131&zkid=88&_=1614650513062
是否已超过一个部位检查：Method=getSfdyJson&yzmlid=24776&_=1614650513063
MRI检查：Method=enhancedMRHint&yzmlid=24776&_=1614650513064
是否用于手术、门诊输液等新冠肺炎排查：Method=GetFyct POST
接触隔离：Method=GetJcgl&av_blid=1492686&_=1614650513065
#### 返回 
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d |  | 返回参数 |


### 查看今日所开特检单
```
POST /ehr/zyyz/TjsqdHandler.ashx?Method=GetAllTjsqdToday
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| av_blid | int |  |

av_blid: 1492686
#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | [] | 返回参数 |


### 获取病程记录
```
GET /ehr/zyblws/blwslist.aspx
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

as_lx=1&as_blid=1492686&as_zyid=1492686&as_zkdm=80&as_zkid=88
#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | HTML | 返回参数 |


### 获取病程记录类型
```
POST /ehr/zyblws/blwslist.aspx/GetWslbItem
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{'al_zkid':88}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 文书中是否只存在一份
```
POST /ehr/zyblws/blwslist.aspx/CheckGsdm
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{'as_gsdm':'5354','al_blid':1492686}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 病程流程
```
POST /ehr/zyblws/blwslist.aspx/GetWslx
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{'as_gsdm': '5354'}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 获取病程详情
```
POST /ehr/zyblws/blwslist.aspx/GetNewWsHtml
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{'as_gsdm': '5354', 'al_blid':1492686, 'al_zyid':1492686, 'as_tmpid':'t1'}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 编辑病程详情
```
POST /ehr/zyblws/blwslist.aspx/GetWsDetail
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{'as_gsdm': '5354','al_blid': 1492686, 'al_zyid': 1492686, 'al_wsid':0,'as_url':'http://10.41.220.25/ehr/'}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 是否完成内或外科住院患者静脉血栓栓塞症的风险评估(VTE)
```
POST /ehr/zyblws/blwslist.aspx/ChkNIHSS
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{ 'al_blid': 1492768,'lb':'1' }

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 完成改良Rankin量表(mRS)评分
```
POST /ehr/zyblws/blwslist.aspx/ChkmRs
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{ 'al_blid': 1492768,'lb':'1' }

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 完成内或外科住院患者静脉血栓栓塞症的风险评估(VTE)
```
POST /ehr/zyblws/blwslist.aspx/CkNWkVte
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{ 'al_blid': 1492768}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 获取病历知情记录
```
POST /ehr/zyblws/zqjllistall.aspx/GetBrWssByLx
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{'al_blid':1492686,'as_wslx':'^24^25^26^27^28^29^55^2A^2B'}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | [] | 返回参数 |


### 病历知情记录搜索
```
POST /ehr/zyblws/zqjllist.aspx/GetZqjlByKey
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{'as_key':'1','as_wslx':'00'}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 获取病历路径
```
POST /ehr/zyblws/zqjllist.aspx/GetWsUrl
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |

{'as_gsdm': '5270','al_wsid':0,'as_wslx':'2A','al_blid':1492686,'al_zyid':1492686}

#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | String | 返回参数 |


### 获取病历字段
```
POST /ehr/blcx/blcxgj.aspx/getdata
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | [] | 返回参数 |


### 按病历内容查询病历
```
POST /ehr/blnrcx.aspx
```

| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |


#### 返回
| 名称 | 类型 | 说明 |
| ------ | ------ | ------ |
| d | HTML | 返回参数 |