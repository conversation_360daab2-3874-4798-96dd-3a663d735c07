﻿<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>医务处伤病器官切除手术审批</title>
  <meta name="description" content="医务处伤病器官切除手术审批">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
  <style>
/*  */
    /* 最外层框 */
    .container_ycwsp {
      height: 100%;
    }
     /* 左边第一行 -- 占据一整行 */
    .flex-row {
      display: flex;
      flex-wrap: nowrap !important; 
    }
    .ycwsp_inner {
      position: relative;
      display: flex;
      /* margin-top: 27px; */
    }

    /* 左边 */
    /* 左侧外框 */
    .ycwsp_inner_left {
      position: relative;
      /* width: 19.3%; */
      width: 400px;
      margin-right: 10px;
      padding: 0 10px;
    }
    .ycwsp_inner_content {
      position: relative;
      /* width: 19.3%; */
      width: 302px;
      margin-right: 10px;
      padding: 0 10px;
    }
    .title {
      text-align: center;
      font-size: 14px;
      font-weight: 700;
      padding-bottom: 15px;
    }

    .mc_search {
      width: 300px;
      margin: auto;
      box-shadow: none;
      height: 34px;
      padding: 6px 12px;
      font-size: 14px;
      line-height: 1.42857143;
      color: #555;
      background-color: #fff;
      background-image: none;
      border: 1px solid #ccc;
      border-radius: 4px;
      /* position: absolute; */
    }

    .input-tag {
      float: right;
      width: 20px;
    }

    #left_Search_tag {
      position: relative;
      background: none;
      color: #4975cb;
      border: none;
      z-index: 999;
    }
    #ycwsp_lists,#ycwspdeail_lists{
      height: 540px;
      overflow-y: auto;
    }
    #ycwsp_lists li{
      font-size: 14px;
      padding: 3px 0;
    }
    #ycwsp_lists li label{
      padding-left: 3px;
      display: initial;
      font-weight: normal;
      cursor: pointer;
    }


    .ycwwsp_inner_right .panel {
      position: relative;
      height: 100%;
      box-shadow: none;
      font-size: 14px;
      border-radius: 0;
      margin-top: 15px;
    }
    .ycwwsp_inner_right .panel-title {
      background: linear-gradient(180deg, #3D6CC8 0%, rgba(61, 108, 200, 0.6) 100%);
      color: #fff;
      border: 1px solid #3D6CC8;
      line-height: 24px;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
    }
    .ycwwsp_inner_right .panel-title>span>a {
      /* color: #fff; */
      margin-left: 10px;
      margin-right: 5px;
      line-height: 24px;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      text-decoration: none;
      border: 1px solid #fff;
      background: #fff;
      padding: 2px 3px;
      color: #000;
      border-radius: 3px;
    }
    .ycwwsp_inner_right .panel-title>span>a:hover {
      /* text-decoration: none; */
      color: #fff;
      background: #707070;
    }
    .blzqnr {
      position: relative;
      height: 662px;
      /* overflow-y: scroll; */
    }
    .xsNow {
      position: relative;
      /* padding: 5px 5px 0px 5px; */
      padding: 15px;
    }
    #deail_title{
      display: inline-block;
      max-width: 100%;
      margin-bottom: 5px;
      font-weight: 700;
    }
    #ycwspdeail_lists li label{
      font-weight: 500;
      cursor: pointer;
    }
    .ycwsp_inner_left .Wdate_input{
      width: 118px;
      margin: auto;
      box-shadow: none;
      height: 29px;
      font-size: 14px;
      color: #555;
      background-color: #fff;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    .select_bz{
      width: 118px;
      margin: auto;
      box-shadow: none;
      height: 25px;
      font-size: 14px;
      color: #555;
      background-color: #fff;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    .select_title{
      width: 60px;
      text-align: right;
    }
    .body_content> .datebox{
      width: 118px;
    }
    .body_content> .combo-p{
      width: 215px;
    }
  </style>
</head>
<body class="body_content">
  <!-- style="display: flex; flex-wrap: nowrap !important;" flex-row flex-column-->
  <div class="container_ycwsp flex-column" style="display: flex; flex-wrap: nowrap !important;">
    <div style="padding:20px 0 8px 10px ;">
      <label>开始时间1:</label><input type="text" id="datetimebox1" class="Wdate" value="" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:false})" autocomplete="off" style="width: 118px;height: 25px;border-radius: 4px;border: 1px solid #ddd;;" tag="0" />
      <label>结束时间2:</label><input type="text" id="datetimebox2" class="Wdate" value="" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:false})" autocomplete="off" style="width: 118px;height: 25px;border-radius: 4px;border: 1px solid #ddd;;" tag="0" />
      <label class="select_title">状态:</label><select class="select_bz">
        <option value="">未审批</option>
        <option value="0">已暂停</option>
        <option value="1">已审批</option>
        <option value="9">已作废</option>
      </select>
      <button class="e_btn_primary" onclick="e_search()">查询</button>
    </div>
    <div class="ycwsp_inner flex-fill">
      <div class="ycwsp_inner_left">
        <div>
          <input type="secrch" class="mc_search" onkeydown="calAge(event)"  />
        </div>
        <div style="padding: 5 0px;">
          <ul id="ycwsp_lists">
          </ul>
        </div>
      </div>
      <div class="ycwsp_inner_content">
        <div id="deail_title">
          
        </div>
        <div style="padding-top: 15px;">
          <ul id="ycwspdeail_lists">
          </ul>
        </div>
      </div>
      <div class="ycwsp_inner_right flex-fill">
        <!-- <div class="flex-row align-items-center">
          <span class="brBlh">
          &nbsp;&nbsp;&nbsp;&nbsp;<span style="color: #555555;"></span>
          <b>0001941554</b>
          </span>
          <span class="brName">
          &nbsp;&nbsp;&nbsp;&nbsp;<b>郑利勤</b>
          </span>
          <span class="brxx">
          &nbsp;&nbsp;&nbsp;&nbsp;女&nbsp;&nbsp;&nbsp;&nbsp;风湿免疫科&nbsp;&nbsp;&nbsp;&nbsp;
          </span>
          <span class="e-tag e-tag-blue brBlue" style="margin-right: 10px;">
          263-003
          </span>
          <span class="e-tag e-tag-red brRed" style="margin-right: 10px;">
          358天
          </span>
          <span class="e-tag e-tag-blue brChangeColor" style="margin-right: 10px;">
          自费
          </span>
        </div> -->
        <div class="ycwwsp_inner_right panel-group"><div>
          <div class="panel panel-default" id="panel">
          <div class="panel-title" href="#type" id="type_title">
            <span>文书内容</span>
            <span class="zqsave"><a class="zqsaveclick" id="zqsave" onclick="db_spModel()">审批</a></span>
            <span class="zqdy"><a class="zqdyclick" id="zqdyv" onclick="xz_zf()">作废</a></span>
          </div>
          <div id="type" class="panel-collapse collapse in">
            <div class="blzqnr">
              
            </div>
          </div>
        </div></div></div>
      </div>
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面js文件 -->
  <!-- <script src="js/e-zqjllistall.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-interface.ycwsp.js?v=" + Date.now() + "'><\/script>");
    document.write("<script type='text/javascript' src='js/e-ycwsp.js?v=" + Date.now() + "'><\/script>");
  </script>
</body>

</html>