import { getUserImgById, getUserInfo } from '@/api/user'
import { login } from '@/api/basic'
import { clearAllUserAuth, getYiLiaoJGDM, getYongHuID, removeToken } from '@/utils/auth'
import router, { resetRouter } from '@/router'
import { getTokenInfo } from '@/api/login'
import tools from '@/utils/tools'
import { Channel } from '@/utils/broadcast'

const AUTHMAPTIME = 60 * 60 * 1000
export const getDefaultState = () => {
  return {
    userInfo: {},
    token: '',
    refreshToken: '',
    uuid: '',
    name: '',
    avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
    introduction: '',
    yongHuID: '',
    roles: [],
    yiLiaoJGDM: '',
    systemID: 'W20', // 系统id
    app_key:
      process.env.NODE_ENV === 'production'
        ? 'iq63g6ouu6780q3snx3ibst6awpnnq49f1lr1ubqoy3xq4jr97yvqaw5sozvr9db'
        : 'womtnsfyv5ahca92yqdzaqak252prpjs8edsz6sc988m9fmm54ytmf0q803u4yn1'
  }
}

const state = getDefaultState()

// 设置AuthMap
const setAuthMap = (keyword1, keyword2, data) => {
  if (!keyword1 || !keyword2) {
    return
  }
  const keyword = `${keyword1}${keyword2}`
  const authMap = JSON.parse(localStorage.getItem('authMap')) || {}
  const item = {
    [keyword]: {
      ...(authMap[keyword] || {}),
      ...data,
      expirationTime: new Date().getTime() + AUTHMAPTIME
    }
  }
  for (const key in authMap) {
    if (Object.hasOwnProperty.call(authMap, key)) {
      const element = authMap[key]
      if (element.expirationTime < new Date().getTime()) {
        delete authMap[key]
      }
    }
  }
  localStorage.setItem('authMap', JSON.stringify({ ...authMap, ...item }))
}
// 清除本应用的AuthMap
const clearAuthMap = (keyword1, keyword2) => {
  if (!keyword1 || !keyword2) {
    return
  }
  const keyword = `${keyword1}${keyword2}`
  const authMap = JSON.parse(localStorage.getItem('authMap')) || {}
  delete authMap[keyword]

  localStorage.setItem('authMap', JSON.stringify(authMap))
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
    setAuthMap(state.app_key, state.yongHuID, { token })
  },
  SET_USER_ID: (state, yongHuID) => {
    state.yongHuID = yongHuID
  },
  SET_REFRESH_TOKEN: (state, refreshToken) => {
    state.refreshToken = refreshToken
    setAuthMap(state.app_key, state.yongHuID, { refreshToken })
  },
  SET_UUID: (state, uuid) => {
    state.uuid = uuid
  },
  SET_YLJGDM: (state, yiLiaoJGDM) => {
    state.yiLiaoJGDM = yiLiaoJGDM
  },
  SET_ORG_KEY: (state, orgKey) => {
    state.orgKey = orgKey
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_USERINFO: (state, userInfo) => {
    state.userInfo = userInfo
  },
  RESET_STATE: (state) => {
    clearAuthMap(state.app_key, state.yongHuID)
    Object.assign(state, getDefaultState())
  }
}

const getters = {
  where(state) {
    let isWhere
    if (process.env.NODE_ENV === 'development') {
      // isWhere = 'http://localhost:9527/web/auth'
      isWhere = 'http://**************:38889/web/auth' // 开发
    } else if (process.env.NODE_ENV === 'test') {
      // isWhere = 'http://**************:38092/web/auth' // 测试环境
      isWhere = location.protocol + '//' + location.host + '/web/auth'
    } else if (process.env.NODE_ENV === 'pre') {
      // isWhere = 'http://**************:38092/web/auth' // 预发布测试
      isWhere = location.protocol + '//' + location.host + '/web/auth'
    } else if (process.env.NODE_ENV === 'production') {
      isWhere = location.protocol + '//' + location.host + '/web/auth' // 生产
    }
    return isWhere
  },
  redirectParams() {
    let params
    if (process.env.NODE_ENV === 'pre') {
      let from = tools.splitUrl(window.location.href)
      from = encodeURIComponent(from.split('#')[0] + '#/home')
      params = `url_redirect=${from}`
    } else {
      params = `app_key=${state.app_key}`
    }
    console.log('redirectParams->', params)
    return params
  },
  pwd(state) {
    return state.userInfo.yongHuMM
  },
  userName(state) {
    return state.userInfo.yongHuXM
  },
  ipAddress(state) {
    return state.userInfo.ip
  }
}

const actions = {
  saveLoginToken({ commit }, { refreshToken, accessToken, yongHuID, yiLiaoJGDM, orgKey }) {
    console.log(accessToken)
    accessToken && commit('SET_TOKEN', accessToken)
    refreshToken && commit('SET_REFRESH_TOKEN', refreshToken)
    yongHuID && commit('SET_USER_ID', yongHuID)
    yiLiaoJGDM && commit('SET_YLJGDM', yiLiaoJGDM)
    orgKey && commit('SET_ORG_KEY', orgKey)
    // 将初始化当前应用的authMap
    setAuthMap(state.app_key, state.yongHuID, { token: accessToken, refreshToken })
  }, // user login
  login({ commit, dispatch }, userInfo) {
    const { yongHuZH, yongHuMM } = userInfo
    return new Promise((resolve, reject) => {
      login({ yongHuZH: yongHuZH.trim(), yongHuMM: yongHuMM })
        .then((response) => {
          const { data } = response
          commit('SET_TOKEN', data.accessToken)
          commit('SET_USER_ID', data.yongHuID)

          // 获取医生信息并存储
          if (data.yongHuID) {
            dispatch('patient/getDoctorInfo', data.yongHuID, { root: true })
          }

          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  }, // 获取用户信息
  getInfo({ commit, state, dispatch }) {
    console.log('=======================', state.systemID)
    return new Promise((resolve, reject) => {
      if (!getYiLiaoJGDM() || !getYongHuID()) {
        reject('医疗机构代码或用户ID为空')
      }
      Promise.all([
        // getLoginUserInfo({ yongHuID: state.yongHuID, yiLiaoJGDM: state.yiLiaoJGDM }),
        getTokenInfo({ yingYongDM: getDefaultState().systemID }),
        getUserInfo({
          yongHuID: getYongHuID(),
          yiLiaoJGDM: getYiLiaoJGDM()
        }),
        getUserImgById({ yongHuID: getYongHuID() })
      ])
        .then((resArr) => {
          console.log('获取用户信息', resArr)
          const userInfo = state.userInfo
          if (resArr[0] && resArr[1] && resArr[2]) {
            commit('SET_USERINFO', {
              ...userInfo,
              ...resArr[0].data,
              ...resArr[1].data
            })
            console.log('resArr[2]', resArr[2])
            resArr[2]?.data?.touXiangDZ && commit('SET_AVATAR', resArr[2]?.data?.touXiangDZ)

            // 获取医生信息并存储
            const yongHuID = getYongHuID()
            if (yongHuID) {
              dispatch('patient/getDoctorInfo', yongHuID, { root: true })
            }
          } else {
            console.log('获取用户信息出错')
          }
          resolve(resArr)
        })
        .catch((err) => {
          console.log('erro', err)
          reject(err)
        })
    })
  }, // 登出
  logOut({ commit, state, getters }, errMessage) {
    return new Promise((resolve, reject) => {
      resetRouter()
      Channel.postMessage({
        logout: true,
        paramObj: { yongHuID: state.yongHuID, yingYongDM: state.systemID }
      })
      commit('RESET_STATE') // 重置个人信息
      localStorage.removeItem('vuex')
      clearAllUserAuth()
      resolve()
      const loginOutUrl = `${getters.where}/#/logout?${getters.redirectParams}`
      // loginOutUrl = errMessage ? loginOutUrl + '&' + 'logout_msg=' + errMessage : loginOutUrl
      console.log('测试应用 logOut 登出地址:', loginOutUrl)
      window.location = loginOutUrl
    })
  }, // 保存外部登陆重定向回传参数
  saveAuth({ state, commit, dispatch }, authInfo) {
    return new Promise((resolve) => {
      const { auth_token, access_code, access_dm, refresh_token, uuid } = authInfo
      if (access_code && access_dm) {
        removeToken() // must remove  token  first
        const userInfo = state.userInfo
        commit('SET_USERINFO', {
          ...userInfo,
          yongHuID: Number(access_code),
          yiLiaoJGDM: access_dm
        })
        auth_token && commit('SET_TOKEN', auth_token) // 设置token
        refresh_token && commit('SET_REFRESH_TOKEN', refresh_token) // 设置刷新token
        access_code && commit('SET_USER_ID', Number(access_code)) // 设置用户id
        access_dm && commit('SET_YLJGDM', access_dm) // 设置医疗机构代码
        uuid && commit('SET_UUID', uuid)

        // 将初始化当前应用的authMap
        setAuthMap(state.app_key, state.yongHuID, {
          token: auth_token,
          refreshToken: refresh_token
        })

        // 获取医生信息并存储
        if (access_code) {
          dispatch('patient/getDoctorInfo', Number(access_code), { root: true })
        }

        resolve()
      } else {
        auth_token && commit('SET_TOKEN', auth_token) // 设置token
        refresh_token && commit('SET_REFRESH_TOKEN', refresh_token)
        uuid && commit('SET_UUID', uuid)
        commit('SET_TOKEN', auth_token) // 设置token
      }
    })
  }, // remove token
  resetToken({ commit, getters }) {
    return new Promise((resolve) => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      const from = tools.splitUrl(window.location.href)
      console.log('安全模块 resetToken url_redirect地址', from)
      console.log('getters', getters.where, getters.redirectParams)
      // console.log("userdeToken?",getters[''])
      window.location = `${getters.where}/#/resetToken?${getters.redirectParams}`
      resolve()
    })
  }, // dynamically modify permissions 这里有问题 到时候再修改
  async changeRoles({ commit, dispatch }, role) {
    const token = role + '-token'
    commit('SET_TOKEN', token)
    const { roles } = await dispatch('getInfo')
    resetRouter()
    // generate accessible routes map based on roles
    const accessRoutes = await dispatch('permission/generateRoutes', roles, {
      root: true
    })
    // dynamically add accessible routes
    router.addRoutes(accessRoutes)

    // reset visited views and cached views
    dispatch('tagsView/delAllViews', null, { root: true })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
