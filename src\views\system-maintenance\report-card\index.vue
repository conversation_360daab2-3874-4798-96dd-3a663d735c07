<!-- 手术ICD维护 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="table-background">
        <div class="table-title">
          <div>
            <span />
            新增传染病报告卡
          </div>
          <div>
            <el-button type="primary">保存</el-button>
            <el-button type="primary">审核通过</el-button>
            <el-button type="primary">审核不通过</el-button>
          </div>
        </div>
        <div class="report-card">
          <div class="table-title">
            <div>
              <span />
              信息填写
            </div>
          </div>
          <div class="table-component">
            <table>
              <tbody>
                <tr>
                  <td class="info-label">卡片编号:</td>
                  <td class="info-value">
                    {{ formData.field1 }}
                  </td>
                </tr>
                <tr>
                  <td class="info-label">序号:</td>
                  <td class="info-value">
                    {{ formData.field2 }}
                  </td>
                </tr>

                <tr>
                  <td class="info-label">
                    <span>*</span>
                    姓名:
                  </td>
                  <td class="info-value">
                    <el-input v-model="formData.field3" />
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span>*</span>
                    有效证件号:
                  </td>
                  <td class="info-value">
                    <el-input v-model="formData.field4" />
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span>*</span>
                    性别:
                  </td>
                  <td class="info-value">
                    <el-radio v-model="formData.field5" label="1">男</el-radio>
                    <el-radio v-model="formData.field5" label="0">女</el-radio>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span>*</span>
                    出生日期:
                  </td>
                  <td class="info-value">
                    <el-date-picker
                      v-model="formData.field6"
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">联系电话:</td>
                  <td class="info-value">
                    <el-input v-model="formData.field7" />
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span>*</span>
                    职业:
                  </td>
                  <td class="info-value">
                    <el-input v-model="formData.field8" />
                  </td>
                </tr>
                <tr>
                  <td class="info-label">患者工作单位:</td>
                  <td class="info-value">
                    <el-input v-model="formData.field9" />
                  </td>
                </tr>
                <tr>
                  <td class="info-label">现住地址类型:</td>
                  <td class="info-value">
                    <el-radio v-model="formData.field10" label="0">本县区</el-radio>
                    <el-radio v-model="formData.field10" label="1">本市其他县区</el-radio>
                    <el-radio v-model="formData.field10" label="2">本省其他地市</el-radio>
                    <el-radio v-model="formData.field10" label="3">其他省</el-radio>
                    <el-radio v-model="formData.field10" label="4">外籍</el-radio>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span>*</span>
                    常住户口地址:
                  </td>
                  <td class="info-value">
                    <el-select v-model="formData.field11" placeholder="">
                      <el-option
                        v-for="item in [{ value: 0, label: '浙江省' }]"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span>*</span>
                    诊断类型:
                  </td>
                  <td class="info-value">
                    <el-input v-model="formData.field12" />
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span>*</span>
                    病例分类:
                  </td>
                  <td class="info-value">
                    <el-input v-model="formData.field13" />
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span>*</span>
                    病发日期:
                  </td>
                  <td class="info-value">
                    <el-date-picker
                      v-model="formData.field14"
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span>*</span>
                    诊断时间:
                  </td>
                  <td class="info-value">
                    <el-date-picker
                      v-model="formData.field15"
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">死亡时间:</td>
                  <td class="info-value">
                    <el-date-picker
                      v-model="formData.field16"
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span>*</span>
                    疾病名称:
                  </td>
                  <td class="info-value">
                    <el-select v-model="formData.field17" placeholder="">
                      <el-option
                        v-for="item in [{ value: 0, label: '浙江省' }]"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span>*</span>
                    填卡医生:
                  </td>
                  <td class="info-value">
                    <el-input v-model="formData.field18" />
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span>*</span>
                    填卡日期:
                  </td>
                  <td class="info-value">
                    <el-date-picker
                      v-model="formData.field19"
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span>*</span>
                    密切接触者有无相同症状:
                  </td>
                  <td class="info-value">
                    <el-radio v-model="formData.field20" label="0">无</el-radio>
                    <el-radio v-model="formData.field20" label="1">有</el-radio>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span>*</span>
                    备注:
                  </td>
                  <td class="info-value">
                    <el-input v-model="formData.field21" />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getBaseInfo } from '@/api/report-card'
export default {
  name: 'ReportCard',
  data() {
    return {
      icdVisible: false,
      formData: {},
      searchValue: '',
      icdList: []
    }
  },
  mounted() {
    for (let i = 1; i < 22; i++) {
      this.formData['field' + i] = ''
    }
    this.init()
  },
  methods: {
    init() {
      getBaseInfo({}).then((res) => {
        if (res.hasError === 0) {
          console.log('报告卡', res)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .table-background {
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        margin-bottom: 10px;
        span {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .report-card {
        padding: 16px;
        background-color: #eaf0f9;
        border-radius: 4px;
        border: 1px solid #ddd;
        .table-component {
          margin-top: 10px;
          table {
            width: 100%;
          }
          td {
            border: 1px solid #ddd;
            border-collapse: collapse; /* 移除表格内边框间的间隙 */
            height: 35px;
            padding: 5px;
          }
          .info-label {
            width: 180px;
            text-align: right;
            background-color: #eff3fb;
            span {
              color: #f35656;
              position: relative;
              top: 3px;
              right: 3px;
            }
          }
          .info-value {
            background-color: #ffffff;
            a {
              text-decoration: underline;
              color: #356ac5;
            }
            .el-input {
              width: 300px;
            }
          }
        }
      }
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
    }
  }
}
</style>
