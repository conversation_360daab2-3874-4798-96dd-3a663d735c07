import request from '@/utils/request'

// 获取其他医嘱目录
export function getAssayAdviceCatalogList(params) {
  return request({
    url: '/medicaladvice/v1/AssayInpatient/getAssayAdviceCatalogList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//获取个人模板及专科模板
export function getAssayTemplateCatalogList(params) {
  return request({
    url: '/medicaladvice/v1/AssayInpatient/getAssayTemplateCatalogList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//根据【模板ID】获取【模板详情】
export function getTemplateDetailByMBID(params) {
  return request({
    url: '/app-emrservice/v1/mdedicalAdvice/getTemplateDetailByMBID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//根据【检验医嘱目录id】获取【检验医嘱目录明细】
export function getItemsByCatalogId(params) {
  return request({
    url: '/app-emrservice/v1/mdedicalAdvice/getItemsByCatalogId',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//获取项目数据
export function getAssayItemInpatientDetail(params) {
  return request({
    url: '/medicaladvice/v1/AssayInpatient/getAssayItemInpatientDetail',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//获取样本类型
export function getSampleTypeDescribeData() {
  return request({
    url: '/medicaladvice/v1/AssayInpatient/getSampleTypeDescribeData',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}

//保存住院化验医嘱
export function saveAssayInpatientAdvice(data) {
  return request({
    url: '/medicaladvice/v1/AssayInpatient/saveAssayInpatientAdvice',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

//删除住院化验医嘱
export function deleteByYzidList(data) {
  return request({
    url: '/medicaladvice/v1/AssayInpatient/deleteByYzidList',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

//获取检验医嘱目录树
export function getAssayAllCatalogTree(params) {
  return request({
    url: '/app-emrservice/v1/mdedicalAdvice/getAssayAllCatalogTree',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//获取未导入化验医嘱
export function getWeiDaoRuAssayAdvice(params) {
  return request({
    url: '/medicaladvice/v1/AssayInpatient/getWeiDaoRuAssayAdvice',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//获取近期导出数据
export function getYiDaoRuAssayAdvice(params) {
  return request({
    url: '/medicaladvice/v1/AssayInpatient/getYiDaoRuAssayAdvice',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//获取执行病区
export function getAssayInpatientExecuteDeptList(params) {
  return request({
    url: '/medicaladvice/v1/AssayInpatient/getAssayInpatientExecuteDeptList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//检查化验套餐项目是否重复
export function checkHytcRepeatItem(data) {
  return request({
    url: '/medicaladvice/v1/AssayInpatient/checkHytcRepeatItem',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

//门诊医师站_查询_根据检索关键字获取医嘱目录
export function searchDocAdviceCatalogue(params) {
  return request({
    url: '/medicaladvice/v1/CheckTreat/SearchDocAdviceCatalogue',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//电子病历_查询_获取历史申请单
export function getHistoryInpatientRequisitions(params) {
  return request({
    url: '/medicaladvice/v1/CheckTreat/getHistoryInpatientRequisitions',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//门诊医师站_查询_获取医嘱目录明细内容
export function getCatalogueDetailed(params) {
  return request({
    url: '/medicaladvice/v1/CheckTreat/getCatalogueDetailed',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//电子病历_获取导入末次门诊检查申请单数据
export function getLatestOutPatientRequisitionDataImport(params) {
  return request({
    url: '/medicaladvice/v1/CheckTreat/getLatestOutPatientRequisitionDataImport',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//电子病历_新增_新增检查申请单
export function addRequisitionInpatient(params, data) {
  return request({
    url: '/medicaladvice/v1/CheckTreat/addRequisitionInpatient',
    method: 'post',
    params,
    data,
    headers: {
      verifyApp: false
    }
  })
}

//电子病历_删除_删除住院申请单
export function deleteRequisitionInpatient(params) {
  return request({
    url: '/app-emrservice/v1/mdedicalAdvice/deleteRequisitionInpatient',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//门诊医师站_查询_获取申请单数据集合
export function getRequisitionData(params) {
  return request({
    url: '/medicaladvice/v1/CheckTreat/getRequisitionData',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//电子病历_修改_修改检查申请单
export function modifyRequisitionInpatient(params, data) {
  return request({
    url: '/medicaladvice/v1/CheckTreat/modifyRequisitionInpatient',
    method: 'post',
    params,
    data,
    headers: {
      verifyApp: false
    }
  })
}

//电子病历_查询_获取当日住院检查申请单
export function getAllInpatientRequisitionToday(params) {
  return request({
    url: '/medicaladvice/v1/CheckTreat/getAllInpatientRequisitionToday',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//获取住院检查申请单闭环地址
export function getCheckRequisitionBiHuanUrl(params) {
  return request({
    url: '/medicaladvice/v1/CheckTreat/getCheckRequisitionBiHuanUrl',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//根据病历ID获取特医食品医嘱初始化信息
export function getBaseInfoTeYiSPYZ(params) {
  return request({
    url: '/medicaladvice/v1/food/getBaseInfoTeYiSPYZ',
    method: 'POST',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//根据病历ID获取特医食品医嘱
export function getTeYiSPYZByBlidAndYzlb(params) {
  return request({
    url: '/medicaladvice/v1/food/getTeYiSPYZByBlidAndYzlb',
    method: 'POST',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//通用_查询_医嘱单
export function getYiZhuDan(params) {
  return request({
    url: '/medicaladvice/v1/AdmissionlistController/getYiZhuDan',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//修改_停止特医食品医嘱
export function stopTeYiSPYZ(data) {
  return request({
    url: '/medicaladvice/v1/food/stopTeYiSPYZ',
    method: 'POST',
    data,
    headers: {
      verifyApp: false
    }
  })
}

//修改_保存特医食品医嘱（集成新增、修改、删除）
export function saveTeYiSPYZ(data) {
  return request({
    url: '/medicaladvice/v1/food/saveTeYiSPYZ',
    method: 'POST',
    data,
    headers: {
      verifyApp: false
    }
  })
}

//搜索医用食品列表
export function getStoreDrugsInUsingByCodeAndName(params) {
  return request({
    url: '/app-doctorstation/v1/drugs/drugInfo/getStoreDrugsInUsingByCodeAndName',
    method: 'POST'
  })
}
