<!-- 手术项目ICD维护 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="head-search">
        <span class="search-label">创建日期:</span>
        <el-date-picker
          v-model="searchData.date"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
        <span class="search-label">手术代码:</span>
        <el-input v-model="searchData.daiMa" placeholder="请输入手术代码" style="width: 200px" />
        <span class="search-label">手术名称:</span>
        <el-input
          v-model="searchData.mingCheng"
          placeholder="请输入手术名称"
          style="width: 200px"
        />
        <span class="search-label">手术类别:</span>
        <el-select v-model="searchData.leiBie" style="width: 130px" placeholder="全部">
          <el-option
            v-for="item in [{ value: 'test', label: '测试选项' }]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <span class="search-label">ICD代码:</span>
        <el-select v-model="searchData.daimaICD" style="width: 130px" placeholder="全部">
          <el-option
            v-for="item in [{ value: 'test', label: '测试选项' }]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <span class="search-label">状态:</span>
        <el-select v-model="searchData.zhuangTai" style="width: 130px" placeholder="全部">
          <el-option
            v-for="item in [{ value: 'test', label: '测试选项' }]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button class="search-button" type="primary">查询</el-button>
        <el-button type="primary">重置</el-button>
      </div>
      <div class="table-background">
        <div class="table-title">
          <div>
            <span />
            手术项目ICD维护
          </div>
        </div>
        <div class="table-component">
          <table>
            <thead>
              <tr style="text-align: left">
                <th>手术代码</th>
                <th style="width: 300px">手术名称</th>
                <th style="width: 120px">手术类别</th>
                <th>ICD类别</th>
                <th style="width: 200px">创建日期</th>
                <th style="width: 70px">状态</th>
                <th style="width: 70px; text-align: center">操作</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in icdList.slice(0, 10)">
                <tr :key="index" :class="[index % 2 == 1 ? 'tr-two' : 'tr-one']">
                  <td>
                    {{ item.daiMa }}
                  </td>
                  <td>
                    {{ item.mingCheng }}
                  </td>
                  <td>
                    {{ item.leiXingMC }}
                  </td>
                  <td>
                    {{ item.shouShuMC }}
                  </td>
                  <td>
                    {{ item.xiuGaiSJ }}
                  </td>
                  <td>
                    <el-tag :type="item.zhuangTaiBZMC === '启用' ? null : 'danger'">
                      {{ item.zhuangTaiBZMC }}
                    </el-tag>
                  </td>
                  <td style="text-align: center">
                    <div style="display: flex; align-items: center">
                      <el-button type="text">查看</el-button>
                      <el-divider direction="vertical" />
                      <el-button type="text" @click="deleteItem(item)">删除</el-button>
                    </div>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <el-dialog :visible.sync="deleteVisible" width="350px">
      <span slot="title">
        <span style="font-size: 16px">
          <i class="el-icon-warning"></i>
          删除提示
        </span>
      </span>
      <div class="delete-component">
        确认删除
        <a>【{{ selectIcd?.mingCheng }}】</a>
        吗?
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="deleteVisible = false">保 存</el-button>
        <el-button @click="deleteVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getShouShuLieBiao } from '@/api/system-maintenance'
export default {
  name: 'SystemOperationProjectIcd',
  data() {
    return {
      searchData: {
        date: ['2023-01-26T16:00:00.000Z', '2024-07-02T16:00:00.000Z'],
        daiMa: '',
        mingCheng: '',
        leiBie: null,
        daimaICD: null,
        zhuangTai: null
      },
      icdList: [],
      selectIcd: null,
      deleteVisible: false
    }
  },
  mounted() {
    this.initSystemIcd()
  },
  methods: {
    initSystemIcd() {
      getShouShuLieBiao({}).then((res) => {
        if (res.hasError === 0) {
          this.icdList = res.data
          console.log('手术项目icd res:', res)
        }
      })
    },
    deleteItem(item) {
      this.selectIcd = item
      this.deleteVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .head-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      .search-label {
        margin: 0 10px;
        font-weight: 600;
      }
      button.search-button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover).search-button {
        background: #a66dd4;
        border-color: #a66dd4;
      }
      :deep(.el-button--primary:focus).search-button {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        font-weight: 600;
        width: 670px;
        span {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        margin-top: 10px;
        width: 1080px;
        table {
          margin-top: 10px;
          width: 100%;
        }
        th,
        td {
          border: 1px solid #ddd;
          border-collapse: collapse; /* 移除表格内边框间的间隙 */
          height: 40px;
          padding: 5px 8px;
        }
        .tr-one {
          background-color: #f6f6f6;
        }
        th,
        .tr-two {
          background-color: #eff3fb;
        }
        .el-tag {
          border-radius: 2px;
        }
      }
    }
  }
  :deep(.el-dialog__footer) {
    border-top: none;
  }

  .el-icon-warning {
    font-size: 20px;
    color: #ed6a0c;
  }
  .delete-component {
    padding-left: 30px;
    color: #96999e;
    a {
      color: #356ac5;
    }
  }
}
</style>
