<script setup>
import { computed, nextTick, onMounted, ref } from 'vue'
import { isEmpty } from 'lodash'

const props = defineProps({
  /**
   * @type {import('vue').PropType<TableConfig>}
   */
  config: {
    type: Object,
    default: () => {}
  },
  /**
   * @type {import('vue').PropType<Array<TableHeaderItem>>}
   */
  tableHeader: {
    type: Array,
    default: () => []
  },
  rowClassName: {
    type: Function,
    default: () => {}
  },
  summaryMethod: {
    type: Function,
    default: () => {}
  },
  spanMethod: {
    type: Function,
    default: () => {}
  },
  rowStyle: {
    type: Function,
    default: () => {}
  },
  cellClassName: {
    type: Function,
    default: () => {}
  },
  cellStyle: {
    type: Function,
    default: () => {}
  },
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Array,
    default: () => []
  },
  showPage: {
    type: Boolean,
    default: false
  },
  pageNum: {
    type: Number,
    default: 1
  },
  /** * 分页大小 */
  pageSize: {
    type: Number,
    default: 10
  },
  total: {
    type: Number,
    default: 0
  },
  pagerCount: {
    type: Number,
    default: 7
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50]
  },
  paginationSmall: {
    type: Boolean,
    default: false
  },
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  disabledArr: {
    type: Array,
    default: () => []
  },
  search: {
    type: Function,
    default: () => {}
  }
})

const emits = defineEmits([
  'expand-change',
  'link-click',
  'row-click',
  'row-dbclick',
  'row-dblclick',
  'selection-change',
  'sort-change',
  'underline-click',
  'update:pageNum',
  'update:pageSize'
])

const closeEmitChange = ref(false)

/**
 * @type {import('vue').Ref<ElTable>}
 */
const normalTable = ref({})

const needStripe = computed(() => {
  if (props.config.hasOwnProperty('isStripe')) {
    return props.config.isStripe
  }
  return true
})

const isShowTip = (item) => {
  if (item.hasOwnProperty('tooltip')) return item.tooltip
  return true
}
const hiddenRow = async () => {
  await nextTick()
  normalTable.value.$el.querySelectorAll('tr[style*="display: none;"]').forEach((tr) => {
    tr.style.display = ''
    tr.style.visibility = 'collapse'
  })
}
const showRow = async () => {
  await nextTick()
  normalTable.value.$el.querySelectorAll('tr[style*="visibility: collapse;"]').forEach((tr) => {
    tr.style.display = ''
    tr.style.visibility = ''
  })
}
const expandChange = (row, expandedRows) => {
  emits('expand-change', row, expandedRows)
  if (!expandedRows) {
    hiddenRow()
  } else {
    showRow()
  }
}

const toggleSelection = (rows, flag) => {
  if (!normalTable.value) {
    return
  }
  if (!isEmpty(rows)) {
    rows.forEach((row) => {
      normalTable.value.toggleRowSelection(row, flag)
    })
  } else {
    normalTable.value.clearSelection()
  }
}

const selectAll = (selection) => {
  if (props.config?.treeProps) {
    let flag = false
    selection.forEach((item) => {
      if (item.children) {
        flag = true
        toggleSelection(item.children, true)
      }
    })
    if (!flag) {
      toggleSelection()
    }
  }
}

const linkClick = (row, title, prop) => {
  emits('link-click', row, title, prop)
}

const selectData = ref({})

const select = (rows, row) => {
  if (props.config?.isSelectMore) {
    emits('selection-change', rows)
    return
  }
  if (rows.length > 1) {
    const newRows = rows.filter((it, index) => {
      if (index === rows.length - 1) {
        normalTable.value.toggleRowSelection(it, true)
        return true
      } else {
        normalTable.value.toggleRowSelection(it, false)
        return false
      }
    })
    selectData.value = newRows[0]
  } else {
    selectData.value = rows[0]

    if (!closeEmitChange.value) {
      emits('selection-change', rows)
    }
    closeEmitChange.value = false
  }
}

const selectable = (row, index) => {
  if (!props.config?.rowKey || !props.disabledArr || props.disabledArr.length === 0) {
    return true
  }
  return !props.disabledArr.some((sI) => sI[props.config?.rowKey] === row[props.config?.rowKey])
}

const rowClick = (row) => {
  if (!props.config?.isRowClick || !selectable(row)) {
    return
  }
  console.log('rowClick', row)
  // 开启了一次change，关闭一次 emit(selection-change)
  closeEmitChange.value = true
  if (props.config?.isSelectMore) {
    normalTable.value.toggleRowSelection(row)
  } else {
    normalTable.value.toggleRowSelection(row, true)
  }
  emits('row-click', row)
}

const rowDblclick = (row) => {
  emits('row-dblclick', row)
  emits('row-dbclick', row)
}

const sortChange = (value) => {
  emits('sort-change', value)
}

const handleNum = (num) => {
  // (1)保留两位小数
  const dot = String(num).indexOf('.')
  if (dot !== -1) {
    const dotCnt = String(num).substring(dot + 1, num.length)
    if (dotCnt.length > 2) num = num.toFixed(2)
  }
  // (2)四舍五入取整数
  // num = Math.round(num)
  // 三位隔开
  return String(num).replace(/\d+/, function (n) {
    return n.replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
  })
}

const handleSizeChange = (pageSize) => {
  emits('update:pageSize', pageSize)
  emits('update:pageNum', 1)
  props.search && props.search()
}

const handleNumChange = (pageNum) => {
  emits('update:pageNum', pageNum)
  props.search && props.search()
}

const _rowClassName = ({ row, rowIndex }) => {
  if (typeof props.rowClassName === 'function') {
    return props.rowClassName({ row, rowIndex }) || ''
  }
  if (typeof props.rowClassName === 'string') {
    return props.rowClassName
  }
  return ''
}

const cellTextClassName = (row) => {
  return ''
}

const underlineClick = (prop, row) => {
  emits('underline-click', prop, row)
}

const indexMethod = (index) => {
  return (props.pageNum - 1) * props.pageSize + index + 1
}

const clearSelection = () => {
  normalTable.value.clearSelection()
}

onMounted(() => {
  hiddenRow()
})

defineExpose({
  clearSelection
})
</script>

<template>
  <div class="normal-table">
    <el-table
      ref="normalTable"
      v-loading="loading"
      :cell-style="cellStyle"
      :data="data"
      :default-expand-all="!!config.treeProps"
      :fit="config.fit"
      :height="`calc(${config.tableHeight ? config.tableHeight + 'px' : '100%'} - ${
        showPage ? '38px' : '0px'
      });`"
      :max-height="config.tableHeight"
      :row-class-name="_rowClassName"
      :row-key="config.rowKey"
      :row-style="rowStyle"
      :show-summary="config.isShowSummary"
      :span-method="spanMethod"
      :stripe="needStripe"
      :style="`width: auto;box-sizing: content-box;height:calc(${
        config.tableHeight
          ? `${
              typeof config.tableHeight === 'number'
                ? config.tableHeight + 'px'
                : config.tableHeight
            }`
          : '100%'
      } - ${showPage ? '42px' : '0px'});
      `"
      :summary-method="summaryMethod"
      :tree-props="config.treeProps"
      class="normal-el-table-class"
      highlight-current-row
      style="width: 100%"
      v-bind="$attrs"
      @row-click="rowClick"
      @row-dblclick="rowDblclick"
      @sort-change="sortChange"
      @selection-change="select"
      @select-all="selectAll"
      @row-contextmenu="(row, column, event) => emits('row-contextmenu', row, column, event)"
      @expand-change="expandChange"
    >
      <el-table-column
        v-if="config.isSelection"
        :label-class-name="!!config.isSelectMore ? '' : 'disabledCheck'"
        :selectable="selectable"
        align="center"
        type="selection"
        width="54"
      ></el-table-column>
      <el-table-column v-if="!!config.treeProps" width="30"></el-table-column>
      <el-table-column
        v-if="config.isIndex"
        :index="indexMethod"
        fixed="left"
        label="序号"
        type="index"
        width="55"
      />
      <el-table-column
        v-for="(item, index) in tableHeader"
        :key="index"
        :align="item.align"
        :class-name="item.className"
        :fixed="item.fixed"
        :formatter="item.formatter"
        :label="item.title"
        :label-class-name="item.labelClassName"
        :min-width="item.colMinWidth"
        :prop="item.prop"
        :resizeable="item.resizeable"
        :show-overflow-tooltip="isShowTip(item)"
        :sort-orders="['ascending', 'descending', null]"
        :sortable="item.isSort ? item.sortType || 'custom' : false"
        :width="item.colWidth"
      >
        <!-- 一级 -->
        <template v-if="!item.children" #default="scope">
          <template v-if="item.type === 'customIndex'">
            <span>{{ scope.row[item.prop] }}</span>
          </template>
          <template v-else-if="item.type === 'link'">
            <el-link
              class="link-item"
              type="primary"
              @click.stop="linkClick(scope.row, item.title, item.prop)"
            >
              {{ scope.row[item.prop] }}
            </el-link>
          </template>
          <template v-else-if="item.type === 'underline-text'">
            <div
              :style="{ color: item.colorRule && item.colorRule(scope.row[item.prop], scope.row) }"
              class="underline-text"
              @click.stop="underlineClick(item.prop, scope.row)"
            >
              {{ scope.row[item.prop] }}
            </div>
          </template>
          <template v-else-if="item.type === 'button'">
            <el-button
              size="mini"
              type="text"
              @click.stop="linkClick(scope.row, item.title, item.prop)"
            >
              {{ item.btnText }}
            </el-button>
          </template>
          <template v-else-if="item.type === 'tag'">
            <el-tag :type="item.tagTypeRule && item.tagTypeRule(scope.row[item.prop])">
              {{ scope.row[item.prop] }}
            </el-tag>
          </template>
          <template v-else-if="item.type === 'price'">
            <span>{{ handleNum(Number(scope.row[item.prop])) }}</span>
          </template>
          <template v-else-if="item.type === 'priceLink'">
            <el-link type="primary" @click.stop="linkClick(scope.row, item.title, item.prop)">
              {{ handleNum(Number(scope.row[item.prop])) }}
            </el-link>
          </template>
          <template v-else-if="item.type === 'medical'">
            <div :class="cellTextClassName(scope.row)">{{ scope.row[item.prop] }}</div>
          </template>
          <template v-else-if="item.type === 'process'">
            <span @click.stop="linkClick(scope.row, item.title, item.prop)">
              <!-- 当0.07*100结果是7.000000000000001 -->
              <el-progress
                :percentage="
                  item.isRide
                    ? ((Number(scope.row[item.prop]) * 10) / 10) * 100
                    : (Number(scope.row[item.prop]) * 10) / 10
                "
                :stroke-width="13"
                :text-inside="true"
              ></el-progress>
            </span>
          </template>
          <template v-else-if="item.type === 'slot'">
            <!-- 插槽 -->
            <slot :index="scope.$index" :name="item.prop" :row="scope.row" />
          </template>
          <template v-else-if="item.type === 'template'">
            <div v-html="scope.row[item.prop]" />
          </template>
          <template v-else>
            <span
              :style="{ color: item.colorRule && item.colorRule(scope.row[item.prop], scope.row) }"
            >
              {{
                item.formatter
                  ? item.formatter(scope.row, scope.column, scope.row[item.prop], scope.$index)
                  : scope.row[item.prop]
              }}
            </span>
          </template>
        </template>

        <!-- 二级 子项 -->
        <!--        <template v-for="(v, i) in item.children">-->
        <!--          <el-table-column-->
        <!--            :key="i"-->
        <!--            :align="v.align"-->
        <!--            :label="v.title"-->
        <!--            :min-width="v.colMinWidth"-->
        <!--            :prop="v.prop"-->
        <!--            :sortable="v.isSort ? 'custom' : false"-->
        <!--            :width="v.colWidth"-->
        <!--            show-overflow-tooltip-->
        <!--          >-->
        <!--            <template #default="scope">-->
        <!--              <template v-if="v.type === 'link'">-->
        <!--                <el-link-->
        <!--                  class="link-item"-->
        <!--                  type="primary"-->
        <!--                  @click.stop="linkClick(scope.row, v.title, v.prop)"-->
        <!--                >-->
        <!--                  {{ scope.row[v.prop] }}-->
        <!--                </el-link>-->
        <!--              </template>-->
        <!--              <template v-else-if="v.type === 'underline-text'">-->
        <!--                <div class="underline-text" @click.stop="underlineClick(v.prop, scope.row)">-->
        <!--                  {{ scope.row[v.prop] }}-->
        <!--                </div>-->
        <!--              </template>-->
        <!--              <template v-else-if="v.type === 'button'">-->
        <!--                <el-button-->
        <!--                  size="mini"-->
        <!--                  type="text"-->
        <!--                  @click.stop="linkClick(scope.row, v.title, v.prop)"-->
        <!--                >-->
        <!--                  {{ v.btnText }}-->
        <!--                </el-button>-->
        <!--              </template>-->
        <!--              <template v-else-if="v.type === 'tag'">-->
        <!--                <el-tag :type="scope.row[v.prop] ? 'success' : 'danger'">-->
        <!--                  {{ scope.row[v.prop] ? '是' : '否' }}-->
        <!--                </el-tag>-->
        <!--              </template>-->
        <!--              <template v-else-if="v.type === 'price'">-->
        <!--                <span>{{ handleNum(Number(scope.row[v.prop])) }}</span>-->
        <!--              </template>-->
        <!--              <template v-else-if="v.type === 'priceLink'">-->
        <!--                <el-link type="primary" @click.stop="linkClick(scope.row, v.title, v.prop)">-->
        <!--                  {{ handleNum(Number(scope.row[v.prop])) }}-->
        <!--                </el-link>-->
        <!--              </template>-->
        <!--              <template v-else-if="v.type === 'process'">-->
        <!--                <span @click.stop="linkClick(scope.row, v.title, v.prop)">-->
        <!--                  <el-progress-->
        <!--                    :percentage="-->
        <!--                      v.isRide-->
        <!--                        ? ((Number(scope.row[v.prop]) * 10) / 10) * 100-->
        <!--                        : (Number(scope.row[v.prop]) * 10) / 10-->
        <!--                    "-->
        <!--                    :stroke-width="13"-->
        <!--                    :text-inside="true"-->
        <!--                  ></el-progress>-->
        <!--                </span>-->
        <!--              </template>-->
        <!--              <template v-else-if="v.type === 'template'">-->
        <!--                <span v-html="scope.row[v.prop]"></span>-->
        <!--              </template>-->
        <!--              <template v-else>-->
        <!--                <span>{{ scope.row[v.prop] }}</span>-->
        <!--              </template>-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <!--        </template>-->
      </el-table-column>
    </el-table>
    <el-pagination
      v-show="showPage && data.length > 0"
      :current-page="pageNum"
      :layout="paginationLayout"
      :page-num="pageNum"
      :page-size="pageSize"
      :page-sizes="pageSizes"
      :pager-count="pagerCount"
      :small="paginationSmall"
      :total="total"
      background
      class="table-pager"
      @size-change="handleSizeChange"
      @current-change="handleNumChange"
    />
  </div>
</template>

<style lang="scss" scoped>
::v-deep .el-table .disabledCheck .cell .el-checkbox__inner {
  display: none !important;
}

::v-deep .el-table .disabledCheck .cell::before {
  line-height: 36px;
  text-align: center;
  content: '选择';
}

::v-deep .el-table thead .el-table__cell {
  background-color: #eaf0f9;
  height: 40px;
  max-height: 40px;
  padding: 1px 0;
}

::v-deep .el-table tbody td.el-table__cell {
  height: 40px;
  max-height: 40px;
  padding: 7px 0;
}

::v-deep a.link-item {
  text-decoration: underline;
}

.normal-table {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0;
  background-color: #eff3fb;
  border-radius: 4px;
  flex: 1;
  overflow: hidden;

  .el-table::v-deep {
    display: flex;
    flex: 1;
    flex-direction: column;
    height: unset !important;
    min-height: unset !important;
    background-color: #f6f6f6;

    .el-table__body-wrapper,
    .el-table__fixed,
    .el-table__fixed-left,
    .el-table__fixed-right {
      flex: 1;
      overflow-y: auto;

      .el-table__body {
        .el-table__row {
          .el-table__cell {
            background-color: transparent;
            //border: 0;
          }
        }

        .el-table__row:not(.current-row):is(.el-table__row--striped) {
          background-color: #eaf0f9;
        }

        .el-table__row:not(.current-row):not(.el-table__row--striped) {
          background-color: #f6f6f6;
        }

        .el-table__row:not(.current-row):is(.hover-row) {
          background-color: #fefee0;
        }

        .el-table__row:not(.current-row):not(.hover-row) {
          &:hover {
            background-color: #fefee0;
            transition: all 0.1s;
          }
        }

        .el-table__row:is(.current-row) {
          background-color: #5b86cf;
          border-color: transparent;

          .el-table__cell,
          .el-link--primary,
          .el-table__expand-icon {
            color: var(--font-color-inner);
            background-color: transparent;

            ::after {
              border-color: var(--font-color-inner);
            }
          }
        }

        .el-table__row:last-child {
          .el-table__cell {
            //border-bottom: 0;
          }
        }
      }

      &::before {
        height: 0;
      }
    }
  }
}

.underline-text {
  text-decoration: underline;
  cursor: pointer;
}
</style>
