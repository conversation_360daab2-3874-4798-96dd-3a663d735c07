<template>
  <div>
    <el-button type="primary" style="margin: 10px" @click="handleClick">跳转</el-button>
    <el-table :data="tableData" border stripe size="mini">
      <el-table-column prop="xingMing" label="病人名称"></el-table-column>
      <el-table-column prop="bingQuMC" label="出院时间"></el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'UnfilledDisease',
  data() {
    return {
      tableData: []
    }
  },
  methods: {
    handleClick() {}
  }
}
</script>

<style lang="scss" scoped></style>
