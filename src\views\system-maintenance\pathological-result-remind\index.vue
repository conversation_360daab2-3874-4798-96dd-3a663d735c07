<template>
  <div class="container">
    <div class="content">
      <div class="content-header">
        <div class="title">出院病人病理结果提醒维护</div>
        <!-- <div class="button"><el-button type="primary">新增</el-button></div> -->
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData" style="width: 1316px">
          <el-table-column
            prop="baoGaoGhenQingSJ"
            width="120"
            label="报告申请日期"
          ></el-table-column>
          <el-table-column prop="baoGaoSJ" width="160" label="报告时间"></el-table-column>
          <el-table-column prop="bingAnHao" width="120" label="病案号"></el-table-column>
          <el-table-column prop="bingRenXingMing" width="110" label="病人姓名"></el-table-column>
          <el-table-column prop="chuYuanSJ" width="160" label="出院日期"></el-table-column>
          <el-table-column prop="chuYuanZhuanKe" width="160" label="出院专科"></el-table-column>
          <el-table-column prop="bingQu" width="110" label="病区"></el-table-column>
          <el-table-column prop="chuangHao" width="110" label="床位"></el-table-column>
          <el-table-column prop="zhiLiaoZu" width="160" label="治疗组"></el-table-column>
          <el-table-column prop="caoZuo" width="104" label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getInPatientCheckResultMes } from '@/api/system-maintenance'
export default {
  data() {
    return {
      query: '',
      zhuanKeSJ: '',
      xingMing: '',
      bingAnHao: '',
      tableData: [
        {
          baoGaoGhenQingSJ: '2022-03-12',
          baoGaoSJ: '2022-03-12 08:14:57',
          bingAnHao: '**********',
          bingRenXingMing: '叶莲华',
          chuYuanSJ: '2022-03-15 08:14:57',
          chuYuanZhuanKe: '内科',
          bingQu: '242',
          chuangHao: '303',
          zhiLiaoZu: ''
        }
      ]
    }
  },
  async mounted() {
    await getInPatientCheckResultMes({
      yongHuID: this.$store.state.user.yongHuID
    })
  },
  methods: {
    handleClick(row) {
      console.log(row)
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 700px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 1476px;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}
</style>
