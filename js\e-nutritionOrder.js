let url = window.location.href.split("?") || []
let text = url[1].split("&")
let param = {}
for (let i of text) {
  let fd = i.split("=")
  param[fd[0]] = fd[1]
}
/******************公共参数******************/
var listypData = [] // 已经选中药品（仅用于接口）原本
var listypDataNew = [] // 已经选中药品（仅用于接口）新
var listypAllDataNew = [] // 已经选中药品（完整数据）新
var yydData = [] // 营养医嘱单数据 （用于保存接口的yyyzdData）
// var yydData = {} // 营养医嘱单数据 （用于保存接口的yyyzdData）
var wzypData =[]// 营养医嘱单选择的药品数据（用于保存接口的yyyzd_ypData）
var xgjcjgDaya = [] // 相关检查结果（用于保存接口的hyjgData）
var yp_list = [] // 已选药品数据

var showBodyData = [] // 当前医嘱单数据（包含计量）
var yz_key=null//当前序号
var mbindex=0 //
var dldr_lists=[] //导入队列
var yyyznr_lists=[] // （用于拼接完整药品数据）
var zxwb = false
//统一页面启动(新营养医嘱)
$(document).ready(() => {
  // 3、页面初始化函数
  WRT_e.api.nutriOrderList.getInit({
    params: {
      al_blid: param["as_blid"],
    },
    success(data) {
      WRT_config.nOList_Init = JSON.parse(data.d) // 页面初始化基础信息,页面初始化上个页面已经存放在
      // 7、获取单条医嘱单的记录
      WRT_e.api.nutriOrderList.getNurtionOrder({
        params: {
          al_yzdid: param["as_id"],
          // al_blid: param["as_blid"],
        },
        success(data) {
          // WRT_config.nutriOrderListOne = data.d
          if(param["as_id"] == 0){
            // 新增
            WRT_config.nutriOrderListOne = {}
          } else {
            // 详情
            WRT_config.nutriOrderListOne = JSON.parse(data.d) // 指定医嘱单
          }
          // 初始化
          app.init()
        }
      })
    }
  })
  
})
var app = {
  init: function () {
    // 基础信息
    var NutriBase = new Base_View();
    NutriBase.$el = $("#nutriOrderShowBaseInfo");
    NutriBase.init({
      data: {
        baseInit: WRT_config.nOList_Init, // 基础信息
        nolOne: WRT_config.nutriOrderListOne, // 患者营养医嘱单的记录
      } 
      // Bdata: WRT_config.nutriOrderListBase
    }).render();
    // 第二部分
    // 8、获取营养医嘱药品列表(list上所有药品) 营养医嘱药品列表getNurtionDrugList
    WRT_e.api.nutriOrderList.getNurtionDrugList({
      params: {
        as_yfdm:  WRT_config.nOList_Init.YFDM, // 药房代码
        // al_yzdid: param["as_id"]
      },
      success(data) {
        WRT_config.nurtionDrugList = JSON.parse(data.d) // 营养医嘱药品列表
        try {
          if(param["as_id"] != 0){
            // 9、获取营养医嘱药品列表,获取营养医嘱药品列表（传al_yzdid）
            WRT_e.api.nutriOrderList.getNurtionOrderDrugs({
              params: {
                al_yzdid: param["as_id"]
              },
              success(data) {
                WRT_config.nurtionDrugListBrYyJL = JSON.parse(data.d)
                // WRT_config.nurtionDrugListBrYyJL
              }
            })
            // 11、获取病人相关检验结果
            WRT_e.api.nutriOrderList.getHyjg({
              params: {
                al_yzdid: param["as_id"],
                al_blid: param["as_blid"],
                as_zyh: WRT_config.nOList_Init.ZYH,
              },
              success(data) {
                WRT_config.noxgjcHyjg = JSON.parse(data.d)
              }
            })
          } else {
            // 获取病人相关检验结果
            WRT_e.api.nutriOrderList.getHyjg({
              params: {
                al_yzdid: param["as_id"],
                al_blid: param["as_blid"],
                as_zyh: WRT_config.nOList_Init.ZYH,
              },
              success(data) {
                WRT_config.noxgjcHyjg = JSON.parse(data.d)
              }
            })
          }
        } finally {

          var NutriLTitle = new listTitle_View();
          NutriLTitle.$el = $("#nutriOrderTableAll");
          NutriLTitle.init({
            data: {
              baseInit: WRT_config.nOList_Init, // 基础信息
              nolOne: WRT_config.nutriOrderListOne, // 获取单条医嘱单的记录(获取身高体重等基础信息)
              listData: WRT_config.nurtionDrugList, // 列表
              listJLData: WRT_config.nurtionDrugListBrYyJL || [], // 对应病人用药剂量（对应详情表格初始参数 ：一次用量）
              xgjcHyjg: WRT_config.noxgjcHyjg // 相关检查

            }
          }).render()
        }
        
      }
    })
  }
}
/********** 方法回调 **********/

//药品审批
function SetSpjg(data) {
  let index = mbindex-1
  if(index<0){
      index=0
  }
  let yemp='ypsp'
  if(WRT_config.ypfjxx.xzfw_bbzt==1){
      yemp='if_ypsp'
  }
  if (data) {
    if (data.sfzf == '9') {
      $(`#${yemp}`).iziModal('destroy')
      removeYp(dldr_lists[index].ZH,dldr_lists[index].MBMC)
      return
    }
    let type = setFylx(WRT_config.nOList_Init.JSLX, WRT_config.nOList_Init.zykt, data.sfzf, dldr_lists[index].KZJB, data.rowIndex)
    dldr_lists[index].ZF = type
    dldr_lists[index].SFZF = data.sfzf
    dldr_lists[index].splb = data.splb
    dldr_lists[index].xdfw = data.xdfw
    // dldr_lists[index].spsl = data.spsl||""
    dldr_lists[index].sqsl = data.spsl|| data.sqsl || ""
    exportMBList()
    $(`#${yemp}`).iziModal('destroy')
  } else {
    removeYp(dldr_lists[index].ZH,dldr_lists[index].MBMC)
    $(`#${yemp}`).iziModal('destroy')
    return
  }
}

/********************公用方法********************/

//加载动画
function openimg(){
  $("#nutriOrderTableAll").html('<div class="zgc_model"><img src="./images/loading.gif" style="width: 360px;"></div>')
}
// 计算（基础能量消耗bee）
function Beecalumet(xb,sg,tz,nl) {
  // if( ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``})
  if(WRT_config.nutriOrderListOne.ZTBZ == '2' || WRT_config.nutriOrderListOne.ZTBZ == '0'){
    WRT_e.ui.hint({
      type: 'warning',
      msg:WRT_config.nutriOrderListOne.ZTBZ == '2'?'该医嘱状态为已提交!':WRT_config.nutriOrderListOne.ZTBZ == '0'?'该医嘱状态为停止，不可操作！':''
    })
  } else{
    if(param["as_id"] == 0){
      xb = WRT_config.nOList_Init.BRXB
      nl = WRT_config.nOList_Init.NL
      sg = $("input[name='height']").val()
      tz = $("input[name='weight']").val()
      if(sg=='' || tz==''){
        WRT_e.ui.hint({
          type: 'warning',
          msg:'请输入身高体重!'
        })
      } 
      if(!isNaN(sg) && !isNaN(tz)){
        // 5、计算（基础能量消耗bee）
        WRT_e.api.nutriOrderList.getBee({
          params: {
            as_xb: xb,
            ad_sg: sg,
            ad_tz: tz,
            ad_nl: nl
          },
          success(data) {
            calXH_callback(data)
          }
        })
      } else {
        WRT_e.ui.hint({
          type: 'error',
          msg: '身高体重不是数字!'
        })
      }
    } else {
      WRT_e.api.nutriOrderList.getBee({
        params: {
          as_xb: xb,
          ad_sg: sg,
          ad_tz: tz,
          ad_nl: nl
        },
        success(data) {
          calXH_callback(data)
        }
      })
    }
  }
}
function calXH_callback(res) {
  if (res == null) {
      return;
  }
  $("input[name='bee']").val(res.d)
}

// 计算（BMI）
function BMIcalumet(sg,tz) {
  if(WRT_config.nutriOrderListOne.ZTBZ == '2' || WRT_config.nutriOrderListOne.ZTBZ == '0'){

  } else{
    // 新增
    if(param["as_id"] == 0){
      // // 获取身高或者体重
      // WRT_e.api.nutriOrderList.getSGTZ({
      //   params: {
      //     al_blid: param["as_blid"],
      //     as_type: 'sg'
      //   },
      //   success(data) {
      //     sg = data.d
      //     WRT_e.api.nutriOrderList.getSGTZ({
      //       params: {
      //         al_blid: param["as_blid"],
      //         as_type: 'tz'
      //       },
      //       success(data) {
      //         tz = data.d
      //         WRT_e.api.nutriOrderList.getBMI({
      //           params: {
      //             ad_sg: sg,
      //             ad_tz: tz
      //           },
      //           success(data) {
      //             return $("input[name='bmi']").val(data.d)
      //           }
      //         })
      //       }
      //     })
      //   }
      // })
      sg = $("input[name='height']").val()
      tz = $("input[name='weight']").val()
      if(sg=='' || tz==''){
        WRT_e.ui.hint({
          type: 'warning',
          msg:'请输入身高体重!'
        })
      } if(!isNaN(sg) && !isNaN(tz)){
        // 4、计算（BMI）
        WRT_e.api.nutriOrderList.getBMI({
          params: {
            ad_sg: sg,
            ad_tz: tz
          },
          success(data) {
            calBMI_callback(data)
          }
        })
      } else {
        WRT_e.ui.hint({
          type: 'error',
          msg: '身高体重不是数字!'
        })
      }
    } else {
      WRT_e.api.nutriOrderList.getBMI({
        params: {
          ad_sg: sg,
          ad_tz: tz
        },
        success(data) {
          calBMI_callback(data)
          // $("input[name='bmi']").val(data.d)
        }
      })
    }
  }
}
function calBMI_callback(res) {
  if (res == null) {
      return;
  }
  $("input[name='bmi']").val(res.d)
}

// 总液体变化计算
function calLiquid(allsj){
  let obj = JSON.parse(allsj.replace(/-/g, `,` ))
  let indexNow = obj.indexSy // 索引
  // model.find('input[name=fjbz]').val()
  let valNow = Number($(`#table_nrInput${indexNow}`).val()) // 当前输入数据
  mbindex=0
  listypDataNew=[]
  listypData=[]
  // let count = 0
  // let i = -1
  // 新增药品完整数据
  // listypAllDataNew
  if(valNow == 0){
    showBodyData.map((item,index)=>{
      if(item.YPMC == obj.MC){
        showBodyData[index].DEL = 1
        showBodyData.splice(index,1)
      }
      // listypData.push({
      //   ypid: item.YPID,
      //   yl: item.YCYL,
      // })
    })
    listypAllDataNew.map((item,index)=>{
      if(item.YPMC == obj.MC){
        listypAllDataNew[index].DEL = 1
        listypAllDataNew.splice(index,1)
      }
      // listypDataNew.push({
      //   ypid: item.YPID,
      //   yl: item.YCYL
      // })
    })
    dldr_lists = showBodyData.concat(listypAllDataNew)
  
  } else {
    // dldr_lists = showBodyData.concat(listypAllDataNew)
    let c = 0
    let i = -1
    showBodyData.map((item,index)=>{
      item.chengeVal = 0
      if(item.YPMC==obj.MC){
        c++
        i = index
      }
      if(index == showBodyData.length-1){
        if(c==1) {
          showBodyData[i].YCYL = valNow
          showBodyData[i].chengeVal = 1
        }
      }
      // listypData.push({
      //   ypid: item.YPID,
      //   yl: item.YCYL,
      // })
    })
    if(c==0) {
      if(listypAllDataNew.length==0){
        listypAllDataNew.push({
          BZL: obj.BZL,
          JL: obj.JL, 
          JLDW: obj.JLDW, 
          SQSL: obj.SQSL || "", 
          XDFW: obj.XDFW || null, 
          XSP: obj.XSP || "", 
          YCYL: valNow, 
          YPID: obj.YPID, 
          YPMC: obj.MC, 
          YZDID: param["as_id"], 
          chengeVal: 0 // 值是否变化
        })
        // listypDataNew.push({
        //   ypid: obj.YPID,
        //   yl: valNow
        // })
        // let chooseYpList = (WRT_config.nurtionDrugListBrYyJL).concat(listypAllDataNew)
        // dldr_lists = (WRT_config.nurtionDrugListBrYyJL).concat(listypAllDataNew)
        dldr_lists = showBodyData.concat(listypAllDataNew)
      } else if(listypAllDataNew.length>0) {
        let count = 0
        let isy = -1
        listypAllDataNew.map((e,index)=>{
          if(e.YPMC == obj.MC){
            count++
            isy = index
          }
          // listypDataNew.push({
          //   ypid: e.YPID,
          //   yl: e.YCYL
          // })
          if(index == listypAllDataNew.length-1){
            if( count== 1) {
              listypAllDataNew[isy].YCYL = valNow
              listypAllDataNew[isy].chengeVal = 1
            } else {
              listypAllDataNew.push({
                BZL: obj.BZL,
                JL: obj.JL, 
                JLDW: obj.JLDW, 
                SQSL: obj.SQSL || "", 
                XDFW: obj.XDFW || null, 
                XSP: obj.XSP || "", 
                YCYL: valNow, 
                YPID: obj.YPID, 
                YPMC: obj.MC, 
                YZDID: param["as_id"], 
                chengeVal: 0 // 值是否变化
              })
            }
            dldr_lists = showBodyData.concat(listypAllDataNew)
            // dldr_lists = (WRT_config.nurtionDrugListBrYyJL).concat(listypAllDataNew)
          }
        })
      }
    }
  }
  mbindex = 0
  for(let i = 0;i<dldr_lists.length;i++){
    mbindex++
    SetYp_callback_new(dldr_lists[i],i+1)
  }
  showBodyData.map(e=>{
    listypData.push({
      ypid: e.YPID,
      yl: e.YCYL,

    })
  })
  listypAllDataNew.map(e=>{
    listypDataNew.push({
      ypid: e.YPID,
      yl: e.YCYL,
    })
  })
  // 仅用于接口
  let arrNow = listypDataNew.concat(listypData)
  // 10、获取所选药品的营养元素
  WRT_e.api.nutriOrderList.getDrugsNurtion({
    params: {
      ypData:arrNow
    },
    success(data) {
      WRT_config.nDrugs = JSON.parse(data.d)
      
      if(WRT_config.nDrugs.BEE!=null){
        $("input[name='bee']").val(WRT_config.nDrugs.BEE)// 基础能量消耗
      } else {
        // Beecalumet(WRT_config.nutriOrderListOne.BRXB,WRT_config.nutriOrderListOne.SG,WRT_config.nutriOrderListOne.TZ,Number(WRT_config.nOList_Init.NL))
      }
      if(WRT_config.nDrugs.FDBNL!=null){
        $("input[name='fdbnl']").val(WRT_config.nDrugs.FDBNL)// 非蛋白能量
      }
      if(WRT_config.nDrugs.ZDL!=null){
        $("input[name='zdl']").val(WRT_config.nDrugs.ZDL)// 总氮量
      }
      if(WRT_config.nDrugs.ZYTL!=null){
        $("input[name='zytl']").val(WRT_config.nDrugs.ZYTL)// 总液体量
      }
      if(WRT_config.nDrugs.T_YDS!=null){
        $("input[name='t_yds']").val(WRT_config.nDrugs.T_YDS)// 糖:胰岛素
      }
      if(WRT_config.nDrugs.NL_D!=null){
        $("input[name='nl_d']").val(WRT_config.nDrugs.NL_D)// 能量:氮
      }
      if(WRT_config.nDrugs.T_ZF!=null){
        $("input[name='t_zf']").val(WRT_config.nDrugs.T_ZF)// 糖:脂
      }
      if(WRT_config.nDrugs.NRS!=null){
        $("input[name='nrs']").val(WRT_config.nDrugs.NRS)// NRS2002
      }
      if(WRT_config.nDrugs.BMI!=null){
        $("input[name='bmi']").val(WRT_config.nDrugs.BMI)// BMI
      } else {
        // BMIcalumet(WRT_config.nutriOrderListOne.SG,WRT_config.nutriOrderListOne.TZ)
      }
      if(WRT_config.nDrugs.STY!=null){
      $("input[name='sty']").val(WRT_config.nDrugs.STY)// 渗透压
      }
    }
  })
}

// //设置费用类型
// function setFylx(jslx, zykt, sfzf, kzjb) {
//   let type = 0
//   if (zykt == "0")
//       type = 1
//   else if (jslx == "00")
//       type = 1
//   else if (sfzf == "1")
//       type = 1
//   else {
//       type = kzjb
//   }
//   return type
// }

// // 保存前获取申请数量对应弹窗方法
// //判断
function SetYp_callback_new(yp_list){
  let zh = "", mbmc = "";
  // mbindex = index
  // zh = yp_list.ZH;
  zh = WRT_config.nutriOrderListOne.ZH;
  // mbmc = yp_list.MBMC; // 模板名称该页面没有接口返回这个参数
  mbmc = yp_list.YPID;
  if(yp_list.YPID==0){
    WRT_e.ui.message({
      title:'提示',
      content:"【" + yp_list.YPMC + "】已被药房停止使用!"
    })
    removeYp(zh, mbmc);
    return;
  }
  // if (yp_list.CLTS == "1") { // 貌似用不到
  //   WRT_e.ui.message({
  //     title:'提示',
  //     content:"根据医院处方点评工作组反馈和医院规定,您被限制开具药品【" + yp_list.YPMC + "】"
  //   })
  //   removeYp(zh, mbmc);
  //   return;
  // }
  WRT_e.api.nutriOrderList.getYpFjxx({
    params: { 
      al_ypid: yp_list.YPID, 
      as_yfdm: WRT_config.nOList_Init.YFDM || "", 
      as_jslx: WRT_config.nOList_Init.JSLX, 
      al_zyid: WRT_config.nOList_Init.ZYID, 
      as_yzlx: 'cq', 
      al_zkid: WRT_config.nOList_Init.ZKID 
    },
    success(data) {
      if (data.Code == 1) {
        let array = JSON.parse(data.Result)
        WRT_config.ypfjxx = array[0] || {}
        if (WRT_config.ypfjxx.gwyp == 1) {
        // if (WRT_config.ypfjxx.gwyp == 1 && (yp_list.yts==undefined || yp_list.yts==0)) {
          // yp_list.yts = 1
          WRT_e.ui.message({
            title: '信息窗口',
            content: `${yp_list.YPMC || yp_list.MC}是高警示药品`,
            onOk() {
            }
          })
        }
        
        if (WRT_config.ypfjxx) {
          yp_list.XSP=WRT_config.ypfjxx.xsp||yp_list.xsp||''
        //   // let index=mbindex
          index=mbindex-1
          if(index<0){
              index=0
          }
          dldr_lists[index].XSP=yp_list.XSP || yp_list.xsp
          // dldr_lists[index].xsp=yp_list.XSP || yp_list.xsp
          setSflb(yp_list)
          // iskjywpd(yp_list,zh,mbmc)
        }
      } else {
        WRT_e.ui.message({
          title: '信息窗口',
          content: data.CodeMsg,
          onOk() {
            exportMBList()
          },
        })
        // WRT_e.ui.hint({type:'error',msg:data.CodeMsg})
      }
    }
  })
}

//导入队列
function exportMBList() {
  // let rtn=false
  // yyyznr_lists = dldr_lists
  // console.log('导入队列',mbindex ,yyyznr_lists,dldr_lists, dldr_lists.length,mbindex == dldr_lists.length)
  if (dldr_lists.length > 0) {
    if (mbindex <= dldr_lists.length) {
      if (mbindex == dldr_lists.length) {
        // 判断哪些药品是关闭弹窗的状态
        // 将带DEL字段的数据删除
        for (let index = 0; index < dldr_lists.length; index++) {
          if(dldr_lists[index].DEL==1){
            dldr_lists.splice(index,1)
            index=0
          }
        }
        // dldr_lists.filter(e=>(e.DEL!=1 || e.DEL==undefined))
        mbindex=0
        //   if(dldr_lists[index].DEL==1){
        //     dldr_lists.splice(index,1)
        //     // yyyznr_lists.splice(i,1)
        //     i--;
        //   }
        //   // dldr_lists.splice(index,1)
        //   index=0
        // }
        // // mbindex=0
        // // yyyznr_lists=[...yyyznr_lists,...dldr_lists]
        return dldr_lists
      }
      // mbindex++
      // SetYp_callback_new(dldr_lists[mbindex-1])
      // mbindex++
    }
  }
}

//药品审批
function setSflb(yp_list) {
  // console.log('药品审批',yp_list,indexNow,WRT_config.nOList_Init.JSLX == '00')
  //自费或者住院未开通
  if (WRT_config.nOList_Init.JSLX == '00') {
    //强制审批
    if (yp_list.XSP == '2' || yp_list.XSP == '3') {
      let as_sbxzfw = escape(yp_list.XZFW)
      let as_brzd = escape(WRT_config.nOList_Init.BRZD)
      if(WRT_config.ypfjxx.xzfw_bbzt==1){
        let html=ypsp_html(WRT_config.ypfjxx)
        WRT_e.ui.model({
          id: "if_ypsp",
          title: "医保限制支付范围提示",
          width: "650px",
          content: html,
          closeButton: false,
          closeOnEscape: false,
          iframe: false,
        })
      }else if(WRT_config.ypfjxx.xzfw_bbzt==2){
        let url = `${WRT_config.server}/xypyz/ypsp.aspx?as_jslx=${WRT_config.nOList_Init.JSLX}&as_splb=${WRT_config.ypfjxx.xsp}&as_sfzf=0&as_version=djm&as_rowindex=`
        // page_iframe.add("审批", url)
        // yzdata.splice(index, 1)
        WRT_e.ui.model({
          id: "ypsp",
          title: "医保限制支付范围提示",
          width: "650px",
          iframeURL: url,
          closeButton: false,
          closeOnEscape: false,
          iframe: true,
        })
        $("#ypsp iframe").load(function () {
          $("#ypsp iframe").contents().find("#tb_sbxzfw").text(WRT_config.ypfjxx.xzfw);//val
          $("#ypsp iframe").contents().find("#tb_brzd").text(WRT_config.nOList_Init.BRZD);
        })
      }
      return;
    }
  } else {
    let xzfw_type=''
    if(WRT_config.ypfjxx.xzfw_bbzt==1){
      xzfw_type='[]'
    }else if(WRT_config.ypfjxx.xzfw_bbzt==2){
      xzfw_type=''
    }
    if (yp_list.XSP == "0" && WRT_config.ypfjxx.xzfw == xzfw_type) {
      if (yp_list.KZJB.indexOf('自费') >= 0 && parseFloat(yp_list.XZDJ) > 2) {
        WRT_e.ui.message({
          title: '信息窗口',
          content: '该药为自费药品,请务必告知病人签字后方可使用!',
          onOk() {
          }
        })
        return;
      }
    }
    else if((yp_list.XSP=='0'&&WRT_config.ypfjxx.xzfw!=xzfw_type)||(yp_list.XSP==null)||(WRT_config.ypfjxx.xzfw=='')){
      //无需审批
      exportMBList()
      return;
    }
    else {
      let as_sbxzfw = escape(WRT_config.ypfjxx.xzfw)
      // let as_brzd = escape(WRT_config.yz_sz.zd)
      if(WRT_config.ypfjxx.xzfw_bbzt==1){
        let html=ypsp_html(WRT_config.ypfjxx)
        WRT_e.ui.model({
          id: "if_ypsp",
          title: "医保限制支付范围提示",
          width: "650px",
          content: html,
          closeButton: false,
          closeOnEscape: false,
          iframe: false,
        })
      }else if(WRT_config.ypfjxx.xzfw_bbzt==2){
        let url = `${WRT_config.server}/xypyz/ypsp.aspx?as_jslx=${WRT_config.nOList_Init.JSLX}&as_splb=${WRT_config.ypfjxx.xsp}&as_sfzf=0&as_version=djm&as_rowindex=`
        // page_iframe.add("审批", url)
        WRT_e.ui.model({
          id: "ypsp",
          title: "医保限制支付范围提示",
          width: "650px",
          iframeURL: url,
          closeButton: false,
          closeOnEscape: false,
          iframe: true,
        })
        $("#ypsp iframe").load(function () {
          $("#ypsp iframe").contents().find("#tb_sbxzfw").text(WRT_config.ypfjxx.xzfw);//val
          $("#ypsp iframe").contents().find("#tb_brzd").text(WRT_config.nOList_Init.BRZD);
        })
      }
      return
    }
  }
  exportMBList()
}
//药品审批html
function ypsp_html(ypfjxx){
  let text=''
  let zxfw=JSON.parse(ypfjxx.xzfw)
  WRT_config.XDFW_list=zxfw.map(function(item){
    return item.XDFW
  })
  // zxfw=JSON.parse('[{"XDFW":"(国版)限肝功能衰竭"},{"XDFW":"(国版)无法使用甘草酸口服制剂的患者"},{"XDFW":"(省版)限抢救"},{"XDFW":"(省版)限肝病"}]')
  let type=false
  let arr=[...zxfw,{XDFW:'均不符合，需自费，请告知患者'}]
  if(ypfjxx.xsp=='0'){
    text=''
    type=false
  }else if(ypfjxx.xsp=='1'){
    type=true
    text='该项目为需【医保窗口】审批项目。'
  }else if(ypfjxx.xsp=='2'){
    type=true
    text='该项目为需【药房】和【医保窗口】审批项目。'
  }else if(ypfjxx.xsp=='3'){
    type=true
    text='该项目为需【药房】审批项目。'
  }else if(ypfjxx.xsp){
    type=true
    text='该项目为需审批项目。'
  }
  // <textarea name="tb_sbxzfw" rows="2" cols="20" id="tb_sbxzfw" style="height:130px;width:369px;">${ypfjxx.xzfw}</textarea>
  let temp=`
    <div id="XDFW_table">
      <span style="font-weight:bold;">药品名称：${ypfjxx.ypmc}</span><br>
      <span id="Label1" style="font-weight:bold;">医保使用限制范围如下，请根据疾病诊断准确选择：</span><br>
      <ul style="height:130px;width:369px;border: 1px solid;overflow: auto;padding: 3px 5px;">
      ${_.map(arr,(item,index)=>`
      <li><input type="checkbox" name="xzfw_check" onclick="editCheckboxTrue(this)" value="${item.XDFW}"><span ${index==arr.length-1?'style="color:red"':''}>${item.XDFW}</sapn></li>
      `).join('')}
      </ul>
      <br>
      <span id="Label2" style="font-weight:bold;">病人诊断</span>&nbsp;<br>
      <textarea name="tb_brzd" rows="2" cols="20" id="tb_brzd" disabled="true" style="height:74px;width:367px;">${WRT_config.nOList_Init.BRZD}</textarea><br>
      ${ypfjxx.xsp!=0?`<span id="lb_tsxx" style="display:inline-block;height:49px;width:372px;">${text}</span><br>`:''}
      <span style="display:inline-block;height:49px;width:372px;color:red">注：请根据患者病情如实勾选并在病历中体现！</span><br>
      <table style="width: 370px" cellpadding="0" cellspacing="0">
        <tbody>
          <tr id="tr_spxm">
            ${ypfjxx.xsp!=0&&ypfjxx.xsp?`<td style="width: 180px; height: 24px;padding: 3px 0;"><span id="Label3">申请审批数量</span>
              <input name="tb_spsl" type="text" id="tb_spsl" style="width:59px;border: 1px solid #736b6b;">
            </td>`:''}
          </tr>
          <tr align="center">
            <td style="width: 180px; height: 15px;" align="center">
              <button class="e_btn" value="确定" onclick="bt_click(1);return false;" id="bt_qd" style="width:160px;">${type?'确认提交审批':'确定'}</button>
            </td>
            <td style="width: 180px; height: 15px;" align="center">
              <button class="e_btn" value="取消" onclick="SetSpjg('',true);return false;" id="bt_qx" style="width:160px;">取消</button>
            </td>
          </tr>
        </tbody>
      </table>
      &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
      <br>
    </div>
  `
  return temp
}

//审批确定
function bt_click(lx){
  var splb = WRT_config.ypfjxx.xsp;
  var obj = new Object();
  obj.SFZF = "";
  obj.SPLB = splb;
  let check=$('#XDFW_table input[type=checkbox]:checked')
  obj.xdfw=''
  if(check[0]){
      obj.xdfw=check[0].value
  }else if(check.length==0){
      alert("社保限制范围必须勾选");
      return
  }

  if (lx=='1')
  {
      if (splb == "0"){
          obj.SFZF = "0";
          if (obj.xdfw=='均不符合，需自费，请告知患者'){
              obj.SFZF = "1";
          }
      }
      else {
          if (splb != "3") {
              if (WRT_config.lclj_init.JSLX == "00")
                  obj.SFZF = "0";
              else {
                  if (WRT_config.XDFW_list.indexOf(obj.xdfw)>=0)
                      obj.SFZF = "0";
                  if (obj.xdfw=='均不符合，需自费，请告知患者')
                      obj.SFZF = "1";
              }
              if (obj.SFZF == "") {
                  alert("请选择 是否自费还是公费!");
                  return;
              }
          }
          else {
              obj.SFZF = 0;
          }
          var spsl = $("#tb_spsl")[0].value;
          if (spsl == "") 
          {
              alert("请输入审批数量");
              return;
          }
          if (isNaN(spsl)) 
          {
              alert("审批数量请输入数字!")
              return;
          }
          if (parseFloat(spsl) <= 0) 
          {
              alert("审批数量不能为零或负数!");
              return;
          }
          obj.SPSL = spsl;
      }
  }
  
  if (obj.SPSL == ""||obj.SPSL == undefined)
      obj.SPSL = 0;

  SetSpjg({sfzf: obj.SFZF, spsl: obj.SPSL, splb: obj.SPLB,xdfw:obj.xdfw},true)
}

//删除
function removeYp(zh, mbmc,ID) {
  if (dldr_lists.length > 0)
  {
    for (var i=dldr_lists.length-1; i>=0 ;i--)
    {
      if (dldr_lists[i].ZH == zh && dldr_lists[i].YPID == mbmc)
      {
        dldr_lists[i].DEL = "1";
      }
      else
        break;
    }
  }
  // dealYznr();
  exportMBList()
}
/********************视图********************/
// 基本信息
var Base_View = WRT_e.view.extend({
  render: function () {
			// <span class="noTBtipL">※请将病人身高、体重信息补充完整</span>
      // this.data.baseInit.BAH  this.data.baseInit.ZYID
    let html = `
			<div class="noTBinfoR">
				<span class="tBinfoPoint" style="padding-right:15px;">●</span>
				<span class="tBinfoBold">${this.data.baseInit.BAH}</span>
				<span class="tBinfoBold" ${this.data.baseInit.BRXM.length>10?`style='min-width:170px'`:''}>${this.data.baseInit.BRXM}</span>
				<span>${this.data.baseInit.BRXB=='1'?`男`:`女`}</span>
				<span style="min-width:45px;max-width:90px">${this.data.baseInit.NL}岁</span>
				<span class="tBinfoBcNum" style="padding: 1px 5px;min-width:52px;max-width:100px">${this.data.baseInit.BQID}-${this.data.baseInit.CWH}</span>
				<span class="tBinfoBold" style="padding-left: 10px;min-width: 135px; max-width: 355px;">
          身高：<input class="tBinfoInput" name="height" min="1" step="0.1" ${param["as_id"]!=0 && (this.data.nolOne.ZTBZ=='2'||this.data.nolOne.ZTBZ=='0')?`readonly style="cursor: auto;"`:``} name="stature" value="${Number(this.data.nolOne.SG) || ''}" type="number"/> CM
        </span>
				<span class="tBinfoBold" style="min-width: 134px; max-width: 355px;">
          体重： <input class="tBinfoInput" name="weight"  min="0.1" step="0.01" ${param["as_id"]!=0 && (this.data.nolOne.ZTBZ=='2'||this.data.nolOne.ZTBZ=='0')?`readonly style="cursor: auto;"`:``} value="${Number(this.data.nolOne.TZ) || ''}" type="number"/> KG
        </span>
			</div>
			<div class="noTBtipL">※病人身高体重信息不全，请您最好先在住院病历二中将病人身高，体重信息补充完整</div>
    `
    this.$el.html(html)
    return this;
  },
})
// 新营养医嘱页面标题+列表
var listTitle_View = WRT_e.view.extend({
  showIconUD:false,// 是否展示
  // showload:false,// 是否加载
  render: function () {
    // 初始数据
    let html = `
    <div class="nutriOrderLT">
      <div class="nutriOrderTitle">
        ${this.getHeaderHtml()}
      </div>
      <div class="nutriOrderList">
        ${this.getBodyHtml().indexOf(',')!=-1?`${this.getBodyHtml().replace(/,/g, '')}`:``}
      </div>
    </div>
    <div class="nutriOrderCountTable">
      ${this.getBodyCountTableHtml()}
    </div>
    <div class="nutriOrderBtnAll">
      ${this.getBodyBtnAllHtml()}
    </div>
    <div class="nutriOrderShowTable">
      ${this.getBodyShowTableHtml()}
    </div>
    `
    this.$el.html(html)
    return this;
  },
  events: {
    "click .iconChange": function () {
      // showIconUDn false-->D(body Show)
      // showIconUDn true-->U(body hidden)
      this.showIconUD = !this.showIconUD
      if(this.showIconUD){
        var body1=this.$el.find(".nutriOrderList");
        body1.css("display","none")
      } else {
        var body1=this.$el.find(".nutriOrderList");
        body1.css("display","flex")
      }
      // this.refresh();
    },
    // 保存（即BtnAll部分）点击无效
    "click .saveClick": function (ev) {
      var objArr = this.getData()
      // 11、获取病人相关检验结果
      WRT_e.api.nutriOrderList.getHyjg({
        params: {
          al_yzdid: param["as_id"],
          al_blid: param["as_blid"],
          as_zyh: WRT_config.nOList_Init.ZYH,
          // as_zyh: WRT_config.nutriOrderListOne.ZYH,
        },
        success(data) {
          // console.log('获取病人相关检验结果',JSON.parse(data.d))
          WRT_config.noxgjcHyjg = JSON.parse(data.d)
          if(WRT_config.noxgjcHyjg,length!=0){
            WRT_config.noxgjcHyjg.map(item=>{
              xgjcjgDaya.push({
                nbdm: item.NBDM,
                jg: item.JG,
              })
            })
          }
          
          let arrNow = listypDataNew.concat(listypData)
          if (arrNow.length>0) {
            // 10、获取所选药品的营养元素
            WRT_e.api.nutriOrderList.getDrugsNurtion({
              params: {
                ypData:arrNow
              },
              success(data) {
                WRT_config.nDrugs = JSON.parse(data.d)
                // param["as_id"]!=0
                // if($("input[name='height']").val()=='' && $("input[name='weight']").val()=='' && ($("input[name='cxts']").val()=='' || $("input[name='cxts']").val()==0)){
                //   if($("input[name='height']").val()=='' && $("input[name='height']").val()==0){
                //     WRT_e.ui.hint({
                //       // title:'提示',
                //       type:'warning',
                //       msg:"身高不能为空!"
                //     })
                //   }
                //   if($("input[name='weight']").val()=='' && $("input[name='weight']").val()==0){
                //     WRT_e.ui.hint({
                //       // title:'提示',
                //       msg:"体重不能为空!"
                //     })
                //   }
                // } else {
                // 保存内容
                yydData.push({
                  id: param["as_id"], // 医嘱单id，新增传 0
                  blid: param["as_blid"], // 病人blid
                  brxm: objArr.baseInit.BRXM, // 病人姓名
                  brxb: objArr.baseInit.BRXB, // 性别
                  zyh: objArr.nolOne.ZYH || WRT_config.nOList_Init.ZYH, // 住院号
                  zkid: objArr.nolOne.ZKID || WRT_config.nOList_Init.ZKID,// 专科id
                  bqid: objArr.nolOne.BQID|| WRT_config.nOList_Init.BQID,// 病区id
                  cwh: objArr.nolOne.CWH || WRT_config.nOList_Init.CWH,// 床位号
                  sg: objArr.nolOne.SG || $("input[name='height']").val(),// 身高
                  tz: objArr.nolOne.TZ || $("input[name='weight']").val(),// 体重
                  lczd: objArr.nolOne.LCZD || $("input[name='fdbnl']").val(),// 临床诊断
                  bee: $("input[name='bee']").val(),// 基础能量消耗
                  fdbnl: objArr.nolOne.FDBNL || $("input[name='fdbnl']").val(),// 非蛋白能量
                  zdl: objArr.nolOne.ZDL || $("input[name='zdl']").val(),// 总氮量
                  zytl: objArr.nolOne.ZYTL || $("input[name='zytl']").val(),// 总液体量
                  t_yds: objArr.nolOne.T_YDS || $("input[name='t_yds']").val(),// 糖：胰岛素
                  // nl: objArr.nolOne.NL_D,// 能量：氮
                  nl_d: objArr.nolOne.NL_D || $("input[name='nl_d']").val(),// 能量：氮
                  t_zf: objArr.nolOne.T_ZF || $("input[name='t_zf']").val(),// 糖：脂肪
                  nrs: objArr.nolOne.NRS || $("input[name='nrs']").val(),// NRS
                  bmi: $("input[name='bmi']").val(),// 体重指数
                  kssj: ($("input[name='kssj']").val()).replace('T',' '),// 开始时间
                  // kssj: (objArr.nolOne.KSSJ).replace('T',' ') || ($("input[name='kssj']").val()).replace('T',' '),// 开始时间
                  cxts: objArr.nolOne.CXTS || $("input[name='cxts']").val(),// 持续天数
                  zxpl: objArr.nolOne.ZXPL || $("option[name='yypl']").val(),// 执行频率
                  sty: objArr.nolOne.STY || $("input[name='sty']").val(),// 渗透压
                })
                // }
                yyyznr_lists = (objArr.list1Data).concat(listypAllDataNew)
                yyyznr_lists.map((e,i)=>{
                  e.SQSL = e.SQSL==null?'':e.SQSL
                  e.XSP =  e.XSP==null?'':e.XSP
                  // e.XDFW = ''
                })
                dldr_lists = yyyznr_lists
                mbindex = 0
                // if(listypAllDataNew.length==0){ // 有新增说明在填写数量的时候已经做过审批了
                  for(let i = 0;i<dldr_lists.length;i++){
                    mbindex++
                    SetYp_callback_new(dldr_lists[i],i+1)
                  }
                // }
                wzypData = []
                dldr_lists.map((item,i)=>{
                  wzypData.push({
                    ypid: item.YPID,
                    ycyl: item.YCYL,
                    jldw: item.JLDW,
                    xsp: item.XSP,
                    sqsl: item.SQSL,
                    xdfw: item.XDFW,
                  })
                })

                let paramList= {
                  al_blid: Number(param["as_blid"]), 
                  al_yhid: WRT_config.nDrugs.YZYSYHID ||  WRT_config.nutriOrderListOne.YZYSYHID || WRT_config.nOList_Init.CURRENTYHID, // 医师用户id
                  as_yfdm: WRT_config.nOList_Init.YFDM, // 药房代码
                  yyyzdData: yydData,
                  yyyzd_ypData: wzypData, // 已选药品具体信息
                  hyjgData: xgjcjgDaya,  // 相关检查结果
                }
                if($("input[name='height']").val()=='' || $("input[name='weight']").val()=='' || ($("input[name='cxts']").val()=='' || $("input[name='cxts']").val()=='0')){
                  if($("input[name='height']").val()=='' || $("input[name='height']").val()==0){
                    WRT_e.ui.hint({
                      // title:'提示',
                      type:'warning',
                      msg:"身高不能为空!"
                    })
                  }
                  if($("input[name='weight']").val()=='' || $("input[name='weight']").val()==0){
                    WRT_e.ui.hint({
                      // title:'提示',
                      type:'warning',
                      msg:"体重不能为空!"
                    })
                  }
                  if($("input[name='cxts']").val()=='' || $("input[name='cxts']").val()=='0'){
                    WRT_e.ui.hint({
                      // title:'提示',
                      type:'warning',
                      msg:"持续输液不能为空!"
                    })
                  }
                } else {
                  setTimeout(() => {
                    WRT_e.api.nutriOrderList.saveNurtionOrder({
                      // params: paramList,CURRENTYHID
                      params: {
                        al_blid: Number(param["as_blid"]), 
                        al_yhid: WRT_config.nDrugs.YZYSYHID ||  WRT_config.nutriOrderListOne.YZYSYHID || WRT_config.nOList_Init.CURRENTYHID, // 医师用户id
                        as_yfdm: WRT_config.nOList_Init.YFDM, // 药房代码
                        // as_yfdm: Number(WRT_config.nOList_Init.YFDM), // 药房代码
                        yyyzdData: yydData,
                        yyyzd_ypData: wzypData, // 已选药品具体信息
                        hyjgData: xgjcjgDaya,  // 相关检查结果
                      },
                      success(data) {
                        // console.log('保存',data)
                        if (data!=0) {
                          WRT_e.ui.hint({
                            type:'success',
                            msg:'保存成功'
                          })
                          parent.closeModalNow()
                        } else {
                          WRT_e.ui.hint({
                            type:'error',
                            msg:'保存失败'
                          })
                        }
                      }
                    })
                  }, 1000);
                }
              }
            })
            
          } else {
            WRT_e.ui.hint({
              type:'error',
              msg:'营养医嘱单为空，请检查核对'
            })
          }
        }
      })

    },
    // 提交（即BtnAll部分）
    "click .subClick": function (ev) {
      var objArr = this.getData()
      // console.log('提交（即BtnAll部分）',WRT_config,this.data,listypData,objArr)
      
      // 11、获取病人相关检验结果
      WRT_e.api.nutriOrderList.getHyjg({
        params: {
          al_yzdid: param["as_id"],
          al_blid: param["as_blid"],
          as_zyh: WRT_config.nOList_Init.ZYH,
          // as_zyh: WRT_config.nutriOrderListOne.ZYH,
        },
        success(data) {
          // console.log('获取病人相关检验结果',JSON.parse(data.d))
          WRT_config.noxgjcHyjg = JSON.parse(data.d)
          if(WRT_config.noxgjcHyjg,length!=0){
            WRT_config.noxgjcHyjg.map(item=>{
              xgjcjgDaya.push({
                nbdm: item.NBDM,
                jg: item.JG,
              })
            })
          }
          
          let arrNow = listypDataNew.concat(listypData)
          if (arrNow.length>0) {
            // 10、获取所选药品的营养元素
            WRT_e.api.nutriOrderList.getDrugsNurtion({
              params: {
                ypData:arrNow
              },
              success(data) {
                WRT_config.nDrugs = JSON.parse(data.d)
                // 保存内容
                yydData.push({
                  id: param["as_id"], // 医嘱单id，新增传 0
                  blid: param["as_blid"], // 病人blid
                  brxm: objArr.baseInit.BRXM, // 病人姓名
                  brxb: objArr.baseInit.BRXB, // 性别
                  zyh: objArr.nolOne.ZYH || WRT_config.nOList_Init.ZYH, // 住院号
                  zkid: objArr.nolOne.ZKID || WRT_config.nOList_Init.ZKID,// 专科id
                  bqid: objArr.nolOne.BQID|| WRT_config.nOList_Init.BQID,// 病区id
                  cwh: objArr.nolOne.CWH || WRT_config.nOList_Init.CWH,// 床位号
                  sg: objArr.nolOne.SG || $("input[name='height']").val(),// 身高
                  tz: objArr.nolOne.TZ || $("input[name='weight']").val(),// 体重
                  lczd: objArr.nolOne.LCZD || $("input[name='fdbnl']").val(),// 临床诊断
                  bee: $("input[name='bee']").val(),// 基础能量消耗
                  fdbnl: objArr.nolOne.FDBNL || $("input[name='fdbnl']").val(),// 非蛋白能量
                  zdl: objArr.nolOne.ZDL || $("input[name='zdl']").val(),// 总氮量
                  zytl: objArr.nolOne.ZYTL || $("input[name='zytl']").val(),// 总液体量
                  t_yds: objArr.nolOne.T_YDS || $("input[name='t_yds']").val(),// 糖：胰岛素
                  // nl: objArr.nolOne.NL_D,// 能量：氮
                  nl_d: objArr.nolOne.NL_D || $("input[name='nl_d']").val(),// 能量：氮
                  t_zf: objArr.nolOne.T_ZF || $("input[name='t_zf']").val(),// 糖：脂肪
                  nrs: objArr.nolOne.NRS || $("input[name='nrs']").val(),// NRS
                  bmi: $("input[name='bmi']").val(),// 体重指数
                  kssj: ($("input[name='kssj']").val()).replace('T',' '),// 开始时间
                  // kssj: (objArr.nolOne.KSSJ).replace('T',' ') || ($("input[name='kssj']").val()).replace('T',' '),// 开始时间
                  cxts: objArr.nolOne.CXTS || $("input[name='cxts']").val(),// 持续天数
                  zxpl: objArr.nolOne.ZXPL || $("option[name='yypl']").val(),// 执行频率
                  sty: objArr.nolOne.STY || $("input[name='sty']").val(),// 渗透压
                })
                // yydData = {
                //   id: param["as_id"], // 医嘱单id，新增传 0
                //   blid: param["as_blid"], // 病人blid
                //   brxm: objArr.baseInit.BRXM, // 病人姓名
                //   brxb: objArr.baseInit.BRXB, // 性别
                //   zyh: objArr.nolOne.ZYH, // 住院号
                //   zkid: objArr.nolOne.ZKID,// 专科id
                //   bqid: objArr.nolOne.BQID,// 病区id
                //   cwh: objArr.nolOne.CWH,// 床位号
                //   sg: objArr.nolOne.SG,// 身高
                //   tz: objArr.nolOne.TZ,// 体重
                //   lczd: objArr.nolOne.LCZD,// 临床诊断
                //   bee: $("input[name='bee']").val(),// 基础能量消耗
                //   fdbnl: objArr.nolOne.FDBNL,// 非蛋白能量
                //   zdl: objArr.nolOne.ZDL,// 总氮量
                //   zytl: objArr.nolOne.ZYTL,// 总液体量
                //   t_yds: objArr.nolOne.T_YDS,// 糖：胰岛素
                //   nl_d: objArr.nolOne.NL_D,// 能量：氮
                //   // nl: objArr.nolOne.NL_D,// 能量：氮
                //   t_zf: objArr.nolOne.T_ZF,// 糖：脂肪
                //   nrs: objArr.nolOne.NRS,// NRS
                //   bmi: $("input[name='bmi']").val(),// 体重指数
                //   kssj: (objArr.nolOne.KSSJ).replace('T',' '),// 开始时间
                //   cxts: objArr.nolOne.CXTS,// 持续天数
                //   zxpl: objArr.nolOne.ZXPL,// 执行频率
                //   sty: objArr.nolOne.STY,// 渗透压
                // }
                yyyznr_lists = (objArr.list1Data).concat(listypAllDataNew)
                yyyznr_lists.map((e,i)=>{
                  e.SQSL = e.SQSL==null?'':e.SQSL
                  e.XSP =  e.XSP==null?'':e.XSP
                  // e.XDFW = ''
                })
                dldr_lists = yyyznr_lists
                mbindex = 0
                // if(listypAllDataNew.length==0){ // 有新增说明在填写数量的时候已经做过审批了
                  for(let i = 0;i<dldr_lists.length;i++){
                    mbindex++
                    SetYp_callback_new(dldr_lists[i],i+1)
                    
                  }
                // }
                wzypData = []
                dldr_lists.map((item,i)=>{
                  wzypData.push({
                    ypid: item.YPID,
                    ycyl: item.YCYL,
                    jldw: item.JLDW,
                    xsp: item.XSP,
                    sqsl: item.SQSL,
                    xdfw: item.XDFW,
                  })
                })

                let paramList= {
                  al_blid: Number(param["as_blid"]), 
                  al_yhid: WRT_config.nDrugs.YZYSYHID ||  WRT_config.nutriOrderListOne.YZYSYHID, // 医师用户id
                  as_yfdm: WRT_config.nOList_Init.YFDM, // 药房代码
                  yyyzdData: yydData,
                  yyyzd_ypData: wzypData, // 已选药品具体信息
                  hyjgData: xgjcjgDaya,  // 相关检查结果
                }
                if($("input[name='height']").val()=='' && $("input[name='weight']").val()=='' && $("input[name='cxts']").val()==''){
                  if($("input[name='height']").val()==''){
                    WRT_e.ui.message({
                      title:'提示',
                      content:"身高不能为空!"
                    })
                  }
                  if($("input[name='weight']").val()==''){
                    WRT_e.ui.message({
                      title:'提示',
                      content:"体重不能为空!"
                    })
                  }
                  if($("input[name='cxts']").val()==''){
                    WRT_e.ui.message({
                      title:'提示',
                      content:"持续输液不能为空!"
                    })
                  }
                } else {
                  setTimeout(() => {
                    WRT_e.api.nutriOrderList.saveNurtionOrder({
                      // params: paramList,
                      params: {
                        al_blid: Number(param["as_blid"]), 
                        al_yhid: WRT_config.nDrugs.YZYSYHID ||  WRT_config.nutriOrderListOne.YZYSYHID, // 医师用户id
                        as_yfdm: WRT_config.nOList_Init.YFDM, // 药房代码
                        // as_yfdm: Number(WRT_config.nOList_Init.YFDM), // 药房代码
                        yyyzdData: yydData,
                        yyyzd_ypData: wzypData, // 已选药品具体信息
                        hyjgData: xgjcjgDaya,  // 相关检查结果
                      },
                      success(data) {
                        // console.log('保存',data,WRT_config)
                        if (data!=0) {
                          // 提交
                          WRT_e.api.nutriOrderList.submitNurtionOrder({
                            params: {
                              al_yzdid: Number(param["as_id"]),
                              as_yfdm: objArr.baseInit.YFDM, // 药房代码
                              al_zyid: Number(objArr.baseInit.ZYID), 
                              al_ysyhid: WRT_config.nDrugs.YZYSYHID ||  WRT_config.nutriOrderListOne.YZYSYHID, // 当前医生用户id
                              al_blid: Number(param["as_blid"]), 
                            },
                            success(data) {
                              // console.log('提交',data,data.d,data.Result)
                              if(data.d == '1'){
                                WRT_e.ui.hint({
                                  type:'success',
                                  msg:'医嘱提交成功'
                                })
                                parent.closeModalNow()
                              } else{
                                WRT_e.ui.hint({
                                  type:'error',
                                  msg:data.d
                                })
                              }
                            }
                          })
                        } else {
                          WRT_e.ui.hint({
                            type:'error',
                            msg:'保存失败'
                          })
                        }
                      }
                    })
                  }, 1000);
                }
              }
            })
          } else {
            WRT_e.ui.hint({
              type:'error',
              msg:'营养医嘱单为空，请检查核对'
            })
          }
        }
      })
    }
  },
  // 数据初始化
  getData:function() {
    // 初始数据(this.data)
    // 基础信息:baseInit ; 所有药品信息:listData (直接处理不做展示) ;
    // 已选药品信息:listJLData ; 第2部分表格(bmi、bee)内初始数据:nolOne ; 
    // 第三部分表格相关检查结果初始数据:xgjcHyjg
    // (营养医嘱数据汇总)
    let initialData = {
      baseInit: this.data.baseInit, // 基础信息
      nolOne: this.data.nolOne, //  第2部分表格(bmi、bee)内初始数据 单条医嘱单的记录(获取身高体重等基础信息)
      xgjcHyjgArr: this.data.xgjcHyjg,  // 相关检验结果
      listBodyData: [], // 第2部分列表处理后数据(拆成2列)
      list1Data: this.data.listJLData, // 列表(点击详情：已选药品信息，点击新增：无数据)
      // listypData1:[], // // 初始药品用量(用于接口使用) ==>取数据放入使用 listypData（）
      yyplArr:[], // 获取第2部分按钮模块用药频率数据
    }
      // 第2部分列表所有内容
    this.data.listData.map((item,index) => {
      item.BH = index+1
      // ,BLZ:null,SQSL:'',XSP:''
      initialData.listBodyData.push($.extend({},item,{YCYL:''}))
    })
    listypData = []  // 初始药品用量
    // 对应病人用药剂量（对应详情表格初始参数 ：一次用量）
    // // 对初始已选数据进行赋值（填入第二部分列表一次用量中） + 整合数据用于计算接口传参
    this.data.listJLData.map(item => {
      // item.
      initialData.listBodyData.filter((e,index)=>{
        // e.YCYL = ''
        if(e.MC == item.YPMC){
          // e.BLZ = item.BLZ 
          // 如果没有数据暂时填空？？？
          // e.SQSL = item.SQSL || item.spsl || item.SL || ''
          // e.XSP = item.XSP || item.xsp || ''
          e.YCYL = item.YCYL 
          // WRT_config.nurtionDrugListBrYyJL[index].YCYL = item.YCYL 
          // 用于在所选药品的营养元素接口
          listypData.push({
            ypid: item.YPID,
            yl: item.YCYL,
          })
        }
         
      })
    })
    showBodyData = initialData.list1Data
    // WRT_config.nurtionDrugListBrYyJL = initialData.listBodyData
    // 获取用药频率
    WRT_e.api.nutriOrderList.getYpyfpl({
      params: {
        as_lb: 2,
      },
      success(data) {
        if(data.Code == '1') {
          // console.log('用药频率',JSON.parse(data.Result))
          initialData.yyplArr = JSON.parse(data.Result)
        }
      }
    })

    // let initialData = [ { '组号':'1', '医嘱名称':'50%葡萄糖针（高糖100ml/玻璃）「基」', '规格/单价':'8.5%*250ml', '一次用量':'', '剂量单位':'ml',},]
    // ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}
    return initialData
  },
  pages:function(){
    var pages = []
    var objArr = this.getData();
    var obj = objArr.listBodyData
    obj.forEach((item, index) => {
      var page = Math.floor(index / ((obj.length/2)))
      if (!pages[page]) {
        pages[page] = []
      }
      pages[page].push(item)
    })
    return pages
  },
  // 列表标题
  getHeaderHtml:function(){
    return `
      <span>胃肠外营养医嘱单</span>
      ${!this.showIconUD?`<i class="glyphicon glyphicon-chevron-down iconChange"></i>`:`<i class="glyphicon glyphicon-chevron-up iconChange"></i>`}
    `
  },
  // Body部分（即list部分）
  getBodyHtml:function(){
    var objArr = this.pages();
    // onchange="calLiquid(${i}+'-'+${item.YPID})"/>
    //  width="50px  border="1" 
    //  ${item.YCYL!=undefined?`value="${item.YCYL}"`:``
    return `
    ${_.map(objArr,(obj,index)=>`
      <table align="start" id="tb_Table" class="tb_title`+index+`">
        <tbody>
          <tr>
            <td>
              <tr align="center" bgcolor="#A3BEFF"  id="table_header"  class="table_headerT table_header`+index+`">
                <td width="30px" class="table-title"><span>编号</span></td>
                <td class="table-title"><span>医嘱名称</span></td>
                <td width="60px" class="table-title"><span>规格/单价</span></td>
                <td width="55px" class="table-title"><span>一次用量</span></td>
                <td width="35px" class="table-title"><span>剂量单位</span></td>
              </tr>
              ${_.map(obj,(item,i)=>`
                <tr align="center" id="table_header" class="table_headerD table_header`+i+`">
                  <td id="table-nr`+index+`_`+i+`" class="table-nr">${item.BH}</td>
                  <td id="table-nr`+index+`_`+i+`" class="table-nr">${item.MC}</td>
                  <td id="table-nr`+index+`_`+i+`" class="table-nr">${item.JL} * </td>
                  <td id="table-nr`+index+`_`+i+`" class="table-nr">
                    <input id="table_nrInput`+index+`_`+i+`" value="${item.YCYL}" name="table_nrInput`+i+`" class="table_nrInput" type="text" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``} onchange='calLiquid(${JSON.stringify(JSON.stringify(($.extend({},item,{indexSy: index+'_'+i}))).replace(/,/g, `-` ))})'/>
                  </td>
                  <td id="table-nr`+index+`_`+i+`" class="table-nr">${item.JLDW}</td>
                </tr>
              `)}
            </td>
          </tr>
        </tbody>
      </table>
    `)}`
  },
  // Body计算表格部分（即CountTable部分）
  getBodyCountTableHtml:function(){
    return `
    <table border="1" align="start" class="ct_Table">
      <tbody>
        <tr>
          <td>
            <tr align="start">
              <td class="table-nr">
                <span>
                  基础能量消耗：
                  <input class="ctTable_nrInput" name="bee" value="${this.data.nolOne.BEE || 0}" type="text" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}/>
                </span>
                <span>Kcal<a herf="javascript:void(0)" onclick="Beecalumet(${this.data.baseInit.BRXB},${this.data.nolOne.TZ},${this.data.nolOne.SG},${this.data.baseInit.NL})">(计算)</a></span>
              </td>
              <td class="table-nr">
                <span>
                  非蛋白能量：
                  <input class="ctTable_nrInput" name="fdbnl" value="${this.data.nolOne.FDBNL || 0}" type="text" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}/>
                </span>
                <span>Kcal</span>
              </td>
              <td class="table-nr">
                <span>
                  总氮量：
                  <input class="ctTable_nrInput" name="zdl" value="${this.data.nolOne.ZDL || 0}" type="text" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}/>
                </span>
                <span>g</span>
              </td>
              <td class="table-nr">
                <span>
                  总液体量：
                  <input id="zytlVal" name="zytl" class="ctTable_nrInput" value="${this.data.nolOne.ZYTL || 0}" type="text" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}/>
                </span>
                <span>ml</span>
              </td>
            </tr>
            <tr align="start">
              <td class="table-nr">
                <span>
                  糖:胰岛素: 
                  <input class="ctTable_nrInput" name="t_yds" value="${this.data.nolOne.T_YDS || 0}" type="text" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}/>
                <span>
              </td>
              <td class="table-nr">
                能量:氮: 
                <input class="ctTable_nrInput" name="nl_d" value="${this.data.nolOne.NL_D || 0}" type="text" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}/>
              </td>
              <td class="table-nr">
                糖:脂: 
                <input class="ctTable_nrInput" name="t_zf" value="${this.data.nolOne.T_ZF || 0}" type="text" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}/>
              </td>
              <td class="table-nr">
                NRS2002: 
                <input class="ctTable_nrInput" name="nrs" value="${this.data.nolOne.NRS || 0}" type="text" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}/>
              </td>
            </tr>
            <tr align="start">
              <td class="table-nr">
                BMI:
                <input class="ctTable_nrInput" name="bmi" value="${this.data.nolOne.BMI || 0}" type="text" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}/>
                <span><a herf="javascript:void(0)" onclick="BMIcalumet(${this.data.nolOne.SG},${this.data.nolOne.TZ})" >(计算)</a></span>
              </td>
              <td class="table-nr">
                渗透压: 
                <input class="ctTable_nrInput" name="sty" value="${this.data.nolOne.STY || 0}" type="text" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}/>
              </td>
              <td colspan="2"></td>
            </tr>
            <tr align="start">
              <td class="table-nr" colspan="4">
                临床诊断: 
                <input class="ctTable_nrInputLong" name="lclj" value="${this.data.nolOne.LCZD || ''}" type="text"/>
              </td>
            </tr>
          </td>
        <tr>
      </tbody>
    </table>
    `
  },
  // Body 按钮部分（即BtnAll部分）
  getBodyBtnAllHtml:function(){
    var objArr = this.getData();
    var obj = objArr.nolOne
    return `
    <span class="inputBtn">
      <span>
        <strong class="inputTit">开始时间</strong>
        <input class="Btn_nrInput" name="kssj" value="${obj.KSSJ ||new Date().Format("yyyy-MM-ddTHH:mm:ss")}" type="datetime-local" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}/>
      </span>
      <span>
        <strong class="inputTit">持续输液</strong>
        <input class="Btn_nrInput" name="cxts" value="${obj.CXTS || 0}" style="width:48px" type="text" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}/>&nbsp;&nbsp;天
      </span>
      <span>
        <strong class="inputTit">用药频率</strong>
        <select ${JSON.stringify(obj) != '{}'?`disabled="disabled" `:""} class="changeYypl Btn_nrInput" name="jjjb" style="width:80px;"> 
          ${JSON.stringify(obj) != '{}' || (this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0')?`
            <option value="${obj.ZXPL}" name="yypl" class="yypl" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}>${obj.ZXPL}</option>`:
            this.getyypl(objArr)
          }
          
        </select>
      </span>
      <span>
        用法:&nbsp;营养静脉滴注
      </span>
    </span>
    <span>
      <button class="e-btn saveClick" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?` disabled="disabled" style="color: #4A4A4A;background:#E6E6E6"`:``}>保存</button>
      <button class="e-btn subClick"  ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?` disabled="disabled" style="color: #4A4A4A;background:#E6E6E6"`:``}>提交</button>
    </span>
    `
  },
  // 用药频率下拉框 （即BtnAll部分）
  getyypl:function(objArr){
    return `
      ${_.map(objArr.yyplArr,(item,index)=>`
        <option value="${item.DM}" name="yypl" class="yypl`+index+`">${item.DM}</option>
      `)}
    `
  },
  // Body展示表格 最终数据展示部分（即ShowTable部分）
  getBodyShowTableHtml:function(){
    return `
    <span>相关检验项目</span> </br>
    <table border="1" align="start" class="show_Table">
      <tbody>
        <tr>
          <td>
            <tr align="start">
              <td class="table-nr"> 总胆红素: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='总胆红素'){return e.JG}}):``}</td>
              <td class="table-nr"> 直接胆红素: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='直接胆红素'){return e.JG}}):``}</td>
              <td class="table-nr"> 总蛋白: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='总蛋白'){return e.JG}}):``}</td>
              <td class="table-nr"> 白蛋白: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='白蛋白'){return e.JG}}):``}</td>
            </tr>
            <tr align="start" colspan="4">
              <td class="table-nr"> 前白蛋白: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='前白蛋白'){return e.JG}}):``}</td>
            </tr>
            <tr align="start">
              <td class="table-nr"> 葡萄糖: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='葡萄糖'){return e.JG}}):``}</td>
              <td class="table-nr"> 尿素氮: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='尿素氮'){return e.JG}}):``}</td>
              <td class="table-nr"> 肌酐: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='肌酐'){return e.JG}}):``}</td>
              <td class="table-nr"> 尿酸: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='尿酸'){return e.JG}}):``}</td>
            </tr>
            <tr align="start">
              <td class="table-nr"> 甘油三脂: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='甘油三脂'){return e.JG}}):``}</td>
              <td class="table-nr"> 丙氨酸氨基转移酶: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='丙氨酸氨基转移酶'){return e.JG}}):``}</td>
              <td class="table-nr"> 天冬酸氨酸氨基转移酶: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='天冬酸氨酸氨基转移酶'){return e.JG}}):``}</td>
              <td class="table-nr"> 碱性磷酸酶: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='碱性磷酸酶'){return e.JG}}):``}</td>
            </tr>
            <tr align="start">
              <td class="table-nr"> 血清钾: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='血清钾'){return e.JG}}):``}</td>
              <td class="table-nr"> 血清钠: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='血清钠'){return e.JG}}):``}</td>
              <td class="table-nr">	血清氯: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='血清氯'){return e.JG}}):``}</td>
              <td class="table-nr"> 血清钙: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='血清钙'){return e.JG}}):``}</td>
            </tr>
            <tr align="start" colspan="4">
              <td class="table-nr"> 血清磷: ${WRT_config.noxgjcHyjg!=[] ?WRT_config.noxgjcHyjg.filter(e=>{if(e.MC=='血清磷'){return e.JG}}):``}</td>
            </tr>
          </td>
        <tr>
      <tbody>
    </table>
    `
  },
  // // 刷新Header（即title部分）
  //  refreshHeader:function(){
  //   var header=this.$el.find(".nutriOrderTitle");
  //   header.html(this.getHeaderHtml())
  //  },
  // // 刷新Body（即list部分）
  // refreshBody:function(){
  //   var header=this.$el.find(".nutriOrderTitle");
  //   header.html(this.getHeaderHtml())
  // },
  // // 刷新(第一部分)
  // refresh:function(){
  //   // this.refreshPanel();
  //   this.refreshHeader();
  //   // this.refreshBody();
  // },
})
