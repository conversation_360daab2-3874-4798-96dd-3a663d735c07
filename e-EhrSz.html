<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>温州医科大学附属第一医院电子病历</title>
  <meta name="description" content="首页">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
  <script src="lib/QRCode/QRCode.js"></script>
</head>

<style>
  #panel9 #YjblFromLtzk_lists .nav-tabs>li.active>a, .nav-tabs>li.active>a:focus, .nav-tabs>li.active>a:hover{
    color: #555;
    cursor: default;
    background-color: #fff;
    border: 1px solid #ddd;
    border-bottom-color: transparent;
  }
  #panel9 #YjblFromLtzk_lists .nav-tabs>li{
    margin: 1px 5px;
    margin-bottom: -1px;
  }
  #panel9 #YjblFromLtzk_lists>.nav-tabs>li>a{
    margin-right: 2px;
    line-height: 10px;
    border: 1px solid transparent;
    border-radius: 4px 4px 0 0;
  }
  #jblFromLtzk{
    background: #d7ebf9;
  }
  html #EhrSz>#usercheck{
    z-index: 2000;
  }
  .patient_list .margin_1{
    margin: 0;
  }
  .patient_list .item .ZFYE_box{
    border: 2px solid #FF9933;
  }
  #patient_info .patient_info .panel-group{
    overflow-y:auto
  }
  #menu .dropdown{
    cursor: pointer;
  }
  #ip_host{
    position: absolute;
    height: 0px;
    font-size: 10px;
    width: 60px;
    line-height: 40px;
    color: #fff;
    opacity: .5;
  }
  .logo_title{
    float: left;
  }
  .shjg_title{
    float: left;
    padding-top: 5px;
    padding-right: 5px;
  }
  .shjg_value{
    float: right;
    width: 20px;
    background-color: red;
    height: 20px;
    color: #fff;
    border-radius: 9px;
    padding: 0px 6px;
  }
  .shjg_value:hover{
    cursor: pointer;
  }
  .btable tbody tr:nth-child(1),.crbtable tbody tr:nth-child(1) {
    height: 40px;
    color: #000;
    background: #b0c4de;
    text-align: center;
  }
  .btable tbody tr td,.crbtable tbody tr td {
    height: 30px;
    color: #000;
  }
  .btable,.crbtable{
    width: 100%;
  }
  .btable tr,.crbtable tr{
    border: 1px solid rgb(0 0 0 / 10%);
    text-align: center;
    cursor: pointer;
  }
  .crbtable tr:hover{
    background-color: lightblue;
  }

  #clbtable_tr{
    height: 40px;
    color: #000;
    background: #b0c4de;
    text-align: center;
  }
  .clbtable tbody tr td {
    height: 30px;
    color: #000;
    text-align: center;
  }
  .clbtable{
    width: 1800px;
    overflow: auto;
  }
  .clbtable .ts_td{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 150px;
    display: block;
    padding-top: 5px;
  }
  .clbtable_tr tr{
    border: 1px solid rgb(0 0 0 / 10%);
    text-align: center;
    cursor: pointer;
  }
  .fix_right_title{
    position: sticky;
    right: 0;
    background: #b0c4de;
  }
  .fix_right{
    position: sticky;
    right: 0;
    background: #fff;
  }
  .fix_right a{
    cursor: pointer;
  }
  #doc_text{
    border: 1px solid;
    height: 30px;
    font-size: 14px;
  }
  .title_hover:hover{
    cursor: pointer;
  }
  .item_box .e-tag-boxred{
    color: #fff;
    background: #ff0000;
  }
  .gxhbtn{
    background: orange;border: none;
  }
  .gxhbtn:hover{
    background: rgb(255, 165, 0,0.7);
    color: #fff;
  }
  .table_head th{
    height: 40px;
    color: #000;
    background: #b0c4de;
    text-align: center;
  }
  .xtgl_table tr td{
    text-align: center;
  }
  .xtgl_table tr:nth-child(even) {
    color: #4e4e4f;
    background: #d8e2f4;
}
.bbh{
  color: rgb(255 255 255 / 50%);
  padding-left: 5px;
}
.bbh:hover{
  text-decoration:none;
  color: rgb(255 255 255 / 50%);
}
.menu_bottom>.open .disnone{
  display: none;
}
.none{
  display: none;
}
#page .tab-content>.tab-pane{
  display: block;
  height: 0px;
  overflow: hidden;
  z-index: -1;
  position: relative;
}

#page .tab-content>.active{
  display: block;
  height: 100%;
  z-index: 100;
  /* overflow: auto; */
}
.patient_info .panel-title-text {
    margin-left: 5px;
}
.patient_info .panel-title {
    background: #3D6CC8;
    color: #fff;
    line-height: 42px;
    font-weight: 700;
    font-size: 14px;
    padding: 0 27px 0 15px;
    text-decoration: none;
}
#UndocumentedPatList tr:nth-child(even) {
  background: #f1f2f5;
}
#UndocumentedPatList tr:hover{
  background: #b0c4de;
}
/* .e_btn{
  padding: 5px 15px;
} */
.zgc_model{
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-flow: column;
}
.opacity_none{
  opacity: 0;
}
.cfjgdeail_foolter h{
  color: #3D6CC8;
}
.cfjgdeail_foolter .e_btn{
  float: right;
  margin-right: 3px;
}
.jhcysjNr {
    text-align: center;
  }
  .jhcysj {
    border: 1px solid #e3e0e0;
    border-radius: 4px;
    padding: 3px;
  }

  .pywb_table{
    height: 400px;
    overflow: auto;
    display: block;
  }
  .pywb_table tr td{
    height: 30px;
    color: #000;
    text-align: center;
  }
  .pywb_table tr th{
    height: 40px;
    color: #000;
    background: #b0c4de;
    text-align: center;
  }
  .pywb_table tbody tr:nth-child(even) {
    color: #4e4e4f;
    background: #d8e2f4;
  }
</style>
<body id="EhrSz">
  <div class="main">
    <!-- 公共头部 -->
    <header class="header">
      <div class="container-fluid">
        <div class="flex-row align-items-center justify-content-between">
          <div class="logo">
            <img src="./images/logo.png" alt="">
            <span class="logo_title">电子病历系统</span>
            <a class="bbh">v94</a>
            <div id="ip_host"></div>
          </div>
          <div class="flex-fill notice">
            <div id="e_notice"></div>
          </div>
          <div class="flex-row">
            <div class="e_menu">
              <div class="shjg_title">
                <div class="logo_title">审方结果：</div><div id="sfjg_val" class="shjg_value">0</div>
              </div>
              <div class="shjg_title" onclick="cfjg_change()">
                <div class="logo_title">处方修改：</div><div id="cfxg_val" class="shjg_value">0</div>
              </div>
              <div class="sjz_title" onclick="sjz_change()" style="width: 80px;margin-left: 40px;font-weight: bold;padding-top: 5px;float: left;cursor: pointer;">
                <div class="logo_title">时间针&nbsp;</div><div id="sjz_val" class="sjz_value">0</div>
              </div>
              <div class="menu_top">
                <ul class="status">
                  <!-- <li id="allCount_span" style="background:#E6ECF8;width: 168px;font-weight: 600;color:#3D6CC8">终末质控病历反馈： 0 份</li> -->
                  <li id="SSO_span" style="background:#E6ECF8;width: 168px;font-weight: 600;color:#3D6CC8;cursor: pointer;" onclick="SSO_click()">病历质控</li>
                  <!-- <li id="red_span" style="background:#C82222;">危险 0</li>
                  <li id="yellow_span" style="background:#FFA654;">紧急 0</li>
                  <li id="green_span" style="background:#45E498;">一般 0</li> -->
                </ul>
                <!-- <a class="feedback" href="#" onclick="fkzx_click()">反馈中心</a> -->
              </div>
              <div id="menu"></div>
            </div>
            <div id="login_info"></div>
          </div>
        </div>
      </div>
    </header>
    <section class="inner">
      <!-- 选项卡 -->
      <div class="page" id="page">
        <!-- Nav tabs -->
        <ul class="nav page_nav_header">
          <li class="active"><a href="#病人一览表" data-toggle="tab">病人一览表</a></li>
        </ul>
        <!-- Tab panes -->
        <div class="tab-content">
          <div class="tab-pane active in fade" id="病人一览表">
            <div class="patient flex-row">
              <div class="flex-row flex-column flex-fill e_inner justify-content-between">
                <b style="font-size:12px;position:absolute;top:2px;right: 10px;">温馨提示：1、<span
                    class="red">右键</span>编辑特殊备注。2、<span class="red">临床路径名红色</span>代表入径后出径。</b>
                <div id="patient_filter"></div>
                <div id="patient_list"><div class="zgc_model"><img src="./images/loading.gif" style="width: 360px;"></div></div>
                
              </div>
              <!-- 日志 -->
              <div id="patient_info">
                <div><img src="./images/loading.gif" style="width: 360px;"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
  <!-- 配置文件 -->
  <!-- <script src="./e-config.js"></script> -->
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='./e-config.js?v=" + Date.now() + "'><\/script>");
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
    document.write("<script type='text/javascript' src='js/e-EhrSz.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面JS -->
  <!-- <script src='js/e-EhrSz.js'></script> -->
  <script type="text/javascript">
    var _oQRCode=undefined;
    var _codeid=0
    var _intervalID=0
    var Timeout=null
    var interCount=null
    //计时器锁屏
    // let name=sessionStorage.getItem("username")
    // let fd=WRT_config.trialuser.find(e=>e==name)
    // if(fd){
    // }
    let type=sessionStorage.getItem("sp_model")
    if(type){
      // spmodel()
    }
    // fn()
    function fn(){
      var maxTime = 60*60 //设定时间,秒为单位

      function event(){
        maxTime = 60*60
      }
      document.addEventListener("keydown",()=>{
        maxTime =60*60
      });
      function bindDown(doc){
        if(!doc){
          return
        }
        doc.removeEventListener("keydown",event);
        doc.addEventListener("keydown",event);
        doc.querySelectorAll("iframe").forEach(item=>{
          bindDown(item.contentDocument);
        })
      }
      function bindMouse(doc){
        if(!doc){
          return
        }
        doc.removeEventListener("mousemove",event);
        doc.addEventListener("mousemove",event);
        doc.querySelectorAll("iframe").forEach(item=>{
          bindMouse(item.contentDocument);
        })
      }
      function bindClick(doc){
        if(!doc){
          return
        }
        doc.removeEventListener("click",event);
        doc.addEventListener("click",event);
        doc.querySelectorAll("iframe").forEach(item=>{
          bindClick(item.contentDocument);
        })
      }
      interCount = setInterval(()=>{
        maxTime--
        bindDown(document);
        bindMouse(document);
        bindClick(document);
        if (maxTime === 0) {
          sessionStorage.setItem("sp_model","true")
          // clearInterval(interCount);
          spmodel()
          // alert(111)
        }
      },1000)
    }
    //
    function spmodel(){
      clearInterval(interCount);
      let user=sessionStorage.getItem("info_user")
      if(user){
        let a=user.split(",")[0]
        let b=user.split(",")[1]
        let name=window.atob(a)
        let pass=window.atob(b)
      let temp=`
      <form>
        <div class="form-group">
          <input type="text" class="form-control" id="exampleInputEmail3" value=${name} autocomplete="off" placeholder="用户名" disabled>
          </div>
          <div class="form-group">
            <input type="password" class="form-control" id="exampleInputPassword3" placeholder="密码" autocomplete="off" onkeydown="calAge(event,'${pass}')">
            </div>
            <button type="button" class="e_btn_primary" onclick="onOk('${name}','${pass}')">确定</button>
            <button type="button" class="e_btn" onclick="onout()">退出登录</button>
            </form>`
        WRT_e.ui.model({
          id: "usercheck",
          title: '因长时间未操作，系统已锁屏，请输入密码解锁',
          width: "650px",
          content: temp,
          closeButton:false,
          closeOnEscape:false,
          iframe: false,
        })
      }else if(!user){
        temp=`<div id="qrcode" style="transform: translate(160px, 10px);"></div>`
        WRT_e.ui.model({
          id: "usercheck",
          title: '因长时间未操作，系统已锁屏，请扫描解锁',
          width: "500px",
          content: temp,
          closeButton:false,
          closeOnEscape:false,
          iframe: false,
        })
        GetQRCode()
        $('.form-control').attr("autocomplete","off")
      }
    }
    //回车事件
    function calAge(event,pass){
      if(event.keyCode==13){
        onOk(event,pass)
      }
    }
    //验证
    function onOk(name,pass){
      let pass1=$("#exampleInputPassword3").val()
      if(pass==''){
        WRT_e.ui.hint({msg:'密码不可为空',type:"error"})
        return
      }
      if(pass1!=pass){
        WRT_e.ui.hint({msg:'用户密码输入不一致',type:"error"})
      }else{
        sessionStorage.setItem("sp_model",'')
        WRT_e.ui.hint({msg:'验证成功',type:"success"})
        $("#usercheck").iziModal('destroy')
        fn(['click', 'keydown','mousemove'])
      }
    }
    //二维码获取
    function GetQRCode(){
      if(!WRT_config.url){
        WRT_e.api.au.getQRinit({
          params:{},
          success(data){
            if(data.Code==1){
              WRT_config.url=data.Result
              GetQRCodemodel()
            }
          }
        })
      }else{
        GetQRCodemodel()
      }
    }
    //
    function GetQRCodemodel(){
      WRT_e.api.au.getQRCode({
        url:WRT_config.url.url1,
        params:{
          appCode: '020',  //应用代码
          opCode: "1",
          tip: "请注意确认后,即授权他人登录您的账号！",
          appType: "WEB"
        },
        success(data){
          if (data.hasError == 0) {
            var codeContent = data.data;
            if (codeContent.codeId != "") {
              _codeid = codeContent.codeId;
              $("#qrcode")[0].innerHTML=''
              _oQRCode = new QRCode(document.getElementById("qrcode"), {
                width : 100,
                height : 100
              });
              _oQRCode.clear(); // clear the code.
              _oQRCode.makeCode(codeContent.content);
              var date=new Date(codeContent.expTime)
              let num=date.getTime()-new Date().getTime()
              let time=parseInt(num)/1000
              if(_intervalID!=null){
                clearTimeout(Timeout)
                Timeout=setTimeout(function(){GetQRCode},time)
              }
              if (_intervalID != 0)
                clearInterval(_intervalID);                     
                _intervalID = setInterval(function () { checkQRCodeLoginProcess(); }, 3000);
            } else {
              alert("生成二维码失败！");
            }
          }
        }
      })
    }
    //询问二维码是否有效、是否登陆；
    function checkQRCodeLoginProcess(){
      WRT_e.api.au.getQRCodeProgress({
        url:WRT_config.url.url2,
        params:{
          codeId: _codeid
        },
        success(data){
          if (data.hasError == 0) {
            var codeContent = data.data;
            if (codeContent.isActive == "1") {
              if (codeContent.userId != 0) {
                clearInterval(_intervalID);
                WRT_e.api.au.login_ewm({
                  params:{json:codeContent},
                  success(data){
                    if(data){
                      WRT_e.ui.hint({msg:'验证成功',type:"success"})
                      sessionStorage.setItem("sp_model",'')
                      $("#usercheck").iziModal('destroy')
                    }
                  }
                })
                // sessionStorage.setItem("username",user)
              }
            } else {
              
            }
          }
        }
      })
    }
  </script>
</body>

</html>