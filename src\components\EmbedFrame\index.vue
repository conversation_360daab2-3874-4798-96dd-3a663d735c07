<!--
 -- @<NAME_EMAIL>
 -- @Date 2023-12-21
 -- @LastEditors hybtalented
 -- @LastEditTime 2024-02-29
 -- @FilePath /base-wyyy-template/src/components/EmbedFrame/index.vue
 -- @Description 嵌入页面，改变地址自动刷新
 -->

<template>
  <div class="embed-frame">
    <iframe
      v-show="iframeSrc"
      ref="myIframe"
      class="embed-frame-content"
      frameborder="0"
      seamless
      :src="iframeSrc"
      :style="{ height }"
    />
    <div v-if="!iframeSrc" :style="{ height }" class="no-info">
      <span>暂无数据</span>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { dispatchActionEvent } from '@/utils/store-bridge'

export default {
  name: 'EmbedFrame',
  props: {
    /**
     * 获取页面地址回调函数
     *
     * 组件会自动调用该回调函数，并追踪回调函数中的响应式的值，并在对应的值更新后自动重新调用该会调函数获取新的页面地址
     */
    src: {
      type: Function,
      default: () => ''
    },
    /**
     * iframe 高度
     */
    height: {
      type: String,
      default: 'calc(var(--content-fixed-height) - 5px)'
    }
  },
  emits: {
    /**
     * iframe onload 事件
     * @param {HTMLIFrameElement} iframe iframe
     * @param {Event} event 事件
     */
    load(iframe, event) {
      return true
    }
  },
  data() {
    const iframeSrc = ''

    return {
      // eslint-disable-next-line vue/no-reserved-keys
      __loading: null,
      timer: null,
      iframeSrc
    }
  },
  computed: {
    ...mapState('theme', ['nightMode'])
  },
  watch: {
    nightMode(value) {
      this.initNightMode(value)
    }
  },
  mounted() {
    this.$watch(this.refresh, () => {})
    const myIframe = this.getFrame()
    // 监听 load 事件
    if (myIframe) {
      myIframe.onload = this.handleIFrameLoaded
    }
  },
  beforeDestroy() {
    this.cleanURL()
    this.stopListener()
  },
  methods: {
    /**
     * 处理 iframe loaded 事件
     * @param {Event} event
     */
    handleIFrameLoaded(event) {
      if (this.iframeSrc) {
        this.stopLoading()
        this.timer && clearTimeout(this.timer)
      }

      this.startListener()
      const frame = event.target
      if (frame) {
        this.initNightMode(this.nightMode, frame)
        this.$emit('load', frame, event)
      }
    },
    /**
     * 初始化 iframe 夜间模式
     * @param {boolean} value
     * @param {HTMLIFrameElement} [frame]
     */
    initNightMode(value, frame) {
      const target = frame ?? this.getFrame()
      if (target?.contentWindow) {
        dispatchActionEvent(target.contentWindow, 'theme/initNightMode', value)
      }
    },
    getFrame() {
      return this.$refs.myIframe
    },
    async refresh() {
      this.startLoading()
      try {
        const promise = this.src()
        await Promise.resolve()
        this.cleanURL()
        this.iframeSrc = (await promise) ?? ''
      } finally {
        // 超时严重主动关闭loading
        this.timer && clearTimeout(this.timer)
        if (!this.iframeSrc) {
          this.stopLoading()
        } else {
          this.timer = setTimeout(() => {
            this.stopLoading()
          }, 3000)
        }
      }
    },
    startLoading() {
      this.stopLoading()
      this.__loading = this.$loading({
        lock: true,
        text: '报表加载缓慢,请稍等...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    },
    stopLoading() {
      this.__loading?.close()
      this.__loading = null
    },
    cleanURL() {
      if (this.iframeSrc?.startsWith('blob:')) {
        URL.revokeObjectURL(this.iframeSrc)
        this.iframeSrc = ''
      }
    },
    handleContentMouseEvent(event) {
      const newEvent = new Event(event.type, event)

      document.dispatchEvent(newEvent)
    },
    stopListener() {
      const contentWindow = this.getFrame()?.contentWindow
      try {
        contentWindow?.removeEventListener('mousedown', this.handleContentMouseEvent)
        contentWindow?.removeEventListener('mouseup', this.handleContentMouseEvent)
      } catch (ex) {
        console.error('stop listen event for iframe failed, ', ex)
      }
    },
    startListener() {
      this.stopListener()
      const contentWindow = this.getFrame()?.contentWindow
      if (contentWindow) {
        try {
          contentWindow.addEventListener('mousedown', this.handleContentMouseEvent)
          contentWindow.addEventListener('mouseup', this.handleContentMouseEvent)
        } catch (ex) {
          console.error('start listen event for iframe failed, ', ex)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.embed-frame {
  position: relative;

  &-content {
    width: 100%;
  }
}
</style>
