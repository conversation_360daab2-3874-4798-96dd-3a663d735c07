<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>病人详情页</title>
  <meta name="description" content="病人详情页">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
  #menu-left .panel{
    border-radius:0px
  }
  #menu-left .panel-group .panel{
    margin-top: 0;
    border-radius: 0;
    border-radius:0px
  }
  #menu-left .panel .panel-collapse .panel-body{
    border: none;
  }
  #menu-right .page .nav_list_header{
    height: 30px;
  }
  #menu-right .page .nav_list_header li a{
    font-size: 14px;
    line-height: 30px;
  }
  #menu-right .page .nav_list_header li i{
    top: 7px;
  }
  #brmain>.combo-p>.panel-body-noheader{
    width: 180px;
    height: auto;
    overflow-x: hidden;
    padding: 0;
  }
  .e_inner .menu-left .panel .coll_list a{
    float: left;
    color: #555555;
  }
  
.lb_ts_tooltip {
    position: relative;
    text-decoration: none;
    display: inline-block;
    /* border-bottom: 1px dotted black; 悬停元素上显示点线 */
}
/* Tooltip 文本 */
.lb_ts_tooltip .lb_ts_tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #6e6969de;
    color: #fff;
    text-align: left;
    padding: 5px 0px 5px 10px;
    border-radius: 6px;
    /* 定位 */
    position: absolute;
    top: 20px;
    z-index: 99;
    left:10px;
}
.lb_ts_tooltip .lb_ts_tooltiptext span {
  font-size: 14px;
}
/* 鼠标移动上去后显示提示框 */
.lb_ts_tooltip:hover .lb_ts_tooltiptext {
    visibility: visible;
}
  /* #menu-left .panel-group .panel-default>.panel-heading{
    background-image: none;
    background: #e8e8e8;
    border: 1px solid #f5f5f5;
  } */
  .zydj:hover{
    cursor: pointer;
  }
  #menu-left .panel-title-text:hover{
    cursor: pointer;
  }
  #doc_mc{
    border: 1px solid;
    height: 30px;
    font-size: 14px;
  }
  #doc_lists{
    padding: 3px 0px;
  }
  .docbody td,.doctr td{
    padding-left: 3px;
  }
  .doctr{
    height: 27px;
  }
  .doctr:hover{
    cursor:context-menu;
    background: #7FFFD4;
  }
  .doc_btns a{
    cursor: pointer;    
  }
  .doc_btns a:hover{
    text-decoration: none;
  }
  .JYHLJB_title{
    color: red;
  }
  .JYHLJB_title:hover{
    text-decoration: none;
  }
  .tag-icon:hover{
    cursor: pointer;
  }
  #qc_message{
    cursor: pointer;
  }
  .user_text {
    font-size: 14px;
    margin: 14px 0 14px 4px;
  }
  #headingOne{
    background: rgb(25 100 250 / 8%);
  }
  .status {
    height: 4px;
    background: #3D6CC8;
    margin-bottom: 4px;
  }
  .panel .panel-default .box_list{
    background: #e3e0e0;
    /* background: linear-gradient(180deg, #FFFFFF 0%, #D1D1D1 100%); */
    /* border: 1px solid #9A9A9A; */
    box-sizing: border-box;
  }
  .list_body{
    background: #fff;
  }
  .img_list{
    position: absolute;
    top: 50%;
  }
  .left_img{
    width: 10px;
    float: left;
    height: 100%;
    padding-left: 5px;
  }
  .none{
    display: none;
  }
  #accordion .panel{
    margin: 1.5px 0;
  }
  #saveJhcysj {
    font-size: 14px;
  }
  .jhcysjNr {
    text-align: center;
  }
  .jhcysj {
    border: 1px solid #e3e0e0;
    border-radius: 4px;
    padding: 3px;
  }
  #editSdCode .showSdBtn {
    cursor: pointer;
    margin-left: 10px;
  }
  #editSdCode .ishowSdBtn:hover {
    text-decoration: none;
  }
  #editSdCode .ishowSdBtn {
    color: #555555c9;
    margin-left: 10px;
  }
  .bnzSingle {
    margin: 0 2px;
  }
  /* .bqypage .tab-content .tab-pane{
    visibility: hidden;
  }
.bqypage .tab-content>.active{
  visibility: visible;
} */
#tbJYSP tbody tr:nth-child(1),.crbtable tbody tr:nth-child(1) {
  height: 40px;
  color: #000;
  background: #b0c4de;
  text-align: center;
}
#tbJYSP tbody tr td {
  text-align: center;
  height: 40px;
  color: #000;
}
#tbJYSP tbody tr td a{
  cursor: pointer;
  margin-left: 3px;
}
#tbJYSP tbody tr:nth-child(odd) {
  color: #4e4e4f;
  background: #d8e2f4;
}
#tssm_input{
  border: 1px solid;
  width: 315px;
  height: 30px;
  margin: 5px 0;
}
.box_foot1{
  bottom: 50%;
  position: absolute;
  background: rgb(87 145 251 / 70%);
  right: 0%;
  height: 50px;
  width: 50px;
  z-index: 2000;
  line-height: 50px;
  border-radius: 50%;
  text-align: center;
  cursor: pointer;
}
.box_foot1:hover{
  background: #5791fb;
}
#brmain .e_message{
  z-index: 998;
}
</style>
<body id="brmain">
  <div class="e_inner">
    <div class="row">
      <div class="box_foot none" style="bottom: 50%;position: absolute;background: #dae0eb;right: 10%;min-height: 200px;width: 250px;z-index: 2000;">
        <div style="padding: 5px;background: #4682b4;color: #fff;"><span style="cursor: pointer;" onclick="$('.box_foot').addClass('none')">关闭</span></div>
        <div>
          <ul>
            <li style="padding: 3px 0;">
              <div style="background: #fff;width: 250px;">护理医嘱开立</div>
              <ul>
                  <li style="background: #fff;margin-top: 2px;">
                    <div style="height: 25px;padding-right: 5px;float: left;"><span class="e-tag e-tag-blue">提示</span></div>
                    <span id="box_lists">该患者的护理ADL评分小于等于40分，建议选择一级护理</span>
                  </li>
                </ul>
            </li>
          </ul> 
        </div>
      </div>
      <div class="box_foot1 none" onclick="dbVtechange()">
        VTE
      </div>
      <div id="menu-left" class="col-md-1 col-sm-3 menu-left">
      </div>
      <div id="left_img" class="">
        <img id="arrow_close" class="img_list none" src="./images/arrow_close.gif" onclick="img_click('close')">
        <img id="arrow_open" class="img_list none" src="./images/arrow_open.gif" onclick="img_click('open')">
      </div>
      <div id="menu-right" class="right-menu flex-row flex-column flex-fill justify-content-between">
      </div>
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <script type="text/javascript" src="./lib/jquery/base64.js"></script>
  <script src="./lib/echarts/echarts.min.js"></script>
  <script src="./lib/charts/echarts.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 登录页js文件 -->
  <!-- <script src="js/e-BrMainLeft.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-BrMainLeft.js?v=" + Date.now() + "'><\/script>");
  </script>
</body>

</html>