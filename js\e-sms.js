
let Templates=[
  {mc:'模版一', ID:'90048', temp:'您好，医师已经电话联系您或者您家人@shiJian来住院，请携带相关资料（@xiangGuanZL）到@diDian来办理住院。办公室联系电话 @lianXiDH，祝早日康复！'},
  {mc:'模版二', ID:'90049', temp:'第一步：门诊楼1楼交费处预存3000。\
  第二步：门诊楼1楼入院准备中心抽血、做心电图\
  第三步：门诊楼1楼入院准备中心预约B超、CT\
  第四步：回家等电话通知，有疑问在工作时间拨打该号码咨询（@lianXiDH，@diDian）\
  注意：办理预住院后，门诊不要挂其他科室门诊，否则影响住院报销，后果自负！'},
  {mc:'模版三', ID:'90050', temp:'请至一号楼一楼入院准备中心完善相关检查，若检查今日无法完成，需要预约，请预约至@shiJian，届时完成后入院。'},
  {mc:'模版四', ID:'90052', temp:'您好，医生已电话联系您或家人@shiJian来复诊，请携带相关资料（@xiangGuanZL）到@diDian来就诊。@lianXiDH'},
  {mc:'模版五', ID:'90053', temp:'医生已安排您住院，请@shijian至@diDian办住院手续。请随身带好行李，医保卡，银行卡以及生活用品换洗衣物等准时入院。请勿佩戴耳饰,项链，手镯等首饰；女士请卸除美甲；陪护需携带被子软枕；备齐各类生活用品（包括水杯 筷子 勺子吸管等）'},
]
let z_index=null//
//统一页面启动
$(document).ready(() => {
  let url = window.location.href.split("?") || []
  let text = url[1].split("&")
  let params = {}
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
  WRT_config.url = params
  WRT_e.api.ehrSz.getInit({
    success(data) {
      let obj = JSON.parse(data.Result)
      if (data.CodeMsg == 1) {
        WRT_e.api.BrMainLeft.getinit({
          params: {
            ll_blid: params["al_blid"],
            ls_idb: params["ls_idb"],
            al_zkid:params["al_zkid"],
            al_yhid:obj.YHXX.YSYHID
          },
          success(data) {
            if (data.Code == 1) {
              WRT_config.init = JSON.parse(data.Result)
              app.init()
            }
          }
        })
      }
    }
  })
})
//
var app = {
  init: function () {
    $("#head_info").html(
      new head_View().init({
        data:[]
      }).render().$el
    )
    
  }
}

var head_View = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
      <div class="head_inner">
        <label><span>手机号:</span><input id="tb_phone" type="text" class="text" autocomplete="off" /></label></br>
        <label><span>内容:</span><button class="e_btn" onclick="onSeach()">模板</button></label></br>
        <textarea id="tb_text" rows="5" readonly></textarea></br>
        <div id="tr_lists">
        </div>
        <button class="e_btn" onclick="onSmg()">发送</button>
        <button class="e_btn" onclick="reset()">重置</button>
        <button class="e_btn" onclick="sendMsg()">发送入院通知</button>
      </div>
    `)
    return this
  },
  events: {
  },
})

/********************公用方法********************/
//模板展示
function onSeach(){
  let temp=`
  <table class="smg_table">
    <thead class="table_head">
      <th width='50px' style="border: 1px solid gray">序号</th>
      <th width='100px'style="border: 1px solid gray">名称</th>
      <th style="border: 1px solid gray">内容</th>
      <th width='100px' style="border: 1px solid gray">操作</th>
    </thead>
    <tbody id="smg_data">
    ${_.map(Templates,(obj,index)=>`
    <tr ondblclick="change(${index})">
      <td style="text-align: center;border: 1px solid gray">${obj.ID}</td>
      <td style="text-align: center;border: 1px solid gray">${obj.mc}</td>
      <td class="smg_text" title="${obj.temp}">${obj.temp}</td>
      <td style="border: 1px solid gray"><a class="smg_btn" onclick="change(${index})">确定</a></td>
    </tr>
    `).join('')}                                                                                                                                                                                                                
    </tbody>
  </table>
  `
  WRT_e.ui.model({
    id: "MsgTemplates",
    title: "模板列表",
    width: "650px",
    content: temp,
    iframe: false,
  })
}
//确定模板
function change(index){
  z_index=index
  let list=Templates[index]
  $("#tb_text")[0].value=list.temp
  let html=''
  if(list.ID==90048){
    html=`<label><span>时间:</span><input id="tb_sj" type="text" class="text" autocomplete="off" /></label></br>
    <label><span>相关资料:</span><input id="tb_xgzl" type="text" class="text" autocomplete="off" /></label></br>
    <label><span>地点:</span><input id="tb_dd" type="text" class="text" autocomplete="off" /></label></br>
    <label><span>咨询电话:</span><input id="tb_lxdh" type="text" class="text" autocomplete="off" /></label></br>`
  }else if(list.ID==90049){
    html=`<label><span>地点:</span><input id="tb_dd" type="text" class="text" autocomplete="off" /></label></br>
    <label><span>咨询电话:</span><input id="tb_lxdh" type="text" class="text" autocomplete="off" /></label></br>`
  }else if(list.ID==90050){
    html=`<label><span>时间:</span><input id="tb_sj" type="text" class="text" autocomplete="off" /></label></br>`
  }else if(list.ID==90052){
    html=`<label><span>时间:</span><input id="tb_sj" type="text" class="text" autocomplete="off" /></label></br>
    <label><span>相关资料:</span><input id="tb_xgzl" type="text" class="text" autocomplete="off" /></label></br>
    <label><span>地点:</span><input id="tb_dd" type="text" class="text" autocomplete="off" /></label></br>
    <label><span>咨询电话:</span><input id="tb_lxdh" type="text" class="text" autocomplete="off" /></label></br>`
  }else if(list.ID==90053){
    html=`
    <label><span>时间:</span><input id="tb_sj" type="text" class="text" autocomplete="off" /></label></br>
    <label><span>地点:</span><input id="tb_dd" type="text" class="text" autocomplete="off" /></label></br>`
  }
  $("#tr_lists").html(html)
  $('#MsgTemplates').iziModal('destroy')
}


//短信发送
function onSmg(){
  if(z_index==null){
    WRT_e.ui.hint({msg:'请先选中模板！'})
    return
  }
  let smg=$("#tb_text")[0].value
  let list=Templates[z_index]||{}
  let text=$("#tb_phone")[0].value
  let phone=text.split(',')
  if(!text){
    WRT_e.ui.hint({msg:'请先输入手机号！'})
    return
  }
  WRT_e.api.BrMainLeft.sendMsg({
    params:{
      al_phoneNums:phone,
      as_msgTemplate:smg,
      al_zkid:WRT_config.url.al_zkid,
      al_yhid:WRT_config.url.al_yhid,
      al_blid:WRT_config.url.al_blid,
      al_lxdh:$("#tb_lxdh")[0]?$("#tb_lxdh")[0].value||'':'',
      al_xxbh:list.ID,
      al_sj:$("#tb_sj")[0]?$("#tb_sj")[0].value||'':'',
      al_dd:$("#tb_dd")[0]?$("#tb_dd")[0].value||'':'',
      al_xgzl:$("#tb_xgzl")[0]?$("#tb_xgzl")[0].value||'':'',
    },
    success(data){
      if(data.Code==1){
        WRT_e.ui.hint({type:'success',msg:'发送成功'})
      }
    }
  })
}
//重置
function reset(){
  $('#tb_text')[0].value=''
  $('#tb_phone')[0].value=''
  change(z_index)
}
// 发送入院通知
function sendMsg() {
  // console.log(WRT_config.url.al_blid);
  let url =`${WRT_config.server}/zydj/sendmessage.aspx?as_blid=${WRT_config.url.al_blid}&as_openmode=ehr3`
  WRT_e.ui.model({
    id: "MsgTemplates",
    title: "发送入院通知",
    width: "650px",
    iframe: true,
    iframeURL: url
  })
  
}