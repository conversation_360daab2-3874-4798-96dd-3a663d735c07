// color
$color-primary: #356ac5 !default;
$color-primary-blue: #3b76ef !default;
$color-primary-purple: #a66dd4 !default;
$color-primary-blue2: #4978ca !default;
$color-success: #66be74 !default;
$color-warning: #ed6a0c !default;
$color-danger: #f35656 !default;
$color-info: #909399 !default;

// sidebar
$sideBarWidth: calc(100vw * 0.113); //弃用，使用--side-bar-width替代

// normalMode
$menuBg: #356ac5;
$menuText: rgb(255 255 255 / 80%);
//https://github.com/ElemeFE/element/issues/12951
$menuActive: #ffffff;
$menuActiveText: #356ac5;
$menuHover: #5888dc;

$subMenuBg: #285bb5;
$subMenuHover: #5888dc;
$subMenuActiveText: rgb(255 255 255 / 80%);


// nightMode
$menuBgNight: #cecece;
$menuTextNight: rgb(52 52 52 / 80%);

$menuActiveNight: #ffffff;
$menuActiveTextNight: rgb(255 255 255 / 80%);
$menuHoverNight: #e1e1e1;

$subMenuBgNight: #a6a6a6;
$subMenuHoverNight: #e1e1e1;
$subMenuActiveTextNight: rgb(53 106 197 / 80%);


// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  colorPrimary: $color-primary;
  colorPrimaryBlue: $color-primary-blue;
  colorPrimaryPurple: $color-primary-purple;
  colorPrimaryBlue2: $color-primary-blue2;
  colorSuccess: $color-success;
  colorWarning: $color-warning;
  colorDanger: $color-danger;
  colorInfo: $color-info;
}
